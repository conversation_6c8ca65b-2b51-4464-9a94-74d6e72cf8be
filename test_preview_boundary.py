#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试预览边界修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_preview_boundary():
    """测试预览边界修复效果"""
    print("=" * 80)
    print("🖼️ 预览边界修复测试")
    print("=" * 80)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QPoint
        from PyQt5.QtGui import QFont, QFontMetrics
        
        app = QApplication(sys.argv)
        
        # 模拟预览标签的尺寸
        preview_width = 640
        preview_height = 360
        
        # 模拟字体和文字
        font = QFont("Microsoft YaHei", 16, QFont.Bold)
        font_metrics = QFontMetrics(font)
        
        # 测试不同长度的文字
        test_texts = [
            "短文字",
            "中等长度的水印文字",
            "这是一个很长的水印文字测试内容",
            "超级超级超级长的水印文字测试内容用来验证边界处理"
        ]
        
        # 测试不同的位置
        positions = [
            (50, 50, "中心"),
            (85, 85, "默认位置"),
            (95, 95, "极右下角"),
            (90, 10, "右上角"),
            (10, 90, "左下角")
        ]
        
        print(f"预览尺寸: {preview_width}x{preview_height}")
        print()
        
        for text in test_texts:
            print(f"测试文字: '{text}'")
            
            # 计算文字尺寸
            text_rect = font_metrics.boundingRect(text)
            text_width = text_rect.width()
            text_height = text_rect.height()
            
            print(f"  文字尺寸: {text_width}x{text_height}")
            
            for x_percent, y_percent, desc in positions:
                # 原始位置计算
                original_x = int((preview_width * x_percent) / 100)
                original_y = int((preview_height * y_percent) / 100)
                
                # 边界调整逻辑（与修复后的代码一致）
                adjusted_x = original_x
                adjusted_y = original_y
                
                # 确保水印不超出右边界
                if adjusted_x + text_width > preview_width - 10:
                    adjusted_x = preview_width - text_width - 10
                
                # 确保水印不超出下边界
                if adjusted_y + text_height > preview_height - 10:
                    adjusted_y = preview_height - text_height - 10
                
                # 确保水印不超出左边界
                if adjusted_x < 10:
                    adjusted_x = 10
                
                # 确保水印不超出上边界
                if adjusted_y < text_height + 10:
                    adjusted_y = text_height + 10
                
                # 检查是否需要调整
                adjusted = (adjusted_x != original_x) or (adjusted_y != original_y)
                
                print(f"    {desc}:")
                print(f"      原始位置: ({original_x}, {original_y})")
                if adjusted:
                    print(f"      调整位置: ({adjusted_x}, {adjusted_y}) ✅ 已调整")
                else:
                    print(f"      调整位置: ({adjusted_x}, {adjusted_y}) ✓ 无需调整")
                
                # 验证调整后的位置是否在边界内
                right_edge = adjusted_x + text_width
                bottom_edge = adjusted_y + text_height
                
                if (adjusted_x >= 10 and 
                    adjusted_y >= text_height + 10 and 
                    right_edge <= preview_width - 10 and 
                    bottom_edge <= preview_height - 10):
                    print(f"      边界检查: ✅ 完全在预览区域内")
                else:
                    print(f"      边界检查: ❌ 仍然超出边界")
                    print(f"        右边缘: {right_edge} (限制: {preview_width - 10})")
                    print(f"        下边缘: {bottom_edge} (限制: {preview_height - 10})")
            
            print()
        
        print("修复效果总结:")
        print("✅ 水印位置会自动调整以保持在预览区域内")
        print("✅ 长文字不会被右边界遮挡")
        print("✅ 水印不会被下边界遮挡")
        print("✅ 保持10像素的安全边距")
        print("✅ 极端位置会被自动修正")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    test_preview_boundary()
