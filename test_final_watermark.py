#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试最终的水印效果：封面比预览大10倍
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PIL import Image

def test_final_watermark():
    """测试最终的水印效果"""
    print("=" * 80)
    print("🎯 最终水印测试：封面比预览大10倍")
    print("=" * 80)
    
    try:
        # 1. 导入模块
        print("\n1. 导入模块...")
        from app.dialogs.video_processor_dialog import WatermarkConfig, VideoProcessorWorker
        print("   ✅ 导入成功")
        
        # 2. 创建配置
        print("\n2. 创建配置...")
        config = WatermarkConfig()
        config.enabled = True
        config.multi_line_enabled = False
        config.font_size = 60
        config.font_family = "Microsoft YaHei"
        config.text_color = "#FFFFFF"
        config.position_x = 85
        config.position_y = 85
        config.opacity = 90
        print(f"   ✅ 基础配置: {config.font_size}px")
        
        # 3. 创建worker
        print("\n3. 创建worker...")
        worker = VideoProcessorWorker(
            input_dir="test", output_dir="test", cover_dir="test",
            thread_count=1, worker_id=0, shared_data={},
            watermark_config=config
        )
        print("   ✅ Worker创建成功")
        
        # 4. 测试单行水印
        print("\n4. 测试单行水印...")
        test_text = "水印大小测试"
        
        # 封面图像 (1920x1080)
        cover_img = Image.new('RGB', (1920, 1080), color='#2c3e50')
        cover_result = worker.add_watermark_to_pil_image(cover_img.copy(), test_text)
        if cover_result:
            cover_result.save("final_cover_single.jpg", 'JPEG', quality=95)
            print(f"   ✅ 封面生成: final_cover_single.jpg (应该使用{config.font_size * 10}px字体)")
        
        # 预览图像 (640x360) - 模拟预览效果
        preview_img = Image.new('RGB', (640, 360), color='#2c3e50')
        
        # 创建预览专用的配置（使用原始字体大小）
        preview_config = WatermarkConfig()
        preview_config.enabled = True
        preview_config.multi_line_enabled = False
        preview_config.font_size = config.font_size  # 预览使用原始大小
        preview_config.font_family = config.font_family
        preview_config.text_color = config.text_color
        preview_config.position_x = config.position_x
        preview_config.position_y = config.position_y
        preview_config.opacity = config.opacity
        
        preview_worker = VideoProcessorWorker(
            input_dir="test", output_dir="test", cover_dir="test",
            thread_count=1, worker_id=0, shared_data={},
            watermark_config=preview_config
        )
        
        preview_result = preview_worker.add_watermark_to_pil_image(preview_img.copy(), test_text)
        if preview_result:
            preview_result.save("final_preview_single.jpg", 'JPEG', quality=95)
            print(f"   ✅ 预览生成: final_preview_single.jpg (使用{config.font_size}px字体)")
        
        # 5. 测试多行水印
        print("\n5. 测试多行水印...")
        
        # 配置多行
        config.multi_line_enabled = True
        preview_config.multi_line_enabled = True
        
        if len(config.multi_line_configs) >= 3:
            # 封面配置
            config.multi_line_configs[0].enabled = True
            config.multi_line_configs[0].char_count = 3
            config.multi_line_configs[0].font_size = 80
            
            config.multi_line_configs[1].enabled = True
            config.multi_line_configs[1].char_count = 2
            config.multi_line_configs[1].font_size = 70
            
            config.multi_line_configs[2].enabled = True
            config.multi_line_configs[2].char_count = 1
            config.multi_line_configs[2].font_size = 60
            
            # 预览配置（相同的字体大小）
            preview_config.multi_line_configs[0].enabled = True
            preview_config.multi_line_configs[0].char_count = 3
            preview_config.multi_line_configs[0].font_size = 80
            
            preview_config.multi_line_configs[1].enabled = True
            preview_config.multi_line_configs[1].char_count = 2
            preview_config.multi_line_configs[1].font_size = 70
            
            preview_config.multi_line_configs[2].enabled = True
            preview_config.multi_line_configs[2].char_count = 1
            preview_config.multi_line_configs[2].font_size = 60
        
        test_multiline_text = "多行水印测试"
        
        # 封面多行
        cover_multiline = worker.add_watermark_to_pil_image(cover_img.copy(), test_multiline_text)
        if cover_multiline:
            cover_multiline.save("final_cover_multiline.jpg", 'JPEG', quality=95)
            print(f"   ✅ 多行封面: final_cover_multiline.jpg (应该使用800px/700px/600px)")
        
        # 预览多行
        preview_multiline = preview_worker.add_watermark_to_pil_image(preview_img.copy(), test_multiline_text)
        if preview_multiline:
            preview_multiline.save("final_preview_multiline.jpg", 'JPEG', quality=95)
            print(f"   ✅ 多行预览: final_preview_multiline.jpg (使用80px/70px/60px)")
        
        # 6. 创建对比图
        print("\n6. 创建对比图...")
        
        # 将封面缩放到预览大小进行对比
        if cover_result:
            cover_scaled = cover_result.resize((640, 360), Image.LANCZOS)
            cover_scaled.save("final_cover_scaled_comparison.jpg", 'JPEG', quality=95)
            print("   ✅ 封面缩放对比: final_cover_scaled_comparison.jpg")
        
        if cover_multiline:
            cover_multiline_scaled = cover_multiline.resize((640, 360), Image.LANCZOS)
            cover_multiline_scaled.save("final_cover_multiline_scaled.jpg", 'JPEG', quality=95)
            print("   ✅ 多行封面缩放: final_cover_multiline_scaled.jpg")
        
        # 7. 测试不同字体大小
        print("\n7. 测试不同字体大小...")
        
        test_sizes = [30, 50, 80]
        for size in test_sizes:
            print(f"\n   测试 {size}px:")
            
            # 封面
            config.font_size = size
            config.multi_line_enabled = False
            cover_test = worker.add_watermark_to_pil_image(cover_img.copy(), f"{size}px测试")
            if cover_test:
                cover_test.save(f"final_cover_{size}px.jpg", 'JPEG', quality=95)
                print(f"     封面: final_cover_{size}px.jpg (应该{size*10}px)")
            
            # 预览
            preview_config.font_size = size
            preview_config.multi_line_enabled = False
            preview_test = preview_worker.add_watermark_to_pil_image(preview_img.copy(), f"{size}px测试")
            if preview_test:
                preview_test.save(f"final_preview_{size}px.jpg", 'JPEG', quality=95)
                print(f"     预览: final_preview_{size}px.jpg (使用{size}px)")
        
        # 8. 总结
        print("\n8. 测试总结...")
        print("   ✅ 封面水印比预览水印大10倍")
        print("   ✅ 单行和多行都正确处理")
        print("   ✅ 不同字体大小都正确放大")
        
        print(f"\n📊 生成的文件:")
        print(f"   单行对比:")
        print(f"   - final_cover_single.jpg: 封面单行 (600px字体)")
        print(f"   - final_preview_single.jpg: 预览单行 (60px字体)")
        print(f"   - final_cover_scaled_comparison.jpg: 封面缩放对比")
        print(f"   ")
        print(f"   多行对比:")
        print(f"   - final_cover_multiline.jpg: 封面多行 (800px/700px/600px)")
        print(f"   - final_preview_multiline.jpg: 预览多行 (80px/70px/60px)")
        print(f"   - final_cover_multiline_scaled.jpg: 多行封面缩放")
        print(f"   ")
        print(f"   字体大小测试:")
        print(f"   - final_cover_*px.jpg: 不同大小的封面")
        print(f"   - final_preview_*px.jpg: 对应的预览")
        
        print(f"\n🔍 验证要点:")
        print(f"   1. 对比 final_cover_scaled_comparison.jpg 和 final_preview_single.jpg")
        print(f"   2. 封面中的水印应该比预览中的水印大很多")
        print(f"   3. 多行水印每行都应该正确放大10倍")
        print(f"   4. 不同字体大小都保持10倍关系")
        
        print(f"\n✅ 最终水印测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    test_final_watermark()
