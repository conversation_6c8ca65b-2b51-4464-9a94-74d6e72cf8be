#!/usr/bin/env python3
"""
最终一致性测试
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QDialog

# 添加项目路径
sys.path.append('.')

def test_final_consistency():
    """最终一致性测试"""
    try:
        from app.dialogs.video_processor_dialog import WatermarkConfigDialog, WatermarkConfig
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("最终一致性测试")
        main_window.setGeometry(100, 100, 1000, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("""
🎯 最终一致性测试

修复内容：
✅ 在WatermarkConfigDialog中添加了add_watermark_to_image方法
✅ 预览使用1920x1080画布
✅ 预览使用与封面相同的水印处理逻辑
✅ 预览缩放到640x360显示

现在预览和封面应该完全一致了！

测试步骤：
1. 打开水印配置对话框
2. 设置字体大小（建议72px或更大）
3. 观察预览中的水印大小
4. 关闭对话框
5. 生成封面，对比水印大小

如果还是不一致，请告诉我具体的差异！
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #e6ffe6;
                padding: 20px;
                border-radius: 8px;
                font-size: 16px;
                line-height: 1.6;
                border: 2px solid #00cc00;
                color: #006600;
            }
        """)
        layout.addWidget(info_label)
        
        # 添加测试按钮
        test_button = QPushButton("🎯 开始最终一致性测试")
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #00cc00;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #009900;
            }
        """)
        
        def open_dialog():
            print("=" * 80)
            print("开始最终一致性测试")
            print("=" * 80)
            
            try:
                # 创建水印配置
                config = WatermarkConfig()
                config.enabled = True
                config.font_size = 72  # 设置较大的字体
                
                print("✅ 水印配置创建成功")
                print(f"测试字体大小: {config.font_size}px")
                
                # 打开配置对话框
                dialog = WatermarkConfigDialog(config, main_window)
                print("✅ 对话框创建成功")
                
                # 检查方法是否存在
                if hasattr(dialog, 'add_watermark_to_image'):
                    print("✅ add_watermark_to_image 方法已添加")
                else:
                    print("❌ add_watermark_to_image 方法不存在")
                
                print("\n🎯 测试说明:")
                print("1. 在对话框中，字体大小已设置为72px")
                print("2. 观察预览中的水印大小")
                print("3. 如果觉得还是小，可以调整到84px或96px")
                print("4. 预览中看到的大小就是封面中的大小")
                print("5. 现在预览使用1920x1080画布，与封面完全一致")
                
                print("\n📊 技术细节:")
                print("- 预览画布: 1920x1080")
                print("- 封面画布: 1920x1080")
                print("- 预览方法: add_watermark_to_image (新添加)")
                print("- 封面方法: add_watermark_to_pil_image")
                print("- 字体大小: 完全一致")
                
                result = dialog.exec_()
                
                if result == QDialog.Accepted:
                    print("\n✅ 测试完成")
                    print(f"最终字体大小: {config.font_size}px")
                    
                    print("\n🎊 如果预览与封面现在一致了:")
                    print("- 问题已完全解决！")
                    print("- 你可以调整字体大小到你满意的程度")
                    
                    print("\n⚠️ 如果预览与封面还是不一致:")
                    print("- 请告诉我具体的差异")
                    print("- 比如：预览大，封面小？还是相反？")
                    print("- 差异大概是多少倍？")
                        
                else:
                    print("\n❌ 测试已取消")
                
            except Exception as e:
                print(f"❌ 测试过程中出现错误: {e}")
                import traceback
                traceback.print_exc()
            
            print("=" * 80)
        
        test_button.clicked.connect(open_dialog)
        layout.addWidget(test_button)
        
        # 添加字体大小建议
        size_label = QLabel("""
📏 字体大小建议：

如果觉得水印太小，建议的字体大小：
• 小水印：48-60px
• 中等水印：60-72px  
• 大水印：72-84px
• 超大水印：84-96px

现在预览与封面完全一致，你在预览中看到的大小就是最终效果！
        """)
        size_label.setStyleSheet("""
            QLabel {
                background-color: #f0f8ff;
                padding: 15px;
                border-radius: 8px;
                font-size: 14px;
                color: #191970;
                border-left: 4px solid #4169e1;
            }
        """)
        layout.addWidget(size_label)
        
        main_window.show()
        
        print("最终一致性测试应用启动成功")
        print("请点击按钮开始测试")
        
        return app.exec_()
        
    except Exception as e:
        print(f"测试启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("启动最终一致性测试...")
    return test_final_consistency()

if __name__ == "__main__":
    sys.exit(main())
