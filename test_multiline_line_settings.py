#!/usr/bin/env python3
"""
测试多行水印行级样式设置功能的脚本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QDialog

# 添加项目路径
sys.path.append('.')

def test_multiline_line_settings():
    """测试多行水印行级样式设置功能"""
    try:
        from app.dialogs.video_processor_dialog import WatermarkConfigDialog, WatermarkConfig
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("多行水印行级样式设置测试")
        main_window.setGeometry(100, 100, 1000, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("""
🎨 多行水印行级样式设置功能测试

核心功能：
✅ 选中任意一行水印进行单独设置
✅ 每行可设置不同的字体、字号、颜色
✅ 每行可设置不同的透明度、阴影等效果
✅ 实时保存和加载行级配置

测试步骤：
1. 点击下方按钮打开水印配置对话框
2. 启用"多行水印模式"
3. 使用"当前编辑行"选择要编辑的行（如第1行）
4. 在字体、样式、特效组中修改设置（如改变字体、颜色）
5. 切换到第2行，设置不同的样式
6. 切换到第3行，再设置不同的样式
7. 观察预览区域中每行的不同效果

预期效果：
- 第1行：可能是微软雅黑、白色、36px
- 第2行：可能是楷体、红色、28px
- 第3行：可能是黑体、蓝色、32px
- 每行都有独立的样式设置
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f0f8ff;
                padding: 20px;
                border-radius: 8px;
                font-size: 14px;
                line-height: 1.6;
                border: 2px solid #4169e1;
                color: #191970;
            }
        """)
        layout.addWidget(info_label)
        
        # 添加测试按钮
        test_button = QPushButton("🎨 打开多行水印配置对话框（测试行级设置）")
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #4169e1;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #1e90ff;
            }
        """)
        
        def open_dialog():
            print("=" * 80)
            print("开始多行水印行级样式设置功能测试")
            print("=" * 80)
            
            try:
                # 创建水印配置
                config = WatermarkConfig()
                config.enabled = True
                config.multi_line_enabled = True  # 直接启用多行模式
                
                print("✅ 水印配置创建成功")
                print(f"多行模式启用: {config.multi_line_enabled}")
                print(f"多行配置数量: {len(config.multi_line_configs)}")
                
                # 显示初始配置
                for i, line_config in enumerate(config.multi_line_configs):
                    print(f"第{i+1}行初始配置:")
                    print(f"  字符数: {line_config.char_count}")
                    print(f"  字体: {line_config.font_family}")
                    print(f"  字号: {line_config.font_size}")
                    print(f"  颜色: {line_config.text_color}")
                    print(f"  位置: ({line_config.position_x}%, {line_config.position_y}%)")
                
                # 打开配置对话框
                dialog = WatermarkConfigDialog(config, main_window)
                print("✅ 对话框创建成功")
                
                print("\n请在对话框中测试行级样式设置功能...")
                print("测试要点：")
                print("1. 选择第1行，设置为微软雅黑、白色、36px")
                print("2. 选择第2行，设置为楷体、红色、28px")
                print("3. 选择第3行，设置为黑体、蓝色、32px")
                print("4. 观察预览区域中每行的不同效果")
                print("5. 切换行时观察界面控件是否正确加载该行的配置")
                
                result = dialog.exec_()
                
                if result == QDialog.Accepted:
                    print("\n✅ 配置已确认")
                    print("最终配置结果:")
                    
                    for i, line_config in enumerate(config.multi_line_configs):
                        print(f"第{i+1}行最终配置:")
                        print(f"  字符数: {line_config.char_count}")
                        print(f"  字体: {line_config.font_family}")
                        print(f"  字号: {line_config.font_size}")
                        print(f"  颜色: {line_config.text_color}")
                        print(f"  透明度: {line_config.opacity}%")
                        print(f"  位置: ({line_config.position_x}%, {line_config.position_y}%)")
                        print(f"  阴影: {'启用' if line_config.shadow_enabled else '禁用'}")
                        print(f"  描边: {'启用' if line_config.stroke_enabled else '禁用'}")
                        print()
                else:
                    print("\n❌ 配置已取消")
                
            except Exception as e:
                print(f"❌ 测试过程中出现错误: {e}")
                import traceback
                traceback.print_exc()
            
            print("=" * 80)
        
        test_button.clicked.connect(open_dialog)
        layout.addWidget(test_button)
        
        # 添加功能说明
        features_label = QLabel("""
🚀 行级样式设置功能特性：

1. 🎯 精确的行选择
   - 使用"当前编辑行"下拉框选择要编辑的行
   - 选择行时自动加载该行的所有配置到界面
   - 支持通过预览区域点击选择行

2. 🎨 完整的样式控制
   - 字体设置：字体分类、字体名称、字体大小
   - 颜色设置：文字颜色、阴影颜色、描边颜色
   - 特效设置：透明度、阴影效果、描边效果
   - 位置设置：X位置、Y位置（支持拖拽）

3. 💾 智能的配置管理
   - 修改任何设置时自动保存到当前选中的行
   - 切换行时自动加载该行的配置到界面
   - 支持配置的序列化和反序列化

4. 👁️ 实时预览效果
   - 所有设置修改立即反映在预览区域
   - 每行显示不同的样式效果
   - 支持独立的行级拖拽调整位置

5. 🔄 无缝的用户体验
   - 界面控件状态与行配置完全同步
   - 避免配置丢失和状态不一致
   - 提供清晰的视觉反馈
        """)
        features_label.setStyleSheet("""
            QLabel {
                background-color: #f5f5f5;
                padding: 15px;
                border-radius: 8px;
                font-size: 13px;
                color: #333333;
                border-left: 4px solid #4169e1;
            }
        """)
        layout.addWidget(features_label)
        
        main_window.show()
        
        print("多行水印行级样式设置测试应用启动成功")
        print("请点击按钮开始测试")
        
        return app.exec_()
        
    except Exception as e:
        print(f"测试启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("开始多行水印行级样式设置功能测试...")
    return test_multiline_line_settings()

if __name__ == "__main__":
    sys.exit(main())
