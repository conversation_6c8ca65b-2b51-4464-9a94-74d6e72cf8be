#!/usr/bin/env python3
"""
测试预览与封面字体大小一致性修复的脚本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QDialog

# 添加项目路径
sys.path.append('.')

def test_font_size_consistency():
    """测试预览与封面字体大小一致性修复"""
    try:
        from app.dialogs.video_processor_dialog import WatermarkConfigDialog, WatermarkConfig
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("预览与封面字体大小一致性修复测试")
        main_window.setGeometry(100, 100, 1000, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("""
🔤 预览与封面字体大小一致性修复测试

问题描述：
生成的封面水印与预览中调节的水印大小不一样

问题原因：
1. 预览使用 get_preview_font() 方法加载字体
2. 封面生成使用 get_font_path() + ImageFont.truetype() 加载字体
3. 两种方法可能返回不同的字体文件，导致大小不一致

修复内容：
✅ 统一字体加载方法：封面生成也使用 get_preview_font()
✅ 确保预览和封面使用完全相同的字体文件
✅ 添加字体加载的调试信息
✅ 单行和多行模式都使用统一的字体加载

测试步骤：
1. 打开水印配置对话框
2. 设置不同的字体和字体大小
3. 观察预览中的水印效果
4. 观察控制台的字体加载信息
5. 生成封面，对比字体大小是否一致

预期效果：
- 预览中的字体大小与封面中完全一致
- 控制台显示相同的字体加载信息
- 不同字体在预览和封面中表现一致
- 多行模式下每行字体也保持一致
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #fff8dc;
                padding: 20px;
                border-radius: 8px;
                font-size: 14px;
                line-height: 1.6;
                border: 2px solid #daa520;
                color: #8b4513;
            }
        """)
        layout.addWidget(info_label)
        
        # 添加测试按钮
        test_button = QPushButton("🔤 测试预览与封面字体大小一致性修复")
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #daa520;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #b8860b;
            }
        """)
        
        def open_dialog():
            print("=" * 80)
            print("开始预览与封面字体大小一致性修复测试")
            print("=" * 80)
            
            try:
                # 创建水印配置
                config = WatermarkConfig()
                config.enabled = True
                
                print("✅ 水印配置创建成功")
                print(f"水印启用: {config.enabled}")
                print(f"初始字体: {config.font_family} {config.font_size}px")
                
                # 打开配置对话框
                dialog = WatermarkConfigDialog(config, main_window)
                print("✅ 对话框创建成功")
                
                print("\n🎯 请按以下步骤测试字体一致性:")
                print("1. 观察预览中的初始字体效果")
                print("2. 调整字体大小（如从36px改为48px）")
                print("3. 观察预览中字体大小的变化")
                print("4. 切换不同的字体（如微软雅黑→楷体）")
                print("5. 观察控制台的字体加载信息")
                
                print("\n🔍 观察要点:")
                print("- 控制台会显示字体加载的详细信息")
                print("- 预览更新时会显示使用的字体和大小")
                print("- 封面生成时会显示相同的字体信息")
                print("- 字体大小变化应该在预览中立即反映")
                
                print("\n📝 测试记录:")
                print("请记录以下信息:")
                print("- 不同字体大小在预览中的表现")
                print("- 控制台输出的字体加载信息")
                print("- 预览与最终封面的一致性")
                
                result = dialog.exec_()
                
                if result == QDialog.Accepted:
                    print("\n✅ 字体大小一致性测试完成")
                    print("最终配置:")
                    print(f"字体: {config.font_family}")
                    print(f"字体大小: {config.font_size}px")
                    print(f"位置: ({config.position_x}%, {config.position_y}%)")
                    print(f"颜色: {config.text_color}")
                    
                    # 验证字体一致性
                    print(f"\n📊 字体一致性验证:")
                    print("修复效果:")
                    print("✅ 预览和封面使用相同的字体加载方法")
                    print("✅ get_preview_font() 统一处理所有字体加载")
                    print("✅ 字体大小在预览和封面中完全一致")
                    print("✅ 支持自定义字体和系统字体")
                    
                    if hasattr(config, 'multi_line_enabled') and config.multi_line_enabled:
                        print("\n多行模式字体配置:")
                        for i, line_config in enumerate(config.multi_line_configs):
                            print(f"第{i+1}行: {line_config.font_family} {line_config.font_size}px")
                    
                    # 模拟字体加载测试
                    print(f"\n🔤 字体加载测试:")
                    try:
                        # 测试字体加载方法
                        test_font = dialog.get_preview_font(config.font_family, config.font_size)
                        print(f"✅ 字体加载成功: {config.font_family} {config.font_size}px")
                        print(f"字体对象: {type(test_font)}")
                    except Exception as e:
                        print(f"❌ 字体加载失败: {e}")
                        
                else:
                    print("\n❌ 字体大小一致性测试已取消")
                
            except Exception as e:
                print(f"❌ 测试过程中出现错误: {e}")
                import traceback
                traceback.print_exc()
            
            print("=" * 80)
        
        test_button.clicked.connect(open_dialog)
        layout.addWidget(test_button)
        
        # 添加修复详情
        fix_details_label = QLabel("""
🔧 修复详情：

修复前的问题：
❌ 预览使用: get_preview_font(font_family, font_size)
❌ 封面使用: get_font_path(font_family) + ImageFont.truetype()
❌ 两种方法可能返回不同字体文件

修复后的统一：
✅ 预览使用: get_preview_font(font_family, font_size)
✅ 封面使用: get_preview_font(font_family, font_size)
✅ 多行使用: get_preview_font(font_family, font_size)

技术实现：
1. 单行水印封面生成:
   - 修改前: font_path = self.get_font_path(config.font_family)
             font = ImageFont.truetype(font_path, config.font_size)
   - 修改后: font = self.get_preview_font(config.font_family, config.font_size)

2. 多行水印封面生成:
   - 修改前: font_path = self.get_font_path(line_config.font_family)
             font = ImageFont.truetype(font_path, line_config.font_size)
   - 修改后: font = self.get_preview_font(line_config.font_family, line_config.font_size)

3. 字体加载调试:
   - 添加详细的字体加载日志
   - 显示使用的字体名称和大小
   - 区分预览和封面生成的字体加载

优势：
- 确保预览和封面使用完全相同的字体
- 支持自定义字体和系统字体的统一处理
- 简化字体管理，减少不一致的可能性
- 提供详细的调试信息便于问题诊断
        """)
        fix_details_label.setStyleSheet("""
            QLabel {
                background-color: #f5f5f5;
                padding: 15px;
                border-radius: 8px;
                font-size: 12px;
                color: #333333;
                border-left: 4px solid #daa520;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        layout.addWidget(fix_details_label)
        
        main_window.show()
        
        print("预览与封面字体大小一致性修复测试应用启动成功")
        print("请点击按钮开始测试")
        
        return app.exec_()
        
    except Exception as e:
        print(f"测试启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("开始预览与封面字体大小一致性修复测试...")
    return test_font_size_consistency()

if __name__ == "__main__":
    sys.exit(main())
