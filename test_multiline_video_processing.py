#!/usr/bin/env python3
"""
测试多行水印在视频处理中的修复脚本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QDialog

# 添加项目路径
sys.path.append('.')

def test_multiline_video_processing():
    """测试多行水印在视频处理中的修复"""
    try:
        from app.dialogs.video_processor_dialog import WatermarkConfigDialog, WatermarkConfig
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("多行水印视频处理修复测试")
        main_window.setGeometry(100, 100, 1000, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("""
🎬 多行水印视频处理修复测试

问题描述：
明明设置了多行水印，但处理视频时水印还是一行

问题原因：
add_watermark_to_pil_image 方法只实现了单行水印逻辑，
没有检查 multi_line_enabled 标志和处理多行配置

修复内容：
✅ 修改 add_watermark_to_pil_image 检查多行模式
✅ 新增 add_multiline_watermark_to_pil_image 方法
✅ 新增 get_line_text_for_video 方法分割文字
✅ 新增 add_line_watermark_to_overlay 方法绘制单行
✅ 支持每行独立的字体、颜色、位置、效果

测试步骤：
1. 打开水印配置对话框
2. 启用"多行水印模式"
3. 设置多行参数（字符数、位置、颜色等）
4. 点击确定保存配置
5. 观察控制台输出的处理信息

预期效果：
- 控制台显示"使用多行水印模式处理"
- 每行水印都会单独绘制
- 文字按字符数正确分割到各行
- 每行使用独立的样式设置
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f0f8ff;
                padding: 20px;
                border-radius: 8px;
                font-size: 14px;
                line-height: 1.6;
                border: 2px solid #4169e1;
                color: #191970;
            }
        """)
        layout.addWidget(info_label)
        
        # 添加测试按钮
        test_button = QPushButton("🎬 测试多行水印视频处理修复")
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #4169e1;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #1e90ff;
            }
        """)
        
        def open_dialog():
            print("=" * 80)
            print("开始多行水印视频处理修复测试")
            print("=" * 80)
            
            try:
                # 创建水印配置
                config = WatermarkConfig()
                config.enabled = True
                config.multi_line_enabled = True
                
                print("✅ 水印配置创建成功")
                print(f"多行模式启用: {config.multi_line_enabled}")
                print(f"多行配置数量: {len(config.multi_line_configs)}")
                
                # 显示初始多行配置
                print("\n📋 初始多行配置:")
                for i, line_config in enumerate(config.multi_line_configs):
                    print(f"第{i+1}行:")
                    print(f"  字符数: {line_config.char_count}")
                    print(f"  位置: ({line_config.position_x}%, {line_config.position_y}%)")
                    print(f"  字体: {line_config.font_family} {line_config.font_size}px")
                    print(f"  颜色: {line_config.text_color}")
                
                # 打开配置对话框
                dialog = WatermarkConfigDialog(config, main_window)
                print("✅ 对话框创建成功")
                
                print("\n🎯 请按以下步骤测试:")
                print("1. 确认多行水印模式已启用")
                print("2. 调整各行的字符数设置")
                print("3. 为不同行设置不同的颜色和位置")
                print("4. 点击确定保存配置")
                print("5. 观察控制台的处理信息")
                
                print("\n🔍 观察要点:")
                print("- 预览区域应该显示多行水印")
                print("- 每行应该有不同的样式")
                print("- 控制台会输出水印处理的调试信息")
                
                result = dialog.exec_()
                
                if result == QDialog.Accepted:
                    print("\n✅ 多行水印配置测试完成")
                    print("最终多行配置:")
                    
                    for i, line_config in enumerate(config.multi_line_configs):
                        print(f"\n第{i+1}行最终配置:")
                        print(f"  启用: {'是' if line_config.enabled else '否'}")
                        print(f"  字符数: {line_config.char_count}")
                        print(f"  字体: {line_config.font_family} {line_config.font_size}px")
                        print(f"  颜色: {line_config.text_color}")
                        print(f"  位置: ({line_config.position_x}%, {line_config.position_y}%)")
                        print(f"  透明度: {line_config.opacity}%")
                        print(f"  阴影: {'启用' if line_config.shadow_enabled else '禁用'}")
                        print(f"  描边: {'启用' if line_config.stroke_enabled else '禁用'}")
                    
                    # 模拟视频处理测试
                    print(f"\n🎬 模拟视频处理测试:")
                    test_filename = "测试视频文件名"
                    print(f"测试文件名: '{test_filename}'")
                    
                    # 测试文字分割
                    print("\n文字分割测试:")
                    for i, line_config in enumerate(config.multi_line_configs):
                        if line_config.enabled:
                            # 模拟 get_line_text_for_video 的逻辑
                            start_pos = sum(config.multi_line_configs[j].char_count for j in range(i))
                            end_pos = start_pos + line_config.char_count
                            line_text = test_filename[start_pos:end_pos] if start_pos < len(test_filename) else ""
                            print(f"第{i+1}行文字: '{line_text}' (字符{start_pos+1}-{end_pos})")
                    
                    # 验证修复效果
                    if config.multi_line_enabled:
                        print("\n✅ 修复验证通过:")
                        print("- 多行模式已启用")
                        print("- 视频处理时会使用多行水印逻辑")
                        print("- 每行会独立绘制水印")
                    else:
                        print("\n⚠️ 注意: 多行模式未启用，将使用单行模式")
                        
                else:
                    print("\n❌ 多行水印配置测试已取消")
                
            except Exception as e:
                print(f"❌ 测试过程中出现错误: {e}")
                import traceback
                traceback.print_exc()
            
            print("=" * 80)
        
        test_button.clicked.connect(open_dialog)
        layout.addWidget(test_button)
        
        # 添加修复详情
        fix_details_label = QLabel("""
🔧 修复详情：

修复前的问题：
❌ add_watermark_to_pil_image 只有单行逻辑
❌ 没有检查 multi_line_enabled 标志
❌ 多行配置被完全忽略

修复后的流程：
✅ 检查 multi_line_enabled 标志
✅ 多行模式 → 调用 add_multiline_watermark_to_pil_image
✅ 单行模式 → 调用 add_single_watermark_to_pil_image

新增方法说明：
1. add_multiline_watermark_to_pil_image:
   - 遍历所有多行配置
   - 为每行生成对应的文字
   - 调用单行绘制方法

2. get_line_text_for_video:
   - 根据字符数设置分割文字
   - 计算每行的起始和结束位置
   - 提取对应的文字片段

3. add_line_watermark_to_overlay:
   - 在overlay上绘制单行水印
   - 支持独立的字体、颜色、位置
   - 支持独立的阴影和描边效果

技术实现：
- 使用overlay图层叠加多行水印
- 每行独立计算位置和样式
- 最终合成到原图像上
        """)
        fix_details_label.setStyleSheet("""
            QLabel {
                background-color: #f5f5f5;
                padding: 15px;
                border-radius: 8px;
                font-size: 12px;
                color: #333333;
                border-left: 4px solid #4169e1;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        layout.addWidget(fix_details_label)
        
        main_window.show()
        
        print("多行水印视频处理修复测试应用启动成功")
        print("请点击按钮开始测试")
        
        return app.exec_()
        
    except Exception as e:
        print(f"测试启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("开始多行水印视频处理修复测试...")
    return test_multiline_video_processing()

if __name__ == "__main__":
    sys.exit(main())
