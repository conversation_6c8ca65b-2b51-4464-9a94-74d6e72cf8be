#!/usr/bin/env python3
"""
测试水印修复最终版本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QDialog

# 添加项目路径
sys.path.append('.')

def test_watermark_fix_final():
    """测试水印修复最终版本"""
    try:
        from app.dialogs.video_processor_dialog import WatermarkConfigDialog, WatermarkConfig
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("水印修复最终测试")
        main_window.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("""
🎯 水印修复最终测试

问题修复：
✅ 在VideoProcessorWorker中添加了get_preview_font方法
✅ 在VideoProcessorWorker中添加了hex_to_rgba方法
✅ 封面生成现在应该正常工作了

修复内容：
- 解决了"'VideoProcessorWorker' object has no attribute 'get_preview_font'"错误
- 确保封面生成使用与预览相同的字体加载逻辑
- 确保封面生成使用与预览相同的颜色处理逻辑

现在封面应该有水印了，并且与预览完全一致！

测试步骤：
1. 打开水印配置对话框
2. 调整字体大小（如120px）
3. 观察预览中的水印
4. 关闭对话框
5. 生成封面 - 应该有水印且与预览一致！

如果封面现在有水印了，说明修复成功！
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f0fff0;
                padding: 20px;
                border-radius: 8px;
                font-size: 16px;
                line-height: 1.6;
                border: 2px solid #32cd32;
                color: #006400;
            }
        """)
        layout.addWidget(info_label)
        
        # 添加测试按钮
        test_button = QPushButton("🎯 测试水印修复最终版本")
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #32cd32;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #228b22;
            }
        """)
        
        def start_test():
            print("=" * 80)
            print("开始水印修复最终测试")
            print("=" * 80)
            
            try:
                # 创建水印配置
                config = WatermarkConfig()
                config.enabled = True
                
                print("✅ 水印配置创建成功")
                print(f"默认字体大小: {config.font_size}px")
                
                # 创建对话框
                dialog = WatermarkConfigDialog(config, main_window)
                print("✅ 对话框创建成功")
                
                print("\n🔧 修复内容确认:")
                print("1. VideoProcessorWorker.get_preview_font() - 已添加")
                print("2. VideoProcessorWorker.hex_to_rgba() - 已添加")
                print("3. 封面生成逻辑 - 已修复")
                
                print("\n📊 预期效果:")
                print("- 封面生成不再报错")
                print("- 封面有水印显示")
                print("- 预览与封面大小一致")
                
                print("\n🔍 测试要点:")
                print("1. 观察预览是否正常显示水印")
                print("2. 生成封面是否有水印")
                print("3. 预览与封面的水印大小是否一致")
                print("4. 控制台是否还有错误信息")
                
                print("\n💡 如果封面现在有水印了:")
                print("- 说明基本修复成功")
                print("- 如果大小还不一致，我们再微调")
                print("- 如果大小一致，问题完全解决！")
                
                result = dialog.exec_()
                
                if result == QDialog.Accepted:
                    print("\n✅ 水印修复最终测试完成")
                    print(f"最终字体大小: {config.font_size}px")
                    
                    print("\n🎊 修复总结:")
                    print("1. 解决了VideoProcessorWorker缺少方法的问题")
                    print("2. 封面生成现在应该正常工作")
                    print("3. 预览与封面使用相同的字体加载逻辑")
                    print("4. 预览与封面使用相同的颜色处理逻辑")
                    
                    print("\n📈 下一步:")
                    print("- 如果封面有水印但大小不一致，告诉我具体差异")
                    print("- 如果封面没有水印，告诉我错误信息")
                    print("- 如果一切正常，问题完全解决！")
                        
                else:
                    print("\n❌ 水印修复最终测试已取消")
                
            except Exception as e:
                print(f"❌ 测试过程中出现错误: {e}")
                import traceback
                traceback.print_exc()
            
            print("=" * 80)
        
        test_button.clicked.connect(start_test)
        layout.addWidget(test_button)
        
        # 添加修复详情
        fix_details_label = QLabel("""
🔧 最终修复详情：

问题：
- VideoProcessorWorker类缺少get_preview_font方法
- VideoProcessorWorker类缺少hex_to_rgba方法
- 导致封面生成失败，完全没有水印

解决方案：
1. 在VideoProcessorWorker中添加get_preview_font方法：
   - 与WatermarkConfigDialog中的方法逻辑相同
   - 支持字体文件映射和回退机制
   - 包含详细的错误处理和调试信息

2. 在VideoProcessorWorker中添加hex_to_rgba方法：
   - 将十六进制颜色转换为RGBA
   - 支持透明度计算
   - 包含错误处理

代码修改：
```python
class VideoProcessorWorker:
    def get_preview_font(self, font_family, font_size):
        # 字体加载逻辑，与预览一致
        
    def hex_to_rgba(self, hex_color, opacity):
        # 颜色转换逻辑，与预览一致
```

技术保证：
- 封面生成现在有完整的字体加载能力
- 封面生成现在有完整的颜色处理能力
- 封面生成使用与预览相同的方法逻辑
- 预览显示多大，封面就是多大

预期结果：
- 封面生成不再报错
- 封面正常显示水印
- 预览与封面完全一致
        """)
        fix_details_label.setStyleSheet("""
            QLabel {
                background-color: #f5f5f5;
                padding: 15px;
                border-radius: 8px;
                font-size: 12px;
                color: #333333;
                border-left: 4px solid #32cd32;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        layout.addWidget(fix_details_label)
        
        main_window.show()
        
        print("水印修复最终测试应用启动成功")
        print("请点击按钮开始测试")
        
        return app.exec_()
        
    except Exception as e:
        print(f"测试启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("启动水印修复最终测试...")
    return test_watermark_fix_final()

if __name__ == "__main__":
    sys.exit(main())
