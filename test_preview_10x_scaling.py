#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试预览水印参数10倍放大的效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PIL import Image

def test_preview_10x_scaling():
    """测试预览水印参数10倍放大的效果"""
    print("=" * 80)
    print("🔍 预览水印参数10倍放大测试")
    print("=" * 80)
    
    try:
        # 1. 导入水印模块
        print("\n1. 导入水印模块...")
        from app.dialogs.video_processor_dialog import WatermarkConfig, VideoProcessorWorker
        print("   ✅ 模块导入成功")
        
        # 2. 创建水印配置
        print("\n2. 创建水印配置...")
        config = WatermarkConfig()
        config.enabled = True
        config.multi_line_enabled = False
        config.font_family = "Microsoft YaHei"
        config.font_size = 60
        config.text_color = "#FFFFFF"
        config.position_x = 85
        config.position_y = 85
        config.opacity = 90
        
        print(f"   ✅ 配置: {config.font_size}px字体")
        
        # 3. 创建worker
        print("\n3. 创建VideoProcessorWorker...")
        worker = VideoProcessorWorker(
            input_dir="test", output_dir="test", cover_dir="test",
            thread_count=1, worker_id=0, shared_data={},
            watermark_config=config
        )
        print("   ✅ Worker创建成功")
        
        # 4. 测试封面生成（1920x1080，原始参数）
        print("\n4. 测试封面生成（1920x1080，原始参数）...")
        test_text = "10倍参数测试"
        
        # 创建1920x1080的封面图像
        cover_img = Image.new('RGB', (1920, 1080), color='#2c3e50')
        
        # 使用封面水印逻辑（原始参数）
        cover_watermarked = worker.add_watermark_to_pil_image(cover_img.copy(), test_text)
        if cover_watermarked:
            cover_watermarked.save("10x_test_cover_1920x1080.jpg", 'JPEG', quality=95)
            print(f"   ✅ 封面生成: 10x_test_cover_1920x1080.jpg (1920x1080, 字体{config.font_size}px)")
        
        # 5. 手动创建10倍放大参数的预览对比
        print("\n5. 手动创建10倍放大参数的预览对比...")
        
        # 创建一个临时配置，参数放大10倍
        scaled_config = WatermarkConfig()
        scaled_config.enabled = True
        scaled_config.multi_line_enabled = False
        scaled_config.font_family = config.font_family
        scaled_config.font_size = config.font_size * 10  # 参数放大10倍
        scaled_config.text_color = config.text_color
        scaled_config.position_x = config.position_x
        scaled_config.position_y = config.position_y
        scaled_config.opacity = config.opacity
        
        # 创建使用放大参数的worker
        scaled_worker = VideoProcessorWorker(
            input_dir="test", output_dir="test", cover_dir="test",
            thread_count=1, worker_id=0, shared_data={},
            watermark_config=scaled_config
        )
        
        # 在640x360画布上使用放大参数
        preview_640_img = Image.new('RGB', (640, 360), color='#2c3e50')
        preview_640_watermarked = scaled_worker.add_watermark_to_pil_image(preview_640_img.copy(), test_text)
        if preview_640_watermarked:
            preview_640_watermarked.save("10x_test_preview_640x360.jpg", 'JPEG', quality=95)
            print(f"   ✅ 预览生成: 10x_test_preview_640x360.jpg (640x360, 字体{scaled_config.font_size}px)")
        
        # 6. 测试不同字体大小的10倍放大
        print("\n6. 测试不同字体大小的10倍放大...")
        
        test_sizes = [30, 50, 70, 90]
        
        for size in test_sizes:
            print(f"\n   测试字体大小: {size}px")
            
            # 封面：1920x1080，原始参数
            config.font_size = size
            cover_result = worker.add_watermark_to_pil_image(cover_img.copy(), f"{size}px测试")
            if cover_result:
                cover_result.save(f"10x_cover_{size}px.jpg", 'JPEG', quality=95)
                print(f"     封面: 10x_cover_{size}px.jpg (1920x1080, {size}px)")
            
            # 预览：640x360，参数放大10倍
            scaled_config.font_size = size * 10
            preview_result = scaled_worker.add_watermark_to_pil_image(preview_640_img.copy(), f"{size}px测试")
            if preview_result:
                preview_result.save(f"10x_preview_{size}px.jpg", 'JPEG', quality=95)
                print(f"     预览: 10x_preview_{size}px.jpg (640x360, {size*10}px)")
                
                # 计算相对大小
                # 封面中的相对大小 = 字体大小 / 画布高度
                cover_relative = size / 1080
                # 预览中的相对大小 = (字体大小*10) / 360
                preview_relative = (size * 10) / 360
                
                print(f"     封面相对大小: {cover_relative:.4f}")
                print(f"     预览相对大小: {preview_relative:.4f}")
                print(f"     放大倍数: {preview_relative/cover_relative:.2f}倍")
        
        # 7. 测试多行水印的10倍放大
        print("\n7. 测试多行水印的10倍放大...")
        
        # 配置多行水印
        config.multi_line_enabled = True
        config.font_size = 60  # 重置
        
        if len(config.multi_line_configs) >= 3:
            config.multi_line_configs[0].enabled = True
            config.multi_line_configs[0].char_count = 3
            config.multi_line_configs[0].font_size = 70
            
            config.multi_line_configs[1].enabled = True
            config.multi_line_configs[1].char_count = 2
            config.multi_line_configs[1].font_size = 60
            
            config.multi_line_configs[2].enabled = True
            config.multi_line_configs[2].char_count = 1
            config.multi_line_configs[2].font_size = 50
        
        # 放大版本的多行配置
        scaled_config.multi_line_enabled = True
        if len(scaled_config.multi_line_configs) >= 3:
            scaled_config.multi_line_configs[0].enabled = True
            scaled_config.multi_line_configs[0].char_count = 3
            scaled_config.multi_line_configs[0].font_size = 70 * 10
            
            scaled_config.multi_line_configs[1].enabled = True
            scaled_config.multi_line_configs[1].char_count = 2
            scaled_config.multi_line_configs[1].font_size = 60 * 10
            
            scaled_config.multi_line_configs[2].enabled = True
            scaled_config.multi_line_configs[2].char_count = 1
            scaled_config.multi_line_configs[2].font_size = 50 * 10
        
        test_text = "多行10倍参数"
        
        # 封面多行
        multiline_cover = worker.add_watermark_to_pil_image(cover_img.copy(), test_text)
        if multiline_cover:
            multiline_cover.save("10x_multiline_cover.jpg", 'JPEG', quality=95)
            print(f"   ✅ 多行封面: 10x_multiline_cover.jpg (1920x1080, 70px/60px/50px)")
        
        # 预览多行
        multiline_preview = scaled_worker.add_watermark_to_pil_image(preview_640_img.copy(), test_text)
        if multiline_preview:
            multiline_preview.save("10x_multiline_preview.jpg", 'JPEG', quality=95)
            print(f"   ✅ 多行预览: 10x_multiline_preview.jpg (640x360, 700px/600px/500px)")
        
        # 8. 创建对比图
        print("\n8. 创建对比图...")
        
        # 将封面缩放到640x360进行直观对比
        if cover_watermarked:
            cover_scaled_for_comparison = cover_watermarked.resize((640, 360), Image.LANCZOS)
            cover_scaled_for_comparison.save("10x_cover_scaled_for_comparison.jpg", 'JPEG', quality=95)
            print(f"   ✅ 封面缩放对比: 10x_cover_scaled_for_comparison.jpg")
        
        # 9. 测试极端情况
        print("\n9. 测试极端情况...")
        
        # 测试小字体放大10倍
        small_sizes = [5, 8, 12]
        for size in small_sizes:
            config.font_size = size
            scaled_config.font_size = size * 10
            
            small_cover = worker.add_watermark_to_pil_image(cover_img.copy(), f"小字体{size}px")
            small_preview = scaled_worker.add_watermark_to_pil_image(preview_640_img.copy(), f"小字体{size}px")
            
            if small_cover:
                small_cover.save(f"10x_small_cover_{size}px.jpg", 'JPEG', quality=95)
                print(f"   小字体封面: 10x_small_cover_{size}px.jpg ({size}px)")
            
            if small_preview:
                small_preview.save(f"10x_small_preview_{size}px.jpg", 'JPEG', quality=95)
                print(f"   小字体预览: 10x_small_preview_{size}px.jpg ({size*10}px)")
        
        # 10. 总结测试结果
        print("\n10. 测试总结...")
        print(f"   ✅ 预览参数10倍放大测试完成")
        print(f"   ✅ 封面: 1920x1080画布，原始参数")
        print(f"   ✅ 预览: 640x360画布，参数放大10倍")
        print(f"   ✅ 预览水印比封面水印大约30倍")
        
        print(f"\n📊 生成的文件:")
        print(f"   - 10x_test_cover_1920x1080.jpg: 封面效果")
        print(f"   - 10x_test_preview_640x360.jpg: 预览效果(参数10倍)")
        print(f"   - 10x_cover_*px.jpg: 不同字体大小的封面")
        print(f"   - 10x_preview_*px.jpg: 对应的10倍参数预览")
        print(f"   - 10x_multiline_*.jpg: 多行水印10倍参数测试")
        print(f"   - 10x_cover_scaled_for_comparison.jpg: 封面缩放对比图")
        print(f"   - 10x_small_*.jpg: 小字体极端情况测试")
        
        print(f"\n🔍 验证要点:")
        print(f"   1. 预览使用640x360画布，参数放大10倍")
        print(f"   2. 封面使用1920x1080画布，参数保持原始")
        print(f"   3. 预览中的水印应该非常大，比封面大很多")
        print(f"   4. 多行水印的每行参数都相应放大10倍")
        print(f"   5. 对比 10x_test_preview_640x360.jpg 和 10x_cover_scaled_for_comparison.jpg")
        
        print(f"\n✅ 预览参数10倍放大测试全部完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    test_preview_10x_scaling()
