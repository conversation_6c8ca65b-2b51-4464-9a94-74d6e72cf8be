# 头条内容社交工具

一个功能强大的头条内容管理和自动化操作桌面应用程序。

## 🚀 快速开始

### 环境要求
- Python 3.9+
- PyQt5
- Windows 操作系统

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行程序
```bash
python main.py
```

### 打包程序
```bash
python build.py
```

## 📚 文档中心

本项目采用统一的文档管理结构，所有详细文档都存储在 `.augment-guidelines/docs/` 目录中：

### 📖 主要文档
- [项目详细说明](.augment-guidelines/docs/README.md) - 完整的项目介绍和功能说明
- [构建打包指南](.augment-guidelines/docs/build/BUILD_README.md) - 详细的打包和部署说明

### 👤 用户指南
- [🎬 视频处理流程指南](.augment-guidelines/docs/user-manual/视频处理流程指南.md) - 完整的视频处理功能使用说明
- [🤖 养号机器人使用说明](.augment-guidelines/docs/user-manual/README_养号机器人.md) - 养号功能详细使用指南

### 🔒 安全文档
- [反破解系统说明](.augment-guidelines/docs/security/ANTI_CRACK_README.md) - 安全防护机制详解
- [安全功能总结](.augment-guidelines/docs/security/ANTI_CRACK_SUMMARY.md) - 安全特性概览

### 📋 完整文档索引
查看 [文档管理中心](.augment-guidelines/README.md) 获取所有文档的完整索引和分类。

## ⚡ 核心功能

- 🤖 **智能账号管理** - 批量账号导入、状态监控、自动登录
- 🎯 **AI内容改写** - 智能内容生成和改写功能
- 📊 **数据采集统计** - 头条数据爬取和收益分析
- 🎬 **视频处理系统** - 智能视频筛选、封面生成、批量处理
  - 横屏视频自动筛选
  - 高质量封面生成 (1920x1080)
  - AI智能文件名改写
  - 多线程并行处理
- 🔒 **安全防护系统** - 反破解检测和行为安全监控
- ⚙️ **自动化操作** - 批量操作管理和定时任务

## 🛠️ 技术架构

- **框架**: PyQt5 桌面应用
- **语言**: Python 3.9+
- **架构**: 模块化设计，组件化开发
- **安全**: 多层安全防护机制

## 📁 项目结构

```
项目根目录/
├── main.py                     # 主程序入口
├── build.py                    # 打包脚本
├── requirements.txt            # 依赖文件
├── app/                        # 核心应用目录
│   ├── main_window/           # 主窗口模块
│   ├── tabs/                  # 功能标签页
│   ├── dialogs/               # 对话框组件
│   ├── utils/                 # 工具模块
│   └── services/              # 服务模块
├── .augment-guidelines/        # 📚 文档管理中心
│   ├── README.md              # 文档索引
│   └── docs/                  # 所有项目文档
└── data/                      # 配置和数据文件
```

## 🤝 贡献指南

1. 查看 [开发规范](.augment-guidelines/development-workflow/) 了解开发流程
2. 遵循 [代码规范](.augment-guidelines/coding-standards/) 进行开发
3. 新增文档请存放到 [文档目录](.augment-guidelines/docs/) 对应分类中
4. 提交前请运行测试确保功能正常

## 📄 许可证

本项目为私有项目，请勿未经授权使用或分发。

## 📞 支持

如有问题或建议，请查看相关文档或联系开发团队。

---

*更多详细信息请查看 [完整项目文档](.augment-guidelines/docs/README.md)*
