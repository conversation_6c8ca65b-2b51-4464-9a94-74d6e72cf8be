# MCP Feedback Enhanced 在 Augment 中的安装指南

## 🎯 概述

MCP Feedback Enhanced 是一个强大的交互反馈工具，支持 Web UI 和桌面应用程序双重界面，完美适配 Augment 开发环境。

## ✅ 安装状态

✅ **已完成安装** - mcp-feedback-enhanced v2.6.0
✅ **Python 环境** - Python 3.11.9 已安装
✅ **依赖包** - 所有必需依赖已安装
✅ **桌面应用** - <PERSON>ri 桌面应用程序可用
✅ **测试通过** - 功能测试成功

## 📋 配置步骤

### 1. 在 Augment 中配置 MCP

将以下配置添加到你的 Augment MCP 配置文件中：

```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "C:\\Program Files\\Python311\\python.exe",
      "args": ["-m", "mcp_feedback_enhanced"],
      "timeout": 600,
      "env": {
        "MCP_DESKTOP_MODE": "true",
        "MCP_WEB_HOST": "127.0.0.1",
        "MCP_WEB_PORT": "8765",
        "MCP_DEBUG": "false",
        "MCP_LANGUAGE": "zh-CN"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

### 2. 配置文件位置

配置文件已创建在：`augment-mcp-config.json`

### 3. 重启 Augment

配置完成后，重启 Augment 以加载新的 MCP 服务器。

## 🚀 使用方法

### 基本使用

1. 在 Augment 中与 AI 对话时，AI 会自动调用 `interactive_feedback` 工具
2. 桌面应用程序会自动启动，显示反馈界面
3. 你可以：
   - 输入文字反馈
   - 上传图片（支持拖拽和 Ctrl+V 粘贴）
   - 使用预设提示词
   - 设置自动提交定时器

### 快捷键

- `Ctrl+Enter` - 提交反馈
- `Ctrl+V` - 粘贴剪贴板图片
- `Ctrl+I` - 快速聚焦输入框

## 🔧 环境变量说明

| 变量 | 值 | 说明 |
|------|-----|------|
| `MCP_DESKTOP_MODE` | `true` | 启用桌面应用程序模式 |
| `MCP_WEB_HOST` | `127.0.0.1` | Web 服务器主机地址 |
| `MCP_WEB_PORT` | `8765` | Web 服务器端口 |
| `MCP_DEBUG` | `false` | 调试模式（建议关闭） |
| `MCP_LANGUAGE` | `zh-CN` | 界面语言（简体中文） |

## 🌟 主要功能

### 双重界面支持
- **桌面应用程序**：原生跨平台桌面体验
- **Web UI 界面**：浏览器界面，适合远程环境

### 智能工作流程
- **提示词管理**：常用提示词的增删改查
- **自动定时提交**：1-86400 秒弹性计时器
- **会话管理追踪**：本地存储，隐私控制
- **连接监控**：WebSocket 状态监控，自动重连

### 图片与媒体
- **全格式支持**：PNG、JPG、JPEG、GIF、BMP、WebP
- **便捷上传**：拖拽文件、剪贴板粘贴
- **无限制处理**：支持任意大小图片

## 🐛 故障排除

### 常见问题

**Q: MCP 工具无法启动**
A: 检查 Python 路径是否正确，确保使用 `C:\\Program Files\\Python311\\python.exe`

**Q: 桌面应用程序无法打开**
A: 确保 `MCP_DESKTOP_MODE` 设置为 `true`

**Q: 端口冲突**
A: 修改 `MCP_WEB_PORT` 为其他可用端口（如 8766、8767）

**Q: 中文显示乱码**
A: 确保 `MCP_LANGUAGE` 设置为 `zh-CN`

### 测试命令

```bash
# 测试桌面应用程序
"C:/Program Files/Python311/python.exe" -m mcp_feedback_enhanced test --desktop

# 测试 Web 界面
"C:/Program Files/Python311/python.exe" -m mcp_feedback_enhanced test --web

# 查看版本信息
"C:/Program Files/Python311/python.exe" -m mcp_feedback_enhanced version
```

## 📞 技术支持

- **GitHub**: https://github.com/Minidoracat/mcp-feedback-enhanced
- **文档**: 查看项目 README.zh-CN.md
- **Issues**: 在 GitHub 上提交问题

## 🎉 安装完成

恭喜！MCP Feedback Enhanced 已成功安装并配置完成。现在你可以在 Augment 中享受强大的交互反馈功能了！
