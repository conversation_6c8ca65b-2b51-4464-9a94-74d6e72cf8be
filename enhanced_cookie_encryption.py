#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
增强版Cookie加密工具 - 硬件绑定防护
"""

import os
import json
import base64
import hashlib
import platform
import uuid
from typing import Optional, Dict
import time

try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False

class EnhancedCookieEncryption:
    """增强版Cookie加密管理器 - 硬件绑定"""

    def __init__(self, base_password: str = "zhengyang.0924"):
        self.base_password = base_password
        self.available = CRYPTOGRAPHY_AVAILABLE
        
    def _get_machine_fingerprint(self) -> str:
        """生成机器指纹"""
        try:
            # 收集硬件特征
            machine_info = []
            
            # CPU信息
            try:
                machine_info.append(platform.processor())
            except:
                machine_info.append("unknown_cpu")
            
            # 主板UUID (Windows)
            try:
                if platform.system() == "Windows":
                    import subprocess
                    result = subprocess.run(['wmic', 'csproduct', 'get', 'uuid'], 
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.strip() and 'UUID' not in line:
                                machine_info.append(line.strip())
                                break
            except:
                pass
            
            # MAC地址
            try:
                mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                               for elements in range(0,2*6,2)][::-1])
                machine_info.append(mac)
            except:
                machine_info.append("unknown_mac")
            
            # 计算机名
            try:
                machine_info.append(platform.node())
            except:
                machine_info.append("unknown_node")
            
            # 操作系统版本
            try:
                machine_info.append(platform.platform())
            except:
                machine_info.append("unknown_os")
            
            # 生成指纹
            fingerprint_data = "|".join(machine_info)
            fingerprint = hashlib.sha256(fingerprint_data.encode('utf-8')).hexdigest()
            
            return fingerprint[:32]  # 取前32位
            
        except Exception as e:
            # 如果获取硬件信息失败，使用备用方案
            fallback_data = f"{platform.system()}_{platform.machine()}_{uuid.getnode()}"
            return hashlib.sha256(fallback_data.encode('utf-8')).hexdigest()[:32]
    
    def _generate_machine_bound_key(self) -> bytes:
        """生成与机器绑定的加密密钥"""
        if not self.available:
            raise RuntimeError("cryptography库未安装")
        
        # 获取机器指纹
        machine_fingerprint = self._get_machine_fingerprint()
        
        # 组合密码：基础密码 + 机器指纹
        combined_password = f"{self.base_password}_{machine_fingerprint}"
        
        # 使用机器指纹作为盐值的一部分
        salt = f"toutiao_salt_{machine_fingerprint[:16]}".encode('utf-8')
        
        # 生成密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=150000,  # 增加迭代次数
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(combined_password.encode('utf-8')))
        return key
    
    def encrypt_cookie_data(self, cookie_data: dict) -> dict:
        """加密Cookie数据（增强版）"""
        if not self.available:
            raise RuntimeError("cryptography库未安装")
        
        try:
            # 生成机器绑定密钥
            key = self._generate_machine_bound_key()
            fernet = Fernet(key)
            
            # 添加时间戳和机器指纹到数据中
            enhanced_data = {
                "original_data": cookie_data,
                "timestamp": int(time.time()),
                "machine_fingerprint": self._get_machine_fingerprint(),
                "version": "2.0"
            }
            
            # 转换为JSON并加密
            json_str = json.dumps(enhanced_data, ensure_ascii=False, separators=(',', ':'))
            encrypted_data = fernet.encrypt(json_str.encode('utf-8'))
            encrypted_b64 = base64.b64encode(encrypted_data).decode('utf-8')
            
            # 返回增强的加密文件结构
            return {
                "encrypted": True,
                "version": "2.0",
                "algorithm": "AES-256-HardwareBound",
                "data": encrypted_b64,
                "machine_bound": True,
                "created_time": int(time.time()),
                "account_id": cookie_data.get("accountId", ""),
                "remark": cookie_data.get("remark", "")
            }
            
        except Exception as e:
            raise Exception(f"加密失败: {str(e)}")
    
    def decrypt_cookie_data(self, encrypted_file_data: dict) -> dict:
        """解密Cookie数据（增强版）"""
        if not self.available:
            raise RuntimeError("cryptography库未安装")
        
        try:
            # 检查是否为硬件绑定加密
            if not encrypted_file_data.get("machine_bound", False):
                raise Exception("此文件不是硬件绑定加密文件")
            
            # 生成当前机器的密钥
            key = self._generate_machine_bound_key()
            fernet = Fernet(key)
            
            # 解密数据
            encrypted_data = encrypted_file_data.get("data", "")
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = fernet.decrypt(encrypted_bytes)
            
            # 解析JSON
            json_str = decrypted_data.decode('utf-8')
            enhanced_data = json.loads(json_str)
            
            # 验证机器指纹
            stored_fingerprint = enhanced_data.get("machine_fingerprint", "")
            current_fingerprint = self._get_machine_fingerprint()
            
            if stored_fingerprint != current_fingerprint:
                raise Exception("机器指纹不匹配，此Cookie文件无法在当前机器上使用")
            
            # 可选：验证时间戳（防止过期Cookie）
            timestamp = enhanced_data.get("timestamp", 0)
            current_time = int(time.time())
            max_age = 30 * 24 * 3600  # 30天过期
            
            if current_time - timestamp > max_age:
                raise Exception("Cookie文件已过期")
            
            # 返回原始Cookie数据
            return enhanced_data.get("original_data", {})
            
        except Exception as e:
            raise Exception(f"解密失败: {str(e)}")
    
    def encrypt_cookie_file(self, input_file: str, output_file: Optional[str] = None) -> bool:
        """加密Cookie文件（增强版）"""
        try:
            if not os.path.exists(input_file):
                return False
            
            # 读取原始文件
            with open(input_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            if not content:
                return False
            
            # 解析Cookie数据
            try:
                cookie_data = json.loads(content)
            except json.JSONDecodeError:
                # 处理文本格式Cookie
                cookie_data = self._parse_text_cookie(content)
                if not cookie_data:
                    return False
            
            # 加密数据
            encrypted_file_data = self.encrypt_cookie_data(cookie_data)
            
            # 保存文件
            if output_file is None:
                output_file = input_file
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(encrypted_file_data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"加密文件失败: {str(e)}")
            return False
    
    def _parse_text_cookie(self, content: str) -> dict:
        """解析文本格式Cookie"""
        # 简化实现，实际应该更完善
        return {"cookies": {"raw": content}}

# 使用示例
def test_enhanced_encryption():
    """测试增强加密功能"""
    encryptor = EnhancedCookieEncryption()
    
    # 测试数据
    test_data = {
        "accountId": "test123",
        "remark": "测试账号",
        "cookies": {
            "sessionid": "test_session",
            "csrf_token": "test_csrf"
        }
    }
    
    try:
        # 加密
        encrypted = encryptor.encrypt_cookie_data(test_data)
        print("✅ 加密成功")
        print(f"机器绑定: {encrypted['machine_bound']}")
        
        # 解密
        decrypted = encryptor.decrypt_cookie_data(encrypted)
        print("✅ 解密成功")
        print(f"数据匹配: {decrypted == test_data}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    test_enhanced_encryption()
