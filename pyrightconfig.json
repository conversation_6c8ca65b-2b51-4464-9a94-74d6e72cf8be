{"include": ["app"], "exclude": ["**/node_modules", "**/__pycache__", "**/.git"], "reportMissingImports": true, "reportMissingTypeStubs": false, "reportAttributeAccessIssue": "warning", "reportUnknownMemberType": "none", "reportUnknownArgumentType": "none", "reportUnknownVariableType": "none", "reportUnknownParameterType": "none", "reportGeneralTypeIssues": "warning", "pythonVersion": "3.8", "pythonPlatform": "Windows", "typeCheckingMode": "basic", "stubPath": "./typings", "reportPrivateUsage": "none", "reportConstantRedefinition": "none", "reportIncompatibleMethodOverride": "warning", "reportIncompatibleVariableOverride": "warning", "reportOverlappingOverload": "warning", "reportUntypedFunctionDecorator": "none", "reportUntypedClassDecorator": "none", "reportUntypedBaseClass": "none", "reportUntypedNamedTuple": "none", "reportPrivateImportUsage": "none", "reportCallInDefaultInitializer": "none", "reportUnnecessaryIsInstance": "none", "reportUnnecessaryCast": "none", "reportUnnecessaryComparison": "none", "reportAssertAlwaysTrue": "warning", "reportSelfClsParameterName": "warning", "reportImplicitStringConcatenation": "none", "reportUndefinedVariable": "error", "reportUnboundVariable": "error", "reportInvalidStringEscapeSequence": "warning", "reportUnknownLambdaType": "none", "reportImplicitOverride": "none", "reportPropertyTypeMismatch": "none", "reportFunctionMemberAccess": "none", "reportMissingSuperCall": "none", "reportUninitializedInstanceVariable": "none", "reportInvalidTypeVarUse": "warning", "reportCallIssue": "warning", "reportArgumentType": "warning", "reportAssignmentType": "warning", "reportReturnType": "warning", "reportOperatorIssue": "warning", "reportIndexIssue": "warning", "reportOptionalSubscript": "warning", "reportOptionalMemberAccess": "warning", "reportOptionalCall": "warning", "reportOptionalIterable": "warning", "reportOptionalContextManager": "warning", "reportOptionalOperand": "warning", "reportTypedDictNotRequiredAccess": "warning"}