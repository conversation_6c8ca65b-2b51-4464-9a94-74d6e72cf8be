#!/usr/bin/env python3
"""
测试多行水印功能修复的脚本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QDialog

# 添加项目路径
sys.path.append('.')

def test_multiline_fix():
    """测试多行水印功能修复"""
    try:
        from app.dialogs.video_processor_dialog import WatermarkConfigDialog, WatermarkConfig
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("多行水印功能修复测试")
        main_window.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("""
🔧 多行水印功能修复测试

修复内容：
✅ 修复了 'NoneType' object has no attribute 'show' 错误
✅ 添加了多行预览标签的空值检查
✅ 改进了多行模式切换的错误处理
✅ 确保界面组件正确初始化

测试步骤：
1. 点击下方按钮打开水印配置对话框
2. 找到"📝 多行水印模式"组
3. 勾选"启用多行水印模式"
4. 观察是否出现错误，预览区域是否正常切换

如果没有出现错误，说明修复成功！
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #d4edda;
                padding: 20px;
                border-radius: 8px;
                font-size: 14px;
                line-height: 1.6;
                border: 2px solid #28a745;
                color: #155724;
            }
        """)
        layout.addWidget(info_label)
        
        # 添加测试按钮
        test_button = QPushButton("🎨 打开水印配置对话框（测试修复）")
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        def open_dialog():
            print("=" * 60)
            print("开始多行水印功能修复测试")
            print("=" * 60)
            
            try:
                # 创建水印配置
                config = WatermarkConfig()
                config.enabled = True
                
                print("✅ 水印配置创建成功")
                print(f"多行模式启用: {config.multi_line_enabled}")
                print(f"多行配置数量: {len(config.multi_line_configs)}")
                
                # 打开配置对话框
                dialog = WatermarkConfigDialog(config, main_window)
                print("✅ 对话框创建成功")
                
                print("\n请在对话框中测试多行模式切换...")
                print("注意观察控制台是否有错误信息")
                
                result = dialog.exec_()
                
                if result == QDialog.Accepted:
                    print("\n✅ 配置已确认")
                    print(f"多行模式: {config.multi_line_enabled}")
                    if config.multi_line_enabled:
                        print("多行配置详情:")
                        for i, line_config in enumerate(config.multi_line_configs):
                            print(f"  第{i+1}行: {line_config.char_count}字符")
                else:
                    print("\n❌ 配置已取消")
                
            except Exception as e:
                print(f"❌ 测试过程中出现错误: {e}")
                import traceback
                traceback.print_exc()
            
            print("=" * 60)
        
        test_button.clicked.connect(open_dialog)
        layout.addWidget(test_button)
        
        # 添加错误检查说明
        error_check_label = QLabel("""
🔍 错误检查要点：

1. 多行模式切换时不应出现 AttributeError
2. 预览区域应该能正常切换
3. 控制台应该显示"多行预览标签创建成功"
4. 行选择器应该正常工作
5. 字符数调整应该实时生效

如果出现任何错误，请查看控制台输出的详细信息。
        """)
        error_check_label.setStyleSheet("""
            QLabel {
                background-color: #fff3cd;
                padding: 15px;
                border-radius: 8px;
                font-size: 13px;
                color: #856404;
                border-left: 4px solid #ffc107;
            }
        """)
        layout.addWidget(error_check_label)
        
        main_window.show()
        
        print("多行水印功能修复测试应用启动成功")
        print("请点击按钮开始测试")
        
        return app.exec_()
        
    except Exception as e:
        print(f"测试启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("开始多行水印功能修复测试...")
    return test_multiline_fix()

if __name__ == "__main__":
    sys.exit(main())
