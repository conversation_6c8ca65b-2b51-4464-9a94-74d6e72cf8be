#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
客户端服务器集成模块
与Cookie验证服务器通信
"""

import os
import json
import hashlib
import hmac
import time
import requests
import platform
import uuid
from typing import Optional, Dict, Tuple
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

class ServerValidatedCookieClient:
    """服务器验证的Cookie客户端"""
    
    def __init__(self, server_url: str = "http://localhost:5000/api", 
                 license_key: str = None):
        self.server_url = server_url
        self.license_key = license_key
        self.client_id = None
        self.client_secret = None
        self.machine_fingerprint = self._generate_machine_fingerprint()
        
        # 加载或注册客户端
        self._load_or_register_client()
    
    def _generate_machine_fingerprint(self) -> str:
        """生成机器指纹"""
        try:
            machine_info = []
            
            # CPU信息
            machine_info.append(platform.processor() or "unknown_cpu")
            
            # MAC地址
            try:
                mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                               for elements in range(0,2*6,2)][::-1])
                machine_info.append(mac)
            except:
                machine_info.append("unknown_mac")
            
            # 计算机名
            machine_info.append(platform.node() or "unknown_node")
            
            # 操作系统
            machine_info.append(platform.platform() or "unknown_os")
            
            # 生成指纹
            fingerprint_data = "|".join(machine_info)
            return hashlib.sha256(fingerprint_data.encode()).hexdigest()[:32]
            
        except Exception:
            # 备用方案
            fallback = f"{platform.system()}_{uuid.getnode()}"
            return hashlib.sha256(fallback.encode()).hexdigest()[:32]
    
    def _load_or_register_client(self):
        """加载或注册客户端"""
        config_file = "client_config.json"
        
        # 尝试加载现有配置
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                self.client_id = config.get('client_id')
                self.client_secret = config.get('client_secret')
                
                # 验证客户端是否仍然有效
                if self._verify_client_status():
                    print("✅ 客户端配置加载成功")
                    return
                else:
                    print("⚠️ 客户端配置已失效，重新注册...")
                    
            except Exception as e:
                print(f"⚠️ 加载客户端配置失败: {str(e)}")
        
        # 注册新客户端
        if self.license_key:
            self._register_client()
        else:
            raise Exception("需要提供license_key来注册客户端")
    
    def _register_client(self) -> bool:
        """注册客户端"""
        try:
            payload = {
                'license_key': self.license_key,
                'machine_fingerprint': self.machine_fingerprint
            }
            
            response = requests.post(
                f"{self.server_url}/register-client",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                self.client_id = result['client_id']
                self.client_secret = result['client_secret']
                
                # 保存配置
                config = {
                    'client_id': self.client_id,
                    'client_secret': self.client_secret,
                    'machine_fingerprint': self.machine_fingerprint,
                    'license_key': self.license_key,
                    'expires_at': result['expires_at']
                }
                
                with open("client_config.json", 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
                
                print("✅ 客户端注册成功")
                return True
            else:
                error_msg = response.json().get('error', '未知错误')
                print(f"❌ 客户端注册失败: {error_msg}")
                return False
                
        except Exception as e:
            print(f"❌ 客户端注册异常: {str(e)}")
            return False
    
    def _verify_client_status(self) -> bool:
        """验证客户端状态"""
        try:
            params = {
                'client_id': self.client_id,
                'client_secret': self.client_secret
            }
            
            response = requests.get(
                f"{self.server_url}/client-status",
                params=params,
                timeout=10
            )
            
            if response.status_code == 200:
                status = response.json()
                return status.get('is_active', False)
            else:
                return False
                
        except Exception:
            return False
    
    def request_encryption_token(self, cookie_hash: str) -> Optional[str]:
        """请求加密令牌"""
        try:
            payload = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'cookie_hash': cookie_hash
            }
            
            response = requests.post(
                f"{self.server_url}/encryption-token",
                json=payload,
                timeout=15
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['encryption_token']
            else:
                error_msg = response.json().get('error', '未知错误')
                print(f"❌ 获取加密令牌失败: {error_msg}")
                return None
                
        except Exception as e:
            print(f"❌ 请求加密令牌异常: {str(e)}")
            return None
    
    def validate_decryption_token(self, token: str, cookie_hash: str) -> bool:
        """验证解密令牌"""
        try:
            payload = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'token': token,
                'cookie_hash': cookie_hash
            }
            
            response = requests.post(
                f"{self.server_url}/validate-token",
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('valid', False)
            else:
                return False
                
        except Exception as e:
            print(f"❌ 验证解密令牌异常: {str(e)}")
            return False
    
    def revoke_token(self, token: str) -> bool:
        """撤销令牌"""
        try:
            payload = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'token': token
            }
            
            response = requests.post(
                f"{self.server_url}/revoke-token",
                json=payload,
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception as e:
            print(f"❌ 撤销令牌异常: {str(e)}")
            return False
    
    def encrypt_cookie_data(self, cookie_data: dict) -> dict:
        """加密Cookie数据"""
        try:
            # 计算Cookie哈希
            cookie_json = json.dumps(cookie_data, sort_keys=True, separators=(',', ':'))
            cookie_hash = hashlib.sha256(cookie_json.encode()).hexdigest()
            
            # 请求加密令牌
            encryption_token = self.request_encryption_token(cookie_hash)
            if not encryption_token:
                raise Exception("获取加密令牌失败")
            
            # 生成动态密钥
            key_material = f"{encryption_token}_{cookie_hash}_{self.client_id}"
            key_hash = hashlib.sha256(key_material.encode()).digest()
            dynamic_key = base64.urlsafe_b64encode(key_hash)
            
            # 准备加密数据
            timestamp = int(time.time())
            enhanced_data = {
                "original_data": cookie_data,
                "timestamp": timestamp,
                "client_id": self.client_id,
                "cookie_hash": cookie_hash,
                "machine_fingerprint": self.machine_fingerprint,
                "version": "4.0"
            }
            
            # 加密数据
            json_data = json.dumps(enhanced_data, ensure_ascii=False, separators=(',', ':')).encode('utf-8')
            fernet = Fernet(dynamic_key)
            encrypted_data = fernet.encrypt(json_data)
            
            # 生成服务器签名
            signature_data = encrypted_data + encryption_token.encode()
            server_signature = hmac.new(
                b"server_secret_key", 
                signature_data, 
                hashlib.sha256
            ).digest()
            
            return {
                "encrypted": True,
                "version": "4.0",
                "algorithm": "AES-256-ServerValidated",
                "data": base64.b64encode(encrypted_data).decode('utf-8'),
                "encryption_token": encryption_token,
                "server_signature": base64.b64encode(server_signature).decode('utf-8'),
                "client_id": self.client_id,
                "created_time": timestamp,
                "server_validated": True,
                "account_id": cookie_data.get("accountId", ""),
                "remark": cookie_data.get("remark", "")
            }
            
        except Exception as e:
            raise Exception(f"服务器验证加密失败: {str(e)}")
    
    def decrypt_cookie_data(self, encrypted_file_data: dict) -> dict:
        """解密Cookie数据"""
        try:
            # 验证是否为服务器验证文件
            if not encrypted_file_data.get("server_validated", False):
                raise Exception("此文件不是服务器验证加密文件")
            
            # 提取数据
            encrypted_data = base64.b64decode(encrypted_file_data["data"])
            encryption_token = encrypted_file_data["encryption_token"]
            server_signature = base64.b64decode(encrypted_file_data["server_signature"])
            
            # 验证服务器签名
            signature_data = encrypted_data + encryption_token.encode()
            expected_signature = hmac.new(
                b"server_secret_key", 
                signature_data, 
                hashlib.sha256
            ).digest()
            
            if not hmac.compare_digest(expected_signature, server_signature):
                raise Exception("服务器签名验证失败")
            
            # 临时解密以获取cookie_hash
            temp_key_material = f"{encryption_token}_temp_{self.client_id}"
            temp_key_hash = hashlib.sha256(temp_key_material.encode()).digest()
            temp_key = base64.urlsafe_b64encode(temp_key_hash)
            
            try:
                fernet = Fernet(temp_key)
                decrypted_json = fernet.decrypt(encrypted_data)
                enhanced_data = json.loads(decrypted_json.decode('utf-8'))
                cookie_hash = enhanced_data.get("cookie_hash", "")
            except:
                # 如果临时密钥失败，尝试其他方法
                raise Exception("无法解析加密数据")
            
            # 验证解密令牌
            if not self.validate_decryption_token(encryption_token, cookie_hash):
                raise Exception("解密令牌验证失败")
            
            # 生成正确的动态密钥
            key_material = f"{encryption_token}_{cookie_hash}_{self.client_id}"
            key_hash = hashlib.sha256(key_material.encode()).digest()
            dynamic_key = base64.urlsafe_b64encode(key_hash)
            
            # 重新解密
            fernet = Fernet(dynamic_key)
            decrypted_json = fernet.decrypt(encrypted_data)
            enhanced_data = json.loads(decrypted_json.decode('utf-8'))
            
            # 验证客户端ID
            if enhanced_data.get("client_id") != self.client_id:
                raise Exception("客户端ID不匹配")
            
            # 验证机器指纹
            if enhanced_data.get("machine_fingerprint") != self.machine_fingerprint:
                raise Exception("机器指纹不匹配")
            
            # 验证时间戳
            timestamp = enhanced_data.get("timestamp", 0)
            current_time = int(time.time())
            max_age = 30 * 24 * 3600  # 30天
            
            if current_time - timestamp > max_age:
                raise Exception("Cookie文件已过期")
            
            return enhanced_data.get("original_data", {})
            
        except Exception as e:
            raise Exception(f"服务器验证解密失败: {str(e)}")

# 使用示例
def test_server_validated_encryption():
    """测试服务器验证加密"""
    try:
        # 初始化客户端（需要提供license_key）
        client = ServerValidatedCookieClient(
            server_url="http://localhost:5000/api",
            license_key="your-license-key-here"
        )
        
        # 测试数据
        test_data = {
            "accountId": "test123",
            "remark": "测试账号",
            "cookies": {
                "sessionid": "test_session",
                "csrf_token": "test_csrf"
            }
        }
        
        print("🔐 开始服务器验证加密测试...")
        
        # 加密
        encrypted = client.encrypt_cookie_data(test_data)
        print("✅ 服务器验证加密成功")
        
        # 解密
        decrypted = client.decrypt_cookie_data(encrypted)
        print("✅ 服务器验证解密成功")
        
        # 验证数据
        if decrypted == test_data:
            print("✅ 数据验证成功")
            return True
        else:
            print("❌ 数据验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    test_server_validated_encryption()
