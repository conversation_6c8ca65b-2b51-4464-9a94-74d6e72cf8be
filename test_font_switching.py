#!/usr/bin/env python3
"""
测试封面水印字体切换功能的脚本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel

# 添加项目路径
sys.path.append('.')

def test_font_switching():
    """测试字体切换功能"""
    try:
        from app.dialogs.video_processor_dialog import CoverWatermarkDialog
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("字体切换功能测试")
        main_window.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("""
🔤 字体切换功能测试

测试步骤：
1. 点击下方按钮打开封面水印配置对话框
2. 在左侧"字体设置"区域中：
   - 尝试切换"字体分类"下拉框
   - 尝试切换"字体"下拉框
   - 观察右侧预览区域的字体变化

预期效果：
✅ 字体分类切换时，字体下拉框应该更新
✅ 字体下拉框切换时，预览区域字体应该立即改变
✅ 控制台应该输出字体切换的调试信息

修复内容：
- 初始化时填充默认字体列表
- 添加字体切换事件处理
- 增强预览更新逻辑
- 改进配置加载时的字体设置
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                font-size: 14px;
                line-height: 1.6;
                border: 2px solid #dee2e6;
            }
        """)
        layout.addWidget(info_label)
        
        # 添加测试按钮
        test_button = QPushButton("🎨 打开封面水印配置对话框")
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        
        def open_dialog():
            print("=" * 50)
            print("开始字体切换功能测试")
            print("=" * 50)
            
            dialog = CoverWatermarkDialog(main_window)
            
            # 检查字体下拉框是否正确初始化
            if hasattr(dialog, 'font_combo'):
                font_count = dialog.font_combo.count()
                current_font = dialog.font_combo.currentText()
                print(f"字体下拉框初始化: {font_count} 个字体")
                print(f"当前选择的字体: {current_font}")
                
                # 列出所有可用字体
                print("可用字体列表:")
                for i in range(font_count):
                    font_name = dialog.font_combo.itemText(i)
                    print(f"  {i+1}. {font_name}")
            else:
                print("❌ 错误: 字体下拉框未找到")
            
            print("\n请在对话框中测试字体切换功能...")
            print("观察控制台输出和预览区域变化")
            
            result = dialog.exec_()
            
            print("=" * 50)
            print(f"对话框关闭，返回值: {result}")
            print("=" * 50)
        
        test_button.clicked.connect(open_dialog)
        layout.addWidget(test_button)
        
        # 添加控制台输出说明
        console_label = QLabel("""
📝 控制台输出说明：

当您切换字体时，应该看到类似的输出：
- "字体切换为: Microsoft YaHei"
- "更新预览，当前字体: Microsoft YaHei"
- "字体已更改为: SimSun"

如果没有看到这些输出，说明字体切换功能存在问题。
        """)
        console_label.setStyleSheet("""
            QLabel {
                background-color: #e9ecef;
                padding: 15px;
                border-radius: 8px;
                font-size: 13px;
                color: #495057;
                border-left: 4px solid #6c757d;
            }
        """)
        layout.addWidget(console_label)
        
        main_window.show()
        
        print("字体切换功能测试应用启动成功")
        print("请点击按钮开始测试")
        
        return app.exec_()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("开始测试封面水印字体切换功能...")
    return test_font_switching()

if __name__ == "__main__":
    sys.exit(main())
