#!/usr/bin/env python3
"""
测试多行水印功能的脚本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel

# 添加项目路径
sys.path.append('.')

def test_multiline_watermark():
    """测试多行水印功能"""
    try:
        from app.dialogs.video_processor_dialog import WatermarkConfigDialog, WatermarkConfig
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("多行水印功能测试")
        main_window.setGeometry(100, 100, 900, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("""
📝 多行水印功能测试

功能说明：
✅ 文件名拆分显示：将完整的视频文件名按字符数拆分成多行水印
✅ 每行字符数配置：为每一行水印单独设置显示的字符数量
✅ 独立的行级拖拽功能：每一行水印都可以独立拖拽到不同位置
✅ 行级样式设置：可以选中任意一行水印进行单独设置

测试步骤：
1. 点击下方按钮打开水印配置对话框
2. 在"📝 多行水印模式"组中：
   - 勾选"启用多行水印模式"
   - 观察预览区域切换到多行模式
   - 使用"当前编辑行"下拉框选择要编辑的行
   - 调整"该行字符数"来控制每行显示的字符数
   - 使用"➕ 添加行"和"➖ 删除行"按钮管理行数
3. 在预览区域中：
   - 每一行水印都可以独立拖拽
   - 点击某行水印会自动选中该行进行编辑
   - 观察不同行的边框颜色和状态提示

预期效果：
- 文件名"示例视频文件名.mp4"会被拆分显示
- 第1行显示3个字符："示例视"
- 第2行显示2个字符："频文"
- 第3行显示1个字符："件"
- 每行都可以独立拖拽到不同位置
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #e8f4fd;
                padding: 20px;
                border-radius: 8px;
                font-size: 14px;
                line-height: 1.6;
                border: 2px solid #3498db;
                color: #2c3e50;
            }
        """)
        layout.addWidget(info_label)
        
        # 添加测试按钮
        test_button = QPushButton("🎨 打开多行水印配置对话框")
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        def open_dialog():
            print("=" * 80)
            print("开始多行水印功能测试")
            print("=" * 80)
            
            # 创建水印配置
            config = WatermarkConfig()
            config.enabled = True
            
            # 打开配置对话框
            dialog = WatermarkConfigDialog(config, main_window)
            
            print("对话框已打开，请测试以下功能：")
            print("1. 启用多行水印模式")
            print("2. 调整每行字符数")
            print("3. 添加/删除水印行")
            print("4. 拖拽每一行水印")
            print("5. 选择不同行进行编辑")
            
            result = dialog.exec_()
            
            if result == QDialog.Accepted:
                print("\n配置已确认，多行水印设置：")
                print(f"多行模式启用: {config.multi_line_enabled}")
                print(f"水印行数: {len(config.multi_line_configs)}")
                
                for i, line_config in enumerate(config.multi_line_configs):
                    print(f"第{i+1}行: {line_config.char_count}字符, 位置({line_config.position_x}%, {line_config.position_y}%)")
            else:
                print("\n配置已取消")
            
            print("=" * 80)
        
        test_button.clicked.connect(open_dialog)
        layout.addWidget(test_button)
        
        # 添加功能特性说明
        features_label = QLabel("""
🚀 多行水印核心功能：

1. 📝 文件名智能拆分
   - 自动去除文件扩展名(.mp4, .avi, .mkv)
   - 按设定的字符数逐行拆分显示
   - 实时预览拆分效果

2. 🎯 精确的字符数控制
   - 每行可设置1-20个字符
   - 动态调整字符数时实时更新预览
   - 显示当前行的实际文本内容

3. 🖱️ 独立的行级拖拽
   - 每一行水印都可以独立拖拽
   - 支持实时拖拽预览
   - 每行位置独立保存

4. 🎨 行级样式设置
   - 选中行显示蓝色边框
   - 拖拽时显示红色虚线边框
   - 悬停时显示绿色边框提示

5. ➕➖ 动态行管理
   - 支持添加新的水印行
   - 支持删除不需要的行（至少保留1行）
   - 自动调整行选择器
        """)
        features_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                font-size: 13px;
                color: #495057;
                border-left: 4px solid #28a745;
            }
        """)
        layout.addWidget(features_label)
        
        main_window.show()
        
        print("多行水印功能测试应用启动成功")
        print("请点击按钮开始测试")
        
        return app.exec_()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("开始测试多行水印功能...")
    return test_multiline_watermark()

if __name__ == "__main__":
    sys.exit(main())
