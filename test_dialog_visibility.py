#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试对话框控件可见性修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_dialog_visibility():
    """测试对话框控件可见性"""
    print("=" * 80)
    print("👁️ 对话框控件可见性测试")
    print("=" * 80)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from app.dialogs.video_processor_dialog import WatermarkConfig, WatermarkConfigDialog
        
        app = QApplication(sys.argv)
        
        # 创建配置
        config = WatermarkConfig()
        config.enabled = True
        config.font_size = 60
        
        print("\n1. 创建水印配置对话框...")
        dialog = WatermarkConfigDialog(config)
        print("   ✅ 对话框创建成功")
        
        # 检查对话框尺寸
        print(f"\n2. 对话框尺寸: {dialog.width()}x{dialog.height()}")
        
        # 强制显示对话框以触发布局
        dialog.show()
        app.processEvents()  # 处理事件以确保布局完成
        
        # 检查关键控件的可见性
        print("\n3. 检查控件可见性（修复后）...")
        
        controls_to_check = [
            ('position_x_spinbox', '水平位置控件'),
            ('position_y_spinbox', '垂直位置控件'),
            ('font_size_spinbox', '字体大小控件'),
            ('opacity_slider', '透明度滑块')
        ]
        
        visible_count = 0
        total_count = len(controls_to_check)
        
        for attr_name, desc in controls_to_check:
            if hasattr(dialog, attr_name):
                control = getattr(dialog, attr_name)
                if control:
                    visible = control.isVisible()
                    if visible:
                        print(f"   ✅ {desc}: 可见")
                        visible_count += 1
                    else:
                        print(f"   ❌ {desc}: 仍然隐藏")
                        
                    # 检查几何信息
                    geom = control.geometry()
                    print(f"      位置: ({geom.x()}, {geom.y()}) 尺寸: {geom.width()}x{geom.height()}")
                else:
                    print(f"   ❌ {desc}: 控件为None")
            else:
                print(f"   ❌ {desc}: 控件不存在")
        
        # 总结可见性
        print(f"\n4. 可见性总结:")
        print(f"   可见控件: {visible_count}/{total_count}")
        if visible_count == total_count:
            print("   ✅ 所有关键控件都可见！")
        elif visible_count > 0:
            print("   ⚠️ 部分控件可见，可能还需要进一步调整")
        else:
            print("   ❌ 所有控件都不可见，需要检查布局问题")
        
        # 检查配置区域宽度
        print(f"\n5. 检查配置区域...")
        for child in dialog.findChildren(type(dialog).__bases__[0]):
            if hasattr(child, 'width') and child.width() == 650:
                print(f"   ✅ 找到650px宽的配置区域")
                break
        else:
            print(f"   ⚠️ 未找到650px宽的配置区域")
        
        dialog.hide()  # 隐藏对话框
        
        print(f"\n✅ 控件可见性测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    test_dialog_visibility()
