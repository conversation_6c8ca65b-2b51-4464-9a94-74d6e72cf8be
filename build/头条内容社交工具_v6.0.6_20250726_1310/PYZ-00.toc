('E:\\toutiaoyuanma1\\tou014\\tou011\\build\\头条内容社交工具_v6.0.6_20250726_1310\\PYZ-00.pyz',
 [('PIL',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageDraw',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE'),
  ('PIL.ImageDraw2',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE'),
  ('PIL.ImageEnhance',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageEnhance.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageFont',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImagePath',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageStat',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageStat.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL._typing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._util',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PyQt5',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\__future__.py',
   'PYMODULE'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\_compression.py',
   'PYMODULE'),
  ('_markupbase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\_markupbase.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\_threading_local.py',
   'PYMODULE'),
  ('app', 'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\__init__.py', 'PYMODULE'),
  ('app.dialogs',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\__init__.py',
   'PYMODULE'),
  ('app.dialogs.account_loading_dialog',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\account_loading_dialog.py',
   'PYMODULE'),
  ('app.dialogs.ai_config_dialog',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\ai_config_dialog.py',
   'PYMODULE'),
  ('app.dialogs.batch_settings_dialog',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\batch_settings_dialog.py',
   'PYMODULE'),
  ('app.dialogs.data_collection_settings_dialog',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\data_collection_settings_dialog.py',
   'PYMODULE'),
  ('app.dialogs.init_progress_dialog',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\init_progress_dialog.py',
   'PYMODULE'),
  ('app.dialogs.loading_animation_dialog',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\loading_animation_dialog.py',
   'PYMODULE'),
  ('app.dialogs.material_selection_dialog',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\material_selection_dialog.py',
   'PYMODULE'),
  ('app.dialogs.nurture_dialog',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\nurture_dialog.py',
   'PYMODULE'),
  ('app.dialogs.password_dialog',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\password_dialog.py',
   'PYMODULE'),
  ('app.dialogs.proxy_settings_dialog',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\proxy_settings_dialog.py',
   'PYMODULE'),
  ('app.dialogs.pyright_disable',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\pyright_disable.py',
   'PYMODULE'),
  ('app.dialogs.stop_tasks_dialog',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\stop_tasks_dialog.py',
   'PYMODULE'),
  ('app.dialogs.toutiao_spider_dialog',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\toutiao_spider_dialog.py',
   'PYMODULE'),
  ('app.dialogs.video_processor_dialog',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\video_processor_dialog.py',
   'PYMODULE'),
  ('app.dialogs.web_browser_dialog',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\dialogs\\web_browser_dialog.py',
   'PYMODULE'),
  ('app.main_window',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\main_window.py',
   'PYMODULE'),
  ('app.modules',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\modules\\__init__.py',
   'PYMODULE'),
  ('app.modules.account_nurture',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\modules\\account_nurture.py',
   'PYMODULE'),
  ('app.tabs',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\tabs\\__init__.py',
   'PYMODULE'),
  ('app.tabs.account_tab',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\tabs\\account_tab.py',
   'PYMODULE'),
  ('app.tabs.setting_tab',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\tabs\\setting_tab.py',
   'PYMODULE'),
  ('app.utils',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\__init__.py',
   'PYMODULE'),
  ('app.utils.account_loader',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\account_loader.py',
   'PYMODULE'),
  ('app.utils.ai_filename_rewriter',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\ai_filename_rewriter.py',
   'PYMODULE'),
  ('app.utils.anti_crack_config',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\anti_crack_config.py',
   'PYMODULE'),
  ('app.utils.auto_login_helper',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\auto_login_helper.py',
   'PYMODULE'),
  ('app.utils.auto_updater',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\auto_updater.py',
   'PYMODULE'),
  ('app.utils.auto_updater_helper',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\auto_updater_helper.py',
   'PYMODULE'),
  ('app.utils.automation_scheduler',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\automation_scheduler.py',
   'PYMODULE'),
  ('app.utils.browser_proxy',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\browser_proxy.py',
   'PYMODULE'),
  ('app.utils.browser_utils',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\browser_utils.py',
   'PYMODULE'),
  ('app.utils.chromedriver_manager',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\chromedriver_manager.py',
   'PYMODULE'),
  ('app.utils.cleanup_chrome',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\cleanup_chrome.py',
   'PYMODULE'),
  ('app.utils.dengluzhanghaocookie',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\dengluzhanghaocookie.py',
   'PYMODULE'),
  ('app.utils.excel_exporter',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\excel_exporter.py',
   'PYMODULE'),
  ('app.utils.greeting_generator',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\greeting_generator.py',
   'PYMODULE'),
  ('app.utils.guantou_cookie_helper',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\guantou_cookie_helper.py',
   'PYMODULE'),
  ('app.utils.kami_manager_patch',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\kami_manager_patch.py',
   'PYMODULE'),
  ('app.utils.kamidenglu',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\kamidenglu.py',
   'PYMODULE'),
  ('app.utils.logger',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\logger.py',
   'PYMODULE'),
  ('app.utils.memory_manager',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\memory_manager.py',
   'PYMODULE'),
  ('app.utils.notification_helper',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\notification_helper.py',
   'PYMODULE'),
  ('app.utils.nurture_proxy',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\nurture_proxy.py',
   'PYMODULE'),
  ('app.utils.piliang_cunggao',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\piliang_cunggao.py',
   'PYMODULE'),
  ('app.utils.proxy_utils',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\proxy_utils.py',
   'PYMODULE'),
  ('app.utils.realname_detector',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\realname_detector.py',
   'PYMODULE'),
  ('app.utils.resource_path',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\resource_path.py',
   'PYMODULE'),
  ('app.utils.stop_tasks_thread',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\stop_tasks_thread.py',
   'PYMODULE'),
  ('app.utils.system_monitor',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\system_monitor.py',
   'PYMODULE'),
  ('app.utils.thread_worker',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\thread_worker.py',
   'PYMODULE'),
  ('app.utils.tianjiazhanghao',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\tianjiazhanghao.py',
   'PYMODULE'),
  ('app.utils.toutiao_data_collector',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\toutiao_data_collector.py',
   'PYMODULE'),
  ('app.utils.toutiao_spider',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\toutiao_spider.py',
   'PYMODULE'),
  ('app.utils.unified_login_manager',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\unified_login_manager.py',
   'PYMODULE'),
  ('app.utils.yonghu_zhuangtai',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\utils\\yonghu_zhuangtai.py',
   'PYMODULE'),
  ('app.widgets',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\widgets\\__init__.py',
   'PYMODULE'),
  ('app.widgets.collapsible_card',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\widgets\\collapsible_card.py',
   'PYMODULE'),
  ('app.widgets.datetime_selector',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\widgets\\datetime_selector.py',
   'PYMODULE'),
  ('app.widgets.earnings_stats_widget',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\widgets\\earnings_stats_widget.py',
   'PYMODULE'),
  ('app.widgets.heartbeat_indicator',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\widgets\\heartbeat_indicator.py',
   'PYMODULE'),
  ('app.widgets.progress_dialog',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\widgets\\progress_dialog.py',
   'PYMODULE'),
  ('app.widgets.sidebar_navigation',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\widgets\\sidebar_navigation.py',
   'PYMODULE'),
  ('app.widgets.status_bar_widget',
   'E:\\toutiaoyuanma1\\tou014\\tou011\\app\\widgets\\status_bar_widget.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\base64.py',
   'PYMODULE'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\bdb.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\bisect.py',
   'PYMODULE'),
  ('bs4',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4._deprecation',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\bs4\\_deprecation.py',
   'PYMODULE'),
  ('bs4._typing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\bs4\\_typing.py',
   'PYMODULE'),
  ('bs4._warnings',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\bs4\\_warnings.py',
   'PYMODULE'),
  ('bs4.builder',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('bs4.css',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('bs4.dammit',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('bs4.element',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.exceptions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\bs4\\exceptions.py',
   'PYMODULE'),
  ('bs4.filter',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\bs4\\filter.py',
   'PYMODULE'),
  ('bs4.formatter',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\calendar.py',
   'PYMODULE'),
  ('certifi',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('cgi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\cgi.py',
   'PYMODULE'),
  ('charset_normalizer',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\cmd.py',
   'PYMODULE'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\code.py',
   'PYMODULE'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\codeop.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\datetime.py',
   'PYMODULE'),
  ('dateutil',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._common',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil._version',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('dateutil.easter',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil.parser',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.tz',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\dis.py',
   'PYMODULE'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\doctest.py',
   'PYMODULE'),
  ('dotenv',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fileinput',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\fileinput.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\gzip.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\html\\parser.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\http\\server.py',
   'PYMODULE'),
  ('idna',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\importlib\\_common.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\importlib\\resources.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lxml',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._core',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy._core.records',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('numpy._globals',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.char',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy.core',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.f2py',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.symbolic',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.fft',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.linalg',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matlib',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.rec',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy.typing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\opcode.py',
   'PYMODULE'),
  ('openpyxl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\optparse.py',
   'PYMODULE'),
  ('packaging',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._structures',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging.version',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pandas',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE'),
  ('pandas._config',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE'),
  ('pandas._config.config',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE'),
  ('pandas._config.dates',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE'),
  ('pandas._config.display',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE'),
  ('pandas._config.localization',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE'),
  ('pandas._libs',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.tslibs',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE'),
  ('pandas._libs.window',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE'),
  ('pandas._testing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE'),
  ('pandas._testing._io',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE'),
  ('pandas._testing._warnings',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE'),
  ('pandas._testing.asserters',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE'),
  ('pandas._testing.compat',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE'),
  ('pandas._testing.contexts',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE'),
  ('pandas._typing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE'),
  ('pandas._version',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE'),
  ('pandas._version_meson',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE'),
  ('pandas.api',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE'),
  ('pandas.api.extensions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE'),
  ('pandas.api.indexers',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.api.interchange',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.api.types',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE'),
  ('pandas.api.typing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE'),
  ('pandas.arrays',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.compat',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE'),
  ('pandas.compat._constants',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE'),
  ('pandas.compat._optional',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE'),
  ('pandas.compat.compressors',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE'),
  ('pandas.compat.numpy',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE'),
  ('pandas.compat.numpy.function',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE'),
  ('pandas.compat.pickle_compat',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE'),
  ('pandas.compat.pyarrow',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE'),
  ('pandas.core',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.executor',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE'),
  ('pandas.core._numba.extensions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.mean_',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.min_max_',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.shared',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.sum_',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE'),
  ('pandas.core._numba.kernels.var_',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE'),
  ('pandas.core.accessor',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE'),
  ('pandas.core.algorithms',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE'),
  ('pandas.core.api',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE'),
  ('pandas.core.apply',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE'),
  ('pandas.core.array_algos',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_accumulations',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE'),
  ('pandas.core.array_algos.masked_reductions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE'),
  ('pandas.core.array_algos.putmask',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE'),
  ('pandas.core.array_algos.quantile',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE'),
  ('pandas.core.array_algos.replace',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE'),
  ('pandas.core.array_algos.take',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE'),
  ('pandas.core.array_algos.transforms',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE'),
  ('pandas.core.arraylike',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE'),
  ('pandas.core.arrays',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays._arrow_string_mixins',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._mixins',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE'),
  ('pandas.core.arrays._ranges',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE'),
  ('pandas.core.arrays._utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.accessors',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.array',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.arrow.extension_types',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE'),
  ('pandas.core.arrays.base',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE'),
  ('pandas.core.arrays.boolean',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE'),
  ('pandas.core.arrays.categorical',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimelike',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.arrays.datetimes',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.arrays.floating',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE'),
  ('pandas.core.arrays.integer',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE'),
  ('pandas.core.arrays.interval',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE'),
  ('pandas.core.arrays.masked',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE'),
  ('pandas.core.arrays.numeric',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE'),
  ('pandas.core.arrays.numpy_',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE'),
  ('pandas.core.arrays.period',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.accessor',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.array',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE'),
  ('pandas.core.arrays.string_arrow',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE'),
  ('pandas.core.arrays.timedeltas',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.base',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE'),
  ('pandas.core.common',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE'),
  ('pandas.core.computation',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE'),
  ('pandas.core.computation.align',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE'),
  ('pandas.core.computation.api',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE'),
  ('pandas.core.computation.check',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE'),
  ('pandas.core.computation.common',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE'),
  ('pandas.core.computation.engines',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE'),
  ('pandas.core.computation.eval',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE'),
  ('pandas.core.computation.expr',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE'),
  ('pandas.core.computation.expressions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE'),
  ('pandas.core.computation.ops',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE'),
  ('pandas.core.computation.parsing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE'),
  ('pandas.core.computation.pytables',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE'),
  ('pandas.core.computation.scope',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE'),
  ('pandas.core.config_init',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE'),
  ('pandas.core.construction',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE'),
  ('pandas.core.dtypes',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.dtypes.api',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE'),
  ('pandas.core.dtypes.astype',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE'),
  ('pandas.core.dtypes.base',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE'),
  ('pandas.core.dtypes.cast',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE'),
  ('pandas.core.dtypes.common',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE'),
  ('pandas.core.dtypes.concat',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE'),
  ('pandas.core.dtypes.dtypes',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE'),
  ('pandas.core.dtypes.generic',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE'),
  ('pandas.core.dtypes.inference',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE'),
  ('pandas.core.dtypes.missing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE'),
  ('pandas.core.flags',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE'),
  ('pandas.core.frame',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE'),
  ('pandas.core.generic',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE'),
  ('pandas.core.groupby.base',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE'),
  ('pandas.core.groupby.categorical',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE'),
  ('pandas.core.groupby.generic',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE'),
  ('pandas.core.groupby.groupby',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE'),
  ('pandas.core.groupby.grouper',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE'),
  ('pandas.core.groupby.indexing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE'),
  ('pandas.core.groupby.numba_',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE'),
  ('pandas.core.groupby.ops',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE'),
  ('pandas.core.indexers',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexers.objects',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE'),
  ('pandas.core.indexers.utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE'),
  ('pandas.core.indexes',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE'),
  ('pandas.core.indexes.accessors',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE'),
  ('pandas.core.indexes.api',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE'),
  ('pandas.core.indexes.base',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE'),
  ('pandas.core.indexes.category',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimelike',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE'),
  ('pandas.core.indexes.datetimes',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.indexes.extension',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE'),
  ('pandas.core.indexes.frozen',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE'),
  ('pandas.core.indexes.interval',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE'),
  ('pandas.core.indexes.multi',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE'),
  ('pandas.core.indexes.period',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE'),
  ('pandas.core.indexes.range',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE'),
  ('pandas.core.indexes.timedeltas',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.indexing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE'),
  ('pandas.core.interchange',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE'),
  ('pandas.core.interchange.buffer',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE'),
  ('pandas.core.interchange.column',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.dataframe_protocol',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE'),
  ('pandas.core.interchange.from_dataframe',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE'),
  ('pandas.core.interchange.utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE'),
  ('pandas.core.internals',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE'),
  ('pandas.core.internals.api',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE'),
  ('pandas.core.internals.array_manager',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE'),
  ('pandas.core.internals.base',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE'),
  ('pandas.core.internals.blocks',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE'),
  ('pandas.core.internals.concat',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE'),
  ('pandas.core.internals.construction',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE'),
  ('pandas.core.internals.managers',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE'),
  ('pandas.core.internals.ops',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE'),
  ('pandas.core.methods',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE'),
  ('pandas.core.methods.describe',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE'),
  ('pandas.core.methods.selectn',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE'),
  ('pandas.core.methods.to_dict',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE'),
  ('pandas.core.missing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE'),
  ('pandas.core.nanops',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE'),
  ('pandas.core.ops',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE'),
  ('pandas.core.ops.array_ops',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.common',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE'),
  ('pandas.core.ops.dispatch',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE'),
  ('pandas.core.ops.docstrings',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE'),
  ('pandas.core.ops.invalid',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE'),
  ('pandas.core.ops.mask_ops',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE'),
  ('pandas.core.ops.missing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE'),
  ('pandas.core.resample',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE'),
  ('pandas.core.reshape',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE'),
  ('pandas.core.reshape.api',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE'),
  ('pandas.core.reshape.concat',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE'),
  ('pandas.core.reshape.encoding',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE'),
  ('pandas.core.reshape.melt',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE'),
  ('pandas.core.reshape.merge',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE'),
  ('pandas.core.reshape.pivot',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE'),
  ('pandas.core.reshape.reshape',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE'),
  ('pandas.core.reshape.tile',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE'),
  ('pandas.core.reshape.util',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE'),
  ('pandas.core.roperator',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE'),
  ('pandas.core.sample',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE'),
  ('pandas.core.series',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE'),
  ('pandas.core.shared_docs',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE'),
  ('pandas.core.sorting',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE'),
  ('pandas.core.strings',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE'),
  ('pandas.core.strings.accessor',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE'),
  ('pandas.core.strings.base',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE'),
  ('pandas.core.strings.object_array',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE'),
  ('pandas.core.tools',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE'),
  ('pandas.core.tools.datetimes',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE'),
  ('pandas.core.tools.numeric',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE'),
  ('pandas.core.tools.timedeltas',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE'),
  ('pandas.core.tools.times',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE'),
  ('pandas.core.util',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.core.util.hashing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE'),
  ('pandas.core.util.numba_',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE'),
  ('pandas.core.window.common',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE'),
  ('pandas.core.window.doc',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE'),
  ('pandas.core.window.ewm',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE'),
  ('pandas.core.window.expanding',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE'),
  ('pandas.core.window.numba_',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE'),
  ('pandas.core.window.online',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE'),
  ('pandas.core.window.rolling',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE'),
  ('pandas.errors',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE'),
  ('pandas.io',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE'),
  ('pandas.io._util',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE'),
  ('pandas.io.api',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE'),
  ('pandas.io.clipboard',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pandas.io.clipboards',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE'),
  ('pandas.io.common',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE'),
  ('pandas.io.excel',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE'),
  ('pandas.io.excel._base',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE'),
  ('pandas.io.excel._calamine',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE'),
  ('pandas.io.excel._odfreader',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE'),
  ('pandas.io.excel._odswriter',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE'),
  ('pandas.io.excel._openpyxl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE'),
  ('pandas.io.excel._pyxlsb',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE'),
  ('pandas.io.excel._util',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE'),
  ('pandas.io.excel._xlrd',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE'),
  ('pandas.io.excel._xlsxwriter',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE'),
  ('pandas.io.feather_format',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE'),
  ('pandas.io.formats',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE'),
  ('pandas.io.formats._color_data',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE'),
  ('pandas.io.formats.console',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE'),
  ('pandas.io.formats.css',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE'),
  ('pandas.io.formats.csvs',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE'),
  ('pandas.io.formats.excel',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE'),
  ('pandas.io.formats.format',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE'),
  ('pandas.io.formats.html',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE'),
  ('pandas.io.formats.info',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE'),
  ('pandas.io.formats.printing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE'),
  ('pandas.io.formats.string',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE'),
  ('pandas.io.formats.style',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE'),
  ('pandas.io.formats.style_render',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE'),
  ('pandas.io.formats.xml',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE'),
  ('pandas.io.gbq',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE'),
  ('pandas.io.html',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE'),
  ('pandas.io.json',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE'),
  ('pandas.io.json._json',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE'),
  ('pandas.io.json._normalize',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE'),
  ('pandas.io.json._table_schema',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE'),
  ('pandas.io.orc',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE'),
  ('pandas.io.parquet',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE'),
  ('pandas.io.parsers',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.base_parser',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.c_parser_wrapper',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE'),
  ('pandas.io.parsers.python_parser',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE'),
  ('pandas.io.parsers.readers',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE'),
  ('pandas.io.pickle',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE'),
  ('pandas.io.pytables',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE'),
  ('pandas.io.sas',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE'),
  ('pandas.io.sas.sas7bdat',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_constants',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE'),
  ('pandas.io.sas.sas_xport',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE'),
  ('pandas.io.sas.sasreader',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE'),
  ('pandas.io.spss',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE'),
  ('pandas.io.sql',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE'),
  ('pandas.io.stata',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE'),
  ('pandas.io.xml',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE'),
  ('pandas.plotting',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE'),
  ('pandas.plotting._core',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE'),
  ('pandas.plotting._misc',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE'),
  ('pandas.testing',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE'),
  ('pandas.tseries',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE'),
  ('pandas.tseries.api',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE'),
  ('pandas.tseries.frequencies',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE'),
  ('pandas.tseries.holiday',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE'),
  ('pandas.tseries.offsets',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE'),
  ('pandas.util',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE'),
  ('pandas.util._decorators',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE'),
  ('pandas.util._exceptions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE'),
  ('pandas.util._print_versions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE'),
  ('pandas.util._tester',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE'),
  ('pandas.util._validators',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE'),
  ('pandas.util.version',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\pathlib.py',
   'PYMODULE'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\pdb.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\pickle.py',
   'PYMODULE'),
  ('pickletools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\pickletools.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\platform.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\pprint.py',
   'PYMODULE'),
  ('psutil',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\py_compile.py',
   'PYMODULE'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\pydoc.py',
   'PYMODULE'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pyperclip',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pyperclip\\__init__.py',
   'PYMODULE'),
  ('pytz',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE'),
  ('pytz.exceptions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE'),
  ('pytz.lazy',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE'),
  ('pytz.tzfile',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE'),
  ('pytz.tzinfo',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\random.py',
   'PYMODULE'),
  ('requests',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.__version__',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('requests.adapters',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('requests.api',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.auth',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests.certs',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('requests.compat',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('requests.cookies',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.exceptions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('requests.hooks',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.models',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('requests.packages',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('requests.sessions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.status_codes',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\selectors.py',
   'PYMODULE'),
  ('selenium',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\__init__.py',
   'PYMODULE'),
  ('selenium.common',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.common.exceptions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\common\\exceptions.py',
   'PYMODULE'),
  ('selenium.types',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\types.py',
   'PYMODULE'),
  ('selenium.webdriver',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\chrome\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.options',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\chrome\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.remote_connection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\chrome\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.service',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\chrome\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chrome.webdriver',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\chrome\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\chromium\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.options',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\chromium\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.remote_connection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\chromium\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.service',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\chromium\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.chromium.webdriver',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\chromium\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.common',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.action_chains',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\action_chains.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.action_builder',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\action_builder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.input_device',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\input_device.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.interaction',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\interaction.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_actions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.key_input',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\key_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.mouse_button',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\mouse_button.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_actions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.pointer_input',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\pointer_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_actions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_actions.py',
   'PYMODULE'),
  ('selenium.webdriver.common.actions.wheel_input',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\actions\\wheel_input.py',
   'PYMODULE'),
  ('selenium.webdriver.common.alert',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\alert.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.browser',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\browser.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.browsing_context',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\browsing_context.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.common',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\common.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.log',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\log.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.network',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\network.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.script',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\script.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.session',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\session.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.storage',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\storage.py',
   'PYMODULE'),
  ('selenium.webdriver.common.bidi.webextension',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\bidi\\webextension.py',
   'PYMODULE'),
  ('selenium.webdriver.common.by',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\by.py',
   'PYMODULE'),
  ('selenium.webdriver.common.desired_capabilities',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\desired_capabilities.py',
   'PYMODULE'),
  ('selenium.webdriver.common.driver_finder',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\driver_finder.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm.account',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\account.py',
   'PYMODULE'),
  ('selenium.webdriver.common.fedcm.dialog',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\fedcm\\dialog.py',
   'PYMODULE'),
  ('selenium.webdriver.common.keys',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\keys.py',
   'PYMODULE'),
  ('selenium.webdriver.common.options',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.print_page_options',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\print_page_options.py',
   'PYMODULE'),
  ('selenium.webdriver.common.proxy',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\proxy.py',
   'PYMODULE'),
  ('selenium.webdriver.common.selenium_manager',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\selenium_manager.py',
   'PYMODULE'),
  ('selenium.webdriver.common.service',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.common.timeouts',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\timeouts.py',
   'PYMODULE'),
  ('selenium.webdriver.common.utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.common.virtual_authenticator',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\common\\virtual_authenticator.py',
   'PYMODULE'),
  ('selenium.webdriver.edge',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\edge\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.options',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\edge\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.remote_connection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\edge\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.service',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\edge\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.edge.webdriver',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\edge\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\firefox\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_binary',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_binary.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.firefox_profile',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\firefox\\firefox_profile.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.options',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\firefox\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.remote_connection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\firefox\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.service',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\firefox\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.firefox.webdriver',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\firefox\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.ie',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\ie\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.options',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\ie\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.service',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\ie\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.ie.webdriver',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\ie\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.bidi_connection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\bidi_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.client_config',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\client_config.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.command',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\command.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.errorhandler',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\errorhandler.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.fedcm',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\fedcm.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.file_detector',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\file_detector.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.locator_converter',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\locator_converter.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.mobile',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\mobile.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.remote_connection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.script_key',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\script_key.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.shadowroot',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\shadowroot.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.switch_to',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\switch_to.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\utils.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webdriver',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.webelement',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\webelement.py',
   'PYMODULE'),
  ('selenium.webdriver.remote.websocket_connection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\remote\\websocket_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\safari\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.options',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\safari\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.remote_connection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\safari\\remote_connection.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.service',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\safari\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.safari.webdriver',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\safari\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.support',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\support\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.support.expected_conditions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\support\\expected_conditions.py',
   'PYMODULE'),
  ('selenium.webdriver.support.relative_locator',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\support\\relative_locator.py',
   'PYMODULE'),
  ('selenium.webdriver.support.select',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\support\\select.py',
   'PYMODULE'),
  ('selenium.webdriver.support.ui',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\support\\ui.py',
   'PYMODULE'),
  ('selenium.webdriver.support.wait',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\support\\wait.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.options',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.service',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.webkitgtk.webdriver',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\webkitgtk\\webdriver.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\__init__.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.options',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\options.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.service',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\service.py',
   'PYMODULE'),
  ('selenium.webdriver.wpewebkit.webdriver',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\selenium\\webdriver\\wpewebkit\\webdriver.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\signal.py',
   'PYMODULE'),
  ('six',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\six.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\socketserver.py',
   'PYMODULE'),
  ('socks',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('soupsieve',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\sqlite3\\__init__.py',
   'PYMODULE'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\sqlite3\\dump.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\ssl.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\subprocess.py',
   'PYMODULE'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\sysconfig.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\threading.py',
   'PYMODULE'),
  ('tkinter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib3',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3._version',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.connection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('urllib3.fields',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.http2',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('urllib3.response',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('urllib3.util',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('uu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\uu.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\uuid.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\webbrowser.py',
   'PYMODULE'),
  ('webdriver_manager',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.chrome',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\chrome.py',
   'PYMODULE'),
  ('webdriver_manager.core',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\core\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.core.archive',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\core\\archive.py',
   'PYMODULE'),
  ('webdriver_manager.core.config',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\core\\config.py',
   'PYMODULE'),
  ('webdriver_manager.core.constants',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\core\\constants.py',
   'PYMODULE'),
  ('webdriver_manager.core.download_manager',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\core\\download_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\core\\driver.py',
   'PYMODULE'),
  ('webdriver_manager.core.driver_cache',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\core\\driver_cache.py',
   'PYMODULE'),
  ('webdriver_manager.core.file_manager',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\core\\file_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.http',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\core\\http.py',
   'PYMODULE'),
  ('webdriver_manager.core.logger',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\core\\logger.py',
   'PYMODULE'),
  ('webdriver_manager.core.manager',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\core\\manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.os_manager',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\core\\os_manager.py',
   'PYMODULE'),
  ('webdriver_manager.core.utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\core\\utils.py',
   'PYMODULE'),
  ('webdriver_manager.drivers',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\drivers\\__init__.py',
   'PYMODULE'),
  ('webdriver_manager.drivers.chrome',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\webdriver_manager\\drivers\\chrome.py',
   'PYMODULE'),
  ('websocket',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\websocket\\__init__.py',
   'PYMODULE'),
  ('websocket._abnf',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\websocket\\_abnf.py',
   'PYMODULE'),
  ('websocket._app',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\websocket\\_app.py',
   'PYMODULE'),
  ('websocket._cookiejar',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\websocket\\_cookiejar.py',
   'PYMODULE'),
  ('websocket._core',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\websocket\\_core.py',
   'PYMODULE'),
  ('websocket._exceptions',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\websocket\\_exceptions.py',
   'PYMODULE'),
  ('websocket._handshake',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\websocket\\_handshake.py',
   'PYMODULE'),
  ('websocket._http',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\websocket\\_http.py',
   'PYMODULE'),
  ('websocket._logging',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\websocket\\_logging.py',
   'PYMODULE'),
  ('websocket._socket',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\websocket\\_socket.py',
   'PYMODULE'),
  ('websocket._ssl_compat',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\websocket\\_ssl_compat.py',
   'PYMODULE'),
  ('websocket._url',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\websocket\\_url.py',
   'PYMODULE'),
  ('websocket._utils',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\websocket\\_utils.py',
   'PYMODULE'),
  ('win32con',
   'e:\\toutiaoyuanma1\\tou014\\tou011\\venv\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.dom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\zipimport.py',
   'PYMODULE')])
