#!/usr/bin/env python
# -*- coding: utf-8 -*-

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("测试Cookie加密模块...")
try:
    from app.utils.cookie_encryption import CRYPTOGRAPHY_AVAILABLE
    print(f"cryptography可用: {CRYPTOGRAPHY_AVAILABLE}")
except Exception as e:
    print(f"导入失败: {e}")

print("\n测试AccountTab...")
try:
    from app.tabs.account_tab import AccountTab
    print("AccountTab导入成功")
except Exception as e:
    print(f"AccountTab导入失败: {e}")
    import traceback
    traceback.print_exc()

print("测试完成")
