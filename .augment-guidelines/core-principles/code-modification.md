# Code Modification Standards

## Core Principles

### 1. Existing File Modification Only
- **Rule**: Only modify existing files on their original code base
- **Prohibition**: No new file creation without explicit user approval
- **Rationale**: Maintains project structure integrity and prevents code sprawl

### 2. Zero Code Duplication
- **Rule**: Absolutely prohibit overlapping or duplicate code generation
- **Requirement**: Each functionality must have only one implementation
- **Enforcement**: Use `str-replace-editor` for precise code replacement

### 3. Structural Consistency
- **Rule**: Maintain code structure consistency and simplicity
- **Requirement**: Follow existing architecture patterns and naming conventions
- **Goal**: Preserve project coherence and readability

## Modification Tools and Techniques

### Primary Tool: str-replace-editor
```bash
# Standard usage pattern
str-replace-editor --command str_replace --path "file.py" \
  --old_str "original code block" \
  --new_str "modified code block" \
  --old_str_start_line_number X \
  --old_str_end_line_number Y
```

### Best Practices
1. **Precise Targeting**: Use exact line numbers and content matching
2. **Minimal Changes**: Make the smallest necessary modifications
3. **Preserve Formatting**: Maintain existing indentation and style
4. **Atomic Operations**: Complete related changes in single operations

## Modification Categories

### 1. Bug Fixes
- **Scope**: Correct specific errors or issues
- **Approach**: Minimal, targeted changes
- **Validation**: Ensure fix doesn't introduce new issues

### 2. Feature Enhancements
- **Scope**: Add new functionality to existing modules
- **Approach**: Extend existing patterns and structures
- **Validation**: Maintain backward compatibility

### 3. Code Refactoring
- **Scope**: Improve code structure without changing functionality
- **Approach**: Systematic restructuring following MECE principles
- **Validation**: Comprehensive testing to ensure no behavioral changes

### 4. Performance Optimization
- **Scope**: Improve efficiency and resource usage
- **Approach**: Targeted improvements with measurable benefits
- **Validation**: Performance benchmarking and regression testing

## Prohibited Practices

### 1. Code Duplication
```python
# BAD: Duplicate functionality
def process_account_v1(account):
    # Original implementation
    pass

def process_account_v2(account):
    # Duplicate implementation - PROHIBITED
    pass

# GOOD: Single implementation with improvements
def process_account(account):
    # Enhanced implementation replacing original
    pass
```

### 2. Overlapping Modules
```python
# BAD: Overlapping responsibilities
class AccountManager:
    def create_account(self): pass
    def collect_data(self): pass  # Should be in DataCollector

class DataCollector:
    def collect_data(self): pass  # Duplicate responsibility
    def process_account(self): pass  # Should be in AccountManager

# GOOD: Clear separation of concerns
class AccountManager:
    def create_account(self): pass
    def manage_account(self): pass

class DataCollector:
    def collect_data(self): pass
    def process_data(self): pass
```

### 3. Inconsistent Patterns
```python
# BAD: Inconsistent error handling
def function_a():
    try:
        # logic
    except Exception as e:
        print(f"Error: {e}")

def function_b():
    try:
        # logic
    except Exception as e:
        logging.error(f"Error in function_b: {e}")  # Different pattern

# GOOD: Consistent error handling
def function_a():
    try:
        # logic
    except Exception as e:
        logger.error(f"Error in function_a: {e}")

def function_b():
    try:
        # logic
    except Exception as e:
        logger.error(f"Error in function_b: {e}")
```

## Quality Assurance

### Pre-Modification Checklist
- [ ] Understand existing code structure and patterns
- [ ] Identify exact modification scope and requirements
- [ ] Plan minimal, targeted changes
- [ ] Prepare comprehensive test strategy

### During Modification
- [ ] Use `str-replace-editor` for precise changes
- [ ] Maintain existing code style and formatting
- [ ] Preserve existing functionality unless explicitly changing
- [ ] Document changes with clear comments

### Post-Modification Validation
- [ ] Syntax and import validation
- [ ] Functional testing of modified components
- [ ] Integration testing with dependent modules
- [ ] Performance impact assessment

## Error Prevention

### Common Mistakes
1. **Scope Creep**: Making unnecessary additional changes
2. **Pattern Breaking**: Introducing inconsistent code patterns
3. **Incomplete Testing**: Not validating all affected functionality
4. **Documentation Gaps**: Not updating related documentation

### Prevention Strategies
1. **Clear Requirements**: Define exact modification scope upfront
2. **Pattern Analysis**: Study existing code patterns before modifying
3. **Incremental Changes**: Make small, testable modifications
4. **Comprehensive Testing**: Test all affected functionality thoroughly
