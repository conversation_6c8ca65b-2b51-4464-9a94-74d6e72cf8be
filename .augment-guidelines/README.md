# Augment Development Guidelines

This directory contains comprehensive development guidelines and standards for the Toutiao Content Social Tool project.

## Directory Structure

```
.augment-guidelines/
├── README.md                    # This file - overview of guidelines
├── index.md                     # Main index of all guidelines
├── docs/                        # 📚 PROJECT DOCUMENTATION CENTER
│   ├── README.md               # Project main documentation
│   ├── build/                  # Build and packaging docs
│   │   └── BUILD_README.md     # Build instructions
│   ├── user-manual/            # User guides and manuals
│   │   └── README_养号机器人.md # Account nurturing guide
│   ├── security/               # Security documentation
│   │   ├── ANTI_CRACK_README.md # Anti-crack system docs
│   │   └── ANTI_CRACK_SUMMARY.md # Security features summary
│   ├── development/            # Development documentation
│   └── api/                    # API documentation
├── core-principles/             # Core development principles
│   ├── mece-principle.md       # MECE principle implementation
│   ├── code-modification.md    # Code modification standards
│   └── architecture-patterns.md # Architecture design patterns
├── development-workflow/        # Development workflow standards
│   ├── modification-process.md  # Standard modification process
│   ├── testing-standards.md    # Testing and validation standards
│   └── quality-assurance.md    # Quality assurance checklist
├── coding-standards/           # Coding standards and conventions
│   ├── python-conventions.md   # Python coding conventions
│   ├── pyqt5-guidelines.md     # PyQt5 specific guidelines
│   └── documentation.md        # Documentation standards
└── templates/                  # Templates for common tasks
    ├── modification-report.md   # Template for modification reports
    ├── testing-checklist.md    # Template for testing checklist
    └── code-review.md          # Template for code review
```

## Quick Reference

### Core Principles
1. **MECE Principle**: Mutually Exclusive, Collectively Exhaustive
2. **Code Modification**: Only modify existing files, avoid duplication
3. **Quality First**: Comprehensive testing and validation

### Standard Workflow
1. Code Modification Execution
2. Modification Content Summary
3. Comparison Analysis
4. Error Fix Verification
5. Functional Testing Validation

### Key Guidelines
- Use `str-replace-editor` for precise code modifications
- Follow existing architecture patterns and naming conventions
- Maintain code structure consistency and simplicity
- Ensure comprehensive test coverage
- Document all changes with specific details

## 📚 Documentation Management

### Unified Documentation Structure
All project Markdown files are now centrally managed in the `docs/` directory with the following organization:

- **docs/README.md** - Main project documentation
- **docs/build/** - Build and packaging documentation
- **docs/user-manual/** - User guides and feature manuals
- **docs/security/** - Security and anti-crack documentation
- **docs/development/** - Development and technical docs
- **docs/api/** - API documentation and references

### Documentation Standards
1. **Centralized Storage**: All `.md` files must be stored in `docs/` subdirectories
2. **Categorized Organization**: Documents are organized by type and purpose
3. **Consistent Naming**: Use descriptive, clear file names
4. **UTF-8 Encoding**: All documents use UTF-8 encoding
5. **Cross-References**: Maintain proper links between related documents

### Quick Access
- 📖 [Project Overview](docs/README.md)
- 🏗️ [Build Guide](docs/build/BUILD_README.md)
- 👤 [User Manuals](docs/user-manual/)
  - 🚀 [视频处理快速入门](docs/user-manual/视频处理快速入门.md)
  - 🎬 [视频处理流程指南](docs/user-manual/视频处理流程指南.md)
  - 🤖 [养号机器人使用说明](docs/user-manual/README_养号机器人.md)
- 💻 [Development Docs](docs/development/)
  - 🎬 [视频处理技术索引](docs/development/视频处理技术索引.md)
  - 🎨 [封面水印功能实现报告](docs/development/封面水印功能实现报告.md)
  - ✨ [水印功能增强实现报告](docs/development/水印功能增强实现报告.md)
- 🔌 [API Reference](docs/api/)
  - 🎬 [视频处理API参考](docs/api/视频处理API参考.md)
- 🔒 [Security Docs](docs/security/)

## Usage

Refer to `index.md` for a complete overview of all available guidelines and their usage instructions.
