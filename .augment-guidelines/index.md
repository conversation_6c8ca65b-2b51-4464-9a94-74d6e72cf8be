# Development Guidelines Index

## Core Principles

### 1. MECE Principle (Mutually Exclusive, Collectively Exhaustive)
- **File**: `core-principles/mece-principle.md`
- **Purpose**: Ensure systematic design and problem analysis
- **Application**: System design, feature decomposition, problem analysis
- **Key Requirements**:
  - Function modules must be mutually independent
  - Avoid duplication and conflicts
  - Ensure complete coverage without omissions
  - Maintain logical clarity and structural integrity

### 2. Code Modification Standards
- **File**: `core-principles/code-modification.md`
- **Purpose**: Standardize code modification practices
- **Key Rules**:
  - Only modify existing files, no new file creation without approval
  - Absolutely prohibit duplicate or overlapping code
  - Use `str-replace-editor` for precise replacements
  - Maintain consistency and simplicity

## Development Workflow

### Standard Modification Process
- **File**: `development-workflow/modification-process.md`
- **Stages**:
  1. Code Modification Execution
  2. Modification Content Summary
  3. Comparison Analysis
  4. Error Fix Verification
  5. Functional Testing Validation

### Testing Standards
- **File**: `development-workflow/testing-standards.md`
- **Coverage**:
  - Code quality checks
  - Functional completeness verification
  - System integration testing
  - Performance and resource management

## Coding Standards

### Python Conventions
- **File**: `coding-standards/python-conventions.md`
- **Scope**: Python-specific coding standards and best practices

### PyQt5 Guidelines
- **File**: `coding-standards/pyqt5-guidelines.md`
- **Scope**: PyQt5 application development guidelines

### Documentation Standards
- **File**: `coding-standards/documentation.md`
- **Scope**: Code documentation and comment standards

## Templates

### Modification Report Template
- **File**: `templates/modification-report.md`
- **Usage**: Standardized format for reporting code modifications

### Testing Checklist Template
- **File**: `templates/testing-checklist.md`
- **Usage**: Comprehensive testing validation checklist

### Code Review Template
- **File**: `templates/code-review.md`
- **Usage**: Structured code review process

## Quick Start Guide

1. **Before Making Changes**: Review relevant guidelines in `core-principles/`
2. **During Development**: Follow the process in `development-workflow/`
3. **Code Standards**: Refer to `coding-standards/` for specific conventions
4. **Documentation**: Use templates in `templates/` for consistent reporting

## Enhanced Guidelines

### Quality Assurance Standards
- **File**: `quality-assurance-standards.md`
- **Purpose**: Comprehensive quality metrics and MECE compliance standards
- **Coverage**: Code quality, functional standards, testing requirements, verification checklists

### Standard Modification Workflow
- **File**: `standard-modification-workflow.md`
- **Purpose**: Systematic 5-stage modification process with detailed procedures
- **Stages**: Code execution, content summary, comparative analysis, error verification, functional testing

## Validation Requirements

All modifications must pass:
- Syntax and import dependency checks
- Core business logic testing
- UI interaction verification
- System integration testing
- Performance and resource management validation
- MECE principle compliance verification
- Quality assurance standards adherence
