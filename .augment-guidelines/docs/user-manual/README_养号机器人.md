# 头条号养号机器人使用说明

## 功能简介

头条号养号机器人可以帮助您自动维护头条账号的活跃度，提升账号权重，防止账号被降权或沉默。养号机器人通过模拟真实用户的浏览、点赞、评论、关注等行为，使您的账号保持活跃状态。

## 主要功能

1. **自动浏览内容**：随机浏览首页推荐文章和视频，模拟真实阅读行为
2. **随机点赞**：根据设定的概率为内容点赞
3. **随机评论**：从预设评论池中随机选择评论发布
4. **随机关注**：有一定概率关注内容作者
5. **防检测机制**：模拟真人操作，加入随机延迟和滚动行为

## 使用方法

1. 在主界面点击底部的「头条号养号」按钮
2. 在弹出的设置对话框中配置养号参数：
   - **时间设置**：配置养号时间段
   - **行为设置**：设置浏览、点赞、评论、关注等行为参数
   - **评论设置**：编辑评论内容池

3. 点击「立即开始养号」或「定时养号计划」按钮开始任务
4. 养号过程中可以在日志标签页查看养号进度和详细日志

## 注意事项

1. **账号选择**：请在账号管理标签页中勾选需要进行养号的账号
2. **行为频率**：建议不要设置过高的点赞和评论频率，保持自然
3. **评论内容**：评论内容尽量多样化，避免重复性过高
4. **操作间隔**：建议开启「操作间随机延迟」，模拟真实用户行为
5. **系统资源**：养号过程会消耗一定系统资源，请确保电脑性能足够

## 推荐配置

- **浏览文章数**：5-8篇/次
- **点赞概率**：50-70%
- **评论概率**：20-30%
- **关注概率**：10-20%
- **平均浏览时长**：40-60秒

## 依赖安装

首次使用养号机器人需要安装Playwright：

```bash
pip install playwright
playwright install chromium
```

## 故障排除

1. **浏览器启动失败**：检查Playwright是否正确安装
2. **Cookie加载失败**：确认账号Cookie文件是否有效
3. **元素查找失败**：可能是头条网页结构发生变化，需要更新养号模块

如有其他问题，请查看系统日志或联系技术支持。 