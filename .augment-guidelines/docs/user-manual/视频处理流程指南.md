# 🎬 视频处理流程指南

## 📋 概述

头条内容社交工具的视频处理功能是一个强大的视频批量处理系统，支持视频筛选、封面生成、AI文件名改写等功能。本文档详细介绍视频处理的完整流程和使用方法。

## 🎯 核心功能

### ✨ 主要特性
- **智能视频筛选** - 自动筛选横屏视频，过滤竖屏和方形视频
- **高质量封面生成** - 生成1920x1080高清封面图片
- **封面水印功能** - 自动为封面添加文件名水印，支持丰富的样式配置
- **AI文件名改写** - 智能改写视频文件名，提升内容质量
- **时长筛选** - 根据视频时长进行精确筛选
- **多线程处理** - 并行处理，大幅提升处理效率
- **智能去重** - 避免重复处理，智能管理输出文件

## 🚀 快速开始

### 1. 打开视频处理工具
```
主界面 → 视频处理按钮 → 🎬 视频高质量去重工具
```

### 2. 基本配置
- **输入目录**: 选择包含待处理视频的文件夹
- **输出目录**: 选择处理后视频的保存位置
- **封面目录**: 选择生成封面图片的保存位置
- **线程数量**: 设置并行处理的线程数（建议8-16个）
- **启用封面水印**: 开启后为每个封面自动添加文件名水印
- **水印配置**: 点击配置按钮设置水印样式、位置、颜色等

### 3. 一键处理
点击"开始处理"按钮，系统将自动完成所有处理步骤。

## 📊 处理流程详解

### 🔄 三阶段处理流程

#### 第一阶段：视频文件扫描与筛选
```
1. 扫描输入目录中的所有视频文件
   ├── 支持格式：.mp4, .avi, .mov, .mkv, .wmv, .flv, .webm, .m4v
   └── 递归扫描子目录

2. 智能筛选处理
   ├── 横屏检测：过滤竖屏和方形视频
   ├── 时长筛选：根据设定的时长范围筛选
   └── 重复检测：避免重复处理已存在的文件
```

#### 第二阶段：AI文件名改写（可选）
```
1. 批量文件名分析
   ├── 提取原始文件名
   ├── 分析内容特征
   └── 生成改写建议

2. AI智能改写
   ├── 调用AI改写服务
   ├── 生成优化后的文件名
   └── 建立文件名映射关系
```

#### 第三阶段：视频处理与封面生成
```
1. 多线程并行处理
   ├── 创建工作线程池
   ├── 分配处理任务
   └── 监控处理进度

2. 单个视频处理流程
   ├── 视频格式检测
   ├── 横屏验证
   ├── 时长验证
   ├── 封面帧提取
   ├── 封面图片生成
   └── 文件复制/移动
```

## ⚙️ 详细配置选项

### 📁 目录设置
- **输入目录**: 待处理视频的源文件夹
- **输出目录**: 处理后视频的目标文件夹
- **封面目录**: 生成封面图片的保存文件夹

### 🎛️ 处理选项
- **覆盖已存在文件**: 是否覆盖输出目录中的同名文件
- **生成封面**: 是否为每个视频生成封面图片
- **时长筛选**: 启用视频时长范围筛选
  - 最小时长（分钟）
  - 最大时长（分钟）

### 🤖 AI改写设置
- **启用AI改写**: 开启智能文件名改写功能
- **智能体ID**: AI服务的智能体标识
- **访问令牌**: AI服务的认证令牌

### 🎨 水印配置设置
- **字体设置**: 支持多种字体类型（微软雅黑、宋体、黑体等）
- **字体大小**: 12-72像素可调节
- **文字颜色**: 自定义文字颜色选择
- **阴影效果**: 可开启/关闭阴影，支持阴影颜色和偏移调节
- **位置控制**: 水平和垂直位置百分比调节（0-100%）
- **透明度**: 0-100%透明度调节
- **实时预览**: 配置过程中实时预览水印效果

### 🔧 高级设置
- **线程数量**: 并行处理的工作线程数
- **自动保存设置**: 自动保存当前配置
- **自动滚动日志**: 处理过程中自动滚动日志显示

## 📈 处理统计

### 📊 实时统计信息
- **总文件数**: 扫描到的视频文件总数
- **已处理数**: 当前已完成处理的文件数
- **成功数**: 处理成功的文件数
- **失败数**: 处理失败的文件数
- **跳过竖屏数**: 因为是竖屏而跳过的文件数
- **跳过时长数**: 因为时长不符而跳过的文件数

### 📋 处理结果
- **处理进度**: 实时显示处理百分比
- **当前状态**: 显示当前处理的文件和状态
- **详细日志**: 完整的处理过程记录

## 🛠️ 技术实现

### 🏗️ 架构设计
```
VideoProcessorDialog (主对话框)
├── VideoProcessorWorker (工作线程)
├── VideoProcessorSignals (信号系统)
├── AIFilenameRewriter (AI改写器)
└── 共享数据管理 (线程安全)
```

### 📚 核心依赖
- **OpenCV (cv2)**: 视频处理和封面生成
- **PIL (Pillow)**: 图像处理和增强
- **PyQt5**: 用户界面和多线程管理
- **AI改写服务**: 智能文件名优化

### 🔒 线程安全
- 使用线程锁保护共享数据
- 信号槽机制实现线程间通信
- 线程池管理并发执行

## 📝 使用技巧

### 💡 最佳实践
1. **合理设置线程数**: 根据CPU核心数设置，建议8-16个线程
2. **预先整理文件**: 将待处理视频集中到一个目录
3. **充足磁盘空间**: 确保输出目录有足够的存储空间
4. **网络连接稳定**: AI改写功能需要稳定的网络连接

### ⚠️ 注意事项
1. **依赖库检查**: 确保OpenCV和PIL库已正确安装
2. **文件权限**: 确保对输入和输出目录有读写权限
3. **视频格式**: 支持主流视频格式，特殊格式可能需要转换
4. **处理时间**: 大量视频处理需要较长时间，请耐心等待

### 🔧 故障排除
- **OpenCV未安装**: 安装命令 `pip install opencv-python`
- **PIL未安装**: 安装命令 `pip install Pillow`
- **AI改写失败**: 检查网络连接和API配置
- **处理卡住**: 检查文件权限和磁盘空间

## 📄 配置文件

### 📋 设置保存
系统会自动保存配置到 `video_processor_settings.json` 文件，包含：
- 目录路径设置
- 处理选项配置
- AI改写参数
- 界面状态信息

### 🔄 配置恢复
重新打开工具时会自动加载上次的配置，无需重复设置。

---

*更多技术细节请参考源码文件：`app/dialogs/video_processor_dialog.py`*
