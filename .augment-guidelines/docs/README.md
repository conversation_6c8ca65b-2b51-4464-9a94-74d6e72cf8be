# 头条内容社交工具

基于PyQt5实现的头条内容管理工具，界面布局参考截图设计。

## 开发规范

本项目严格遵循 **MECE原则**（相互独立，完全穷尽）进行开发：

- 📋 [开发规范文档](DEVELOPMENT_STANDARDS.md) - 详细的MECE原则应用指导
- ✅ [MECE检查清单](docs/MECE_CHECKLIST.md) - 开发过程中的质量检查清单
- 💡 [MECE应用示例](docs/MECE_EXAMPLES.md) - 实际项目中的应用案例
- 🔍 [MECE快速参考](docs/MECE_QUICK_REFERENCE.md) - 快速查阅的参考卡片
- 📊 [MECE实施总结](MECE_IMPLEMENTATION_SUMMARY.md) - 当前项目状态分析和重构建议

### MECE原则要求
- **必须**在系统设计、功能分解、问题分析中应用MECE原则
- **必须**确保功能模块之间相互独立，避免重复和冲突
- **必须**确保功能覆盖完整，不遗漏任何重要场景
- **必须**在分类和分组时保持逻辑清晰和结构完整

## 功能特点

- 账号管理：添加、删除、编辑账号信息
- 状态监控：检测账号状态，自动执行任务
- 数据导出：支持将数据导出为Excel
- 系统日志：记录操作日志，方便排查问题
- 自定义设置：代理设置、通知设置等

## 安装与运行

### 环境要求
- Python 3.7或更高版本
- PyQt5 5.15.9

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行程序
```bash
python main.py
```

### 打包成EXE

使用以下命令打包成EXE可执行文件：

```bash
python build.py
```

打包完成后，可执行文件将生成在`dist/头条内容社交工具`目录下。

#### EXE打包注意事项

1. 确保所有资源文件路径正确
2. 在生产环境使用前，请充分测试打包后的应用
3. 打包需要包含的静态资源已在`build.py`中配置

## 开发流程

### 1. 设计阶段
- 使用MECE原则进行功能分解和模块设计
- 参考[开发规范文档](DEVELOPMENT_STANDARDS.md)进行架构设计
- 使用[MECE检查清单](docs/MECE_CHECKLIST.md)验证设计完整性

### 2. 开发阶段
- 严格按照模块职责进行开发，确保相互独立
- 每个功能模块必须覆盖完整的业务场景
- 遵循单一职责原则和接口隔离原则

### 3. 测试阶段
- 按照MECE原则设计测试用例
- 确保功能覆盖、场景覆盖、异常覆盖完整
- 进行模块独立性和集成完整性测试

### 4. 代码审查
- 使用MECE检查清单进行系统性审查
- 验证模块间是否相互独立
- 确认功能覆盖是否完整穷尽

### 5. MECE原则验证
项目提供了自动化的MECE原则验证工具：

```bash
# 运行MECE验证工具
python tools/mece_validator.py

# 或使用批处理文件（Windows）
验证MECE原则.bat
```

验证工具会检查：
- 模块独立性（是否存在功能重叠）
- 功能完整性（是否覆盖所有必要场景）
- 代码重复（是否违反相互独立原则）
- 异常处理（是否考虑完整的错误场景）

## 界面预览

- 账号管理：显示所有头条账号的状态和相关信息
- 系统日志：记录系统操作和状态变化
- 设置页面：配置系统运行参数和代理设置