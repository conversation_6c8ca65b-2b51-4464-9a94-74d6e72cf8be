# 防破解保护系统使用说明

## 概述

本防破解保护系统为头条自媒体自动存稿工具提供全面的安全保护，防止逆向工程、调试分析和破解攻击。

## 功能特性

### 🔍 检测功能

#### 调试工具检测
- ✅ **Cheat Engine (CE)** - 内存修改工具
- ✅ **OllyDbg** - 32位调试器
- ✅ **x64dbg/x32dbg** - 64位/32位调试器
- ✅ **IDA Pro** - 反汇编分析工具
- ✅ **WinDbg** - Windows调试器
- ✅ **Process Hacker** - 进程分析工具
- ✅ **Process Monitor** - 进程监控工具
- ✅ **API Monitor** - API调用监控
- ✅ **Wireshark** - 网络数据包分析
- ✅ **Fiddler** - HTTP代理调试工具
- ✅ **火绒剑** - 系统分析工具

#### 环境检测
- ✅ **虚拟机检测** - VMware, VirtualBox, QEMU等
- ✅ **沙箱检测** - 自动化分析环境
- ✅ **调试器附加检测** - 运行时调试器检测
- ✅ **系统盘检测** - 确保程序在系统盘运行
- ✅ **文件完整性检测** - 防止文件篡改

### 🛡️ 保护机制

#### 自动响应
- 🚨 **实时检测** - 持续监控威胁
- 📝 **自动黑名单** - 检测到威胁自动加入黑名单
- 🚪 **立即退出** - 检测到威胁立即终止程序
- 🔄 **后台监控** - 30秒间隔持续检测

#### VMP壳保护
- 🔐 **代码虚拟化** - 核心代码虚拟化保护
- 🛡️ **反调试** - 多层反调试机制
- 🚫 **反虚拟机** - 防止虚拟机环境运行
- 💾 **内存保护** - 防止内存转储
- 🔒 **完整性检查** - 文件完整性验证

## 快速开始

### 1. 基础集成

```python
from app.utils.kamidenglu import initialize_anti_crack_protection, KamiManager

# 在程序启动时初始化防破解保护
def main():
    # 初始化防破解保护
    success, results = initialize_anti_crack_protection()
    if not success:
        print("安全检测失败，程序退出")
        return
    
    # 正常的程序逻辑
    kami_manager = KamiManager()
    license_valid, info = kami_manager.check_license()
    
    if license_valid:
        print("程序启动成功")
    else:
        print("许可证验证失败")
```

### 2. 配置管理

```python
from app.utils.anti_crack_config import get_config

# 获取配置实例
config = get_config()

# 检查功能是否启用
if config.is_enabled('check_ce'):
    print("CE检测已启用")

# 启用/禁用功能
config.enable_feature('check_vm')
config.disable_feature('check_debugger')

# 获取危险进程列表
dangerous_processes = config.get_dangerous_processes()
```

### 3. 后台监控

```python
from app.utils.kamidenglu import start_background_protection

# 启动后台保护监控
start_background_protection()
print("后台监控已启动，每30秒检测一次")
```

## 配置说明

### 配置文件位置
- `anti_crack_config.json` - 主配置文件
- `blacklist.dat` - 黑名单文件

### 主要配置项

```json
{
  "check_ce": true,              // 检测Cheat Engine
  "check_ollydbg": true,         // 检测OllyDbg
  "check_vm": true,              // 检测虚拟机
  "auto_blacklist": true,        // 自动黑名单
  "exit_on_detect": true,        // 检测到后立即退出
  "background_monitor": true,    // 后台监控
  "monitor_interval": 30         // 监控间隔(秒)
}
```

## 部署指南

### 1. 开发环境
```bash
# 安装依赖
pip install psutil wmi pywin32

# 运行演示程序
python app/utils/anti_crack_demo.py
```

### 2. 生产环境打包

#### 使用VMP壳保护
1. **选择保护级别**: 强化型
2. **启用选项**:
   - ✅ 反调试
   - ✅ 反虚拟机
   - ✅ 反转储
   - ✅ 代码虚拟化
   - ✅ 导入表保护
   - ✅ 资源保护
   - ✅ 字符串加密
   - ✅ 控制流混淆

#### PyInstaller打包
```bash
# 基础打包
pyinstaller --onefile --windowed main.py

# 高级打包（推荐）
pyinstaller --onefile --windowed --add-data "app/utils;app/utils" --hidden-import psutil --hidden-import wmi main.py
```

### 3. 系统要求
- **操作系统**: Windows 7/8/10/11
- **运行位置**: 支持任意盘符运行（已取消C盘限制）
- **环境**: 物理机器（虚拟机检测可配置）
- **权限**: 普通用户权限即可

## 安全建议

### 1. 代码保护
- 使用VMP或其他专业壳保护工具
- 启用所有反调试和反分析选项
- 定期更新保护配置

### 2. 分发安全
- 通过安全渠道分发程序
- 使用数字签名验证文件完整性
- 定期检查黑名单和威胁情报

### 3. 监控告警
- 启用后台监控功能
- 定期检查检测日志
- 及时更新威胁特征库

## 故障排除

### 常见问题

#### Q: 程序在虚拟机中无法运行
A: 虚拟机检测可以通过配置文件禁用，设置 "check_vm": false 即可。

#### Q: 误报某些正常软件
A: 可以通过配置文件调整检测规则，或联系技术支持。

#### Q: 程序启动时立即退出
A: 检查是否有调试工具在运行，关闭所有可疑程序后重试。

### 日志分析
```
防破解检测失败:
  - 危险工具检测: 发现运行中的调试工具 ollydbg.exe
  - 虚拟机检测: 发现虚拟机进程 vmware.exe
  - 自动黑名单: 机器码 ABC123 已加入黑名单
```

## 技术支持

### 联系方式
- 技术文档: 查看源码注释
- 问题反馈: 通过项目Issue提交
- 紧急支持: 联系开发团队

### 更新日志
- v1.0.0: 初始版本，基础防破解功能
- v1.1.0: 增加配置管理和VMP支持
- v1.2.0: 优化检测算法，减少误报

---

**⚠️ 重要提醒**: 本防破解系统仅用于保护合法软件，请遵守相关法律法规。
