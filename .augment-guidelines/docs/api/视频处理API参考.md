# 🎬 视频处理API参考

## 📋 概述

本文档提供头条内容社交工具视频处理模块的完整API参考，包括类定义、方法签名、参数说明和使用示例。

## 🏗️ 核心API

### VideoProcessorDialog

#### 类定义
```python
class VideoProcessorDialog(QDialog):
    """视频高质量去重工具对话框
    
    主要功能：
    - 自动筛选横屏视频（去除竖屏和方形视频）
    - 生成高质量1920x1080封面图片
    - 智能清空输出目录，避免文件冲突
    - 多线程并行处理，提升处理效率
    """
```

#### 构造方法
```python
def __init__(self, parent=None):
    """初始化视频处理对话框
    
    Args:
        parent (QWidget, optional): 父窗口对象
    """
```

#### 主要方法

##### start_processing()
```python
def start_processing(self):
    """开始处理流程
    
    执行完整的三阶段处理：
    1. 文件扫描与筛选
    2. AI文件名改写（可选）
    3. 视频处理与封面生成
    
    Returns:
        None
    
    Raises:
        Exception: 处理过程中的各种异常
    """
```

##### scan_video_files()
```python
def scan_video_files(self, input_dir: str) -> List[str]:
    """扫描输入目录中的视频文件
    
    Args:
        input_dir (str): 输入目录路径
    
    Returns:
        List[str]: 视频文件路径列表
    
    支持的视频格式:
        .mp4, .avi, .mov, .mkv, .wmv, .flv, .webm, .m4v
    """
```

##### validate_settings()
```python
def validate_settings(self) -> bool:
    """验证设置参数
    
    Returns:
        bool: 设置是否有效
    
    验证项目:
        - 输入目录是否存在
        - 输出目录是否可写
        - 线程数是否合理
        - AI配置是否正确
    """
```

### VideoProcessorWorker

#### 类定义
```python
class VideoProcessorWorker(QRunnable):
    """视频处理工作线程
    
    负责实际的视频文件处理工作，包括：
    - 视频格式检测
    - 横屏筛选
    - 时长筛选
    - 封面生成
    - 文件复制
    """
```

#### 构造方法
```python
def __init__(self, input_dir: str, output_dir: str, cover_dir: str,
             thread_count: int, worker_id: int, shared_data: Dict[str, Any],
             duration_settings: Optional[Dict[str, Any]] = None, 
             ai_rewriter=None):
    """初始化工作线程
    
    Args:
        input_dir (str): 输入目录路径
        output_dir (str): 输出目录路径
        cover_dir (str): 封面目录路径
        thread_count (int): 总线程数
        worker_id (int): 当前线程ID
        shared_data (Dict[str, Any]): 共享数据字典
        duration_settings (Dict[str, Any], optional): 时长筛选设置
        ai_rewriter (AIFilenameRewriter, optional): AI改写器实例
    """
```

#### 主要方法

##### process_single_video()
```python
def process_single_video(self, video_path: str):
    """处理单个视频文件
    
    Args:
        video_path (str): 视频文件路径
    
    处理步骤:
        1. 检查横屏格式
        2. 验证时长筛选
        3. 生成封面图片
        4. 复制到输出目录
        5. 更新处理统计
    """
```

##### is_landscape_video()
```python
def is_landscape_video(self, video_path: str) -> bool:
    """检测是否为横屏视频
    
    Args:
        video_path (str): 视频文件路径
    
    Returns:
        bool: True表示横屏，False表示竖屏或方形
    
    检测逻辑:
        width > height 为横屏视频
    """
```

##### generate_cover()
```python
def generate_cover(self, video_path: str, cover_path: str) -> bool:
    """生成视频封面
    
    Args:
        video_path (str): 视频文件路径
        cover_path (str): 封面保存路径
    
    Returns:
        bool: 生成是否成功
    
    生成规格:
        - 尺寸: 1920x1080
        - 格式: JPG
        - 帧选择: 视频中间帧
    """
```

##### check_duration_filter()
```python
def check_duration_filter(self, video_path: str) -> bool:
    """检查视频时长筛选
    
    Args:
        video_path (str): 视频文件路径
    
    Returns:
        bool: 是否通过时长筛选
    
    筛选条件:
        - 最小时长限制
        - 最大时长限制
    """
```

### VideoProcessorSignals

#### 类定义
```python
class VideoProcessorSignals(QObject):
    """视频处理信号类
    
    提供线程间通信的信号机制
    """
```

#### 信号定义
```python
# 进度更新信号
progress_updated = pyqtSignal(int, str)
"""
Args:
    int: 进度百分比 (0-100)
    str: 状态消息
"""

# 日志消息信号
log_message = pyqtSignal(str, str)
"""
Args:
    str: 日志消息内容
    str: 日志级别 (INFO, WARNING, ERROR, SUCCESS)
"""

# 任务完成信号
task_completed = pyqtSignal(dict)
"""
Args:
    dict: 完成统计信息
    {
        'total': 总数,
        'success': 成功数,
        'failed': 失败数,
        'skipped_portrait': 跳过竖屏数,
        'skipped_duration': 跳过时长数
    }
"""

# 错误发生信号
error_occurred = pyqtSignal(str)
"""
Args:
    str: 错误消息
"""
```

## 🔧 工具函数API

### 文件操作函数

#### get_video_duration_minutes()
```python
def get_video_duration_minutes(self, video_path: str) -> float:
    """获取视频时长（分钟）
    
    Args:
        video_path (str): 视频文件路径
    
    Returns:
        float: 视频时长（分钟）
    
    Raises:
        Exception: 视频文件读取失败
    """
```

#### safe_copy_file()
```python
def safe_copy_file(self, src_path: str, dst_path: str) -> bool:
    """安全复制文件
    
    Args:
        src_path (str): 源文件路径
        dst_path (str): 目标文件路径
    
    Returns:
        bool: 复制是否成功
    
    特性:
        - 自动创建目标目录
        - 处理文件名冲突
        - 错误恢复机制
    """
```

### 配置管理API

#### load_settings()
```python
def load_settings(self):
    """加载配置设置
    
    从 video_processor_settings.json 文件加载配置
    
    配置项:
        - 目录路径设置
        - 处理选项配置
        - AI改写参数
        - 界面状态信息
    """
```

#### save_settings()
```python
def save_settings(self):
    """保存配置设置
    
    将当前配置保存到 video_processor_settings.json 文件
    
    自动保存时机:
        - 设置变更时
        - 对话框关闭时
        - 处理完成时
    """
```

## 🤖 AI改写API

### AIFilenameRewriter

#### 类定义
```python
class AIFilenameRewriter:
    """AI文件名改写器
    
    提供智能文件名优化功能
    """
```

#### 构造方法
```python
def __init__(self, agent_id: str, token: str):
    """初始化AI改写器
    
    Args:
        agent_id (str): AI智能体ID
        token (str): 访问令牌
    """
```

#### 主要方法

##### rewrite_filename()
```python
def rewrite_filename(self, original_filename: str) -> str:
    """改写单个文件名
    
    Args:
        original_filename (str): 原始文件名
    
    Returns:
        str: 优化后的文件名
    
    改写策略:
        - 内容特征提取
        - 关键词优化
        - 可读性增强
        - 长度控制
    """
```

##### batch_rewrite()
```python
def batch_rewrite(self, filenames: List[str]) -> Dict[str, str]:
    """批量改写文件名
    
    Args:
        filenames (List[str]): 原始文件名列表
    
    Returns:
        Dict[str, str]: 文件名映射字典 {原始名: 改写名}
    
    优化特性:
        - 批量处理提升效率
        - 并发请求管理
        - 错误重试机制
    """
```

## 📊 数据结构定义

### 共享数据结构
```python
SharedData = TypedDict('SharedData', {
    'video_queue': List[str],           # 待处理视频队列
    'total_count': int,                 # 总文件数
    'processed_count': int,             # 已处理数
    'success_count': int,               # 成功数
    'failed_count': int,                # 失败数
    'skipped_portrait_count': int,      # 跳过竖屏数
    'skipped_duration_count': int,      # 跳过时长数
    'lock': threading.Lock,             # 线程锁
    'filename_mapping': Dict[str, str]  # 文件名映射
})
```

### 时长设置结构
```python
DurationSettings = TypedDict('DurationSettings', {
    'enabled': bool,        # 是否启用时长筛选
    'min_duration': float,  # 最小时长（分钟）
    'max_duration': float   # 最大时长（分钟）
})
```

### 处理统计结构
```python
ProcessingStats = TypedDict('ProcessingStats', {
    'total': int,               # 总文件数
    'processed': int,           # 已处理数
    'success': int,             # 成功数
    'failed': int,              # 失败数
    'skipped_portrait': int,    # 跳过竖屏数
    'skipped_duration': int,    # 跳过时长数
    'start_time': float,        # 开始时间
    'end_time': float,          # 结束时间
    'duration': float           # 处理耗时
})
```

## 🔄 使用示例

### 基本使用
```python
# 创建视频处理对话框
dialog = VideoProcessorDialog(parent_window)

# 设置处理参数
dialog.input_dir_edit.setText("/path/to/input")
dialog.output_dir_edit.setText("/path/to/output")
dialog.cover_dir_edit.setText("/path/to/covers")

# 启用AI改写
dialog.ai_rewrite_checkbox.setChecked(True)

# 开始处理
dialog.start_processing()

# 显示对话框
dialog.exec_()
```

### 编程调用
```python
# 直接调用处理函数
def process_videos_programmatically():
    processor = VideoProcessorDialog()
    
    # 配置参数
    processor.shared_data['video_queue'] = video_files
    processor.shared_data['total_count'] = len(video_files)
    
    # 创建工作线程
    worker = VideoProcessorWorker(
        input_dir="/path/to/input",
        output_dir="/path/to/output", 
        cover_dir="/path/to/covers",
        thread_count=8,
        worker_id=0,
        shared_data=processor.shared_data
    )
    
    # 启动处理
    thread_pool = QThreadPool()
    thread_pool.start(worker)
```

---

*完整API实现请参考源码：`app/dialogs/video_processor_dialog.py`*
