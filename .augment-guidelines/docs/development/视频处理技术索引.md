# 🎬 视频处理技术索引

## 📋 模块概览

视频处理功能是头条内容社交工具的核心模块之一，提供完整的视频批量处理解决方案。本文档提供技术实现的详细索引和架构说明。

## 🏗️ 核心架构

### 📁 文件结构
```
app/
├── dialogs/
│   └── video_processor_dialog.py    # 主要视频处理对话框
├── utils/
│   ├── piliang_cunggao.py          # 批量存稿中的视频上传功能
│   └── piliang_cunggao_backup.py   # 视频上传备份实现
└── main_window.py                   # 主窗口中的视频处理入口

配置文件/
└── video_processor_settings.json   # 视频处理配置文件
```

### 🔧 核心类结构

#### 1. VideoProcessorDialog
**文件**: `app/dialogs/video_processor_dialog.py`
**功能**: 视频处理主对话框
```python
class VideoProcessorDialog(QDialog):
    """视频高质量去重工具对话框
    
    主要功能：
    - 自动筛选横屏视频（去除竖屏和方形视频）
    - 生成高质量1920x1080封面图片
    - 智能清空输出目录，避免文件冲突
    - 多线程并行处理，提升处理效率
    """
```

**关键方法**:
- `__init__()` - 初始化界面和配置
- `start_processing()` - 开始处理流程
- `scan_video_files()` - 扫描视频文件
- `start_batch_rewrite()` - 启动AI文件名改写
- `start_video_processing()` - 启动视频处理

#### 2. VideoProcessorWorker
**文件**: `app/dialogs/video_processor_dialog.py`
**功能**: 视频处理工作线程
```python
class VideoProcessorWorker(QRunnable):
    """视频处理工作线程"""
```

**关键方法**:
- `run()` - 线程主执行方法
- `process_single_video()` - 处理单个视频
- `is_landscape_video()` - 检测横屏视频
- `generate_cover()` - 生成视频封面
- `check_duration_filter()` - 检查时长筛选

#### 3. VideoProcessorSignals
**文件**: `app/dialogs/video_processor_dialog.py`
**功能**: 视频处理信号系统
```python
class VideoProcessorSignals(QObject):
    """视频处理信号类"""
    progress_updated = pyqtSignal(int, str)  # 进度值, 状态消息
    log_message = pyqtSignal(str, str)       # 消息, 级别
    task_completed = pyqtSignal(dict)        # 完成统计信息
    error_occurred = pyqtSignal(str)         # 错误消息
```

## 🔄 处理流程技术实现

### 第一阶段：文件扫描与筛选

#### 1. 视频文件扫描
```python
def scan_video_files(self, input_dir: str) -> List[str]:
    """扫描输入目录中的视频文件"""
    video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}
    # 递归扫描所有子目录
    for root, _, files in os.walk(input_dir):
        for file in files:
            if os.path.splitext(file.lower())[1] in video_extensions:
                video_files.append(os.path.join(root, file))
```

#### 2. 横屏视频检测
```python
def is_landscape_video(self, video_path: str) -> bool:
    """检测是否为横屏视频"""
    if not CV2_AVAILABLE:
        return True  # 如果OpenCV不可用，默认认为是横屏
    
    cap = cv2.VideoCapture(video_path)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    cap.release()
    
    return width > height  # 宽度大于高度为横屏
```

#### 3. 时长筛选
```python
def check_duration_filter(self, video_path: str) -> bool:
    """检查视频时长是否符合筛选条件"""
    if not self.duration_settings['enabled']:
        return True
    
    duration_minutes = self.get_video_duration_minutes(video_path)
    min_duration = self.duration_settings['min_duration']
    max_duration = self.duration_settings['max_duration']
    
    if min_duration > 0 and duration_minutes < min_duration:
        return False
    if max_duration > 0 and duration_minutes > max_duration:
        return False
    
    return True
```

### 第二阶段：AI文件名改写

#### 1. 批量改写启动
```python
def start_batch_rewrite(self, video_files, thread_count):
    """启动批量AI文件名改写"""
    if not self.ai_rewriter:
        self.add_log("AI改写器未初始化", "ERROR")
        return
    
    # 创建改写工作线程
    rewrite_worker = BatchFilenameRewriteWorker(
        video_files, self.ai_rewriter, thread_count
    )
    # 连接信号槽
    rewrite_worker.signals.progress_updated.connect(self.on_rewrite_progress)
    rewrite_worker.signals.completed.connect(self.on_rewrite_completed)
    
    self.thread_pool.start(rewrite_worker)
```

#### 2. AI改写器集成
```python
class AIFilenameRewriter:
    """AI文件名改写器"""
    def __init__(self, agent_id: str, token: str):
        self.agent_id = agent_id
        self.token = token
    
    def rewrite_filename(self, original_filename: str) -> str:
        """改写单个文件名"""
        # 调用AI服务API
        # 返回优化后的文件名
```

### 第三阶段：视频处理与封面生成

#### 1. 多线程处理架构
```python
def start_video_processing(self, filtered_video_files, filename_mapping, thread_count):
    """开始视频处理阶段"""
    # 设置线程池
    self.thread_pool.setMaxThreadCount(thread_count)
    
    # 分批创建工作线程
    batch_size = 2
    for batch_start in range(0, thread_count, batch_size):
        batch_end = min(batch_start + batch_size, thread_count)
        
        for i in range(batch_start, batch_end):
            worker = VideoProcessorWorker(
                input_dir, output_dir, cover_dir,
                thread_count, i, self.shared_data, duration_settings
            )
            self.thread_pool.start(worker)
```

#### 2. 封面生成技术
```python
def generate_cover(self, video_path: str, cover_path: str) -> bool:
    """生成视频封面"""
    if not CV2_AVAILABLE:
        return False
    
    cap = cv2.VideoCapture(video_path)
    
    # 获取视频中间帧
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    middle_frame = frame_count // 2
    cap.set(cv2.CAP_PROP_POS_FRAMES, middle_frame)
    
    ret, frame = cap.read()
    if ret:
        # 调整尺寸为1920x1080
        resized_frame = cv2.resize(frame, (1920, 1080))
        cv2.imwrite(cover_path, resized_frame)
        return True
    
    cap.release()
    return False
```

## 🔧 技术依赖

### 核心库依赖
```python
# 视频处理
import cv2  # OpenCV - 视频读取、帧提取、封面生成

# 图像处理
from PIL import Image, ImageEnhance, ImageDraw, ImageFont

# 界面框架
from PyQt5.QtCore import QObject, QRunnable, QThreadPool, pyqtSignal
from PyQt5.QtWidgets import QDialog, QMessageBox

# 系统库
import os, threading, time, json
```

### 依赖检查机制
```python
# OpenCV可用性检查
try:
    import cv2
    CV2_AVAILABLE = True
    info("OpenCV库已加载，支持视频处理功能")
except ImportError:
    CV2_AVAILABLE = False
    warning("OpenCV库未安装，视频处理功能将受限")

# PIL可用性检查
try:
    from PIL import Image, ImageEnhance, ImageDraw, ImageFont
    PIL_AVAILABLE = True
    info("PIL库已加载，支持图像处理功能")
except ImportError:
    PIL_AVAILABLE = False
    warning("PIL库未安装，图像处理功能将受限")
```

## 📊 数据结构

### 共享数据结构
```python
self.shared_data = {
    'video_queue': [],              # 待处理视频队列
    'total_count': 0,               # 总文件数
    'processed_count': 0,           # 已处理数
    'success_count': 0,             # 成功数
    'failed_count': 0,              # 失败数
    'skipped_portrait_count': 0,    # 跳过竖屏数
    'skipped_duration_count': 0,    # 跳过时长数
    'lock': threading.Lock(),       # 线程锁
    'filename_mapping': {}          # 文件名映射
}
```

### 配置数据结构
```json
{
  "input_dir": "输入目录路径",
  "output_dir": "输出目录路径", 
  "cover_dir": "封面目录路径",
  "thread_count": "线程数量",
  "overwrite_files": true,
  "generate_cover": true,
  "duration_filter_enabled": true,
  "min_duration": 0,
  "max_duration": 0,
  "ai_rewrite_enabled": true,
  "agent_id": "AI智能体ID",
  "token": "访问令牌"
}
```

## 🔄 信号槽机制

### 进度更新信号
```python
# 发送进度更新
self.signals.progress_updated.emit(progress_percentage, status_message)

# 接收进度更新
worker.signals.progress_updated.connect(self.on_progress_updated)
```

### 日志消息信号
```python
# 发送日志消息
self.signals.log_message.emit(message, level)  # level: INFO, WARNING, ERROR, SUCCESS

# 接收日志消息
worker.signals.log_message.connect(self.add_log)
```

### 任务完成信号
```python
# 发送完成统计
self.signals.task_completed.emit(statistics_dict)

# 接收完成通知
worker.signals.task_completed.connect(self.on_task_completed)
```

## 🛠️ 扩展接口

### 自定义处理器接口
```python
class CustomVideoProcessor:
    """自定义视频处理器接口"""
    
    def process_video(self, input_path: str, output_path: str) -> bool:
        """处理单个视频文件"""
        pass
    
    def generate_thumbnail(self, video_path: str, thumbnail_path: str) -> bool:
        """生成缩略图"""
        pass
```

### 插件扩展机制
```python
def register_video_processor(processor_class):
    """注册自定义视频处理器"""
    # 插件注册逻辑
    pass
```

---

*技术实现详情请参考源码：`app/dialogs/video_processor_dialog.py`*
