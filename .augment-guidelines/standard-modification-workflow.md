# Standard Modification Workflow

## Overview
Systematic 5-stage workflow ensuring high-quality code modifications following MECE principles and comprehensive validation.

## Stage 1: Code Modification Execution

### Objectives
- Perform precise code replacements using str-replace-editor
- Maintain code consistency and formatting
- Ensure no duplication or redundancy

### Process Steps
1. **Analysis Phase**
   ```bash
   # Use codebase-retrieval to understand context
   codebase-retrieval: "Analyze existing implementation of [feature]"
   ```

2. **Modification Phase**
   ```bash
   # Use str-replace-editor for precise replacements
   str-replace-editor:
     - command: str_replace
     - path: [target_file]
     - old_str: [exact_existing_code]
     - new_str: [improved_code]
     - old_str_start_line_number: [start_line]
     - old_str_end_line_number: [end_line]
   ```

3. **Validation Phase**
   - Verify replacement accuracy
   - Check code formatting consistency
   - Ensure no syntax errors introduced

### Success Criteria
- [ ] All replacements executed successfully
- [ ] No syntax errors introduced
- [ ] Code formatting maintained
- [ ] No duplicate code created

## Stage 2: Modification Content Summary

### Documentation Requirements
Document all changes with specific details:

#### File Modifications
- **Files Modified**: List all modified files with line ranges
- **Code Removed**: Specific code segments deleted
- **Code Added**: New code segments with explanations
- **Logic Simplified**: Complex logic improvements
- **Errors Fixed**: Specific error types and resolution methods

#### Change Categories
1. **Bug Fixes**: Error corrections and issue resolutions
2. **Feature Enhancements**: New functionality additions
3. **Code Refactoring**: Structure and organization improvements
4. **Performance Optimizations**: Efficiency improvements
5. **Documentation Updates**: Comment and docstring improvements

### Template Format
```markdown
## Modification Summary

### Files Modified
- `[file_path]` (lines [start]-[end]): [description]

### Changes Made
1. **Removed**: [specific_code_segment]
   - **Reason**: [explanation]
   - **Impact**: [effect_description]

2. **Added**: [new_code_segment]
   - **Purpose**: [functionality_description]
   - **Benefits**: [improvement_details]

3. **Modified**: [changed_code_segment]
   - **Before**: [original_implementation]
   - **After**: [new_implementation]
   - **Improvement**: [enhancement_description]
```

## Stage 3: Comparative Analysis

### Metrics Comparison
Analyze before/after differences:

#### Quantitative Metrics
- **Lines of Code**: Before vs After count
- **Function Count**: Added/removed/modified functions
- **Class Count**: Added/removed/modified classes
- **Complexity**: Cyclomatic complexity changes
- **Dependencies**: Import and module dependency changes

#### Qualitative Improvements
- **Maintainability**: Code readability and structure improvements
- **Performance**: Execution speed and resource usage improvements
- **Reliability**: Error handling and robustness improvements
- **Extensibility**: Future modification ease improvements

### Analysis Template
```markdown
## Comparative Analysis

### Quantitative Changes
- **Total Lines**: [before] → [after] ([change])
- **Functions**: [before] → [after] ([change])
- **Classes**: [before] → [after] ([change])
- **Complexity**: [before] → [after] ([improvement])

### Qualitative Improvements
- **Maintainability**: [specific_improvements]
- **Performance**: [performance_gains]
- **Reliability**: [robustness_improvements]
- **Extensibility**: [future_flexibility]
```

## Stage 4: Error Fix Verification

### Error Categories
Systematically verify resolution of:

#### Compilation Errors
- [ ] Syntax errors resolved
- [ ] Import dependency issues fixed
- [ ] Type annotation errors corrected
- [ ] Indentation and formatting issues resolved

#### Runtime Errors
- [ ] Exception handling improved
- [ ] Null pointer/attribute errors fixed
- [ ] Resource management issues resolved
- [ ] Thread safety problems addressed

#### Logic Errors
- [ ] Business logic defects corrected
- [ ] Data validation issues fixed
- [ ] State management problems resolved
- [ ] Algorithm implementation errors corrected

#### Performance Issues
- [ ] Memory leaks eliminated
- [ ] CPU usage optimized
- [ ] I/O operations improved
- [ ] Database query optimization

### Verification Process
1. **Static Analysis**: Code review and automated checks
2. **Unit Testing**: Individual component testing
3. **Integration Testing**: Module interaction testing
4. **System Testing**: End-to-end functionality testing

## Stage 5: Functional Testing Validation

### Testing Strategy
Comprehensive validation using launch-process tool:

#### Core Module Testing
```bash
# Test module imports
launch-process:
  command: python -c "import [module]; print('Import successful')"
  wait: true
  max_wait_seconds: 30
```

#### Functionality Testing
```bash
# Test specific functionality
launch-process:
  command: python [test_script].py
  wait: true
  max_wait_seconds: 60
```

#### Integration Testing
```bash
# Test system integration
launch-process:
  command: python -m pytest [test_directory] -v
  wait: true
  max_wait_seconds: 120
```

### Success Criteria
- [ ] All imports successful
- [ ] Core functionality working
- [ ] No regression in existing features
- [ ] Performance within acceptable limits
- [ ] Error handling working correctly

## Quality Gates

### Mandatory Checks
Before considering modification complete:

1. **Code Quality Gate**
   - [ ] No syntax errors
   - [ ] All imports resolve
   - [ ] Code follows project conventions
   - [ ] Documentation is complete

2. **Functional Quality Gate**
   - [ ] All tests pass
   - [ ] No regression detected
   - [ ] Performance acceptable
   - [ ] Error handling robust

3. **Integration Quality Gate**
   - [ ] Module interfaces work correctly
   - [ ] Signal/slot connections functional
   - [ ] Resource management proper
   - [ ] Thread safety maintained

## Rollback Procedures

### When to Rollback
- Critical errors introduced
- Performance significantly degraded
- Existing functionality broken
- Integration failures detected

### Rollback Process
1. **Immediate**: Revert using version control
2. **Analysis**: Identify root cause of failure
3. **Planning**: Develop alternative approach
4. **Re-implementation**: Apply lessons learned
5. **Re-testing**: Complete validation cycle
