#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单的Cookie加密功能测试
"""

import os
import sys
import json

def test_basic_encryption():
    """测试基础加密功能"""
    try:
        print("测试基础加密功能...")
        
        # 尝试导入cryptography
        try:
            from cryptography.fernet import Fernet
            print("✅ cryptography库导入成功")
        except ImportError:
            print("❌ cryptography库未安装，请运行: pip install cryptography")
            return False
        
        # 测试基础加密解密
        key = Fernet.generate_key()
        f = Fernet(key)
        
        test_data = "Hello, Cookie Encryption!"
        encrypted = f.encrypt(test_data.encode())
        decrypted = f.decrypt(encrypted).decode()
        
        if decrypted == test_data:
            print("✅ 基础加密解密测试通过")
            return True
        else:
            print("❌ 基础加密解密测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_cookie_encryption_module():
    """测试Cookie加密模块"""
    try:
        print("\n测试Cookie加密模块...")
        
        # 添加项目路径
        project_root = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, project_root)
        
        # 导入Cookie加密模块
        from app.utils.cookie_encryption import CookieEncryption
        print("✅ Cookie加密模块导入成功")
        
        # 创建加密器
        encryptor = CookieEncryption("zhengyang.0924")
        print("✅ 加密器创建成功")
        
        # 测试数据
        test_cookie = {
            "accountId": "test123",
            "cookies": {
                "sessionid": "test_session",
                "csrf_token": "test_csrf"
            }
        }
        
        # 测试加密
        encrypted_data = encryptor.encrypt_cookie_data(test_cookie)
        print(f"✅ 数据加密成功，长度: {len(encrypted_data)}")
        
        # 测试解密
        decrypted_data = encryptor.decrypt_cookie_data(encrypted_data)
        print("✅ 数据解密成功")
        
        # 验证数据一致性
        if decrypted_data == test_cookie:
            print("✅ 数据一致性验证通过")
            return True
        else:
            print("❌ 数据一致性验证失败")
            return False
            
    except Exception as e:
        print(f"❌ Cookie加密模块测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Cookie加密功能简单测试")
    print("=" * 50)
    
    # 测试基础加密
    basic_result = test_basic_encryption()
    
    if basic_result:
        # 测试Cookie加密模块
        module_result = test_cookie_encryption_module()
        
        if module_result:
            print("\n" + "=" * 50)
            print("🎉 所有测试通过！Cookie加密功能正常")
            print("=" * 50)
        else:
            print("\n❌ Cookie加密模块测试失败")
    else:
        print("\n❌ 基础加密测试失败")
