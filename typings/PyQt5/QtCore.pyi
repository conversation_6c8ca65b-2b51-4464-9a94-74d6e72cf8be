# PyQt5 类型存根文件 - 解决信号类型检查问题

from typing import Any, Callable, Optional, Union, overload
from typing_extensions import ParamSpec

P = ParamSpec('P')

class pyqtSignal:
    """PyQt5信号类型存根"""
    
    def __init__(self, *args: Any, **kwargs: Any) -> None: ...
    
    def emit(self, *args: Any) -> None:
        """发射信号"""
        ...
    
    def connect(self, slot: Callable[..., Any]) -> None:
        """连接信号到槽"""
        ...
    
    def disconnect(self, slot: Optional[Callable[..., Any]] = None) -> None:
        """断开信号连接"""
        ...

class pyqtBoundSignal:
    """PyQt5绑定信号类型存根"""
    
    def emit(self, *args: Any) -> None:
        """发射信号"""
        ...
    
    def connect(self, slot: Callable[..., Any]) -> None:
        """连接信号到槽"""
        ...
    
    def disconnect(self, slot: Optional[Callable[..., Any]] = None) -> None:
        """断开信号连接"""
        ...

class QObject:
    """QObject类型存根"""
    
    def __init__(self, parent: Optional['QObject'] = None) -> None: ...

class Qt:
    """Qt常量类型存根"""
    
    class AlignmentFlag:
        AlignCenter: int
        AlignLeft: int
        AlignRight: int
        AlignTop: int
        AlignBottom: int
    
    class CheckState:
        Checked: int
        Unchecked: int
        PartiallyChecked: int

class QTimer(QObject):
    """QTimer类型存根"""
    
    timeout: pyqtSignal
    
    def __init__(self, parent: Optional[QObject] = None) -> None: ...
    def start(self, msec: int) -> None: ...
    def stop(self) -> None: ...
    def singleShot(msec: int, slot: Callable[[], None]) -> None: ...

class QThread(QObject):
    """QThread类型存根"""
    
    started: pyqtSignal
    finished: pyqtSignal
    
    def __init__(self, parent: Optional[QObject] = None) -> None: ...
    def start(self) -> None: ...
    def quit(self) -> None: ...
    def wait(self) -> bool: ...

class QRunnable:
    """QRunnable类型存根"""
    
    def __init__(self) -> None: ...
    def run(self) -> None: ...

class QThreadPool:
    """QThreadPool类型存根"""
    
    @staticmethod
    def globalInstance() -> 'QThreadPool': ...
    def start(self, runnable: QRunnable) -> None: ...

# 其他常用类型
PYQT_SLOT = Union[Callable[..., Any], pyqtBoundSignal]
