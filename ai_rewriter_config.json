{"ai_service": "yuanqi", "services": {"yuanqi": {"name": "腾讯元器智能体", "url": "https://yuanqi.tencent.com/openapi/v1/agent/chat/completions", "api_key": "O4tOE7n389DRVpFtHfwUyoNwzPRvGJVY", "assistant_id": "WXR2oUKfqsAm", "user_id": "test_user_001", "timeout": 100, "max_retries": 3}, "openai": {"name": "OpenAI", "url": "https://api.openai.com/v1/chat/completions", "api_key": "", "model": "gpt-3.5-turbo", "timeout": 30, "max_retries": 3}, "local": {"name": "本地模型", "url": "http://localhost:11434/api/chat", "api_key": "", "model": "qwen2", "timeout": 60, "max_retries": 2}}, "rewrite_rules": {"enable_rewrite_style": false, "enable_length_limit": false, "max_length": 30, "remove_special_chars": false, "use_chinese": true, "style": "objective", "toutiao_compliance": true, "keywords_to_avoid": ["test", "temp", "copy", "新建", "未命名", "（1）", "（2）", "（3）"]}, "cache_settings": {"enabled": true, "max_age_days": 7, "max_entries": 1009}, "concurrent_settings": {"enabled": true, "max_workers": 3, "strategy": "随机选择"}, "agents": [{"assistant_id": "WXR2oUKfqsAm", "api_key": "O4tOE7n389DRVpFtHfwUyoNwzPRvGJVY", "weight": 10, "rate_limit": 50, "timeout": 60, "success_rate": 0.0, "total_requests": 0, "success_requests": 0}, {"assistant_id": "ndOwNp5mEiaG", "api_key": "qdGLwhdxO1VaP5cSxsBS9jOwvskpfOhv", "weight": 10, "rate_limit": 50, "timeout": 60, "success_rate": 0.0, "total_requests": 0, "success_requests": 0}, {"assistant_id": "lIxu7elyweLN", "api_key": "wVr1qGIgyZjpg2f4FgqhPsfYSN7BUWk3", "weight": 10, "rate_limit": 50, "timeout": 69, "success_rate": 0.0, "total_requests": 0, "success_requests": 0}], "debug_settings": {"enable_debug": false}}