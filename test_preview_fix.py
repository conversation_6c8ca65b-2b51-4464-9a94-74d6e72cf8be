#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试预览图片修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_preview_image_fix():
    """测试预览图片修复"""
    print("=" * 80)
    print("🖼️ 预览图片修复测试")
    print("=" * 80)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from app.dialogs.video_processor_dialog import WatermarkConfig, WatermarkConfigDialog
        
        app = QApplication(sys.argv)
        
        # 创建配置
        config = WatermarkConfig()
        config.enabled = True
        config.font_size = 60
        
        print("\n1. 创建水印配置对话框...")
        dialog = WatermarkConfigDialog(config)
        print("   ✅ 对话框创建成功")
        
        # 强制显示对话框以触发布局
        dialog.show()
        app.processEvents()
        
        # 检查预览容器和标签尺寸
        print("\n2. 检查尺寸设置...")
        
        if hasattr(dialog, 'preview_width') and hasattr(dialog, 'preview_height'):
            container_width = dialog.preview_width
            container_height = dialog.preview_height
            print(f"   预览容器尺寸: {container_width}x{container_height}")
            
            border_width = 3
            expected_inner_width = container_width - (border_width * 2)
            expected_inner_height = container_height - (border_width * 2)
            print(f"   期望内部尺寸: {expected_inner_width}x{expected_inner_height}")
            
            if hasattr(dialog, 'preview_label') and dialog.preview_label:
                label = dialog.preview_label
                if hasattr(label, 'preview_width') and hasattr(label, 'preview_height'):
                    actual_inner_width = label.preview_width
                    actual_inner_height = label.preview_height
                    print(f"   实际内部尺寸: {actual_inner_width}x{actual_inner_height}")
                    
                    if (actual_inner_width == expected_inner_width and 
                        actual_inner_height == expected_inner_height):
                        print("   ✅ 内部尺寸设置正确")
                    else:
                        print("   ❌ 内部尺寸设置不正确")
                else:
                    print("   ❌ 预览标签缺少尺寸属性")
            else:
                print("   ❌ 预览标签不存在")
        else:
            print("   ❌ 对话框缺少尺寸属性")
        
        # 测试预览更新
        print("\n3. 测试预览更新...")
        try:
            dialog.update_preview()
            print("   ✅ 预览更新成功")
            
            # 检查预览标签的背景图片
            if hasattr(dialog, 'preview_label') and dialog.preview_label:
                label = dialog.preview_label
                if hasattr(label, 'background_pixmap') and label.background_pixmap:
                    pixmap = label.background_pixmap
                    pixmap_width = pixmap.width()
                    pixmap_height = pixmap.height()
                    print(f"   背景图片尺寸: {pixmap_width}x{pixmap_height}")
                    
                    # 验证图片尺寸是否与内部尺寸匹配
                    if (pixmap_width == expected_inner_width and 
                        pixmap_height == expected_inner_height):
                        print("   ✅ 背景图片尺寸与内部尺寸匹配")
                    else:
                        print("   ❌ 背景图片尺寸与内部尺寸不匹配")
                        print(f"       期望: {expected_inner_width}x{expected_inner_height}")
                        print(f"       实际: {pixmap_width}x{pixmap_height}")
                else:
                    print("   ❌ 背景图片不存在")
            
        except Exception as e:
            print(f"   ❌ 预览更新失败: {e}")
        
        # 问题诊断
        print("\n4. 问题诊断...")
        print("   彩色条纹问题通常由以下原因造成:")
        print("   1. 图片数据尺寸与QImage尺寸不匹配")
        print("   2. 字节数据格式错误")
        print("   3. 内存对齐问题")
        
        print("\n   修复方案:")
        print("   ✅ 确保图片创建使用正确的内部尺寸")
        print("   ✅ 确保QImage使用图片的实际尺寸")
        print("   ✅ 确保预览标签尺寸与图片尺寸匹配")
        
        dialog.hide()
        
        print(f"\n✅ 预览图片修复测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    test_preview_image_fix()
