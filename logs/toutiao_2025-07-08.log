[2025-07-08 17:22:42,432] [INFO] OpenCV库已加载，支持视频处理功能
[2025-07-08 17:22:42,475] [INFO] PIL库已加载，支持图像处理功能
[2025-07-08 17:22:44,070] [INFO] 自动化配置加载成功
[2025-07-08 17:22:44,070] [INFO] 自动化任务调度器初始化完成
[2025-07-08 17:22:44,087] [INFO] 心跳状态指示器初始化完成
[2025-07-08 17:22:44,160] [INFO] 跳过示例数据加载，等待真实账号数据
[2025-07-08 17:22:44,165] [ERROR] 连接表格信号时出错: 'AccountTab' object has no attribute 'on_cell_changed'
[2025-07-08 17:22:45,181] [INFO] 账号加载模式: 延迟加载
[2025-07-08 17:22:45,182] [INFO] 数据目录功能已移除
[2025-07-08 17:22:45,182] [INFO] 开始同步加载Cookie文件，路径: E:/软件共享/头条/头条
[2025-07-08 17:22:45,183] [INFO] 路径 E:/软件共享/头条/头条 下有 0 个JSON文件和 370 个TXT文件
[2025-07-08 17:22:45,183] [INFO] 调用account_loader.load_accounts_sync方法前
[2025-07-08 17:22:45,186] [INFO] 启动快速启动模式 - 后台加载账号数据
[2025-07-08 17:22:45,285] [DEBUG] 批量数据处理开始时内存使用: 248.34 MB，数据量: 67
[2025-07-08 17:22:45,286] [DEBUG] 更新账号 m50 的表格数据，是否有数据: True
[2025-07-08 17:22:45,287] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,287] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,288] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,288] [DEBUG] 开始查找账号 m50 对应的行索引
[2025-07-08 17:22:45,288] [DEBUG] 未找到账号 m50 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,288] [DEBUG] 更新账号 m63 的表格数据，是否有数据: True
[2025-07-08 17:22:45,288] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,289] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,289] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,289] [DEBUG] 开始查找账号 m63 对应的行索引
[2025-07-08 17:22:45,289] [DEBUG] 未找到账号 m63 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,289] [DEBUG] 更新账号 m53 的表格数据，是否有数据: True
[2025-07-08 17:22:45,289] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,290] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,290] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,290] [DEBUG] 开始查找账号 m53 对应的行索引
[2025-07-08 17:22:45,290] [DEBUG] 未找到账号 m53 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,290] [DEBUG] 更新账号 m54 的表格数据，是否有数据: True
[2025-07-08 17:22:45,291] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,291] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,291] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,291] [DEBUG] 开始查找账号 m54 对应的行索引
[2025-07-08 17:22:45,291] [DEBUG] 未找到账号 m54 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,292] [DEBUG] 更新账号 m64 的表格数据，是否有数据: True
[2025-07-08 17:22:45,292] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,292] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,293] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,293] [DEBUG] 开始查找账号 m64 对应的行索引
[2025-07-08 17:22:45,293] [DEBUG] 未找到账号 m64 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,293] [DEBUG] 更新账号 m65 的表格数据，是否有数据: True
[2025-07-08 17:22:45,293] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,294] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,294] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,294] [DEBUG] 开始查找账号 m65 对应的行索引
[2025-07-08 17:22:45,294] [DEBUG] 未找到账号 m65 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,294] [DEBUG] 更新账号 m66 的表格数据，是否有数据: True
[2025-07-08 17:22:45,294] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,295] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,295] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,295] [DEBUG] 开始查找账号 m66 对应的行索引
[2025-07-08 17:22:45,295] [DEBUG] 未找到账号 m66 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,295] [DEBUG] 更新账号 m56 的表格数据，是否有数据: True
[2025-07-08 17:22:45,295] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,296] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,296] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,296] [DEBUG] 开始查找账号 m56 对应的行索引
[2025-07-08 17:22:45,297] [DEBUG] 未找到账号 m56 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,297] [DEBUG] 更新账号 m59 的表格数据，是否有数据: True
[2025-07-08 17:22:45,297] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,297] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,297] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,297] [DEBUG] 开始查找账号 m59 对应的行索引
[2025-07-08 17:22:45,298] [DEBUG] 未找到账号 m59 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,298] [DEBUG] 更新账号 m6 的表格数据，是否有数据: True
[2025-07-08 17:22:45,298] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,298] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,298] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,298] [DEBUG] 开始查找账号 m6 对应的行索引
[2025-07-08 17:22:45,299] [DEBUG] 未找到账号 m6 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,299] [DEBUG] 更新账号 m60 的表格数据，是否有数据: True
[2025-07-08 17:22:45,299] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,299] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,299] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,300] [DEBUG] 开始查找账号 m60 对应的行索引
[2025-07-08 17:22:45,300] [DEBUG] 未找到账号 m60 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,300] [DEBUG] 更新账号 m62 的表格数据，是否有数据: True
[2025-07-08 17:22:45,301] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,301] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,301] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,301] [DEBUG] 开始查找账号 m62 对应的行索引
[2025-07-08 17:22:45,301] [DEBUG] 未找到账号 m62 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,302] [DEBUG] 更新账号 m67 的表格数据，是否有数据: True
[2025-07-08 17:22:45,302] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,302] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,302] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,302] [DEBUG] 开始查找账号 m67 对应的行索引
[2025-07-08 17:22:45,303] [DEBUG] 未找到账号 m67 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,303] [DEBUG] 更新账号 m7 的表格数据，是否有数据: True
[2025-07-08 17:22:45,303] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,303] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,303] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,304] [DEBUG] 开始查找账号 m7 对应的行索引
[2025-07-08 17:22:45,304] [DEBUG] 未找到账号 m7 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,305] [DEBUG] 更新账号 m9 的表格数据，是否有数据: True
[2025-07-08 17:22:45,305] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,305] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,305] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,305] [DEBUG] 开始查找账号 m9 对应的行索引
[2025-07-08 17:22:45,306] [DEBUG] 未找到账号 m9 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,306] [DEBUG] 更新账号 m8 的表格数据，是否有数据: True
[2025-07-08 17:22:45,306] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,306] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,306] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,306] [DEBUG] 开始查找账号 m8 对应的行索引
[2025-07-08 17:22:45,307] [DEBUG] 未找到账号 m8 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,307] [DEBUG] 更新账号 m10 的表格数据，是否有数据: True
[2025-07-08 17:22:45,307] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,307] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,307] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,308] [DEBUG] 开始查找账号 m10 对应的行索引
[2025-07-08 17:22:45,308] [DEBUG] 未找到账号 m10 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,308] [DEBUG] 更新账号 m11 的表格数据，是否有数据: True
[2025-07-08 17:22:45,309] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,309] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,309] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,309] [DEBUG] 开始查找账号 m11 对应的行索引
[2025-07-08 17:22:45,310] [DEBUG] 未找到账号 m11 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,310] [DEBUG] 更新账号 m1 的表格数据，是否有数据: True
[2025-07-08 17:22:45,310] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,310] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,311] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,311] [DEBUG] 开始查找账号 m1 对应的行索引
[2025-07-08 17:22:45,311] [DEBUG] 未找到账号 m1 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,312] [DEBUG] 更新账号 m12 的表格数据，是否有数据: True
[2025-07-08 17:22:45,312] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,312] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,312] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,312] [DEBUG] 开始查找账号 m12 对应的行索引
[2025-07-08 17:22:45,313] [DEBUG] 未找到账号 m12 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,313] [DEBUG] 更新账号 m13 的表格数据，是否有数据: True
[2025-07-08 17:22:45,313] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,314] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,314] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,314] [DEBUG] 开始查找账号 m13 对应的行索引
[2025-07-08 17:22:45,314] [DEBUG] 未找到账号 m13 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,314] [DEBUG] 更新账号 m14 的表格数据，是否有数据: True
[2025-07-08 17:22:45,314] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,315] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,315] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,315] [DEBUG] 开始查找账号 m14 对应的行索引
[2025-07-08 17:22:45,315] [DEBUG] 未找到账号 m14 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,315] [DEBUG] 更新账号 m16 的表格数据，是否有数据: True
[2025-07-08 17:22:45,316] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,316] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,316] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,316] [DEBUG] 开始查找账号 m16 对应的行索引
[2025-07-08 17:22:45,316] [DEBUG] 未找到账号 m16 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,317] [DEBUG] 更新账号 m15 的表格数据，是否有数据: True
[2025-07-08 17:22:45,317] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,318] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,318] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,318] [DEBUG] 开始查找账号 m15 对应的行索引
[2025-07-08 17:22:45,318] [DEBUG] 未找到账号 m15 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,318] [DEBUG] 更新账号 m17 的表格数据，是否有数据: True
[2025-07-08 17:22:45,318] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,319] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,319] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,319] [DEBUG] 开始查找账号 m17 对应的行索引
[2025-07-08 17:22:45,319] [DEBUG] 未找到账号 m17 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,319] [DEBUG] 更新账号 m18 的表格数据，是否有数据: True
[2025-07-08 17:22:45,320] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,320] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,320] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,320] [DEBUG] 开始查找账号 m18 对应的行索引
[2025-07-08 17:22:45,320] [DEBUG] 未找到账号 m18 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,320] [DEBUG] 更新账号 m19 的表格数据，是否有数据: True
[2025-07-08 17:22:45,321] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,321] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,321] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,321] [DEBUG] 开始查找账号 m19 对应的行索引
[2025-07-08 17:22:45,321] [DEBUG] 未找到账号 m19 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,321] [DEBUG] 更新账号 m2 的表格数据，是否有数据: True
[2025-07-08 17:22:45,321] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,321] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,323] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,323] [DEBUG] 开始查找账号 m2 对应的行索引
[2025-07-08 17:22:45,323] [DEBUG] 未找到账号 m2 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,323] [DEBUG] 更新账号 m20 的表格数据，是否有数据: True
[2025-07-08 17:22:45,323] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,323] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,324] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,324] [DEBUG] 开始查找账号 m20 对应的行索引
[2025-07-08 17:22:45,324] [DEBUG] 未找到账号 m20 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,324] [DEBUG] 更新账号 m21 的表格数据，是否有数据: True
[2025-07-08 17:22:45,324] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,324] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,324] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,325] [DEBUG] 开始查找账号 m21 对应的行索引
[2025-07-08 17:22:45,325] [DEBUG] 未找到账号 m21 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,325] [DEBUG] 更新账号 m22 的表格数据，是否有数据: True
[2025-07-08 17:22:45,325] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,325] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,326] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,326] [DEBUG] 开始查找账号 m22 对应的行索引
[2025-07-08 17:22:45,326] [DEBUG] 未找到账号 m22 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,327] [DEBUG] 更新账号 m23 的表格数据，是否有数据: True
[2025-07-08 17:22:45,327] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,327] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,327] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,327] [DEBUG] 开始查找账号 m23 对应的行索引
[2025-07-08 17:22:45,327] [DEBUG] 未找到账号 m23 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,328] [DEBUG] 更新账号 m24 的表格数据，是否有数据: True
[2025-07-08 17:22:45,328] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,328] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,328] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,328] [DEBUG] 开始查找账号 m24 对应的行索引
[2025-07-08 17:22:45,328] [DEBUG] 未找到账号 m24 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,329] [DEBUG] 更新账号 m25 的表格数据，是否有数据: True
[2025-07-08 17:22:45,329] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,329] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,329] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,329] [DEBUG] 开始查找账号 m25 对应的行索引
[2025-07-08 17:22:45,330] [DEBUG] 未找到账号 m25 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,331] [DEBUG] 更新账号 m26 的表格数据，是否有数据: True
[2025-07-08 17:22:45,331] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,331] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,331] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,331] [DEBUG] 开始查找账号 m26 对应的行索引
[2025-07-08 17:22:45,332] [DEBUG] 未找到账号 m26 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,332] [DEBUG] 更新账号 m27 的表格数据，是否有数据: True
[2025-07-08 17:22:45,332] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,332] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,332] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,332] [DEBUG] 开始查找账号 m27 对应的行索引
[2025-07-08 17:22:45,333] [DEBUG] 未找到账号 m27 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,333] [DEBUG] 更新账号 m28 的表格数据，是否有数据: True
[2025-07-08 17:22:45,333] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,333] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,333] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,334] [DEBUG] 开始查找账号 m28 对应的行索引
[2025-07-08 17:22:45,334] [DEBUG] 未找到账号 m28 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,334] [DEBUG] 更新账号 m30 的表格数据，是否有数据: True
[2025-07-08 17:22:45,335] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,335] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,335] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,335] [DEBUG] 开始查找账号 m30 对应的行索引
[2025-07-08 17:22:45,335] [DEBUG] 未找到账号 m30 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,336] [DEBUG] 更新账号 m3 的表格数据，是否有数据: True
[2025-07-08 17:22:45,336] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,336] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,336] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,336] [DEBUG] 开始查找账号 m3 对应的行索引
[2025-07-08 17:22:45,336] [DEBUG] 未找到账号 m3 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,337] [DEBUG] 更新账号 m29 的表格数据，是否有数据: True
[2025-07-08 17:22:45,337] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,337] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,337] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,337] [DEBUG] 开始查找账号 m29 对应的行索引
[2025-07-08 17:22:45,337] [DEBUG] 未找到账号 m29 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,338] [DEBUG] 更新账号 m31 的表格数据，是否有数据: True
[2025-07-08 17:22:45,338] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,338] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,338] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,338] [DEBUG] 开始查找账号 m31 对应的行索引
[2025-07-08 17:22:45,339] [DEBUG] 未找到账号 m31 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,339] [DEBUG] 更新账号 m32 的表格数据，是否有数据: True
[2025-07-08 17:22:45,339] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,340] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,340] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,340] [DEBUG] 开始查找账号 m32 对应的行索引
[2025-07-08 17:22:45,340] [DEBUG] 未找到账号 m32 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,340] [DEBUG] 更新账号 m33 的表格数据，是否有数据: True
[2025-07-08 17:22:45,340] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,341] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,341] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,341] [DEBUG] 开始查找账号 m33 对应的行索引
[2025-07-08 17:22:45,341] [DEBUG] 未找到账号 m33 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,341] [DEBUG] 更新账号 m34 的表格数据，是否有数据: True
[2025-07-08 17:22:45,341] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,342] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,342] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,342] [DEBUG] 开始查找账号 m34 对应的行索引
[2025-07-08 17:22:45,342] [DEBUG] 未找到账号 m34 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,342] [DEBUG] 更新账号 m35 的表格数据，是否有数据: True
[2025-07-08 17:22:45,342] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,343] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,343] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,343] [DEBUG] 开始查找账号 m35 对应的行索引
[2025-07-08 17:22:45,343] [DEBUG] 未找到账号 m35 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,343] [DEBUG] 更新账号 m36 的表格数据，是否有数据: True
[2025-07-08 17:22:45,344] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,344] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,344] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,345] [DEBUG] 开始查找账号 m36 对应的行索引
[2025-07-08 17:22:45,345] [DEBUG] 未找到账号 m36 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,345] [DEBUG] 更新账号 m37 的表格数据，是否有数据: True
[2025-07-08 17:22:45,345] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,345] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,346] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,346] [DEBUG] 开始查找账号 m37 对应的行索引
[2025-07-08 17:22:45,346] [DEBUG] 未找到账号 m37 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,346] [DEBUG] 更新账号 m38 的表格数据，是否有数据: True
[2025-07-08 17:22:45,346] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,346] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,347] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,347] [DEBUG] 开始查找账号 m38 对应的行索引
[2025-07-08 17:22:45,348] [DEBUG] 未找到账号 m38 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,348] [DEBUG] 更新账号 m39 的表格数据，是否有数据: True
[2025-07-08 17:22:45,348] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,348] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,348] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,349] [DEBUG] 开始查找账号 m39 对应的行索引
[2025-07-08 17:22:45,349] [DEBUG] 未找到账号 m39 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,349] [DEBUG] 更新账号 m4 的表格数据，是否有数据: True
[2025-07-08 17:22:45,349] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,349] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,350] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,350] [DEBUG] 开始查找账号 m4 对应的行索引
[2025-07-08 17:22:45,350] [DEBUG] 未找到账号 m4 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,350] [DEBUG] 更新账号 m40 的表格数据，是否有数据: True
[2025-07-08 17:22:45,350] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,351] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,351] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,351] [DEBUG] 开始查找账号 m40 对应的行索引
[2025-07-08 17:22:45,352] [DEBUG] 未找到账号 m40 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,352] [DEBUG] 更新账号 m41 的表格数据，是否有数据: True
[2025-07-08 17:22:45,352] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,352] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,352] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,352] [DEBUG] 开始查找账号 m41 对应的行索引
[2025-07-08 17:22:45,353] [DEBUG] 未找到账号 m41 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,353] [DEBUG] 更新账号 m42 的表格数据，是否有数据: True
[2025-07-08 17:22:45,353] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,353] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,353] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,353] [DEBUG] 开始查找账号 m42 对应的行索引
[2025-07-08 17:22:45,354] [DEBUG] 未找到账号 m42 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,354] [DEBUG] 更新账号 m43 的表格数据，是否有数据: True
[2025-07-08 17:22:45,354] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,354] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,354] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,354] [DEBUG] 开始查找账号 m43 对应的行索引
[2025-07-08 17:22:45,355] [DEBUG] 未找到账号 m43 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,355] [DEBUG] 更新账号 m44 的表格数据，是否有数据: True
[2025-07-08 17:22:45,355] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,355] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,355] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,355] [DEBUG] 开始查找账号 m44 对应的行索引
[2025-07-08 17:22:45,356] [DEBUG] 未找到账号 m44 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,356] [DEBUG] 更新账号 m45 的表格数据，是否有数据: True
[2025-07-08 17:22:45,356] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,356] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,356] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,357] [DEBUG] 开始查找账号 m45 对应的行索引
[2025-07-08 17:22:45,357] [DEBUG] 未找到账号 m45 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,357] [DEBUG] 更新账号 m46 的表格数据，是否有数据: True
[2025-07-08 17:22:45,357] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,358] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,358] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,358] [DEBUG] 开始查找账号 m46 对应的行索引
[2025-07-08 17:22:45,358] [DEBUG] 未找到账号 m46 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,358] [DEBUG] 更新账号 m47 的表格数据，是否有数据: True
[2025-07-08 17:22:45,358] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,359] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,359] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,359] [DEBUG] 开始查找账号 m47 对应的行索引
[2025-07-08 17:22:45,359] [DEBUG] 未找到账号 m47 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,359] [DEBUG] 更新账号 m48 的表格数据，是否有数据: True
[2025-07-08 17:22:45,360] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,360] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,360] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,360] [DEBUG] 开始查找账号 m48 对应的行索引
[2025-07-08 17:22:45,361] [DEBUG] 未找到账号 m48 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,361] [DEBUG] 更新账号 m49 的表格数据，是否有数据: True
[2025-07-08 17:22:45,361] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,362] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,362] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,362] [DEBUG] 开始查找账号 m49 对应的行索引
[2025-07-08 17:22:45,362] [DEBUG] 未找到账号 m49 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,362] [DEBUG] 更新账号 m5 的表格数据，是否有数据: True
[2025-07-08 17:22:45,362] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,363] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,363] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,363] [DEBUG] 开始查找账号 m5 对应的行索引
[2025-07-08 17:22:45,364] [DEBUG] 未找到账号 m5 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,364] [DEBUG] 更新账号 m51 的表格数据，是否有数据: True
[2025-07-08 17:22:45,364] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,364] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,364] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,364] [DEBUG] 开始查找账号 m51 对应的行索引
[2025-07-08 17:22:45,364] [DEBUG] 未找到账号 m51 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,364] [DEBUG] 更新账号 m52 的表格数据，是否有数据: True
[2025-07-08 17:22:45,365] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,365] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,366] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,366] [DEBUG] 开始查找账号 m52 对应的行索引
[2025-07-08 17:22:45,366] [DEBUG] 未找到账号 m52 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,366] [DEBUG] 更新账号 m55 的表格数据，是否有数据: True
[2025-07-08 17:22:45,366] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,367] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,367] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,367] [DEBUG] 开始查找账号 m55 对应的行索引
[2025-07-08 17:22:45,367] [DEBUG] 未找到账号 m55 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,367] [DEBUG] 更新账号 m57 的表格数据，是否有数据: True
[2025-07-08 17:22:45,368] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,368] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,368] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,368] [DEBUG] 开始查找账号 m57 对应的行索引
[2025-07-08 17:22:45,368] [DEBUG] 未找到账号 m57 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,369] [DEBUG] 更新账号 m58 的表格数据，是否有数据: True
[2025-07-08 17:22:45,369] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,369] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,370] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,370] [DEBUG] 开始查找账号 m58 对应的行索引
[2025-07-08 17:22:45,370] [DEBUG] 未找到账号 m58 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,370] [DEBUG] 更新账号 m61 的表格数据，是否有数据: True
[2025-07-08 17:22:45,370] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:22:45,370] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:22:45,371] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:22:45,371] [DEBUG] 开始查找账号 m61 对应的行索引
[2025-07-08 17:22:45,371] [DEBUG] 未找到账号 m61 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:22:45,410] [INFO] 用户取消后台加载
[2025-07-08 17:22:45,410] [INFO] 后台加载完成: 账号数据加载完成
[2025-07-08 17:22:45,614] [DEBUG] 批量数据处理完成后内存使用: 249.02 MB
[2025-07-08 17:22:45,616] [ERROR] 更新加载进度时出错: 'NoneType' object has no attribute 'setLabelText'
[2025-07-08 17:22:46,649] [INFO] account_loader.load_accounts_sync方法返回: success=True, accounts数量=370, error_msg=
[2025-07-08 17:22:46,649] [INFO] 同步加载账号数据完成，结果: True, 账号数量: 370, 错误信息: 
[2025-07-08 17:22:46,649] [INFO] 账号数据加载成功，共 370 个账号
[2025-07-08 17:22:46,650] [INFO] 账号加载完成回调开始时内存使用: 258.87 MB
[2025-07-08 17:22:46,650] [INFO] 已创建账号列表的浅拷贝，大小: 370
[2025-07-08 17:22:46,650] [INFO] 成功加载 370 个账号文件，延迟加载模式: True
[2025-07-08 17:22:46,677] [INFO] 账号列表大小: 370
[2025-07-08 17:22:46,677] [INFO] 第一个账号信息: {'file_path': 'E:/软件共享/头条/头条\\***********.txt', 'account_id': '***********', 'data': {'accountId': '***********', 'remark': '***********', 'cookies': {'xigua_csrf_token': 'Rh9tB8w72kFdjHWXzmwLx4FN', 'is_staff_user': 'false', 'sessionid_ss': '68c4cc3f1c5dedd10b37702f8cfc5581', 'uid_tt_ss': '4138e04b7027f10e81ee57334a3871f1', 'store-region': 'cn-gd', 'uid_tt': '4138e04b7027f10e81ee57334a3871f1', 'passport_auth_status': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'passport_mfa_token': 'CjfDS1ognyyaR5OXvuo1ZhOlSC8orpJykXqU3XrL0%2F4LK3qwDRucEFU8wl5EeoQDFgQXMDSnVv64GkoKPIA87d76RVvezzRe1eiXyd81F%2FQYdzX0iWRhqgsPoaSXZlpDutIfPN2NISNG6lXHe%2BhFo3Alo9axe2OZMhDY3OsNGPax0WwgAiIBA6K42yM%3D', 'passport_auth_status_ss': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'sid_tt': '68c4cc3f1c5dedd10b37702f8cfc5581', 'odin_tt': 'f681b2fee0f84dd926c09d58e132e2328c436e9fcf004f758697a032da7adc66d0d445a80508f8d675d1357603c3efe11f5237f59ac2ca2893e80a3e5da52dcf', 'sessionid': '68c4cc3f1c5dedd10b37702f8cfc5581', 'gfgarrcache_8005812e': '1', 'ssid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'toutiao_sso_user_ss': 'c2032ee116a4c329079d18ddc99f62c6', 'gfgarrcache_6a2703f1': '1', 'sid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'store-region-src': 'uid', 'sid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'toutiao_sso_user': 'c2032ee116a4c329079d18ddc99f62c6', 'sso_uid_tt_ss': '707d66e7d745b6ca510877ff49d6deca', 'xg_p_tos_token': '79aa2d6e8307f3027308bb8063808201', 'sso_uid_tt': '707d66e7d745b6ca510877ff49d6deca', 'sso_auth_status_ss': '99d6be0fe40fbeccd26dbd011f7e4345', 'ssid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'sid_guard': '68c4cc3f1c5dedd10b37702f8cfc5581%7C1741687410%7C5184002%7CSat%2C+10-May-2025+10%3A03%3A32+GMT', 'sso_auth_status': '99d6be0fe40fbeccd26dbd011f7e4345', 'passport_csrf_token': '3d631c058164d798d48475833f13446e', 'n_mh': '30A9sphpGJEPcXGYEGo8hiNGyEbb5VtQ_Ov5DGw0tbA', 'ttwid': '1%7C0FfHeK6FyRv027NKpnZinttVopHTQty8nSXP9P7wnNw%7C1741687388%7C33b1d18a6f3d000bd20fd4a1d0f5d6b37682c37752a91a6e1265423a4b1e9968', 'gfkadpd': '1231,25897', 'd_ticket': '0da2a81adc5bb557ff718c555c0a4801d76e7', 'passport_csrf_token_default': '3d631c058164d798d48475833f13446e', 'csrf_session_id': 'e1a99642f2b9648ef30c79ef589a852a', 's_v_web_id': 'verify_m84bp59m_dED7JGij_AkV2_4J3T_B268_83sqBwAURoyE'}}, 'file_type': '.txt'}
[2025-07-08 17:22:46,678] [INFO] 使用延迟加载模式，准备更新表格基本信息
[2025-07-08 17:22:46,680] [DEBUG] 已导入高级内存管理器
[2025-07-08 17:22:46,689] [INFO] 更新表格开始时内存使用: 258.93MB, 可用: 1550.06MB/16236.06MB
[2025-07-08 17:22:46,718] [INFO] 准备设置表格行数: 370
[2025-07-08 17:22:46,718] [INFO] 已设置表格行数: 370，当前表格行数: 370
[2025-07-08 17:22:46,718] [DEBUG] 处理账号批次: 1-50/370
[2025-07-08 17:22:46,992] [DEBUG] 处理账号批次: 51-100/370
[2025-07-08 17:22:47,264] [DEBUG] 处理账号批次: 101-150/370
[2025-07-08 17:22:47,539] [DEBUG] 处理账号批次: 151-200/370
[2025-07-08 17:22:47,815] [DEBUG] 处理账号批次: 201-250/370
[2025-07-08 17:22:48,105] [DEBUG] 处理账号批次: 251-300/370
[2025-07-08 17:22:48,394] [DEBUG] 处理账号批次: 301-350/370
[2025-07-08 17:22:48,670] [DEBUG] 处理账号批次: 351-370/370
[2025-07-08 17:22:48,877] [INFO] 表格更新完成，耗时: 2.19秒，内存使用: 378.37MB
[2025-07-08 17:22:48,878] [INFO] 使用延迟加载模式，已更新表格基本信息
[2025-07-08 17:22:48,878] [INFO] 延迟加载模式下，表格行数: 370
[2025-07-08 17:22:48,929] [INFO] 不设置账号已加载标志，允许重复加载
[2025-07-08 17:22:48,930] [INFO] 开始计算收益统计，表格行数: 370, 列数: 19
[2025-07-08 17:22:48,932] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-08 17:22:48,932] [INFO] 七天总收益: ¥0.00
[2025-07-08 17:22:48,933] [INFO] 昨日总收益: ¥0.00
[2025-07-08 17:22:48,933] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-08 17:22:48,936] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-08 17:22:53,024] [INFO] 开始计算收益统计，表格行数: 370, 列数: 19
[2025-07-08 17:22:53,026] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-08 17:22:53,026] [INFO] 七天总收益: ¥0.00
[2025-07-08 17:22:53,027] [INFO] 昨日总收益: ¥0.00
[2025-07-08 17:22:53,027] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-08 17:22:53,029] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-08 17:23:30,000] [INFO] OpenCV库已加载，支持视频处理功能
[2025-07-08 17:23:30,043] [INFO] PIL库已加载，支持图像处理功能
[2025-07-08 17:23:31,498] [INFO] 自动化配置加载成功
[2025-07-08 17:23:31,498] [INFO] 自动化任务调度器初始化完成
[2025-07-08 17:23:31,506] [INFO] 心跳状态指示器初始化完成
[2025-07-08 17:23:31,574] [INFO] 跳过示例数据加载，等待真实账号数据
[2025-07-08 17:23:31,582] [ERROR] 连接表格信号时出错: 'AccountTab' object has no attribute 'on_cell_changed'
[2025-07-08 17:23:32,574] [INFO] 账号加载模式: 延迟加载
[2025-07-08 17:23:32,575] [INFO] 数据目录功能已移除
[2025-07-08 17:23:32,575] [INFO] 开始同步加载Cookie文件，路径: E:/软件共享/头条/头条
[2025-07-08 17:23:32,576] [INFO] 路径 E:/软件共享/头条/头条 下有 0 个JSON文件和 370 个TXT文件
[2025-07-08 17:23:32,576] [INFO] 调用account_loader.load_accounts_sync方法前
[2025-07-08 17:23:32,620] [INFO] 启动快速启动模式 - 后台加载账号数据
[2025-07-08 17:23:32,665] [DEBUG] 批量数据处理开始时内存使用: 249.70 MB，数据量: 67
[2025-07-08 17:23:32,666] [DEBUG] 更新账号 m50 的表格数据，是否有数据: True
[2025-07-08 17:23:32,666] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,666] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,666] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,667] [DEBUG] 开始查找账号 m50 对应的行索引
[2025-07-08 17:23:32,667] [DEBUG] 未找到账号 m50 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,667] [DEBUG] 更新账号 m63 的表格数据，是否有数据: True
[2025-07-08 17:23:32,667] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,668] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,668] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,668] [DEBUG] 开始查找账号 m63 对应的行索引
[2025-07-08 17:23:32,668] [DEBUG] 未找到账号 m63 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,668] [DEBUG] 更新账号 m53 的表格数据，是否有数据: True
[2025-07-08 17:23:32,668] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,669] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,669] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,669] [DEBUG] 开始查找账号 m53 对应的行索引
[2025-07-08 17:23:32,669] [DEBUG] 未找到账号 m53 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,670] [DEBUG] 更新账号 m54 的表格数据，是否有数据: True
[2025-07-08 17:23:32,670] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,671] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,671] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,672] [DEBUG] 开始查找账号 m54 对应的行索引
[2025-07-08 17:23:32,672] [DEBUG] 未找到账号 m54 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,672] [DEBUG] 更新账号 m64 的表格数据，是否有数据: True
[2025-07-08 17:23:32,672] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,673] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,673] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,673] [DEBUG] 开始查找账号 m64 对应的行索引
[2025-07-08 17:23:32,673] [DEBUG] 未找到账号 m64 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,674] [DEBUG] 更新账号 m65 的表格数据，是否有数据: True
[2025-07-08 17:23:32,674] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,674] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,675] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,675] [DEBUG] 开始查找账号 m65 对应的行索引
[2025-07-08 17:23:32,675] [DEBUG] 未找到账号 m65 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,675] [DEBUG] 更新账号 m66 的表格数据，是否有数据: True
[2025-07-08 17:23:32,675] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,676] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,676] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,676] [DEBUG] 开始查找账号 m66 对应的行索引
[2025-07-08 17:23:32,676] [DEBUG] 未找到账号 m66 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,676] [DEBUG] 更新账号 m56 的表格数据，是否有数据: True
[2025-07-08 17:23:32,676] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,677] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,677] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,677] [DEBUG] 开始查找账号 m56 对应的行索引
[2025-07-08 17:23:32,678] [DEBUG] 未找到账号 m56 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,678] [DEBUG] 更新账号 m59 的表格数据，是否有数据: True
[2025-07-08 17:23:32,678] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,678] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,678] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,678] [DEBUG] 开始查找账号 m59 对应的行索引
[2025-07-08 17:23:32,679] [DEBUG] 未找到账号 m59 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,679] [DEBUG] 更新账号 m6 的表格数据，是否有数据: True
[2025-07-08 17:23:32,679] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,679] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,679] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,680] [DEBUG] 开始查找账号 m6 对应的行索引
[2025-07-08 17:23:32,680] [DEBUG] 未找到账号 m6 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,680] [DEBUG] 更新账号 m60 的表格数据，是否有数据: True
[2025-07-08 17:23:32,680] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,681] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,681] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,681] [DEBUG] 开始查找账号 m60 对应的行索引
[2025-07-08 17:23:32,682] [DEBUG] 未找到账号 m60 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,682] [DEBUG] 更新账号 m62 的表格数据，是否有数据: True
[2025-07-08 17:23:32,682] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,683] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,683] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,683] [DEBUG] 开始查找账号 m62 对应的行索引
[2025-07-08 17:23:32,683] [DEBUG] 未找到账号 m62 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,683] [DEBUG] 更新账号 m67 的表格数据，是否有数据: True
[2025-07-08 17:23:32,683] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,684] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,684] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,684] [DEBUG] 开始查找账号 m67 对应的行索引
[2025-07-08 17:23:32,684] [DEBUG] 未找到账号 m67 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,684] [DEBUG] 更新账号 m7 的表格数据，是否有数据: True
[2025-07-08 17:23:32,685] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,685] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,686] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,686] [DEBUG] 开始查找账号 m7 对应的行索引
[2025-07-08 17:23:32,686] [DEBUG] 未找到账号 m7 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,686] [DEBUG] 更新账号 m9 的表格数据，是否有数据: True
[2025-07-08 17:23:32,686] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,686] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,687] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,687] [DEBUG] 开始查找账号 m9 对应的行索引
[2025-07-08 17:23:32,687] [DEBUG] 未找到账号 m9 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,687] [DEBUG] 更新账号 m8 的表格数据，是否有数据: True
[2025-07-08 17:23:32,687] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,687] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,688] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,688] [DEBUG] 开始查找账号 m8 对应的行索引
[2025-07-08 17:23:32,688] [DEBUG] 未找到账号 m8 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,688] [DEBUG] 更新账号 m10 的表格数据，是否有数据: True
[2025-07-08 17:23:32,688] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,689] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,689] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,689] [DEBUG] 开始查找账号 m10 对应的行索引
[2025-07-08 17:23:32,690] [DEBUG] 未找到账号 m10 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,690] [DEBUG] 更新账号 m11 的表格数据，是否有数据: True
[2025-07-08 17:23:32,690] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,690] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,691] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,691] [DEBUG] 开始查找账号 m11 对应的行索引
[2025-07-08 17:23:32,691] [DEBUG] 未找到账号 m11 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,691] [DEBUG] 更新账号 m1 的表格数据，是否有数据: True
[2025-07-08 17:23:32,691] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,692] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,692] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,693] [DEBUG] 开始查找账号 m1 对应的行索引
[2025-07-08 17:23:32,693] [DEBUG] 未找到账号 m1 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,693] [DEBUG] 更新账号 m12 的表格数据，是否有数据: True
[2025-07-08 17:23:32,693] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,693] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,694] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,694] [DEBUG] 开始查找账号 m12 对应的行索引
[2025-07-08 17:23:32,694] [DEBUG] 未找到账号 m12 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,694] [DEBUG] 更新账号 m13 的表格数据，是否有数据: True
[2025-07-08 17:23:32,694] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,695] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,695] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,695] [DEBUG] 开始查找账号 m13 对应的行索引
[2025-07-08 17:23:32,695] [DEBUG] 未找到账号 m13 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,695] [DEBUG] 更新账号 m14 的表格数据，是否有数据: True
[2025-07-08 17:23:32,696] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,696] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,696] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,696] [DEBUG] 开始查找账号 m14 对应的行索引
[2025-07-08 17:23:32,696] [DEBUG] 未找到账号 m14 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,697] [DEBUG] 更新账号 m16 的表格数据，是否有数据: True
[2025-07-08 17:23:32,697] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,698] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,698] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,698] [DEBUG] 开始查找账号 m16 对应的行索引
[2025-07-08 17:23:32,698] [DEBUG] 未找到账号 m16 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,698] [DEBUG] 更新账号 m15 的表格数据，是否有数据: True
[2025-07-08 17:23:32,699] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,699] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,699] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,699] [DEBUG] 开始查找账号 m15 对应的行索引
[2025-07-08 17:23:32,699] [DEBUG] 未找到账号 m15 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,700] [DEBUG] 更新账号 m17 的表格数据，是否有数据: True
[2025-07-08 17:23:32,700] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,700] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,700] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,701] [DEBUG] 开始查找账号 m17 对应的行索引
[2025-07-08 17:23:32,701] [DEBUG] 未找到账号 m17 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,701] [DEBUG] 更新账号 m18 的表格数据，是否有数据: True
[2025-07-08 17:23:32,701] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,701] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,702] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,703] [DEBUG] 开始查找账号 m18 对应的行索引
[2025-07-08 17:23:32,703] [DEBUG] 未找到账号 m18 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,703] [DEBUG] 更新账号 m19 的表格数据，是否有数据: True
[2025-07-08 17:23:32,703] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,703] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,703] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,704] [DEBUG] 开始查找账号 m19 对应的行索引
[2025-07-08 17:23:32,704] [DEBUG] 未找到账号 m19 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,704] [DEBUG] 更新账号 m2 的表格数据，是否有数据: True
[2025-07-08 17:23:32,704] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,704] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,705] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,705] [DEBUG] 开始查找账号 m2 对应的行索引
[2025-07-08 17:23:32,705] [DEBUG] 未找到账号 m2 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,705] [DEBUG] 更新账号 m20 的表格数据，是否有数据: True
[2025-07-08 17:23:32,705] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,706] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,706] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,706] [DEBUG] 开始查找账号 m20 对应的行索引
[2025-07-08 17:23:32,707] [DEBUG] 未找到账号 m20 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,707] [DEBUG] 更新账号 m21 的表格数据，是否有数据: True
[2025-07-08 17:23:32,707] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,707] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,707] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,708] [DEBUG] 开始查找账号 m21 对应的行索引
[2025-07-08 17:23:32,708] [DEBUG] 未找到账号 m21 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,708] [DEBUG] 更新账号 m22 的表格数据，是否有数据: True
[2025-07-08 17:23:32,708] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,708] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,708] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,709] [DEBUG] 开始查找账号 m22 对应的行索引
[2025-07-08 17:23:32,709] [DEBUG] 未找到账号 m22 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,709] [DEBUG] 更新账号 m23 的表格数据，是否有数据: True
[2025-07-08 17:23:32,709] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,709] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,709] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,710] [DEBUG] 开始查找账号 m23 对应的行索引
[2025-07-08 17:23:32,710] [DEBUG] 未找到账号 m23 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,711] [DEBUG] 更新账号 m24 的表格数据，是否有数据: True
[2025-07-08 17:23:32,711] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,711] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,711] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,711] [DEBUG] 开始查找账号 m24 对应的行索引
[2025-07-08 17:23:32,712] [DEBUG] 未找到账号 m24 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,712] [DEBUG] 更新账号 m25 的表格数据，是否有数据: True
[2025-07-08 17:23:32,712] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,712] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,713] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,713] [DEBUG] 开始查找账号 m25 对应的行索引
[2025-07-08 17:23:32,713] [DEBUG] 未找到账号 m25 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,713] [DEBUG] 更新账号 m26 的表格数据，是否有数据: True
[2025-07-08 17:23:32,713] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,714] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,714] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,715] [DEBUG] 开始查找账号 m26 对应的行索引
[2025-07-08 17:23:32,715] [DEBUG] 未找到账号 m26 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,715] [DEBUG] 更新账号 m27 的表格数据，是否有数据: True
[2025-07-08 17:23:32,715] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,715] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,716] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,716] [DEBUG] 开始查找账号 m27 对应的行索引
[2025-07-08 17:23:32,716] [DEBUG] 未找到账号 m27 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,717] [DEBUG] 更新账号 m28 的表格数据，是否有数据: True
[2025-07-08 17:23:32,717] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,717] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,717] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,718] [DEBUG] 开始查找账号 m28 对应的行索引
[2025-07-08 17:23:32,718] [DEBUG] 未找到账号 m28 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,718] [DEBUG] 更新账号 m30 的表格数据，是否有数据: True
[2025-07-08 17:23:32,718] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,719] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,720] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,720] [DEBUG] 开始查找账号 m30 对应的行索引
[2025-07-08 17:23:32,720] [DEBUG] 未找到账号 m30 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,720] [DEBUG] 更新账号 m3 的表格数据，是否有数据: True
[2025-07-08 17:23:32,721] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,721] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,721] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,722] [DEBUG] 开始查找账号 m3 对应的行索引
[2025-07-08 17:23:32,723] [DEBUG] 未找到账号 m3 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,723] [DEBUG] 更新账号 m29 的表格数据，是否有数据: True
[2025-07-08 17:23:32,723] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,723] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,724] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,724] [DEBUG] 开始查找账号 m29 对应的行索引
[2025-07-08 17:23:32,724] [DEBUG] 未找到账号 m29 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,725] [DEBUG] 更新账号 m31 的表格数据，是否有数据: True
[2025-07-08 17:23:32,725] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,726] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,726] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,726] [DEBUG] 开始查找账号 m31 对应的行索引
[2025-07-08 17:23:32,727] [DEBUG] 未找到账号 m31 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,727] [DEBUG] 更新账号 m32 的表格数据，是否有数据: True
[2025-07-08 17:23:32,727] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,727] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,728] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,728] [DEBUG] 开始查找账号 m32 对应的行索引
[2025-07-08 17:23:32,728] [DEBUG] 未找到账号 m32 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,728] [DEBUG] 更新账号 m33 的表格数据，是否有数据: True
[2025-07-08 17:23:32,729] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,729] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,730] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,730] [DEBUG] 开始查找账号 m33 对应的行索引
[2025-07-08 17:23:32,730] [DEBUG] 未找到账号 m33 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,731] [DEBUG] 更新账号 m34 的表格数据，是否有数据: True
[2025-07-08 17:23:32,731] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,731] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,732] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,732] [DEBUG] 开始查找账号 m34 对应的行索引
[2025-07-08 17:23:32,732] [DEBUG] 未找到账号 m34 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,732] [DEBUG] 更新账号 m35 的表格数据，是否有数据: True
[2025-07-08 17:23:32,733] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,733] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,734] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,734] [DEBUG] 开始查找账号 m35 对应的行索引
[2025-07-08 17:23:32,734] [DEBUG] 未找到账号 m35 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,734] [DEBUG] 更新账号 m36 的表格数据，是否有数据: True
[2025-07-08 17:23:32,735] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,735] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,735] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,735] [DEBUG] 开始查找账号 m36 对应的行索引
[2025-07-08 17:23:32,736] [DEBUG] 未找到账号 m36 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,736] [DEBUG] 更新账号 m37 的表格数据，是否有数据: True
[2025-07-08 17:23:32,736] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,737] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,738] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,738] [DEBUG] 开始查找账号 m37 对应的行索引
[2025-07-08 17:23:32,738] [DEBUG] 未找到账号 m37 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,738] [DEBUG] 更新账号 m38 的表格数据，是否有数据: True
[2025-07-08 17:23:32,739] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,739] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,739] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,739] [DEBUG] 开始查找账号 m38 对应的行索引
[2025-07-08 17:23:32,739] [DEBUG] 未找到账号 m38 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,740] [DEBUG] 更新账号 m39 的表格数据，是否有数据: True
[2025-07-08 17:23:32,740] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,740] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,741] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,741] [DEBUG] 开始查找账号 m39 对应的行索引
[2025-07-08 17:23:32,742] [DEBUG] 未找到账号 m39 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,742] [DEBUG] 更新账号 m4 的表格数据，是否有数据: True
[2025-07-08 17:23:32,742] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,742] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,742] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,743] [DEBUG] 开始查找账号 m4 对应的行索引
[2025-07-08 17:23:32,743] [DEBUG] 未找到账号 m4 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,743] [DEBUG] 更新账号 m40 的表格数据，是否有数据: True
[2025-07-08 17:23:32,743] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,744] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,744] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,744] [DEBUG] 开始查找账号 m40 对应的行索引
[2025-07-08 17:23:32,744] [DEBUG] 未找到账号 m40 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,744] [DEBUG] 更新账号 m41 的表格数据，是否有数据: True
[2025-07-08 17:23:32,744] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,745] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,745] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,746] [DEBUG] 开始查找账号 m41 对应的行索引
[2025-07-08 17:23:32,746] [DEBUG] 未找到账号 m41 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,746] [DEBUG] 更新账号 m42 的表格数据，是否有数据: True
[2025-07-08 17:23:32,746] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,746] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,747] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,747] [DEBUG] 开始查找账号 m42 对应的行索引
[2025-07-08 17:23:32,747] [DEBUG] 未找到账号 m42 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,747] [DEBUG] 更新账号 m43 的表格数据，是否有数据: True
[2025-07-08 17:23:32,747] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,747] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,748] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,748] [DEBUG] 开始查找账号 m43 对应的行索引
[2025-07-08 17:23:32,748] [DEBUG] 未找到账号 m43 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,748] [DEBUG] 更新账号 m44 的表格数据，是否有数据: True
[2025-07-08 17:23:32,748] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,749] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,749] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,750] [DEBUG] 开始查找账号 m44 对应的行索引
[2025-07-08 17:23:32,750] [DEBUG] 未找到账号 m44 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,750] [DEBUG] 更新账号 m45 的表格数据，是否有数据: True
[2025-07-08 17:23:32,750] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,751] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,751] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,751] [DEBUG] 开始查找账号 m45 对应的行索引
[2025-07-08 17:23:32,752] [DEBUG] 未找到账号 m45 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,752] [DEBUG] 更新账号 m46 的表格数据，是否有数据: True
[2025-07-08 17:23:32,753] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,753] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,753] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,753] [DEBUG] 开始查找账号 m46 对应的行索引
[2025-07-08 17:23:32,754] [DEBUG] 未找到账号 m46 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,754] [DEBUG] 更新账号 m47 的表格数据，是否有数据: True
[2025-07-08 17:23:32,754] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,754] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,755] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,755] [DEBUG] 开始查找账号 m47 对应的行索引
[2025-07-08 17:23:32,756] [DEBUG] 未找到账号 m47 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,756] [DEBUG] 更新账号 m48 的表格数据，是否有数据: True
[2025-07-08 17:23:32,756] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,756] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,756] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,756] [DEBUG] 开始查找账号 m48 对应的行索引
[2025-07-08 17:23:32,757] [DEBUG] 未找到账号 m48 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,757] [DEBUG] 更新账号 m49 的表格数据，是否有数据: True
[2025-07-08 17:23:32,757] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,757] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,757] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,757] [DEBUG] 开始查找账号 m49 对应的行索引
[2025-07-08 17:23:32,758] [DEBUG] 未找到账号 m49 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,758] [DEBUG] 更新账号 m5 的表格数据，是否有数据: True
[2025-07-08 17:23:32,758] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,758] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,758] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,759] [DEBUG] 开始查找账号 m5 对应的行索引
[2025-07-08 17:23:32,759] [DEBUG] 未找到账号 m5 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,759] [DEBUG] 更新账号 m51 的表格数据，是否有数据: True
[2025-07-08 17:23:32,759] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,759] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,759] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,760] [DEBUG] 开始查找账号 m51 对应的行索引
[2025-07-08 17:23:32,760] [DEBUG] 未找到账号 m51 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,760] [DEBUG] 更新账号 m52 的表格数据，是否有数据: True
[2025-07-08 17:23:32,760] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,761] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,761] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,762] [DEBUG] 开始查找账号 m52 对应的行索引
[2025-07-08 17:23:32,762] [DEBUG] 未找到账号 m52 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,762] [DEBUG] 更新账号 m55 的表格数据，是否有数据: True
[2025-07-08 17:23:32,762] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,762] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,763] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,763] [DEBUG] 开始查找账号 m55 对应的行索引
[2025-07-08 17:23:32,763] [DEBUG] 未找到账号 m55 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,763] [DEBUG] 更新账号 m57 的表格数据，是否有数据: True
[2025-07-08 17:23:32,763] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,764] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,764] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,764] [DEBUG] 开始查找账号 m57 对应的行索引
[2025-07-08 17:23:32,764] [DEBUG] 未找到账号 m57 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,764] [DEBUG] 更新账号 m58 的表格数据，是否有数据: True
[2025-07-08 17:23:32,765] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,765] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,766] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,766] [DEBUG] 开始查找账号 m58 对应的行索引
[2025-07-08 17:23:32,766] [DEBUG] 未找到账号 m58 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,766] [DEBUG] 更新账号 m61 的表格数据，是否有数据: True
[2025-07-08 17:23:32,766] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:23:32,766] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:23:32,767] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:23:32,767] [DEBUG] 开始查找账号 m61 对应的行索引
[2025-07-08 17:23:32,767] [DEBUG] 未找到账号 m61 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:23:32,808] [INFO] 用户取消后台加载
[2025-07-08 17:23:32,808] [INFO] 后台加载完成: 账号数据加载完成
[2025-07-08 17:23:33,013] [DEBUG] 批量数据处理完成后内存使用: 249.77 MB
[2025-07-08 17:23:33,015] [ERROR] 更新加载进度时出错: 'NoneType' object has no attribute 'setLabelText'
[2025-07-08 17:23:33,958] [INFO] account_loader.load_accounts_sync方法返回: success=True, accounts数量=370, error_msg=
[2025-07-08 17:23:33,958] [INFO] 同步加载账号数据完成，结果: True, 账号数量: 370, 错误信息: 
[2025-07-08 17:23:33,959] [INFO] 账号数据加载成功，共 370 个账号
[2025-07-08 17:23:33,959] [INFO] 账号加载完成回调开始时内存使用: 259.84 MB
[2025-07-08 17:23:33,959] [INFO] 已创建账号列表的浅拷贝，大小: 370
[2025-07-08 17:23:33,959] [INFO] 成功加载 370 个账号文件，延迟加载模式: True
[2025-07-08 17:23:33,987] [INFO] 账号列表大小: 370
[2025-07-08 17:23:33,987] [INFO] 第一个账号信息: {'file_path': 'E:/软件共享/头条/头条\\***********.txt', 'account_id': '***********', 'data': {'accountId': '***********', 'remark': '***********', 'cookies': {'xigua_csrf_token': 'Rh9tB8w72kFdjHWXzmwLx4FN', 'is_staff_user': 'false', 'sessionid_ss': '68c4cc3f1c5dedd10b37702f8cfc5581', 'uid_tt_ss': '4138e04b7027f10e81ee57334a3871f1', 'store-region': 'cn-gd', 'uid_tt': '4138e04b7027f10e81ee57334a3871f1', 'passport_auth_status': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'passport_mfa_token': 'CjfDS1ognyyaR5OXvuo1ZhOlSC8orpJykXqU3XrL0%2F4LK3qwDRucEFU8wl5EeoQDFgQXMDSnVv64GkoKPIA87d76RVvezzRe1eiXyd81F%2FQYdzX0iWRhqgsPoaSXZlpDutIfPN2NISNG6lXHe%2BhFo3Alo9axe2OZMhDY3OsNGPax0WwgAiIBA6K42yM%3D', 'passport_auth_status_ss': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'sid_tt': '68c4cc3f1c5dedd10b37702f8cfc5581', 'odin_tt': 'f681b2fee0f84dd926c09d58e132e2328c436e9fcf004f758697a032da7adc66d0d445a80508f8d675d1357603c3efe11f5237f59ac2ca2893e80a3e5da52dcf', 'sessionid': '68c4cc3f1c5dedd10b37702f8cfc5581', 'gfgarrcache_8005812e': '1', 'ssid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'toutiao_sso_user_ss': 'c2032ee116a4c329079d18ddc99f62c6', 'gfgarrcache_6a2703f1': '1', 'sid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'store-region-src': 'uid', 'sid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'toutiao_sso_user': 'c2032ee116a4c329079d18ddc99f62c6', 'sso_uid_tt_ss': '707d66e7d745b6ca510877ff49d6deca', 'xg_p_tos_token': '79aa2d6e8307f3027308bb8063808201', 'sso_uid_tt': '707d66e7d745b6ca510877ff49d6deca', 'sso_auth_status_ss': '99d6be0fe40fbeccd26dbd011f7e4345', 'ssid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'sid_guard': '68c4cc3f1c5dedd10b37702f8cfc5581%7C1741687410%7C5184002%7CSat%2C+10-May-2025+10%3A03%3A32+GMT', 'sso_auth_status': '99d6be0fe40fbeccd26dbd011f7e4345', 'passport_csrf_token': '3d631c058164d798d48475833f13446e', 'n_mh': '30A9sphpGJEPcXGYEGo8hiNGyEbb5VtQ_Ov5DGw0tbA', 'ttwid': '1%7C0FfHeK6FyRv027NKpnZinttVopHTQty8nSXP9P7wnNw%7C1741687388%7C33b1d18a6f3d000bd20fd4a1d0f5d6b37682c37752a91a6e1265423a4b1e9968', 'gfkadpd': '1231,25897', 'd_ticket': '0da2a81adc5bb557ff718c555c0a4801d76e7', 'passport_csrf_token_default': '3d631c058164d798d48475833f13446e', 'csrf_session_id': 'e1a99642f2b9648ef30c79ef589a852a', 's_v_web_id': 'verify_m84bp59m_dED7JGij_AkV2_4J3T_B268_83sqBwAURoyE'}}, 'file_type': '.txt'}
[2025-07-08 17:23:33,988] [INFO] 使用延迟加载模式，准备更新表格基本信息
[2025-07-08 17:23:33,990] [DEBUG] 已导入高级内存管理器
[2025-07-08 17:23:34,000] [INFO] 更新表格开始时内存使用: 259.86MB, 可用: 1554.45MB/16236.06MB
[2025-07-08 17:23:34,031] [INFO] 准备设置表格行数: 370
[2025-07-08 17:23:34,031] [INFO] 已设置表格行数: 370，当前表格行数: 370
[2025-07-08 17:23:34,031] [DEBUG] 处理账号批次: 1-50/370
[2025-07-08 17:23:34,288] [DEBUG] 处理账号批次: 51-100/370
[2025-07-08 17:23:34,547] [DEBUG] 处理账号批次: 101-150/370
[2025-07-08 17:23:34,807] [DEBUG] 处理账号批次: 151-200/370
[2025-07-08 17:23:35,068] [DEBUG] 处理账号批次: 201-250/370
[2025-07-08 17:23:35,340] [DEBUG] 处理账号批次: 251-300/370
[2025-07-08 17:23:35,615] [DEBUG] 处理账号批次: 301-350/370
[2025-07-08 17:23:35,905] [DEBUG] 处理账号批次: 351-370/370
[2025-07-08 17:23:36,112] [INFO] 表格更新完成，耗时: 2.11秒，内存使用: 378.10MB
[2025-07-08 17:23:36,113] [INFO] 使用延迟加载模式，已更新表格基本信息
[2025-07-08 17:23:36,113] [INFO] 延迟加载模式下，表格行数: 370
[2025-07-08 17:23:36,167] [INFO] 不设置账号已加载标志，允许重复加载
[2025-07-08 17:23:36,167] [INFO] 开始计算收益统计，表格行数: 370, 列数: 19
[2025-07-08 17:23:36,169] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-08 17:23:36,170] [INFO] 七天总收益: ¥0.00
[2025-07-08 17:23:36,170] [INFO] 昨日总收益: ¥0.00
[2025-07-08 17:23:36,170] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-08 17:23:36,173] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-08 17:23:40,408] [INFO] 开始计算收益统计，表格行数: 370, 列数: 19
[2025-07-08 17:23:40,409] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-08 17:23:40,410] [INFO] 七天总收益: ¥0.00
[2025-07-08 17:23:40,410] [INFO] 昨日总收益: ¥0.00
[2025-07-08 17:23:40,410] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-08 17:23:40,413] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-08 17:24:01,264] [INFO] OpenCV库已加载，支持视频处理功能
[2025-07-08 17:24:01,305] [INFO] PIL库已加载，支持图像处理功能
[2025-07-08 17:24:02,778] [INFO] 自动化配置加载成功
[2025-07-08 17:24:02,778] [INFO] 自动化任务调度器初始化完成
[2025-07-08 17:24:02,786] [INFO] 心跳状态指示器初始化完成
[2025-07-08 17:24:02,847] [INFO] 跳过示例数据加载，等待真实账号数据
[2025-07-08 17:24:02,852] [ERROR] 连接表格信号时出错: 'AccountTab' object has no attribute 'on_cell_changed'
[2025-07-08 17:24:03,779] [INFO] 账号加载模式: 延迟加载
[2025-07-08 17:24:03,781] [INFO] 数据目录功能已移除
[2025-07-08 17:24:03,781] [INFO] 开始同步加载Cookie文件，路径: E:/软件共享/头条/头条
[2025-07-08 17:24:03,782] [INFO] 路径 E:/软件共享/头条/头条 下有 0 个JSON文件和 370 个TXT文件
[2025-07-08 17:24:03,783] [INFO] 调用account_loader.load_accounts_sync方法前
[2025-07-08 17:24:03,853] [INFO] 启动快速启动模式 - 后台加载账号数据
[2025-07-08 17:24:03,900] [DEBUG] 批量数据处理开始时内存使用: 249.20 MB，数据量: 67
[2025-07-08 17:24:03,901] [DEBUG] 更新账号 m50 的表格数据，是否有数据: True
[2025-07-08 17:24:03,901] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,902] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,902] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,902] [DEBUG] 开始查找账号 m50 对应的行索引
[2025-07-08 17:24:03,902] [DEBUG] 未找到账号 m50 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,902] [DEBUG] 更新账号 m63 的表格数据，是否有数据: True
[2025-07-08 17:24:03,902] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,903] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,903] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,903] [DEBUG] 开始查找账号 m63 对应的行索引
[2025-07-08 17:24:03,903] [DEBUG] 未找到账号 m63 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,903] [DEBUG] 更新账号 m53 的表格数据，是否有数据: True
[2025-07-08 17:24:03,903] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,904] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,904] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,904] [DEBUG] 开始查找账号 m53 对应的行索引
[2025-07-08 17:24:03,904] [DEBUG] 未找到账号 m53 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,904] [DEBUG] 更新账号 m54 的表格数据，是否有数据: True
[2025-07-08 17:24:03,905] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,905] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,905] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,906] [DEBUG] 开始查找账号 m54 对应的行索引
[2025-07-08 17:24:03,906] [DEBUG] 未找到账号 m54 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,906] [DEBUG] 更新账号 m64 的表格数据，是否有数据: True
[2025-07-08 17:24:03,907] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,907] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,907] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,907] [DEBUG] 开始查找账号 m64 对应的行索引
[2025-07-08 17:24:03,907] [DEBUG] 未找到账号 m64 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,908] [DEBUG] 更新账号 m65 的表格数据，是否有数据: True
[2025-07-08 17:24:03,908] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,908] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,908] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,909] [DEBUG] 开始查找账号 m65 对应的行索引
[2025-07-08 17:24:03,909] [DEBUG] 未找到账号 m65 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,909] [DEBUG] 更新账号 m66 的表格数据，是否有数据: True
[2025-07-08 17:24:03,910] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,910] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,910] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,910] [DEBUG] 开始查找账号 m66 对应的行索引
[2025-07-08 17:24:03,910] [DEBUG] 未找到账号 m66 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,911] [DEBUG] 更新账号 m56 的表格数据，是否有数据: True
[2025-07-08 17:24:03,911] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,911] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,911] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,912] [DEBUG] 开始查找账号 m56 对应的行索引
[2025-07-08 17:24:03,912] [DEBUG] 未找到账号 m56 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,913] [DEBUG] 更新账号 m59 的表格数据，是否有数据: True
[2025-07-08 17:24:03,913] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,913] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,913] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,913] [DEBUG] 开始查找账号 m59 对应的行索引
[2025-07-08 17:24:03,914] [DEBUG] 未找到账号 m59 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,914] [DEBUG] 更新账号 m6 的表格数据，是否有数据: True
[2025-07-08 17:24:03,914] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,914] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,914] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,914] [DEBUG] 开始查找账号 m6 对应的行索引
[2025-07-08 17:24:03,915] [DEBUG] 未找到账号 m6 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,915] [DEBUG] 更新账号 m60 的表格数据，是否有数据: True
[2025-07-08 17:24:03,915] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,915] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,915] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,915] [DEBUG] 开始查找账号 m60 对应的行索引
[2025-07-08 17:24:03,916] [DEBUG] 未找到账号 m60 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,916] [DEBUG] 更新账号 m62 的表格数据，是否有数据: True
[2025-07-08 17:24:03,917] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,917] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,917] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,917] [DEBUG] 开始查找账号 m62 对应的行索引
[2025-07-08 17:24:03,917] [DEBUG] 未找到账号 m62 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,918] [DEBUG] 更新账号 m67 的表格数据，是否有数据: True
[2025-07-08 17:24:03,918] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,918] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,918] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,919] [DEBUG] 开始查找账号 m67 对应的行索引
[2025-07-08 17:24:03,919] [DEBUG] 未找到账号 m67 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,919] [DEBUG] 更新账号 m7 的表格数据，是否有数据: True
[2025-07-08 17:24:03,919] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,919] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,920] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,920] [DEBUG] 开始查找账号 m7 对应的行索引
[2025-07-08 17:24:03,921] [DEBUG] 未找到账号 m7 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,921] [DEBUG] 更新账号 m9 的表格数据，是否有数据: True
[2025-07-08 17:24:03,921] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,921] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,922] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,922] [DEBUG] 开始查找账号 m9 对应的行索引
[2025-07-08 17:24:03,922] [DEBUG] 未找到账号 m9 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,922] [DEBUG] 更新账号 m8 的表格数据，是否有数据: True
[2025-07-08 17:24:03,923] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,924] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,924] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,924] [DEBUG] 开始查找账号 m8 对应的行索引
[2025-07-08 17:24:03,924] [DEBUG] 未找到账号 m8 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,924] [DEBUG] 更新账号 m10 的表格数据，是否有数据: True
[2025-07-08 17:24:03,925] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,925] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,925] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,925] [DEBUG] 开始查找账号 m10 对应的行索引
[2025-07-08 17:24:03,925] [DEBUG] 未找到账号 m10 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,925] [DEBUG] 更新账号 m11 的表格数据，是否有数据: True
[2025-07-08 17:24:03,926] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,926] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,926] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,926] [DEBUG] 开始查找账号 m11 对应的行索引
[2025-07-08 17:24:03,926] [DEBUG] 未找到账号 m11 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,927] [DEBUG] 更新账号 m1 的表格数据，是否有数据: True
[2025-07-08 17:24:03,927] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,927] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,927] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,927] [DEBUG] 开始查找账号 m1 对应的行索引
[2025-07-08 17:24:03,928] [DEBUG] 未找到账号 m1 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,928] [DEBUG] 更新账号 m12 的表格数据，是否有数据: True
[2025-07-08 17:24:03,928] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,929] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,929] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,929] [DEBUG] 开始查找账号 m12 对应的行索引
[2025-07-08 17:24:03,929] [DEBUG] 未找到账号 m12 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,929] [DEBUG] 更新账号 m13 的表格数据，是否有数据: True
[2025-07-08 17:24:03,929] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,929] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,930] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,930] [DEBUG] 开始查找账号 m13 对应的行索引
[2025-07-08 17:24:03,930] [DEBUG] 未找到账号 m13 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,930] [DEBUG] 更新账号 m14 的表格数据，是否有数据: True
[2025-07-08 17:24:03,930] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,931] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,932] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,932] [DEBUG] 开始查找账号 m14 对应的行索引
[2025-07-08 17:24:03,932] [DEBUG] 未找到账号 m14 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,932] [DEBUG] 更新账号 m16 的表格数据，是否有数据: True
[2025-07-08 17:24:03,932] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,932] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,933] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,933] [DEBUG] 开始查找账号 m16 对应的行索引
[2025-07-08 17:24:03,933] [DEBUG] 未找到账号 m16 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,933] [DEBUG] 更新账号 m15 的表格数据，是否有数据: True
[2025-07-08 17:24:03,933] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,934] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,934] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,934] [DEBUG] 开始查找账号 m15 对应的行索引
[2025-07-08 17:24:03,934] [DEBUG] 未找到账号 m15 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,934] [DEBUG] 更新账号 m17 的表格数据，是否有数据: True
[2025-07-08 17:24:03,935] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,935] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,935] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,936] [DEBUG] 开始查找账号 m17 对应的行索引
[2025-07-08 17:24:03,936] [DEBUG] 未找到账号 m17 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,936] [DEBUG] 更新账号 m18 的表格数据，是否有数据: True
[2025-07-08 17:24:03,936] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,936] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,937] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,937] [DEBUG] 开始查找账号 m18 对应的行索引
[2025-07-08 17:24:03,937] [DEBUG] 未找到账号 m18 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,937] [DEBUG] 更新账号 m19 的表格数据，是否有数据: True
[2025-07-08 17:24:03,937] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,937] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,938] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,938] [DEBUG] 开始查找账号 m19 对应的行索引
[2025-07-08 17:24:03,938] [DEBUG] 未找到账号 m19 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,938] [DEBUG] 更新账号 m2 的表格数据，是否有数据: True
[2025-07-08 17:24:03,938] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,939] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,939] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,939] [DEBUG] 开始查找账号 m2 对应的行索引
[2025-07-08 17:24:03,939] [DEBUG] 未找到账号 m2 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,939] [DEBUG] 更新账号 m20 的表格数据，是否有数据: True
[2025-07-08 17:24:03,940] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,940] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,940] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,941] [DEBUG] 开始查找账号 m20 对应的行索引
[2025-07-08 17:24:03,941] [DEBUG] 未找到账号 m20 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,941] [DEBUG] 更新账号 m21 的表格数据，是否有数据: True
[2025-07-08 17:24:03,941] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,941] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,942] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,942] [DEBUG] 开始查找账号 m21 对应的行索引
[2025-07-08 17:24:03,942] [DEBUG] 未找到账号 m21 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,942] [DEBUG] 更新账号 m22 的表格数据，是否有数据: True
[2025-07-08 17:24:03,942] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,942] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,943] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,943] [DEBUG] 开始查找账号 m22 对应的行索引
[2025-07-08 17:24:03,944] [DEBUG] 未找到账号 m22 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,944] [DEBUG] 更新账号 m23 的表格数据，是否有数据: True
[2025-07-08 17:24:03,944] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,944] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,944] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,944] [DEBUG] 开始查找账号 m23 对应的行索引
[2025-07-08 17:24:03,945] [DEBUG] 未找到账号 m23 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,945] [DEBUG] 更新账号 m24 的表格数据，是否有数据: True
[2025-07-08 17:24:03,945] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,945] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,945] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,945] [DEBUG] 开始查找账号 m24 对应的行索引
[2025-07-08 17:24:03,946] [DEBUG] 未找到账号 m24 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,946] [DEBUG] 更新账号 m25 的表格数据，是否有数据: True
[2025-07-08 17:24:03,946] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,946] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,946] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,947] [DEBUG] 开始查找账号 m25 对应的行索引
[2025-07-08 17:24:03,947] [DEBUG] 未找到账号 m25 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,947] [DEBUG] 更新账号 m26 的表格数据，是否有数据: True
[2025-07-08 17:24:03,947] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,947] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,947] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,947] [DEBUG] 开始查找账号 m26 对应的行索引
[2025-07-08 17:24:03,947] [DEBUG] 未找到账号 m26 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,948] [DEBUG] 更新账号 m27 的表格数据，是否有数据: True
[2025-07-08 17:24:03,948] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,949] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,949] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,949] [DEBUG] 开始查找账号 m27 对应的行索引
[2025-07-08 17:24:03,950] [DEBUG] 未找到账号 m27 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,950] [DEBUG] 更新账号 m28 的表格数据，是否有数据: True
[2025-07-08 17:24:03,950] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,950] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,950] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,950] [DEBUG] 开始查找账号 m28 对应的行索引
[2025-07-08 17:24:03,951] [DEBUG] 未找到账号 m28 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,951] [DEBUG] 更新账号 m30 的表格数据，是否有数据: True
[2025-07-08 17:24:03,951] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,951] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,952] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,952] [DEBUG] 开始查找账号 m30 对应的行索引
[2025-07-08 17:24:03,952] [DEBUG] 未找到账号 m30 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,952] [DEBUG] 更新账号 m3 的表格数据，是否有数据: True
[2025-07-08 17:24:03,952] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,953] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,953] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,954] [DEBUG] 开始查找账号 m3 对应的行索引
[2025-07-08 17:24:03,954] [DEBUG] 未找到账号 m3 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,954] [DEBUG] 更新账号 m29 的表格数据，是否有数据: True
[2025-07-08 17:24:03,954] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,955] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,955] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,955] [DEBUG] 开始查找账号 m29 对应的行索引
[2025-07-08 17:24:03,955] [DEBUG] 未找到账号 m29 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,955] [DEBUG] 更新账号 m31 的表格数据，是否有数据: True
[2025-07-08 17:24:03,955] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,956] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,956] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,956] [DEBUG] 开始查找账号 m31 对应的行索引
[2025-07-08 17:24:03,956] [DEBUG] 未找到账号 m31 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,956] [DEBUG] 更新账号 m32 的表格数据，是否有数据: True
[2025-07-08 17:24:03,957] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,957] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,957] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,957] [DEBUG] 开始查找账号 m32 对应的行索引
[2025-07-08 17:24:03,957] [DEBUG] 未找到账号 m32 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,958] [DEBUG] 更新账号 m33 的表格数据，是否有数据: True
[2025-07-08 17:24:03,958] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,958] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,958] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,959] [DEBUG] 开始查找账号 m33 对应的行索引
[2025-07-08 17:24:03,959] [DEBUG] 未找到账号 m33 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,960] [DEBUG] 更新账号 m34 的表格数据，是否有数据: True
[2025-07-08 17:24:03,960] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,961] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,961] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,961] [DEBUG] 开始查找账号 m34 对应的行索引
[2025-07-08 17:24:03,961] [DEBUG] 未找到账号 m34 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,962] [DEBUG] 更新账号 m35 的表格数据，是否有数据: True
[2025-07-08 17:24:03,962] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,962] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,962] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,962] [DEBUG] 开始查找账号 m35 对应的行索引
[2025-07-08 17:24:03,963] [DEBUG] 未找到账号 m35 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,963] [DEBUG] 更新账号 m36 的表格数据，是否有数据: True
[2025-07-08 17:24:03,963] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,964] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,964] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,964] [DEBUG] 开始查找账号 m36 对应的行索引
[2025-07-08 17:24:03,964] [DEBUG] 未找到账号 m36 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,964] [DEBUG] 更新账号 m37 的表格数据，是否有数据: True
[2025-07-08 17:24:03,965] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,965] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,965] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,965] [DEBUG] 开始查找账号 m37 对应的行索引
[2025-07-08 17:24:03,965] [DEBUG] 未找到账号 m37 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,965] [DEBUG] 更新账号 m38 的表格数据，是否有数据: True
[2025-07-08 17:24:03,966] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,966] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,966] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,966] [DEBUG] 开始查找账号 m38 对应的行索引
[2025-07-08 17:24:03,966] [DEBUG] 未找到账号 m38 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,967] [DEBUG] 更新账号 m39 的表格数据，是否有数据: True
[2025-07-08 17:24:03,967] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,967] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,968] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,968] [DEBUG] 开始查找账号 m39 对应的行索引
[2025-07-08 17:24:03,968] [DEBUG] 未找到账号 m39 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,969] [DEBUG] 更新账号 m4 的表格数据，是否有数据: True
[2025-07-08 17:24:03,969] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,969] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,969] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,969] [DEBUG] 开始查找账号 m4 对应的行索引
[2025-07-08 17:24:03,970] [DEBUG] 未找到账号 m4 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,970] [DEBUG] 更新账号 m40 的表格数据，是否有数据: True
[2025-07-08 17:24:03,970] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,970] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,971] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,971] [DEBUG] 开始查找账号 m40 对应的行索引
[2025-07-08 17:24:03,972] [DEBUG] 未找到账号 m40 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,972] [DEBUG] 更新账号 m41 的表格数据，是否有数据: True
[2025-07-08 17:24:03,972] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,972] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,973] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,973] [DEBUG] 开始查找账号 m41 对应的行索引
[2025-07-08 17:24:03,973] [DEBUG] 未找到账号 m41 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,973] [DEBUG] 更新账号 m42 的表格数据，是否有数据: True
[2025-07-08 17:24:03,973] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,974] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,974] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,975] [DEBUG] 开始查找账号 m42 对应的行索引
[2025-07-08 17:24:03,975] [DEBUG] 未找到账号 m42 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,975] [DEBUG] 更新账号 m43 的表格数据，是否有数据: True
[2025-07-08 17:24:03,975] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,975] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,975] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,976] [DEBUG] 开始查找账号 m43 对应的行索引
[2025-07-08 17:24:03,976] [DEBUG] 未找到账号 m43 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,976] [DEBUG] 更新账号 m44 的表格数据，是否有数据: True
[2025-07-08 17:24:03,976] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,976] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,977] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,977] [DEBUG] 开始查找账号 m44 对应的行索引
[2025-07-08 17:24:03,977] [DEBUG] 未找到账号 m44 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,977] [DEBUG] 更新账号 m45 的表格数据，是否有数据: True
[2025-07-08 17:24:03,977] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,978] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,978] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,978] [DEBUG] 开始查找账号 m45 对应的行索引
[2025-07-08 17:24:03,978] [DEBUG] 未找到账号 m45 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,978] [DEBUG] 更新账号 m46 的表格数据，是否有数据: True
[2025-07-08 17:24:03,979] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,979] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,979] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,980] [DEBUG] 开始查找账号 m46 对应的行索引
[2025-07-08 17:24:03,980] [DEBUG] 未找到账号 m46 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,980] [DEBUG] 更新账号 m47 的表格数据，是否有数据: True
[2025-07-08 17:24:03,980] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,981] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,981] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,981] [DEBUG] 开始查找账号 m47 对应的行索引
[2025-07-08 17:24:03,981] [DEBUG] 未找到账号 m47 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,981] [DEBUG] 更新账号 m48 的表格数据，是否有数据: True
[2025-07-08 17:24:03,982] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,982] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,982] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,982] [DEBUG] 开始查找账号 m48 对应的行索引
[2025-07-08 17:24:03,982] [DEBUG] 未找到账号 m48 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,983] [DEBUG] 更新账号 m49 的表格数据，是否有数据: True
[2025-07-08 17:24:03,983] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,983] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,983] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,983] [DEBUG] 开始查找账号 m49 对应的行索引
[2025-07-08 17:24:03,984] [DEBUG] 未找到账号 m49 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,984] [DEBUG] 更新账号 m5 的表格数据，是否有数据: True
[2025-07-08 17:24:03,985] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,985] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,985] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,985] [DEBUG] 开始查找账号 m5 对应的行索引
[2025-07-08 17:24:03,985] [DEBUG] 未找到账号 m5 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,986] [DEBUG] 更新账号 m51 的表格数据，是否有数据: True
[2025-07-08 17:24:03,986] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,986] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,986] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,987] [DEBUG] 开始查找账号 m51 对应的行索引
[2025-07-08 17:24:03,987] [DEBUG] 未找到账号 m51 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,987] [DEBUG] 更新账号 m52 的表格数据，是否有数据: True
[2025-07-08 17:24:03,987] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,987] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,988] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,988] [DEBUG] 开始查找账号 m52 对应的行索引
[2025-07-08 17:24:03,988] [DEBUG] 未找到账号 m52 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,989] [DEBUG] 更新账号 m55 的表格数据，是否有数据: True
[2025-07-08 17:24:03,989] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,989] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,989] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,989] [DEBUG] 开始查找账号 m55 对应的行索引
[2025-07-08 17:24:03,990] [DEBUG] 未找到账号 m55 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,990] [DEBUG] 更新账号 m57 的表格数据，是否有数据: True
[2025-07-08 17:24:03,990] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,990] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,990] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,991] [DEBUG] 开始查找账号 m57 对应的行索引
[2025-07-08 17:24:03,991] [DEBUG] 未找到账号 m57 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,992] [DEBUG] 更新账号 m58 的表格数据，是否有数据: True
[2025-07-08 17:24:03,992] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,992] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,992] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,992] [DEBUG] 开始查找账号 m58 对应的行索引
[2025-07-08 17:24:03,993] [DEBUG] 未找到账号 m58 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:03,993] [DEBUG] 更新账号 m61 的表格数据，是否有数据: True
[2025-07-08 17:24:03,993] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:24:03,993] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:24:03,993] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:24:03,993] [DEBUG] 开始查找账号 m61 对应的行索引
[2025-07-08 17:24:03,994] [DEBUG] 未找到账号 m61 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:24:04,036] [INFO] 用户取消后台加载
[2025-07-08 17:24:04,036] [INFO] 后台加载完成: 账号数据加载完成
[2025-07-08 17:24:04,240] [DEBUG] 批量数据处理完成后内存使用: 249.28 MB
[2025-07-08 17:24:04,242] [ERROR] 更新加载进度时出错: 'NoneType' object has no attribute 'setLabelText'
[2025-07-08 17:24:04,755] [INFO] account_loader.load_accounts_sync方法返回: success=True, accounts数量=370, error_msg=
[2025-07-08 17:24:04,755] [INFO] 同步加载账号数据完成，结果: True, 账号数量: 370, 错误信息: 
[2025-07-08 17:24:04,755] [INFO] 账号数据加载成功，共 370 个账号
[2025-07-08 17:24:04,755] [INFO] 账号加载完成回调开始时内存使用: 259.01 MB
[2025-07-08 17:24:04,756] [INFO] 已创建账号列表的浅拷贝，大小: 370
[2025-07-08 17:24:04,756] [INFO] 成功加载 370 个账号文件，延迟加载模式: True
[2025-07-08 17:24:04,784] [INFO] 账号列表大小: 370
[2025-07-08 17:24:04,785] [INFO] 第一个账号信息: {'file_path': 'E:/软件共享/头条/头条\\***********.txt', 'account_id': '***********', 'data': {'accountId': '***********', 'remark': '***********', 'cookies': {'xigua_csrf_token': 'Rh9tB8w72kFdjHWXzmwLx4FN', 'is_staff_user': 'false', 'sessionid_ss': '68c4cc3f1c5dedd10b37702f8cfc5581', 'uid_tt_ss': '4138e04b7027f10e81ee57334a3871f1', 'store-region': 'cn-gd', 'uid_tt': '4138e04b7027f10e81ee57334a3871f1', 'passport_auth_status': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'passport_mfa_token': 'CjfDS1ognyyaR5OXvuo1ZhOlSC8orpJykXqU3XrL0%2F4LK3qwDRucEFU8wl5EeoQDFgQXMDSnVv64GkoKPIA87d76RVvezzRe1eiXyd81F%2FQYdzX0iWRhqgsPoaSXZlpDutIfPN2NISNG6lXHe%2BhFo3Alo9axe2OZMhDY3OsNGPax0WwgAiIBA6K42yM%3D', 'passport_auth_status_ss': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'sid_tt': '68c4cc3f1c5dedd10b37702f8cfc5581', 'odin_tt': 'f681b2fee0f84dd926c09d58e132e2328c436e9fcf004f758697a032da7adc66d0d445a80508f8d675d1357603c3efe11f5237f59ac2ca2893e80a3e5da52dcf', 'sessionid': '68c4cc3f1c5dedd10b37702f8cfc5581', 'gfgarrcache_8005812e': '1', 'ssid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'toutiao_sso_user_ss': 'c2032ee116a4c329079d18ddc99f62c6', 'gfgarrcache_6a2703f1': '1', 'sid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'store-region-src': 'uid', 'sid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'toutiao_sso_user': 'c2032ee116a4c329079d18ddc99f62c6', 'sso_uid_tt_ss': '707d66e7d745b6ca510877ff49d6deca', 'xg_p_tos_token': '79aa2d6e8307f3027308bb8063808201', 'sso_uid_tt': '707d66e7d745b6ca510877ff49d6deca', 'sso_auth_status_ss': '99d6be0fe40fbeccd26dbd011f7e4345', 'ssid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'sid_guard': '68c4cc3f1c5dedd10b37702f8cfc5581%7C1741687410%7C5184002%7CSat%2C+10-May-2025+10%3A03%3A32+GMT', 'sso_auth_status': '99d6be0fe40fbeccd26dbd011f7e4345', 'passport_csrf_token': '3d631c058164d798d48475833f13446e', 'n_mh': '30A9sphpGJEPcXGYEGo8hiNGyEbb5VtQ_Ov5DGw0tbA', 'ttwid': '1%7C0FfHeK6FyRv027NKpnZinttVopHTQty8nSXP9P7wnNw%7C1741687388%7C33b1d18a6f3d000bd20fd4a1d0f5d6b37682c37752a91a6e1265423a4b1e9968', 'gfkadpd': '1231,25897', 'd_ticket': '0da2a81adc5bb557ff718c555c0a4801d76e7', 'passport_csrf_token_default': '3d631c058164d798d48475833f13446e', 'csrf_session_id': 'e1a99642f2b9648ef30c79ef589a852a', 's_v_web_id': 'verify_m84bp59m_dED7JGij_AkV2_4J3T_B268_83sqBwAURoyE'}}, 'file_type': '.txt'}
[2025-07-08 17:24:04,786] [INFO] 使用延迟加载模式，准备更新表格基本信息
[2025-07-08 17:24:04,788] [DEBUG] 已导入高级内存管理器
[2025-07-08 17:24:04,799] [INFO] 更新表格开始时内存使用: 259.05MB, 可用: 1949.23MB/16236.06MB
[2025-07-08 17:24:04,826] [INFO] 准备设置表格行数: 370
[2025-07-08 17:24:04,827] [INFO] 已设置表格行数: 370，当前表格行数: 370
[2025-07-08 17:24:04,827] [DEBUG] 处理账号批次: 1-50/370
[2025-07-08 17:24:05,514] [DEBUG] 处理账号批次: 51-100/370
[2025-07-08 17:24:05,775] [DEBUG] 处理账号批次: 101-150/370
[2025-07-08 17:24:06,035] [DEBUG] 处理账号批次: 151-200/370
[2025-07-08 17:24:06,295] [DEBUG] 处理账号批次: 201-250/370
[2025-07-08 17:24:06,555] [DEBUG] 处理账号批次: 251-300/370
[2025-07-08 17:24:06,815] [DEBUG] 处理账号批次: 301-350/370
[2025-07-08 17:24:07,090] [DEBUG] 处理账号批次: 351-370/370
[2025-07-08 17:24:07,281] [INFO] 表格更新完成，耗时: 2.48秒，内存使用: 377.58MB
[2025-07-08 17:24:07,282] [INFO] 使用延迟加载模式，已更新表格基本信息
[2025-07-08 17:24:07,282] [INFO] 延迟加载模式下，表格行数: 370
[2025-07-08 17:24:07,335] [INFO] 不设置账号已加载标志，允许重复加载
[2025-07-08 17:24:07,336] [INFO] 开始计算收益统计，表格行数: 370, 列数: 19
[2025-07-08 17:24:07,338] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-08 17:24:07,338] [INFO] 七天总收益: ¥0.00
[2025-07-08 17:24:07,338] [INFO] 昨日总收益: ¥0.00
[2025-07-08 17:24:07,338] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-08 17:24:07,341] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-08 17:24:11,620] [INFO] 开始计算收益统计，表格行数: 370, 列数: 19
[2025-07-08 17:24:11,621] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-08 17:24:11,622] [INFO] 七天总收益: ¥0.00
[2025-07-08 17:24:11,622] [INFO] 昨日总收益: ¥0.00
[2025-07-08 17:24:11,623] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-08 17:24:11,625] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-08 17:25:40,705] [INFO] OpenCV库已加载，支持视频处理功能
[2025-07-08 17:25:40,744] [INFO] PIL库已加载，支持图像处理功能
[2025-07-08 17:25:42,175] [INFO] 自动化配置加载成功
[2025-07-08 17:25:42,175] [INFO] 自动化任务调度器初始化完成
[2025-07-08 17:25:42,180] [INFO] 心跳状态指示器初始化完成
[2025-07-08 17:25:42,244] [INFO] 跳过示例数据加载，等待真实账号数据
[2025-07-08 17:25:42,249] [ERROR] 连接表格信号时出错: 'AccountTab' object has no attribute 'on_cell_changed'
[2025-07-08 17:25:43,108] [INFO] 账号加载模式: 延迟加载
[2025-07-08 17:25:43,108] [INFO] 数据目录功能已移除
[2025-07-08 17:25:43,109] [INFO] 开始同步加载Cookie文件，路径: E:/软件共享/头条/头条
[2025-07-08 17:25:43,110] [INFO] 路径 E:/软件共享/头条/头条 下有 0 个JSON文件和 370 个TXT文件
[2025-07-08 17:25:43,110] [INFO] 调用account_loader.load_accounts_sync方法前
[2025-07-08 17:25:43,250] [INFO] 启动快速启动模式 - 后台加载账号数据
[2025-07-08 17:25:43,307] [DEBUG] 批量数据处理开始时内存使用: 255.41 MB，数据量: 67
[2025-07-08 17:25:43,308] [DEBUG] 更新账号 m50 的表格数据，是否有数据: True
[2025-07-08 17:25:43,308] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,308] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,309] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,309] [DEBUG] 开始查找账号 m50 对应的行索引
[2025-07-08 17:25:43,309] [DEBUG] 未找到账号 m50 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,309] [DEBUG] 更新账号 m63 的表格数据，是否有数据: True
[2025-07-08 17:25:43,309] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,310] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,310] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,310] [DEBUG] 开始查找账号 m63 对应的行索引
[2025-07-08 17:25:43,310] [DEBUG] 未找到账号 m63 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,310] [DEBUG] 更新账号 m53 的表格数据，是否有数据: True
[2025-07-08 17:25:43,311] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,311] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,311] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,311] [DEBUG] 开始查找账号 m53 对应的行索引
[2025-07-08 17:25:43,311] [DEBUG] 未找到账号 m53 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,312] [DEBUG] 更新账号 m54 的表格数据，是否有数据: True
[2025-07-08 17:25:43,312] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,313] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,313] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,313] [DEBUG] 开始查找账号 m54 对应的行索引
[2025-07-08 17:25:43,313] [DEBUG] 未找到账号 m54 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,314] [DEBUG] 更新账号 m64 的表格数据，是否有数据: True
[2025-07-08 17:25:43,314] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,314] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,314] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,315] [DEBUG] 开始查找账号 m64 对应的行索引
[2025-07-08 17:25:43,315] [DEBUG] 未找到账号 m64 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,315] [DEBUG] 更新账号 m65 的表格数据，是否有数据: True
[2025-07-08 17:25:43,316] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,316] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,316] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,316] [DEBUG] 开始查找账号 m65 对应的行索引
[2025-07-08 17:25:43,317] [DEBUG] 未找到账号 m65 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,317] [DEBUG] 更新账号 m66 的表格数据，是否有数据: True
[2025-07-08 17:25:43,317] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,318] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,318] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,318] [DEBUG] 开始查找账号 m66 对应的行索引
[2025-07-08 17:25:43,318] [DEBUG] 未找到账号 m66 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,318] [DEBUG] 更新账号 m56 的表格数据，是否有数据: True
[2025-07-08 17:25:43,319] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,319] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,319] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,320] [DEBUG] 开始查找账号 m56 对应的行索引
[2025-07-08 17:25:43,320] [DEBUG] 未找到账号 m56 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,320] [DEBUG] 更新账号 m59 的表格数据，是否有数据: True
[2025-07-08 17:25:43,320] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,320] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,321] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,321] [DEBUG] 开始查找账号 m59 对应的行索引
[2025-07-08 17:25:43,321] [DEBUG] 未找到账号 m59 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,321] [DEBUG] 更新账号 m6 的表格数据，是否有数据: True
[2025-07-08 17:25:43,321] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,322] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,322] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,322] [DEBUG] 开始查找账号 m6 对应的行索引
[2025-07-08 17:25:43,322] [DEBUG] 未找到账号 m6 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,322] [DEBUG] 更新账号 m60 的表格数据，是否有数据: True
[2025-07-08 17:25:43,323] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,323] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,323] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,324] [DEBUG] 开始查找账号 m60 对应的行索引
[2025-07-08 17:25:43,324] [DEBUG] 未找到账号 m60 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,324] [DEBUG] 更新账号 m62 的表格数据，是否有数据: True
[2025-07-08 17:25:43,324] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,324] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,325] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,325] [DEBUG] 开始查找账号 m62 对应的行索引
[2025-07-08 17:25:43,325] [DEBUG] 未找到账号 m62 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,326] [DEBUG] 更新账号 m67 的表格数据，是否有数据: True
[2025-07-08 17:25:43,326] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,326] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,326] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,326] [DEBUG] 开始查找账号 m67 对应的行索引
[2025-07-08 17:25:43,327] [DEBUG] 未找到账号 m67 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,327] [DEBUG] 更新账号 m7 的表格数据，是否有数据: True
[2025-07-08 17:25:43,327] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,328] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,328] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,328] [DEBUG] 开始查找账号 m7 对应的行索引
[2025-07-08 17:25:43,328] [DEBUG] 未找到账号 m7 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,328] [DEBUG] 更新账号 m9 的表格数据，是否有数据: True
[2025-07-08 17:25:43,329] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,329] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,329] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,329] [DEBUG] 开始查找账号 m9 对应的行索引
[2025-07-08 17:25:43,329] [DEBUG] 未找到账号 m9 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,330] [DEBUG] 更新账号 m8 的表格数据，是否有数据: True
[2025-07-08 17:25:43,330] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,330] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,330] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,330] [DEBUG] 开始查找账号 m8 对应的行索引
[2025-07-08 17:25:43,330] [DEBUG] 未找到账号 m8 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,330] [DEBUG] 更新账号 m10 的表格数据，是否有数据: True
[2025-07-08 17:25:43,331] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,331] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,331] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,332] [DEBUG] 开始查找账号 m10 对应的行索引
[2025-07-08 17:25:43,332] [DEBUG] 未找到账号 m10 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,332] [DEBUG] 更新账号 m11 的表格数据，是否有数据: True
[2025-07-08 17:25:43,332] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,332] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,332] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,333] [DEBUG] 开始查找账号 m11 对应的行索引
[2025-07-08 17:25:43,333] [DEBUG] 未找到账号 m11 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,333] [DEBUG] 更新账号 m1 的表格数据，是否有数据: True
[2025-07-08 17:25:43,333] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,334] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,334] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,334] [DEBUG] 开始查找账号 m1 对应的行索引
[2025-07-08 17:25:43,334] [DEBUG] 未找到账号 m1 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,334] [DEBUG] 更新账号 m12 的表格数据，是否有数据: True
[2025-07-08 17:25:43,334] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,335] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,335] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,335] [DEBUG] 开始查找账号 m12 对应的行索引
[2025-07-08 17:25:43,335] [DEBUG] 未找到账号 m12 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,335] [DEBUG] 更新账号 m13 的表格数据，是否有数据: True
[2025-07-08 17:25:43,336] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,336] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,336] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,337] [DEBUG] 开始查找账号 m13 对应的行索引
[2025-07-08 17:25:43,337] [DEBUG] 未找到账号 m13 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,337] [DEBUG] 更新账号 m14 的表格数据，是否有数据: True
[2025-07-08 17:25:43,337] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,337] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,337] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,338] [DEBUG] 开始查找账号 m14 对应的行索引
[2025-07-08 17:25:43,338] [DEBUG] 未找到账号 m14 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,338] [DEBUG] 更新账号 m16 的表格数据，是否有数据: True
[2025-07-08 17:25:43,338] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,338] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,339] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,339] [DEBUG] 开始查找账号 m16 对应的行索引
[2025-07-08 17:25:43,339] [DEBUG] 未找到账号 m16 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,339] [DEBUG] 更新账号 m15 的表格数据，是否有数据: True
[2025-07-08 17:25:43,339] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,339] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,340] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,340] [DEBUG] 开始查找账号 m15 对应的行索引
[2025-07-08 17:25:43,340] [DEBUG] 未找到账号 m15 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,340] [DEBUG] 更新账号 m17 的表格数据，是否有数据: True
[2025-07-08 17:25:43,340] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,341] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,341] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,341] [DEBUG] 开始查找账号 m17 对应的行索引
[2025-07-08 17:25:43,341] [DEBUG] 未找到账号 m17 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,342] [DEBUG] 更新账号 m18 的表格数据，是否有数据: True
[2025-07-08 17:25:43,342] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,342] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,342] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,342] [DEBUG] 开始查找账号 m18 对应的行索引
[2025-07-08 17:25:43,343] [DEBUG] 未找到账号 m18 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,343] [DEBUG] 更新账号 m19 的表格数据，是否有数据: True
[2025-07-08 17:25:43,343] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,343] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,343] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,343] [DEBUG] 开始查找账号 m19 对应的行索引
[2025-07-08 17:25:43,344] [DEBUG] 未找到账号 m19 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,344] [DEBUG] 更新账号 m2 的表格数据，是否有数据: True
[2025-07-08 17:25:43,344] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,344] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,345] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,345] [DEBUG] 开始查找账号 m2 对应的行索引
[2025-07-08 17:25:43,346] [DEBUG] 未找到账号 m2 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,346] [DEBUG] 更新账号 m20 的表格数据，是否有数据: True
[2025-07-08 17:25:43,346] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,346] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,346] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,346] [DEBUG] 开始查找账号 m20 对应的行索引
[2025-07-08 17:25:43,347] [DEBUG] 未找到账号 m20 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,347] [DEBUG] 更新账号 m21 的表格数据，是否有数据: True
[2025-07-08 17:25:43,347] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,347] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,347] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,347] [DEBUG] 开始查找账号 m21 对应的行索引
[2025-07-08 17:25:43,347] [DEBUG] 未找到账号 m21 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,348] [DEBUG] 更新账号 m22 的表格数据，是否有数据: True
[2025-07-08 17:25:43,348] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,348] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,348] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,348] [DEBUG] 开始查找账号 m22 对应的行索引
[2025-07-08 17:25:43,348] [DEBUG] 未找到账号 m22 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,349] [DEBUG] 更新账号 m23 的表格数据，是否有数据: True
[2025-07-08 17:25:43,349] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,349] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,349] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,349] [DEBUG] 开始查找账号 m23 对应的行索引
[2025-07-08 17:25:43,349] [DEBUG] 未找到账号 m23 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,350] [DEBUG] 更新账号 m24 的表格数据，是否有数据: True
[2025-07-08 17:25:43,350] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,350] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,351] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,351] [DEBUG] 开始查找账号 m24 对应的行索引
[2025-07-08 17:25:43,351] [DEBUG] 未找到账号 m24 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,351] [DEBUG] 更新账号 m25 的表格数据，是否有数据: True
[2025-07-08 17:25:43,351] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,351] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,352] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,352] [DEBUG] 开始查找账号 m25 对应的行索引
[2025-07-08 17:25:43,352] [DEBUG] 未找到账号 m25 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,352] [DEBUG] 更新账号 m26 的表格数据，是否有数据: True
[2025-07-08 17:25:43,352] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,352] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,353] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,353] [DEBUG] 开始查找账号 m26 对应的行索引
[2025-07-08 17:25:43,353] [DEBUG] 未找到账号 m26 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,353] [DEBUG] 更新账号 m27 的表格数据，是否有数据: True
[2025-07-08 17:25:43,353] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,354] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,354] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,354] [DEBUG] 开始查找账号 m27 对应的行索引
[2025-07-08 17:25:43,354] [DEBUG] 未找到账号 m27 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,354] [DEBUG] 更新账号 m28 的表格数据，是否有数据: True
[2025-07-08 17:25:43,354] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,355] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,355] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,355] [DEBUG] 开始查找账号 m28 对应的行索引
[2025-07-08 17:25:43,356] [DEBUG] 未找到账号 m28 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,356] [DEBUG] 更新账号 m30 的表格数据，是否有数据: True
[2025-07-08 17:25:43,356] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,356] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,356] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,357] [DEBUG] 开始查找账号 m30 对应的行索引
[2025-07-08 17:25:43,357] [DEBUG] 未找到账号 m30 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,357] [DEBUG] 更新账号 m3 的表格数据，是否有数据: True
[2025-07-08 17:25:43,357] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,357] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,358] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,358] [DEBUG] 开始查找账号 m3 对应的行索引
[2025-07-08 17:25:43,358] [DEBUG] 未找到账号 m3 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,358] [DEBUG] 更新账号 m29 的表格数据，是否有数据: True
[2025-07-08 17:25:43,358] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,359] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,359] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,360] [DEBUG] 开始查找账号 m29 对应的行索引
[2025-07-08 17:25:43,360] [DEBUG] 未找到账号 m29 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,360] [DEBUG] 更新账号 m31 的表格数据，是否有数据: True
[2025-07-08 17:25:43,360] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,360] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,360] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,360] [DEBUG] 开始查找账号 m31 对应的行索引
[2025-07-08 17:25:43,361] [DEBUG] 未找到账号 m31 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,361] [DEBUG] 更新账号 m32 的表格数据，是否有数据: True
[2025-07-08 17:25:43,361] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,361] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,361] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,362] [DEBUG] 开始查找账号 m32 对应的行索引
[2025-07-08 17:25:43,362] [DEBUG] 未找到账号 m32 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,362] [DEBUG] 更新账号 m33 的表格数据，是否有数据: True
[2025-07-08 17:25:43,362] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,362] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,362] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,362] [DEBUG] 开始查找账号 m33 对应的行索引
[2025-07-08 17:25:43,363] [DEBUG] 未找到账号 m33 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,363] [DEBUG] 更新账号 m34 的表格数据，是否有数据: True
[2025-07-08 17:25:43,364] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,364] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,364] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,364] [DEBUG] 开始查找账号 m34 对应的行索引
[2025-07-08 17:25:43,364] [DEBUG] 未找到账号 m34 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,365] [DEBUG] 更新账号 m35 的表格数据，是否有数据: True
[2025-07-08 17:25:43,365] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,365] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,365] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,365] [DEBUG] 开始查找账号 m35 对应的行索引
[2025-07-08 17:25:43,365] [DEBUG] 未找到账号 m35 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,366] [DEBUG] 更新账号 m36 的表格数据，是否有数据: True
[2025-07-08 17:25:43,366] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,366] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,366] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,366] [DEBUG] 开始查找账号 m36 对应的行索引
[2025-07-08 17:25:43,366] [DEBUG] 未找到账号 m36 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,367] [DEBUG] 更新账号 m37 的表格数据，是否有数据: True
[2025-07-08 17:25:43,367] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,367] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,367] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,367] [DEBUG] 开始查找账号 m37 对应的行索引
[2025-07-08 17:25:43,367] [DEBUG] 未找到账号 m37 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,368] [DEBUG] 更新账号 m38 的表格数据，是否有数据: True
[2025-07-08 17:25:43,368] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,368] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,368] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,368] [DEBUG] 开始查找账号 m38 对应的行索引
[2025-07-08 17:25:43,369] [DEBUG] 未找到账号 m38 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,369] [DEBUG] 更新账号 m39 的表格数据，是否有数据: True
[2025-07-08 17:25:43,369] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,370] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,370] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,370] [DEBUG] 开始查找账号 m39 对应的行索引
[2025-07-08 17:25:43,370] [DEBUG] 未找到账号 m39 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,370] [DEBUG] 更新账号 m4 的表格数据，是否有数据: True
[2025-07-08 17:25:43,370] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,370] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,371] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,371] [DEBUG] 开始查找账号 m4 对应的行索引
[2025-07-08 17:25:43,371] [DEBUG] 未找到账号 m4 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,371] [DEBUG] 更新账号 m40 的表格数据，是否有数据: True
[2025-07-08 17:25:43,371] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,372] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,372] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,372] [DEBUG] 开始查找账号 m40 对应的行索引
[2025-07-08 17:25:43,372] [DEBUG] 未找到账号 m40 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,372] [DEBUG] 更新账号 m41 的表格数据，是否有数据: True
[2025-07-08 17:25:43,372] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,373] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,373] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,373] [DEBUG] 开始查找账号 m41 对应的行索引
[2025-07-08 17:25:43,374] [DEBUG] 未找到账号 m41 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,374] [DEBUG] 更新账号 m42 的表格数据，是否有数据: True
[2025-07-08 17:25:43,374] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,374] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,374] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,375] [DEBUG] 开始查找账号 m42 对应的行索引
[2025-07-08 17:25:43,375] [DEBUG] 未找到账号 m42 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,375] [DEBUG] 更新账号 m43 的表格数据，是否有数据: True
[2025-07-08 17:25:43,375] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,375] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,376] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,376] [DEBUG] 开始查找账号 m43 对应的行索引
[2025-07-08 17:25:43,376] [DEBUG] 未找到账号 m43 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,376] [DEBUG] 更新账号 m44 的表格数据，是否有数据: True
[2025-07-08 17:25:43,376] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,376] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,377] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,377] [DEBUG] 开始查找账号 m44 对应的行索引
[2025-07-08 17:25:43,377] [DEBUG] 未找到账号 m44 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,377] [DEBUG] 更新账号 m45 的表格数据，是否有数据: True
[2025-07-08 17:25:43,378] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,378] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,378] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,378] [DEBUG] 开始查找账号 m45 对应的行索引
[2025-07-08 17:25:43,378] [DEBUG] 未找到账号 m45 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,378] [DEBUG] 更新账号 m46 的表格数据，是否有数据: True
[2025-07-08 17:25:43,379] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,379] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,379] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,379] [DEBUG] 开始查找账号 m46 对应的行索引
[2025-07-08 17:25:43,379] [DEBUG] 未找到账号 m46 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,379] [DEBUG] 更新账号 m47 的表格数据，是否有数据: True
[2025-07-08 17:25:43,380] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,380] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,380] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,380] [DEBUG] 开始查找账号 m47 对应的行索引
[2025-07-08 17:25:43,380] [DEBUG] 未找到账号 m47 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,380] [DEBUG] 更新账号 m48 的表格数据，是否有数据: True
[2025-07-08 17:25:43,381] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,381] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,381] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,381] [DEBUG] 开始查找账号 m48 对应的行索引
[2025-07-08 17:25:43,381] [DEBUG] 未找到账号 m48 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,382] [DEBUG] 更新账号 m49 的表格数据，是否有数据: True
[2025-07-08 17:25:43,382] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,382] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,382] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,382] [DEBUG] 开始查找账号 m49 对应的行索引
[2025-07-08 17:25:43,382] [DEBUG] 未找到账号 m49 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,383] [DEBUG] 更新账号 m5 的表格数据，是否有数据: True
[2025-07-08 17:25:43,383] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,384] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,384] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,384] [DEBUG] 开始查找账号 m5 对应的行索引
[2025-07-08 17:25:43,384] [DEBUG] 未找到账号 m5 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,384] [DEBUG] 更新账号 m51 的表格数据，是否有数据: True
[2025-07-08 17:25:43,384] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,385] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,385] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,385] [DEBUG] 开始查找账号 m51 对应的行索引
[2025-07-08 17:25:43,385] [DEBUG] 未找到账号 m51 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,386] [DEBUG] 更新账号 m52 的表格数据，是否有数据: True
[2025-07-08 17:25:43,386] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,386] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,386] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,386] [DEBUG] 开始查找账号 m52 对应的行索引
[2025-07-08 17:25:43,386] [DEBUG] 未找到账号 m52 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,387] [DEBUG] 更新账号 m55 的表格数据，是否有数据: True
[2025-07-08 17:25:43,387] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,387] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,387] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,387] [DEBUG] 开始查找账号 m55 对应的行索引
[2025-07-08 17:25:43,388] [DEBUG] 未找到账号 m55 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,388] [DEBUG] 更新账号 m57 的表格数据，是否有数据: True
[2025-07-08 17:25:43,389] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,389] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,389] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,389] [DEBUG] 开始查找账号 m57 对应的行索引
[2025-07-08 17:25:43,389] [DEBUG] 未找到账号 m57 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,390] [DEBUG] 更新账号 m58 的表格数据，是否有数据: True
[2025-07-08 17:25:43,390] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,390] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,390] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,390] [DEBUG] 开始查找账号 m58 对应的行索引
[2025-07-08 17:25:43,390] [DEBUG] 未找到账号 m58 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,391] [DEBUG] 更新账号 m61 的表格数据，是否有数据: True
[2025-07-08 17:25:43,391] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:25:43,391] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:25:43,391] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:25:43,391] [DEBUG] 开始查找账号 m61 对应的行索引
[2025-07-08 17:25:43,391] [DEBUG] 未找到账号 m61 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:25:43,429] [INFO] 用户取消后台加载
[2025-07-08 17:25:43,429] [INFO] 后台加载完成: 账号数据加载完成
[2025-07-08 17:25:43,636] [DEBUG] 批量数据处理完成后内存使用: 255.50 MB
[2025-07-08 17:25:43,637] [ERROR] 更新加载进度时出错: 'NoneType' object has no attribute 'setLabelText'
[2025-07-08 17:25:44,052] [INFO] account_loader.load_accounts_sync方法返回: success=True, accounts数量=370, error_msg=
[2025-07-08 17:25:44,052] [INFO] 同步加载账号数据完成，结果: True, 账号数量: 370, 错误信息: 
[2025-07-08 17:25:44,052] [INFO] 账号数据加载成功，共 370 个账号
[2025-07-08 17:25:44,053] [INFO] 账号加载完成回调开始时内存使用: 259.54 MB
[2025-07-08 17:25:44,053] [INFO] 已创建账号列表的浅拷贝，大小: 370
[2025-07-08 17:25:44,053] [INFO] 成功加载 370 个账号文件，延迟加载模式: True
[2025-07-08 17:25:44,081] [INFO] 账号列表大小: 370
[2025-07-08 17:25:44,081] [INFO] 第一个账号信息: {'file_path': 'E:/软件共享/头条/头条\\***********.txt', 'account_id': '***********', 'data': {'accountId': '***********', 'remark': '***********', 'cookies': {'xigua_csrf_token': 'Rh9tB8w72kFdjHWXzmwLx4FN', 'is_staff_user': 'false', 'sessionid_ss': '68c4cc3f1c5dedd10b37702f8cfc5581', 'uid_tt_ss': '4138e04b7027f10e81ee57334a3871f1', 'store-region': 'cn-gd', 'uid_tt': '4138e04b7027f10e81ee57334a3871f1', 'passport_auth_status': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'passport_mfa_token': 'CjfDS1ognyyaR5OXvuo1ZhOlSC8orpJykXqU3XrL0%2F4LK3qwDRucEFU8wl5EeoQDFgQXMDSnVv64GkoKPIA87d76RVvezzRe1eiXyd81F%2FQYdzX0iWRhqgsPoaSXZlpDutIfPN2NISNG6lXHe%2BhFo3Alo9axe2OZMhDY3OsNGPax0WwgAiIBA6K42yM%3D', 'passport_auth_status_ss': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'sid_tt': '68c4cc3f1c5dedd10b37702f8cfc5581', 'odin_tt': 'f681b2fee0f84dd926c09d58e132e2328c436e9fcf004f758697a032da7adc66d0d445a80508f8d675d1357603c3efe11f5237f59ac2ca2893e80a3e5da52dcf', 'sessionid': '68c4cc3f1c5dedd10b37702f8cfc5581', 'gfgarrcache_8005812e': '1', 'ssid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'toutiao_sso_user_ss': 'c2032ee116a4c329079d18ddc99f62c6', 'gfgarrcache_6a2703f1': '1', 'sid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'store-region-src': 'uid', 'sid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'toutiao_sso_user': 'c2032ee116a4c329079d18ddc99f62c6', 'sso_uid_tt_ss': '707d66e7d745b6ca510877ff49d6deca', 'xg_p_tos_token': '79aa2d6e8307f3027308bb8063808201', 'sso_uid_tt': '707d66e7d745b6ca510877ff49d6deca', 'sso_auth_status_ss': '99d6be0fe40fbeccd26dbd011f7e4345', 'ssid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'sid_guard': '68c4cc3f1c5dedd10b37702f8cfc5581%7C1741687410%7C5184002%7CSat%2C+10-May-2025+10%3A03%3A32+GMT', 'sso_auth_status': '99d6be0fe40fbeccd26dbd011f7e4345', 'passport_csrf_token': '3d631c058164d798d48475833f13446e', 'n_mh': '30A9sphpGJEPcXGYEGo8hiNGyEbb5VtQ_Ov5DGw0tbA', 'ttwid': '1%7C0FfHeK6FyRv027NKpnZinttVopHTQty8nSXP9P7wnNw%7C1741687388%7C33b1d18a6f3d000bd20fd4a1d0f5d6b37682c37752a91a6e1265423a4b1e9968', 'gfkadpd': '1231,25897', 'd_ticket': '0da2a81adc5bb557ff718c555c0a4801d76e7', 'passport_csrf_token_default': '3d631c058164d798d48475833f13446e', 'csrf_session_id': 'e1a99642f2b9648ef30c79ef589a852a', 's_v_web_id': 'verify_m84bp59m_dED7JGij_AkV2_4J3T_B268_83sqBwAURoyE'}}, 'file_type': '.txt'}
[2025-07-08 17:25:44,082] [INFO] 使用延迟加载模式，准备更新表格基本信息
[2025-07-08 17:25:44,084] [DEBUG] 已导入高级内存管理器
[2025-07-08 17:25:44,093] [INFO] 更新表格开始时内存使用: 259.60MB, 可用: 1941.47MB/16236.06MB
[2025-07-08 17:25:44,127] [INFO] 准备设置表格行数: 370
[2025-07-08 17:25:44,128] [INFO] 已设置表格行数: 370，当前表格行数: 370
[2025-07-08 17:25:44,128] [DEBUG] 处理账号批次: 1-50/370
[2025-07-08 17:25:49,446] [DEBUG] 处理账号批次: 51-100/370
[2025-07-08 17:25:49,708] [DEBUG] 处理账号批次: 101-150/370
[2025-07-08 17:25:49,966] [DEBUG] 处理账号批次: 151-200/370
[2025-07-08 17:25:50,225] [DEBUG] 处理账号批次: 201-250/370
[2025-07-08 17:25:50,500] [DEBUG] 处理账号批次: 251-300/370
[2025-07-08 17:25:50,775] [DEBUG] 处理账号批次: 301-350/370
[2025-07-08 17:25:50,990] [INFO] 开始计算收益统计，表格行数: 370, 列数: 19
[2025-07-08 17:25:50,992] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-08 17:25:50,992] [INFO] 七天总收益: ¥0.00
[2025-07-08 17:25:50,992] [INFO] 昨日总收益: ¥0.00
[2025-07-08 17:25:50,993] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-08 17:25:50,996] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-08 17:25:51,065] [DEBUG] 处理账号批次: 351-370/370
[2025-07-08 17:25:51,259] [INFO] 表格更新完成，耗时: 7.17秒，内存使用: 376.94MB
[2025-07-08 17:25:51,260] [INFO] 使用延迟加载模式，已更新表格基本信息
[2025-07-08 17:25:51,260] [INFO] 延迟加载模式下，表格行数: 370
[2025-07-08 17:25:51,314] [INFO] 不设置账号已加载标志，允许重复加载
[2025-07-08 17:25:51,315] [INFO] 开始计算收益统计，表格行数: 370, 列数: 19
[2025-07-08 17:25:51,316] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-08 17:25:51,316] [INFO] 七天总收益: ¥0.00
[2025-07-08 17:25:51,317] [INFO] 昨日总收益: ¥0.00
[2025-07-08 17:25:51,317] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-08 17:25:51,320] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-08 17:35:01,235] [INFO] OpenCV库已加载，支持视频处理功能
[2025-07-08 17:35:01,275] [INFO] PIL库已加载，支持图像处理功能
[2025-07-08 17:39:20,044] [INFO] OpenCV库已加载，支持视频处理功能
[2025-07-08 17:39:20,084] [INFO] PIL库已加载，支持图像处理功能
[2025-07-08 17:51:47,365] [INFO] OpenCV库已加载，支持视频处理功能
[2025-07-08 17:51:47,408] [INFO] PIL库已加载，支持图像处理功能
[2025-07-08 17:53:00,129] [INFO] OpenCV库已加载，支持视频处理功能
[2025-07-08 17:53:00,169] [INFO] PIL库已加载，支持图像处理功能
[2025-07-08 17:53:49,785] [INFO] OpenCV库已加载，支持视频处理功能
[2025-07-08 17:53:49,824] [INFO] PIL库已加载，支持图像处理功能
[2025-07-08 17:54:26,574] [INFO] OpenCV库已加载，支持视频处理功能
[2025-07-08 17:54:26,613] [INFO] PIL库已加载，支持图像处理功能
[2025-07-08 17:55:28,666] [INFO] OpenCV库已加载，支持视频处理功能
[2025-07-08 17:55:28,705] [INFO] PIL库已加载，支持图像处理功能
[2025-07-08 17:58:26,624] [INFO] OpenCV库已加载，支持视频处理功能
[2025-07-08 17:58:26,664] [INFO] PIL库已加载，支持图像处理功能
[2025-07-08 17:59:07,193] [INFO] 自动化配置加载成功
[2025-07-08 17:59:07,193] [INFO] 自动化任务调度器初始化完成
[2025-07-08 17:59:07,198] [INFO] 心跳状态指示器初始化完成
[2025-07-08 17:59:07,228] [INFO] 跳过示例数据加载，等待真实账号数据
[2025-07-08 17:59:07,234] [ERROR] 连接表格信号时出错: 'AccountTab' object has no attribute 'on_cell_changed'
[2025-07-08 17:59:08,014] [INFO] 账号加载模式: 延迟加载
[2025-07-08 17:59:08,015] [INFO] 数据目录功能已移除
[2025-07-08 17:59:08,016] [INFO] 开始同步加载Cookie文件，路径: E:/软件共享/头条/头条
[2025-07-08 17:59:08,017] [INFO] 路径 E:/软件共享/头条/头条 下有 0 个JSON文件和 370 个TXT文件
[2025-07-08 17:59:08,017] [INFO] 调用account_loader.load_accounts_sync方法前
[2025-07-08 17:59:08,234] [INFO] 启动快速启动模式 - 后台加载账号数据
[2025-07-08 17:59:08,279] [DEBUG] 批量数据处理开始时内存使用: 272.93 MB，数据量: 67
[2025-07-08 17:59:08,280] [DEBUG] 更新账号 m50 的表格数据，是否有数据: True
[2025-07-08 17:59:08,280] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,280] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,281] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,281] [DEBUG] 开始查找账号 m50 对应的行索引
[2025-07-08 17:59:08,281] [DEBUG] 未找到账号 m50 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,281] [DEBUG] 更新账号 m63 的表格数据，是否有数据: True
[2025-07-08 17:59:08,281] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,282] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,282] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,282] [DEBUG] 开始查找账号 m63 对应的行索引
[2025-07-08 17:59:08,282] [DEBUG] 未找到账号 m63 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,282] [DEBUG] 更新账号 m53 的表格数据，是否有数据: True
[2025-07-08 17:59:08,282] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,282] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,283] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,283] [DEBUG] 开始查找账号 m53 对应的行索引
[2025-07-08 17:59:08,283] [DEBUG] 未找到账号 m53 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,284] [DEBUG] 更新账号 m54 的表格数据，是否有数据: True
[2025-07-08 17:59:08,284] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,284] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,284] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,284] [DEBUG] 开始查找账号 m54 对应的行索引
[2025-07-08 17:59:08,284] [DEBUG] 未找到账号 m54 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,285] [DEBUG] 更新账号 m64 的表格数据，是否有数据: True
[2025-07-08 17:59:08,285] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,285] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,285] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,285] [DEBUG] 开始查找账号 m64 对应的行索引
[2025-07-08 17:59:08,286] [DEBUG] 未找到账号 m64 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,286] [DEBUG] 更新账号 m65 的表格数据，是否有数据: True
[2025-07-08 17:59:08,286] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,286] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,286] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,287] [DEBUG] 开始查找账号 m65 对应的行索引
[2025-07-08 17:59:08,287] [DEBUG] 未找到账号 m65 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,287] [DEBUG] 更新账号 m66 的表格数据，是否有数据: True
[2025-07-08 17:59:08,287] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,287] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,288] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,288] [DEBUG] 开始查找账号 m66 对应的行索引
[2025-07-08 17:59:08,288] [DEBUG] 未找到账号 m66 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,289] [DEBUG] 更新账号 m56 的表格数据，是否有数据: True
[2025-07-08 17:59:08,289] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,289] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,289] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,289] [DEBUG] 开始查找账号 m56 对应的行索引
[2025-07-08 17:59:08,289] [DEBUG] 未找到账号 m56 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,290] [DEBUG] 更新账号 m59 的表格数据，是否有数据: True
[2025-07-08 17:59:08,290] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,290] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,290] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,290] [DEBUG] 开始查找账号 m59 对应的行索引
[2025-07-08 17:59:08,291] [DEBUG] 未找到账号 m59 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,291] [DEBUG] 更新账号 m6 的表格数据，是否有数据: True
[2025-07-08 17:59:08,291] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,291] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,291] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,292] [DEBUG] 开始查找账号 m6 对应的行索引
[2025-07-08 17:59:08,292] [DEBUG] 未找到账号 m6 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,292] [DEBUG] 更新账号 m60 的表格数据，是否有数据: True
[2025-07-08 17:59:08,292] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,292] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,292] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,293] [DEBUG] 开始查找账号 m60 对应的行索引
[2025-07-08 17:59:08,293] [DEBUG] 未找到账号 m60 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,293] [DEBUG] 更新账号 m62 的表格数据，是否有数据: True
[2025-07-08 17:59:08,294] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,294] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,294] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,294] [DEBUG] 开始查找账号 m62 对应的行索引
[2025-07-08 17:59:08,294] [DEBUG] 未找到账号 m62 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,294] [DEBUG] 更新账号 m67 的表格数据，是否有数据: True
[2025-07-08 17:59:08,295] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,295] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,295] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,295] [DEBUG] 开始查找账号 m67 对应的行索引
[2025-07-08 17:59:08,295] [DEBUG] 未找到账号 m67 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,295] [DEBUG] 更新账号 m7 的表格数据，是否有数据: True
[2025-07-08 17:59:08,296] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,296] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,296] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,296] [DEBUG] 开始查找账号 m7 对应的行索引
[2025-07-08 17:59:08,296] [DEBUG] 未找到账号 m7 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,297] [DEBUG] 更新账号 m9 的表格数据，是否有数据: True
[2025-07-08 17:59:08,297] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,297] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,298] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,298] [DEBUG] 开始查找账号 m9 对应的行索引
[2025-07-08 17:59:08,298] [DEBUG] 未找到账号 m9 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,298] [DEBUG] 更新账号 m8 的表格数据，是否有数据: True
[2025-07-08 17:59:08,298] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,298] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,299] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,299] [DEBUG] 开始查找账号 m8 对应的行索引
[2025-07-08 17:59:08,299] [DEBUG] 未找到账号 m8 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,299] [DEBUG] 更新账号 m10 的表格数据，是否有数据: True
[2025-07-08 17:59:08,299] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,299] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,300] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,300] [DEBUG] 开始查找账号 m10 对应的行索引
[2025-07-08 17:59:08,300] [DEBUG] 未找到账号 m10 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,300] [DEBUG] 更新账号 m11 的表格数据，是否有数据: True
[2025-07-08 17:59:08,300] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,300] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,301] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,301] [DEBUG] 开始查找账号 m11 对应的行索引
[2025-07-08 17:59:08,301] [DEBUG] 未找到账号 m11 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,301] [DEBUG] 更新账号 m1 的表格数据，是否有数据: True
[2025-07-08 17:59:08,301] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,301] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,302] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,302] [DEBUG] 开始查找账号 m1 对应的行索引
[2025-07-08 17:59:08,302] [DEBUG] 未找到账号 m1 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,303] [DEBUG] 更新账号 m12 的表格数据，是否有数据: True
[2025-07-08 17:59:08,303] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,303] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,303] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,303] [DEBUG] 开始查找账号 m12 对应的行索引
[2025-07-08 17:59:08,304] [DEBUG] 未找到账号 m12 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,304] [DEBUG] 更新账号 m13 的表格数据，是否有数据: True
[2025-07-08 17:59:08,304] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,304] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,304] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,304] [DEBUG] 开始查找账号 m13 对应的行索引
[2025-07-08 17:59:08,305] [DEBUG] 未找到账号 m13 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,305] [DEBUG] 更新账号 m14 的表格数据，是否有数据: True
[2025-07-08 17:59:08,305] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,305] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,305] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,306] [DEBUG] 开始查找账号 m14 对应的行索引
[2025-07-08 17:59:08,306] [DEBUG] 未找到账号 m14 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,306] [DEBUG] 更新账号 m16 的表格数据，是否有数据: True
[2025-07-08 17:59:08,307] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,307] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,307] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,307] [DEBUG] 开始查找账号 m16 对应的行索引
[2025-07-08 17:59:08,307] [DEBUG] 未找到账号 m16 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,308] [DEBUG] 更新账号 m15 的表格数据，是否有数据: True
[2025-07-08 17:59:08,308] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,308] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,308] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,308] [DEBUG] 开始查找账号 m15 对应的行索引
[2025-07-08 17:59:08,308] [DEBUG] 未找到账号 m15 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,308] [DEBUG] 更新账号 m17 的表格数据，是否有数据: True
[2025-07-08 17:59:08,309] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,309] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,309] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,309] [DEBUG] 开始查找账号 m17 对应的行索引
[2025-07-08 17:59:08,309] [DEBUG] 未找到账号 m17 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,310] [DEBUG] 更新账号 m18 的表格数据，是否有数据: True
[2025-07-08 17:59:08,310] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,310] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,310] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,310] [DEBUG] 开始查找账号 m18 对应的行索引
[2025-07-08 17:59:08,311] [DEBUG] 未找到账号 m18 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,312] [DEBUG] 更新账号 m19 的表格数据，是否有数据: True
[2025-07-08 17:59:08,312] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,312] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,312] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,312] [DEBUG] 开始查找账号 m19 对应的行索引
[2025-07-08 17:59:08,312] [DEBUG] 未找到账号 m19 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,313] [DEBUG] 更新账号 m2 的表格数据，是否有数据: True
[2025-07-08 17:59:08,313] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,313] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,313] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,313] [DEBUG] 开始查找账号 m2 对应的行索引
[2025-07-08 17:59:08,313] [DEBUG] 未找到账号 m2 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,314] [DEBUG] 更新账号 m20 的表格数据，是否有数据: True
[2025-07-08 17:59:08,314] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,314] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,314] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,314] [DEBUG] 开始查找账号 m20 对应的行索引
[2025-07-08 17:59:08,314] [DEBUG] 未找到账号 m20 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,315] [DEBUG] 更新账号 m21 的表格数据，是否有数据: True
[2025-07-08 17:59:08,315] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,315] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,316] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,316] [DEBUG] 开始查找账号 m21 对应的行索引
[2025-07-08 17:59:08,316] [DEBUG] 未找到账号 m21 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,316] [DEBUG] 更新账号 m22 的表格数据，是否有数据: True
[2025-07-08 17:59:08,316] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,316] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,317] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,317] [DEBUG] 开始查找账号 m22 对应的行索引
[2025-07-08 17:59:08,317] [DEBUG] 未找到账号 m22 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,317] [DEBUG] 更新账号 m23 的表格数据，是否有数据: True
[2025-07-08 17:59:08,317] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,318] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,318] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,318] [DEBUG] 开始查找账号 m23 对应的行索引
[2025-07-08 17:59:08,318] [DEBUG] 未找到账号 m23 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,318] [DEBUG] 更新账号 m24 的表格数据，是否有数据: True
[2025-07-08 17:59:08,318] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,319] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,319] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,319] [DEBUG] 开始查找账号 m24 对应的行索引
[2025-07-08 17:59:08,319] [DEBUG] 未找到账号 m24 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,319] [DEBUG] 更新账号 m25 的表格数据，是否有数据: True
[2025-07-08 17:59:08,319] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,320] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,320] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,321] [DEBUG] 开始查找账号 m25 对应的行索引
[2025-07-08 17:59:08,321] [DEBUG] 未找到账号 m25 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,321] [DEBUG] 更新账号 m26 的表格数据，是否有数据: True
[2025-07-08 17:59:08,321] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,321] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,322] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,322] [DEBUG] 开始查找账号 m26 对应的行索引
[2025-07-08 17:59:08,322] [DEBUG] 未找到账号 m26 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,322] [DEBUG] 更新账号 m27 的表格数据，是否有数据: True
[2025-07-08 17:59:08,322] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,322] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,323] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,323] [DEBUG] 开始查找账号 m27 对应的行索引
[2025-07-08 17:59:08,323] [DEBUG] 未找到账号 m27 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,323] [DEBUG] 更新账号 m28 的表格数据，是否有数据: True
[2025-07-08 17:59:08,323] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,323] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,324] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,324] [DEBUG] 开始查找账号 m28 对应的行索引
[2025-07-08 17:59:08,324] [DEBUG] 未找到账号 m28 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,325] [DEBUG] 更新账号 m30 的表格数据，是否有数据: True
[2025-07-08 17:59:08,325] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,325] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,325] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,325] [DEBUG] 开始查找账号 m30 对应的行索引
[2025-07-08 17:59:08,325] [DEBUG] 未找到账号 m30 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,326] [DEBUG] 更新账号 m3 的表格数据，是否有数据: True
[2025-07-08 17:59:08,326] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,326] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,326] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,326] [DEBUG] 开始查找账号 m3 对应的行索引
[2025-07-08 17:59:08,327] [DEBUG] 未找到账号 m3 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,327] [DEBUG] 更新账号 m29 的表格数据，是否有数据: True
[2025-07-08 17:59:08,327] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,327] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,327] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,327] [DEBUG] 开始查找账号 m29 对应的行索引
[2025-07-08 17:59:08,327] [DEBUG] 未找到账号 m29 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,328] [DEBUG] 更新账号 m31 的表格数据，是否有数据: True
[2025-07-08 17:59:08,328] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,328] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,329] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,329] [DEBUG] 开始查找账号 m31 对应的行索引
[2025-07-08 17:59:08,329] [DEBUG] 未找到账号 m31 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,329] [DEBUG] 更新账号 m32 的表格数据，是否有数据: True
[2025-07-08 17:59:08,329] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,329] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,330] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,330] [DEBUG] 开始查找账号 m32 对应的行索引
[2025-07-08 17:59:08,330] [DEBUG] 未找到账号 m32 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,330] [DEBUG] 更新账号 m33 的表格数据，是否有数据: True
[2025-07-08 17:59:08,330] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,331] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,331] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,331] [DEBUG] 开始查找账号 m33 对应的行索引
[2025-07-08 17:59:08,331] [DEBUG] 未找到账号 m33 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,331] [DEBUG] 更新账号 m34 的表格数据，是否有数据: True
[2025-07-08 17:59:08,331] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,332] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,332] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,332] [DEBUG] 开始查找账号 m34 对应的行索引
[2025-07-08 17:59:08,332] [DEBUG] 未找到账号 m34 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,332] [DEBUG] 更新账号 m35 的表格数据，是否有数据: True
[2025-07-08 17:59:08,332] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,333] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,333] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,333] [DEBUG] 开始查找账号 m35 对应的行索引
[2025-07-08 17:59:08,333] [DEBUG] 未找到账号 m35 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,333] [DEBUG] 更新账号 m36 的表格数据，是否有数据: True
[2025-07-08 17:59:08,333] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,334] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,334] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,334] [DEBUG] 开始查找账号 m36 对应的行索引
[2025-07-08 17:59:08,335] [DEBUG] 未找到账号 m36 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,335] [DEBUG] 更新账号 m37 的表格数据，是否有数据: True
[2025-07-08 17:59:08,335] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,335] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,335] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,335] [DEBUG] 开始查找账号 m37 对应的行索引
[2025-07-08 17:59:08,336] [DEBUG] 未找到账号 m37 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,336] [DEBUG] 更新账号 m38 的表格数据，是否有数据: True
[2025-07-08 17:59:08,336] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,336] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,336] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,337] [DEBUG] 开始查找账号 m38 对应的行索引
[2025-07-08 17:59:08,337] [DEBUG] 未找到账号 m38 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,337] [DEBUG] 更新账号 m39 的表格数据，是否有数据: True
[2025-07-08 17:59:08,337] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,337] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,337] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,338] [DEBUG] 开始查找账号 m39 对应的行索引
[2025-07-08 17:59:08,338] [DEBUG] 未找到账号 m39 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,338] [DEBUG] 更新账号 m4 的表格数据，是否有数据: True
[2025-07-08 17:59:08,339] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,339] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,339] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,339] [DEBUG] 开始查找账号 m4 对应的行索引
[2025-07-08 17:59:08,339] [DEBUG] 未找到账号 m4 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,339] [DEBUG] 更新账号 m40 的表格数据，是否有数据: True
[2025-07-08 17:59:08,339] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,340] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,340] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,340] [DEBUG] 开始查找账号 m40 对应的行索引
[2025-07-08 17:59:08,340] [DEBUG] 未找到账号 m40 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,340] [DEBUG] 更新账号 m41 的表格数据，是否有数据: True
[2025-07-08 17:59:08,341] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,341] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,341] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,341] [DEBUG] 开始查找账号 m41 对应的行索引
[2025-07-08 17:59:08,342] [DEBUG] 未找到账号 m41 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,342] [DEBUG] 更新账号 m42 的表格数据，是否有数据: True
[2025-07-08 17:59:08,342] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,343] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,343] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,343] [DEBUG] 开始查找账号 m42 对应的行索引
[2025-07-08 17:59:08,343] [DEBUG] 未找到账号 m42 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,343] [DEBUG] 更新账号 m43 的表格数据，是否有数据: True
[2025-07-08 17:59:08,343] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,344] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,344] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,344] [DEBUG] 开始查找账号 m43 对应的行索引
[2025-07-08 17:59:08,344] [DEBUG] 未找到账号 m43 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,344] [DEBUG] 更新账号 m44 的表格数据，是否有数据: True
[2025-07-08 17:59:08,344] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,345] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,345] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,345] [DEBUG] 开始查找账号 m44 对应的行索引
[2025-07-08 17:59:08,345] [DEBUG] 未找到账号 m44 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,345] [DEBUG] 更新账号 m45 的表格数据，是否有数据: True
[2025-07-08 17:59:08,345] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,346] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,346] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,347] [DEBUG] 开始查找账号 m45 对应的行索引
[2025-07-08 17:59:08,347] [DEBUG] 未找到账号 m45 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,347] [DEBUG] 更新账号 m46 的表格数据，是否有数据: True
[2025-07-08 17:59:08,347] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,347] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,347] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,348] [DEBUG] 开始查找账号 m46 对应的行索引
[2025-07-08 17:59:08,348] [DEBUG] 未找到账号 m46 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,348] [DEBUG] 更新账号 m47 的表格数据，是否有数据: True
[2025-07-08 17:59:08,348] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,348] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,348] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,349] [DEBUG] 开始查找账号 m47 对应的行索引
[2025-07-08 17:59:08,349] [DEBUG] 未找到账号 m47 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,349] [DEBUG] 更新账号 m48 的表格数据，是否有数据: True
[2025-07-08 17:59:08,349] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,349] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,349] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,350] [DEBUG] 开始查找账号 m48 对应的行索引
[2025-07-08 17:59:08,350] [DEBUG] 未找到账号 m48 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,350] [DEBUG] 更新账号 m49 的表格数据，是否有数据: True
[2025-07-08 17:59:08,350] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,350] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,351] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,351] [DEBUG] 开始查找账号 m49 对应的行索引
[2025-07-08 17:59:08,351] [DEBUG] 未找到账号 m49 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,352] [DEBUG] 更新账号 m5 的表格数据，是否有数据: True
[2025-07-08 17:59:08,352] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,352] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,352] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,352] [DEBUG] 开始查找账号 m5 对应的行索引
[2025-07-08 17:59:08,352] [DEBUG] 未找到账号 m5 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,353] [DEBUG] 更新账号 m51 的表格数据，是否有数据: True
[2025-07-08 17:59:08,353] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,353] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,353] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,353] [DEBUG] 开始查找账号 m51 对应的行索引
[2025-07-08 17:59:08,353] [DEBUG] 未找到账号 m51 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,354] [DEBUG] 更新账号 m52 的表格数据，是否有数据: True
[2025-07-08 17:59:08,354] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,354] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,354] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,354] [DEBUG] 开始查找账号 m52 对应的行索引
[2025-07-08 17:59:08,354] [DEBUG] 未找到账号 m52 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,354] [DEBUG] 更新账号 m55 的表格数据，是否有数据: True
[2025-07-08 17:59:08,355] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,355] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,355] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,355] [DEBUG] 开始查找账号 m55 对应的行索引
[2025-07-08 17:59:08,355] [DEBUG] 未找到账号 m55 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,355] [DEBUG] 更新账号 m57 的表格数据，是否有数据: True
[2025-07-08 17:59:08,356] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,356] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,357] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,357] [DEBUG] 开始查找账号 m57 对应的行索引
[2025-07-08 17:59:08,357] [DEBUG] 未找到账号 m57 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,357] [DEBUG] 更新账号 m58 的表格数据，是否有数据: True
[2025-07-08 17:59:08,357] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,358] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,358] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,358] [DEBUG] 开始查找账号 m58 对应的行索引
[2025-07-08 17:59:08,358] [DEBUG] 未找到账号 m58 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,358] [DEBUG] 更新账号 m61 的表格数据，是否有数据: True
[2025-07-08 17:59:08,358] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-08 17:59:08,359] [DEBUG] 表格结构: 0行 x 19列
[2025-07-08 17:59:08,359] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-08 17:59:08,359] [DEBUG] 开始查找账号 m61 对应的行索引
[2025-07-08 17:59:08,359] [DEBUG] 未找到账号 m61 对应的行索引，尝试记录所有行的账号ID
[2025-07-08 17:59:08,398] [INFO] 用户取消后台加载
[2025-07-08 17:59:08,398] [INFO] 后台加载完成: 账号数据加载完成
[2025-07-08 17:59:08,608] [DEBUG] 批量数据处理完成后内存使用: 272.97 MB
[2025-07-08 17:59:08,610] [ERROR] 更新加载进度时出错: 'NoneType' object has no attribute 'setLabelText'
[2025-07-08 17:59:08,881] [INFO] account_loader.load_accounts_sync方法返回: success=True, accounts数量=370, error_msg=
[2025-07-08 17:59:08,881] [INFO] 同步加载账号数据完成，结果: True, 账号数量: 370, 错误信息: 
[2025-07-08 17:59:08,881] [INFO] 账号数据加载成功，共 370 个账号
[2025-07-08 17:59:08,881] [INFO] 账号加载完成回调开始时内存使用: 275.80 MB
[2025-07-08 17:59:08,881] [INFO] 已创建账号列表的浅拷贝，大小: 370
[2025-07-08 17:59:08,882] [INFO] 成功加载 370 个账号文件，延迟加载模式: True
[2025-07-08 17:59:08,908] [INFO] 账号列表大小: 370
[2025-07-08 17:59:08,908] [INFO] 第一个账号信息: {'file_path': 'E:/软件共享/头条/头条\\***********.txt', 'account_id': '***********', 'data': {'accountId': '***********', 'remark': '***********', 'cookies': {'xigua_csrf_token': 'Rh9tB8w72kFdjHWXzmwLx4FN', 'is_staff_user': 'false', 'sessionid_ss': '68c4cc3f1c5dedd10b37702f8cfc5581', 'uid_tt_ss': '4138e04b7027f10e81ee57334a3871f1', 'store-region': 'cn-gd', 'uid_tt': '4138e04b7027f10e81ee57334a3871f1', 'passport_auth_status': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'passport_mfa_token': 'CjfDS1ognyyaR5OXvuo1ZhOlSC8orpJykXqU3XrL0%2F4LK3qwDRucEFU8wl5EeoQDFgQXMDSnVv64GkoKPIA87d76RVvezzRe1eiXyd81F%2FQYdzX0iWRhqgsPoaSXZlpDutIfPN2NISNG6lXHe%2BhFo3Alo9axe2OZMhDY3OsNGPax0WwgAiIBA6K42yM%3D', 'passport_auth_status_ss': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'sid_tt': '68c4cc3f1c5dedd10b37702f8cfc5581', 'odin_tt': 'f681b2fee0f84dd926c09d58e132e2328c436e9fcf004f758697a032da7adc66d0d445a80508f8d675d1357603c3efe11f5237f59ac2ca2893e80a3e5da52dcf', 'sessionid': '68c4cc3f1c5dedd10b37702f8cfc5581', 'gfgarrcache_8005812e': '1', 'ssid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'toutiao_sso_user_ss': 'c2032ee116a4c329079d18ddc99f62c6', 'gfgarrcache_6a2703f1': '1', 'sid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'store-region-src': 'uid', 'sid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'toutiao_sso_user': 'c2032ee116a4c329079d18ddc99f62c6', 'sso_uid_tt_ss': '707d66e7d745b6ca510877ff49d6deca', 'xg_p_tos_token': '79aa2d6e8307f3027308bb8063808201', 'sso_uid_tt': '707d66e7d745b6ca510877ff49d6deca', 'sso_auth_status_ss': '99d6be0fe40fbeccd26dbd011f7e4345', 'ssid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'sid_guard': '68c4cc3f1c5dedd10b37702f8cfc5581%7C1741687410%7C5184002%7CSat%2C+10-May-2025+10%3A03%3A32+GMT', 'sso_auth_status': '99d6be0fe40fbeccd26dbd011f7e4345', 'passport_csrf_token': '3d631c058164d798d48475833f13446e', 'n_mh': '30A9sphpGJEPcXGYEGo8hiNGyEbb5VtQ_Ov5DGw0tbA', 'ttwid': '1%7C0FfHeK6FyRv027NKpnZinttVopHTQty8nSXP9P7wnNw%7C1741687388%7C33b1d18a6f3d000bd20fd4a1d0f5d6b37682c37752a91a6e1265423a4b1e9968', 'gfkadpd': '1231,25897', 'd_ticket': '0da2a81adc5bb557ff718c555c0a4801d76e7', 'passport_csrf_token_default': '3d631c058164d798d48475833f13446e', 'csrf_session_id': 'e1a99642f2b9648ef30c79ef589a852a', 's_v_web_id': 'verify_m84bp59m_dED7JGij_AkV2_4J3T_B268_83sqBwAURoyE'}}, 'file_type': '.txt'}
[2025-07-08 17:59:08,909] [INFO] 使用延迟加载模式，准备更新表格基本信息
[2025-07-08 17:59:08,912] [DEBUG] 已导入高级内存管理器
[2025-07-08 17:59:08,920] [INFO] 更新表格开始时内存使用: 275.81MB, 可用: 3664.55MB/16236.06MB
[2025-07-08 17:59:08,949] [INFO] 准备设置表格行数: 370
[2025-07-08 17:59:08,949] [INFO] 已设置表格行数: 370，当前表格行数: 370
[2025-07-08 17:59:08,949] [DEBUG] 处理账号批次: 1-50/370
[2025-07-08 17:59:10,198] [DEBUG] 处理账号批次: 51-100/370
[2025-07-08 17:59:10,457] [DEBUG] 处理账号批次: 101-150/370
[2025-07-08 17:59:10,717] [DEBUG] 处理账号批次: 151-200/370
[2025-07-08 17:59:10,977] [DEBUG] 处理账号批次: 201-250/370
[2025-07-08 17:59:11,234] [DEBUG] 处理账号批次: 251-300/370
[2025-07-08 17:59:11,510] [DEBUG] 处理账号批次: 301-350/370
[2025-07-08 17:59:11,785] [DEBUG] 处理账号批次: 351-370/370
[2025-07-08 17:59:11,972] [INFO] 表格更新完成，耗时: 3.05秒，内存使用: 392.72MB
[2025-07-08 17:59:11,972] [INFO] 使用延迟加载模式，已更新表格基本信息
[2025-07-08 17:59:11,973] [INFO] 延迟加载模式下，表格行数: 370
[2025-07-08 17:59:12,024] [INFO] 不设置账号已加载标志，允许重复加载
[2025-07-08 17:59:12,024] [INFO] 开始计算收益统计，表格行数: 370, 列数: 19
[2025-07-08 17:59:12,026] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-08 17:59:12,027] [INFO] 七天总收益: ¥0.00
[2025-07-08 17:59:12,027] [INFO] 昨日总收益: ¥0.00
[2025-07-08 17:59:12,027] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-08 17:59:12,030] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-08 17:59:15,913] [INFO] 开始计算收益统计，表格行数: 370, 列数: 19
[2025-07-08 17:59:15,915] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-08 17:59:15,916] [INFO] 七天总收益: ¥0.00
[2025-07-08 17:59:15,916] [INFO] 昨日总收益: ¥0.00
[2025-07-08 17:59:15,916] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-08 17:59:15,918] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-08 17:59:17,423] [INFO] 正在停止所有活动线程...
[2025-07-08 17:59:17,423] [INFO] 成功停止了 0 个线程
