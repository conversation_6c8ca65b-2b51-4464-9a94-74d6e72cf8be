发生时间: 2025-06-09 02:35:39
异常类型: RuntimeError
异常信息: wrapped C/C++ object of type QThread has been deleted

堆栈跟踪:
  File "E:\toutiaoyuanma1\tou0\app\utils\piliang_cunggao.py", line 1963, in check_completion
    active_threads = sum(1 for thread in self.active_threads if thread.isRunning())
  File "E:\toutiaoyuanma1\tou0\app\utils\piliang_cunggao.py", line 1963, in <genexpr>
    active_threads = sum(1 for thread in self.active_threads if thread.isRunning())


系统信息:
操作系统: Windows-10-10.0.26100-SP0
Python版本: 3.9.13
内存使用: 335.19 MB
