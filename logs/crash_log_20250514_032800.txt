发生时间: 2025-05-14 03:28:00
异常类型: RuntimeError
异常信息: wrapped C/C++ object of type BatchCollectionWorker has been deleted

堆栈跟踪:
  File "E:\toutiaoyuanma1\tou2\app\utils\batch_account_data_collector.py", line 369, in <lambda>
    timer.timeout.connect(lambda: self.handle_account_timeout(account_id, future))
  File "E:\toutiaoyuanma1\tou2\app\utils\batch_account_data_collector.py", line 376, in handle_account_timeout
    self.detailed_status_signal.emit(account_id, "采集超时", "error")


系统信息:
操作系统: Windows-10-10.0.26100-SP0
Python版本: 3.9.13
内存使用: 350.58 MB
