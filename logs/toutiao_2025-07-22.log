[2025-07-22 01:09:26,426] [INFO] OpenCV库已加载，支持视频处理功能
[2025-07-22 01:09:26,537] [INFO] PIL库已加载，支持图像处理功能
[2025-07-22 01:09:35,634] [INFO] 自动化配置加载成功
[2025-07-22 01:09:35,634] [INFO] 自动化任务调度器初始化完成
[2025-07-22 01:09:35,653] [INFO] 心跳状态指示器初始化完成
[2025-07-22 01:09:35,727] [INFO] 跳过示例数据加载，等待真实账号数据
[2025-07-22 01:09:35,735] [ERROR] 连接表格信号时出错: 'AccountTab' object has no attribute 'on_cell_changed'
[2025-07-22 01:09:36,709] [INFO] 账号加载模式: 延迟加载
[2025-07-22 01:09:36,709] [INFO] 数据目录功能已移除
[2025-07-22 01:09:36,709] [INFO] 开始同步加载Cookie文件，路径: E:/软件共享/账号合并
[2025-07-22 01:09:36,713] [INFO] 路径 E:/软件共享/账号合并 下有 0 个JSON文件和 305 个TXT文件
[2025-07-22 01:09:36,713] [INFO] 调用account_loader.load_accounts_sync方法前
[2025-07-22 01:09:36,753] [INFO] 启动快速启动模式 - 后台加载账号数据
[2025-07-22 01:09:36,807] [DEBUG] 批量数据处理开始时内存使用: 253.48 MB，数据量: 95
[2025-07-22 01:09:36,808] [DEBUG] 更新账号 m172 的表格数据，是否有数据: True
[2025-07-22 01:09:36,808] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,808] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,809] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,809] [DEBUG] 开始查找账号 m172 对应的行索引
[2025-07-22 01:09:36,809] [DEBUG] 未找到账号 m172 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,809] [DEBUG] 更新账号 m174 的表格数据，是否有数据: True
[2025-07-22 01:09:36,810] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,810] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,810] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,810] [DEBUG] 开始查找账号 m174 对应的行索引
[2025-07-22 01:09:36,811] [DEBUG] 未找到账号 m174 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,811] [DEBUG] 更新账号 m2 的表格数据，是否有数据: True
[2025-07-22 01:09:36,811] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,811] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,811] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,812] [DEBUG] 开始查找账号 m2 对应的行索引
[2025-07-22 01:09:36,812] [DEBUG] 未找到账号 m2 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,812] [DEBUG] 更新账号 m20 的表格数据，是否有数据: True
[2025-07-22 01:09:36,812] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,812] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,813] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,813] [DEBUG] 开始查找账号 m20 对应的行索引
[2025-07-22 01:09:36,813] [DEBUG] 未找到账号 m20 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,813] [DEBUG] 更新账号 m19 的表格数据，是否有数据: True
[2025-07-22 01:09:36,813] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,814] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,814] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,814] [DEBUG] 开始查找账号 m19 对应的行索引
[2025-07-22 01:09:36,815] [DEBUG] 未找到账号 m19 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,815] [DEBUG] 更新账号 m28 的表格数据，是否有数据: True
[2025-07-22 01:09:36,815] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,815] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,816] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,816] [DEBUG] 开始查找账号 m28 对应的行索引
[2025-07-22 01:09:36,816] [DEBUG] 未找到账号 m28 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,816] [DEBUG] 更新账号 m22 的表格数据，是否有数据: True
[2025-07-22 01:09:36,817] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,817] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,817] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,817] [DEBUG] 开始查找账号 m22 对应的行索引
[2025-07-22 01:09:36,818] [DEBUG] 未找到账号 m22 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,818] [DEBUG] 更新账号 m35 的表格数据，是否有数据: True
[2025-07-22 01:09:36,818] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,818] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,818] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,818] [DEBUG] 开始查找账号 m35 对应的行索引
[2025-07-22 01:09:36,820] [DEBUG] 未找到账号 m35 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,820] [DEBUG] 更新账号 m24 的表格数据，是否有数据: True
[2025-07-22 01:09:36,820] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,820] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,821] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,821] [DEBUG] 开始查找账号 m24 对应的行索引
[2025-07-22 01:09:36,821] [DEBUG] 未找到账号 m24 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,821] [DEBUG] 更新账号 m37 的表格数据，是否有数据: True
[2025-07-22 01:09:36,822] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,822] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,822] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,822] [DEBUG] 开始查找账号 m37 对应的行索引
[2025-07-22 01:09:36,822] [DEBUG] 未找到账号 m37 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,823] [DEBUG] 更新账号 m25 的表格数据，是否有数据: True
[2025-07-22 01:09:36,823] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,823] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,823] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,823] [DEBUG] 开始查找账号 m25 对应的行索引
[2025-07-22 01:09:36,824] [DEBUG] 未找到账号 m25 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,824] [DEBUG] 更新账号 m26 的表格数据，是否有数据: True
[2025-07-22 01:09:36,824] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,824] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,824] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,825] [DEBUG] 开始查找账号 m26 对应的行索引
[2025-07-22 01:09:36,825] [DEBUG] 未找到账号 m26 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,825] [DEBUG] 更新账号 m27 的表格数据，是否有数据: True
[2025-07-22 01:09:36,825] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,826] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,826] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,826] [DEBUG] 开始查找账号 m27 对应的行索引
[2025-07-22 01:09:36,826] [DEBUG] 未找到账号 m27 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,826] [DEBUG] 更新账号 m29 的表格数据，是否有数据: True
[2025-07-22 01:09:36,827] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,827] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,827] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,827] [DEBUG] 开始查找账号 m29 对应的行索引
[2025-07-22 01:09:36,828] [DEBUG] 未找到账号 m29 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,828] [DEBUG] 更新账号 m3 的表格数据，是否有数据: True
[2025-07-22 01:09:36,828] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,828] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,829] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,829] [DEBUG] 开始查找账号 m3 对应的行索引
[2025-07-22 01:09:36,829] [DEBUG] 未找到账号 m3 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,830] [DEBUG] 更新账号 m30 的表格数据，是否有数据: True
[2025-07-22 01:09:36,830] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,830] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,831] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,831] [DEBUG] 开始查找账号 m30 对应的行索引
[2025-07-22 01:09:36,831] [DEBUG] 未找到账号 m30 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,831] [DEBUG] 更新账号 m33 的表格数据，是否有数据: True
[2025-07-22 01:09:36,831] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,832] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,832] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,832] [DEBUG] 开始查找账号 m33 对应的行索引
[2025-07-22 01:09:36,833] [DEBUG] 未找到账号 m33 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,833] [DEBUG] 更新账号 m34 的表格数据，是否有数据: True
[2025-07-22 01:09:36,833] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,833] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,834] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,834] [DEBUG] 开始查找账号 m34 对应的行索引
[2025-07-22 01:09:36,834] [DEBUG] 未找到账号 m34 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,834] [DEBUG] 更新账号 m36 的表格数据，是否有数据: True
[2025-07-22 01:09:36,835] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,835] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,835] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,835] [DEBUG] 开始查找账号 m36 对应的行索引
[2025-07-22 01:09:36,836] [DEBUG] 未找到账号 m36 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,836] [DEBUG] 更新账号 m52 的表格数据，是否有数据: True
[2025-07-22 01:09:36,836] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,836] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,836] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,837] [DEBUG] 开始查找账号 m52 对应的行索引
[2025-07-22 01:09:36,837] [DEBUG] 未找到账号 m52 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,837] [DEBUG] 更新账号 m38 的表格数据，是否有数据: True
[2025-07-22 01:09:36,837] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,837] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,838] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,838] [DEBUG] 开始查找账号 m38 对应的行索引
[2025-07-22 01:09:36,838] [DEBUG] 未找到账号 m38 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,838] [DEBUG] 更新账号 m39 的表格数据，是否有数据: True
[2025-07-22 01:09:36,838] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,839] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,839] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,839] [DEBUG] 开始查找账号 m39 对应的行索引
[2025-07-22 01:09:36,839] [DEBUG] 未找到账号 m39 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,840] [DEBUG] 更新账号 m40 的表格数据，是否有数据: True
[2025-07-22 01:09:36,840] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,840] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,840] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,840] [DEBUG] 开始查找账号 m40 对应的行索引
[2025-07-22 01:09:36,840] [DEBUG] 未找到账号 m40 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,840] [DEBUG] 更新账号 m55 的表格数据，是否有数据: True
[2025-07-22 01:09:36,841] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,841] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,841] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,841] [DEBUG] 开始查找账号 m55 对应的行索引
[2025-07-22 01:09:36,841] [DEBUG] 未找到账号 m55 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,842] [DEBUG] 更新账号 m58 的表格数据，是否有数据: True
[2025-07-22 01:09:36,842] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,842] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,842] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,843] [DEBUG] 开始查找账号 m58 对应的行索引
[2025-07-22 01:09:36,843] [DEBUG] 未找到账号 m58 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,843] [DEBUG] 更新账号 m61 的表格数据，是否有数据: True
[2025-07-22 01:09:36,843] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,843] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,844] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,844] [DEBUG] 开始查找账号 m61 对应的行索引
[2025-07-22 01:09:36,844] [DEBUG] 未找到账号 m61 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,844] [DEBUG] 更新账号 m41 的表格数据，是否有数据: True
[2025-07-22 01:09:36,844] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,845] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,845] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,845] [DEBUG] 开始查找账号 m41 对应的行索引
[2025-07-22 01:09:36,846] [DEBUG] 未找到账号 m41 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,846] [DEBUG] 更新账号 m64 的表格数据，是否有数据: True
[2025-07-22 01:09:36,846] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,846] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,847] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,847] [DEBUG] 开始查找账号 m64 对应的行索引
[2025-07-22 01:09:36,847] [DEBUG] 未找到账号 m64 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,848] [DEBUG] 更新账号 m65 的表格数据，是否有数据: True
[2025-07-22 01:09:36,848] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,848] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,848] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,849] [DEBUG] 开始查找账号 m65 对应的行索引
[2025-07-22 01:09:36,849] [DEBUG] 未找到账号 m65 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,849] [DEBUG] 更新账号 m66 的表格数据，是否有数据: True
[2025-07-22 01:09:36,849] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,849] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,850] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,850] [DEBUG] 开始查找账号 m66 对应的行索引
[2025-07-22 01:09:36,851] [DEBUG] 未找到账号 m66 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,851] [DEBUG] 更新账号 m43 的表格数据，是否有数据: True
[2025-07-22 01:09:36,851] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,851] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,852] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,852] [DEBUG] 开始查找账号 m43 对应的行索引
[2025-07-22 01:09:36,852] [DEBUG] 未找到账号 m43 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,852] [DEBUG] 更新账号 m44 的表格数据，是否有数据: True
[2025-07-22 01:09:36,852] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,853] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,853] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,853] [DEBUG] 开始查找账号 m44 对应的行索引
[2025-07-22 01:09:36,853] [DEBUG] 未找到账号 m44 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,854] [DEBUG] 更新账号 m46 的表格数据，是否有数据: True
[2025-07-22 01:09:36,854] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,854] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,854] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,854] [DEBUG] 开始查找账号 m46 对应的行索引
[2025-07-22 01:09:36,855] [DEBUG] 未找到账号 m46 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,855] [DEBUG] 更新账号 m47 的表格数据，是否有数据: True
[2025-07-22 01:09:36,855] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,855] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,855] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,856] [DEBUG] 开始查找账号 m47 对应的行索引
[2025-07-22 01:09:36,856] [DEBUG] 未找到账号 m47 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,856] [DEBUG] 更新账号 m48 的表格数据，是否有数据: True
[2025-07-22 01:09:36,856] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,856] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,857] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,857] [DEBUG] 开始查找账号 m48 对应的行索引
[2025-07-22 01:09:36,857] [DEBUG] 未找到账号 m48 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,857] [DEBUG] 更新账号 m54 的表格数据，是否有数据: True
[2025-07-22 01:09:36,857] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,858] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,858] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,858] [DEBUG] 开始查找账号 m54 对应的行索引
[2025-07-22 01:09:36,858] [DEBUG] 未找到账号 m54 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,859] [DEBUG] 更新账号 m5 的表格数据，是否有数据: True
[2025-07-22 01:09:36,859] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,859] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,859] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,860] [DEBUG] 开始查找账号 m5 对应的行索引
[2025-07-22 01:09:36,860] [DEBUG] 未找到账号 m5 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,860] [DEBUG] 更新账号 m6 的表格数据，是否有数据: True
[2025-07-22 01:09:36,860] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,861] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,861] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,861] [DEBUG] 开始查找账号 m6 对应的行索引
[2025-07-22 01:09:36,862] [DEBUG] 未找到账号 m6 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,862] [DEBUG] 更新账号 m60 的表格数据，是否有数据: True
[2025-07-22 01:09:36,862] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,862] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,863] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,863] [DEBUG] 开始查找账号 m60 对应的行索引
[2025-07-22 01:09:36,863] [DEBUG] 未找到账号 m60 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,863] [DEBUG] 更新账号 m62 的表格数据，是否有数据: True
[2025-07-22 01:09:36,864] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,864] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,864] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,864] [DEBUG] 开始查找账号 m62 对应的行索引
[2025-07-22 01:09:36,864] [DEBUG] 未找到账号 m62 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,865] [DEBUG] 更新账号 m63 的表格数据，是否有数据: True
[2025-07-22 01:09:36,865] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,865] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,865] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,866] [DEBUG] 开始查找账号 m63 对应的行索引
[2025-07-22 01:09:36,866] [DEBUG] 未找到账号 m63 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,866] [DEBUG] 更新账号 m76 的表格数据，是否有数据: True
[2025-07-22 01:09:36,866] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,866] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,867] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,867] [DEBUG] 开始查找账号 m76 对应的行索引
[2025-07-22 01:09:36,867] [DEBUG] 未找到账号 m76 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,867] [DEBUG] 更新账号 m7 的表格数据，是否有数据: True
[2025-07-22 01:09:36,867] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,868] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,868] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,868] [DEBUG] 开始查找账号 m7 对应的行索引
[2025-07-22 01:09:36,869] [DEBUG] 未找到账号 m7 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,869] [DEBUG] 更新账号 m70 的表格数据，是否有数据: True
[2025-07-22 01:09:36,869] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,869] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,869] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,870] [DEBUG] 开始查找账号 m70 对应的行索引
[2025-07-22 01:09:36,870] [DEBUG] 未找到账号 m70 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,870] [DEBUG] 更新账号 m71 的表格数据，是否有数据: True
[2025-07-22 01:09:36,870] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,871] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,871] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,871] [DEBUG] 开始查找账号 m71 对应的行索引
[2025-07-22 01:09:36,871] [DEBUG] 未找到账号 m71 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,871] [DEBUG] 更新账号 m72 的表格数据，是否有数据: True
[2025-07-22 01:09:36,871] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,871] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,873] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,873] [DEBUG] 开始查找账号 m72 对应的行索引
[2025-07-22 01:09:36,873] [DEBUG] 未找到账号 m72 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,873] [DEBUG] 更新账号 m73 的表格数据，是否有数据: True
[2025-07-22 01:09:36,874] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,874] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,874] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,874] [DEBUG] 开始查找账号 m73 对应的行索引
[2025-07-22 01:09:36,874] [DEBUG] 未找到账号 m73 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,875] [DEBUG] 更新账号 m74 的表格数据，是否有数据: True
[2025-07-22 01:09:36,875] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,875] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,875] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,875] [DEBUG] 开始查找账号 m74 对应的行索引
[2025-07-22 01:09:36,876] [DEBUG] 未找到账号 m74 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,876] [DEBUG] 更新账号 m75 的表格数据，是否有数据: True
[2025-07-22 01:09:36,877] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,877] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,877] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,878] [DEBUG] 开始查找账号 m75 对应的行索引
[2025-07-22 01:09:36,878] [DEBUG] 未找到账号 m75 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,878] [DEBUG] 更新账号 m8 的表格数据，是否有数据: True
[2025-07-22 01:09:36,878] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,879] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,879] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,879] [DEBUG] 开始查找账号 m8 对应的行索引
[2025-07-22 01:09:36,880] [DEBUG] 未找到账号 m8 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,880] [DEBUG] 更新账号 m86 的表格数据，是否有数据: True
[2025-07-22 01:09:36,880] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,880] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,881] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,881] [DEBUG] 开始查找账号 m86 对应的行索引
[2025-07-22 01:09:36,881] [DEBUG] 未找到账号 m86 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,881] [DEBUG] 更新账号 m88 的表格数据，是否有数据: True
[2025-07-22 01:09:36,881] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,882] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,882] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,882] [DEBUG] 开始查找账号 m88 对应的行索引
[2025-07-22 01:09:36,882] [DEBUG] 未找到账号 m88 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,883] [DEBUG] 更新账号 m9 的表格数据，是否有数据: True
[2025-07-22 01:09:36,883] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,883] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,883] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,883] [DEBUG] 开始查找账号 m9 对应的行索引
[2025-07-22 01:09:36,884] [DEBUG] 未找到账号 m9 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,884] [DEBUG] 更新账号 m91 的表格数据，是否有数据: True
[2025-07-22 01:09:36,884] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,884] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,884] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,885] [DEBUG] 开始查找账号 m91 对应的行索引
[2025-07-22 01:09:36,885] [DEBUG] 未找到账号 m91 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,885] [DEBUG] 更新账号 m92 的表格数据，是否有数据: True
[2025-07-22 01:09:36,885] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,886] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,886] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,886] [DEBUG] 开始查找账号 m92 对应的行索引
[2025-07-22 01:09:36,886] [DEBUG] 未找到账号 m92 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,886] [DEBUG] 更新账号 m93 的表格数据，是否有数据: True
[2025-07-22 01:09:36,886] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,887] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,887] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,887] [DEBUG] 开始查找账号 m93 对应的行索引
[2025-07-22 01:09:36,887] [DEBUG] 未找到账号 m93 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,888] [DEBUG] 更新账号 m94 的表格数据，是否有数据: True
[2025-07-22 01:09:36,888] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,888] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,888] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,889] [DEBUG] 开始查找账号 m94 对应的行索引
[2025-07-22 01:09:36,889] [DEBUG] 未找到账号 m94 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,889] [DEBUG] 更新账号 m95 的表格数据，是否有数据: True
[2025-07-22 01:09:36,889] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,890] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,890] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,890] [DEBUG] 开始查找账号 m95 对应的行索引
[2025-07-22 01:09:36,891] [DEBUG] 未找到账号 m95 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,891] [DEBUG] 更新账号 m96 的表格数据，是否有数据: True
[2025-07-22 01:09:36,891] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,892] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,892] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,892] [DEBUG] 开始查找账号 m96 对应的行索引
[2025-07-22 01:09:36,892] [DEBUG] 未找到账号 m96 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,893] [DEBUG] 更新账号 m97 的表格数据，是否有数据: True
[2025-07-22 01:09:36,893] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,893] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,893] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,893] [DEBUG] 开始查找账号 m97 对应的行索引
[2025-07-22 01:09:36,893] [DEBUG] 未找到账号 m97 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,894] [DEBUG] 更新账号 m1 的表格数据，是否有数据: True
[2025-07-22 01:09:36,894] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,894] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,894] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,895] [DEBUG] 开始查找账号 m1 对应的行索引
[2025-07-22 01:09:36,895] [DEBUG] 未找到账号 m1 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,895] [DEBUG] 更新账号 m10 的表格数据，是否有数据: True
[2025-07-22 01:09:36,895] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,896] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,896] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,896] [DEBUG] 开始查找账号 m10 对应的行索引
[2025-07-22 01:09:36,896] [DEBUG] 未找到账号 m10 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,896] [DEBUG] 更新账号 m100 的表格数据，是否有数据: True
[2025-07-22 01:09:36,897] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,897] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,897] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,897] [DEBUG] 开始查找账号 m100 对应的行索引
[2025-07-22 01:09:36,897] [DEBUG] 未找到账号 m100 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,898] [DEBUG] 更新账号 m101 的表格数据，是否有数据: True
[2025-07-22 01:09:36,898] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,898] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,898] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,899] [DEBUG] 开始查找账号 m101 对应的行索引
[2025-07-22 01:09:36,899] [DEBUG] 未找到账号 m101 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,899] [DEBUG] 更新账号 m102 的表格数据，是否有数据: True
[2025-07-22 01:09:36,899] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,900] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,900] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,900] [DEBUG] 开始查找账号 m102 对应的行索引
[2025-07-22 01:09:36,900] [DEBUG] 未找到账号 m102 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,901] [DEBUG] 更新账号 m103 的表格数据，是否有数据: True
[2025-07-22 01:09:36,901] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,901] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,901] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,902] [DEBUG] 开始查找账号 m103 对应的行索引
[2025-07-22 01:09:36,902] [DEBUG] 未找到账号 m103 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,902] [DEBUG] 更新账号 m104 的表格数据，是否有数据: True
[2025-07-22 01:09:36,902] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,902] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,903] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,903] [DEBUG] 开始查找账号 m104 对应的行索引
[2025-07-22 01:09:36,903] [DEBUG] 未找到账号 m104 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,903] [DEBUG] 更新账号 m12 的表格数据，是否有数据: True
[2025-07-22 01:09:36,903] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,903] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,905] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,905] [DEBUG] 开始查找账号 m12 对应的行索引
[2025-07-22 01:09:36,905] [DEBUG] 未找到账号 m12 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,905] [DEBUG] 更新账号 m13 的表格数据，是否有数据: True
[2025-07-22 01:09:36,905] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,906] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,906] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,906] [DEBUG] 开始查找账号 m13 对应的行索引
[2025-07-22 01:09:36,906] [DEBUG] 未找到账号 m13 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,906] [DEBUG] 更新账号 m141 的表格数据，是否有数据: True
[2025-07-22 01:09:36,907] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,907] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,907] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,907] [DEBUG] 开始查找账号 m141 对应的行索引
[2025-07-22 01:09:36,908] [DEBUG] 未找到账号 m141 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,908] [DEBUG] 更新账号 m142 的表格数据，是否有数据: True
[2025-07-22 01:09:36,908] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,908] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,909] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,909] [DEBUG] 开始查找账号 m142 对应的行索引
[2025-07-22 01:09:36,909] [DEBUG] 未找到账号 m142 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,910] [DEBUG] 更新账号 m143 的表格数据，是否有数据: True
[2025-07-22 01:09:36,910] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,910] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,911] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,911] [DEBUG] 开始查找账号 m143 对应的行索引
[2025-07-22 01:09:36,911] [DEBUG] 未找到账号 m143 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,911] [DEBUG] 更新账号 m15 的表格数据，是否有数据: True
[2025-07-22 01:09:36,911] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,912] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,912] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,912] [DEBUG] 开始查找账号 m15 对应的行索引
[2025-07-22 01:09:36,912] [DEBUG] 未找到账号 m15 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,913] [DEBUG] 更新账号 m151 的表格数据，是否有数据: True
[2025-07-22 01:09:36,913] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,913] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,914] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,914] [DEBUG] 开始查找账号 m151 对应的行索引
[2025-07-22 01:09:36,914] [DEBUG] 未找到账号 m151 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,914] [DEBUG] 更新账号 m152 的表格数据，是否有数据: True
[2025-07-22 01:09:36,914] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,915] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,915] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,915] [DEBUG] 开始查找账号 m152 对应的行索引
[2025-07-22 01:09:36,915] [DEBUG] 未找到账号 m152 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,915] [DEBUG] 更新账号 m153 的表格数据，是否有数据: True
[2025-07-22 01:09:36,916] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,916] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,916] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,916] [DEBUG] 开始查找账号 m153 对应的行索引
[2025-07-22 01:09:36,917] [DEBUG] 未找到账号 m153 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,917] [DEBUG] 更新账号 m154 的表格数据，是否有数据: True
[2025-07-22 01:09:36,917] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,917] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,917] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,918] [DEBUG] 开始查找账号 m154 对应的行索引
[2025-07-22 01:09:36,918] [DEBUG] 未找到账号 m154 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,918] [DEBUG] 更新账号 m155 的表格数据，是否有数据: True
[2025-07-22 01:09:36,918] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,919] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,919] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,919] [DEBUG] 开始查找账号 m155 对应的行索引
[2025-07-22 01:09:36,919] [DEBUG] 未找到账号 m155 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,920] [DEBUG] 更新账号 m156 的表格数据，是否有数据: True
[2025-07-22 01:09:36,920] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,920] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,920] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,921] [DEBUG] 开始查找账号 m156 对应的行索引
[2025-07-22 01:09:36,921] [DEBUG] 未找到账号 m156 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,921] [DEBUG] 更新账号 m157 的表格数据，是否有数据: True
[2025-07-22 01:09:36,921] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,921] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,922] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,922] [DEBUG] 开始查找账号 m157 对应的行索引
[2025-07-22 01:09:36,922] [DEBUG] 未找到账号 m157 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,923] [DEBUG] 更新账号 m158 的表格数据，是否有数据: True
[2025-07-22 01:09:36,923] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,923] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,923] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,924] [DEBUG] 开始查找账号 m158 对应的行索引
[2025-07-22 01:09:36,924] [DEBUG] 未找到账号 m158 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,924] [DEBUG] 更新账号 m159 的表格数据，是否有数据: True
[2025-07-22 01:09:36,924] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,924] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,925] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,925] [DEBUG] 开始查找账号 m159 对应的行索引
[2025-07-22 01:09:36,925] [DEBUG] 未找到账号 m159 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,925] [DEBUG] 更新账号 m16 的表格数据，是否有数据: True
[2025-07-22 01:09:36,925] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,925] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,925] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,926] [DEBUG] 开始查找账号 m16 对应的行索引
[2025-07-22 01:09:36,926] [DEBUG] 未找到账号 m16 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,926] [DEBUG] 更新账号 m161 的表格数据，是否有数据: True
[2025-07-22 01:09:36,926] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,927] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,927] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,927] [DEBUG] 开始查找账号 m161 对应的行索引
[2025-07-22 01:09:36,927] [DEBUG] 未找到账号 m161 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,927] [DEBUG] 更新账号 m162 的表格数据，是否有数据: True
[2025-07-22 01:09:36,928] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,928] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,928] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,928] [DEBUG] 开始查找账号 m162 对应的行索引
[2025-07-22 01:09:36,929] [DEBUG] 未找到账号 m162 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,929] [DEBUG] 更新账号 m163 的表格数据，是否有数据: True
[2025-07-22 01:09:36,929] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,929] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,929] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,930] [DEBUG] 开始查找账号 m163 对应的行索引
[2025-07-22 01:09:36,930] [DEBUG] 未找到账号 m163 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,930] [DEBUG] 更新账号 m164 的表格数据，是否有数据: True
[2025-07-22 01:09:36,930] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,931] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,931] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,931] [DEBUG] 开始查找账号 m164 对应的行索引
[2025-07-22 01:09:36,931] [DEBUG] 未找到账号 m164 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,931] [DEBUG] 更新账号 m165 的表格数据，是否有数据: True
[2025-07-22 01:09:36,931] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,932] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,932] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,932] [DEBUG] 开始查找账号 m165 对应的行索引
[2025-07-22 01:09:36,932] [DEBUG] 未找到账号 m165 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,932] [DEBUG] 更新账号 m166 的表格数据，是否有数据: True
[2025-07-22 01:09:36,933] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,933] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,933] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,933] [DEBUG] 开始查找账号 m166 对应的行索引
[2025-07-22 01:09:36,934] [DEBUG] 未找到账号 m166 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,934] [DEBUG] 更新账号 m167 的表格数据，是否有数据: True
[2025-07-22 01:09:36,934] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,934] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,934] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,935] [DEBUG] 开始查找账号 m167 对应的行索引
[2025-07-22 01:09:36,935] [DEBUG] 未找到账号 m167 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,935] [DEBUG] 更新账号 m168 的表格数据，是否有数据: True
[2025-07-22 01:09:36,935] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,935] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,935] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,935] [DEBUG] 开始查找账号 m168 对应的行索引
[2025-07-22 01:09:36,935] [DEBUG] 未找到账号 m168 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,937] [DEBUG] 更新账号 m17 的表格数据，是否有数据: True
[2025-07-22 01:09:36,937] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,937] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,938] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,938] [DEBUG] 开始查找账号 m17 对应的行索引
[2025-07-22 01:09:36,938] [DEBUG] 未找到账号 m17 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,938] [DEBUG] 更新账号 m171 的表格数据，是否有数据: True
[2025-07-22 01:09:36,939] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,939] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,939] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,939] [DEBUG] 开始查找账号 m171 对应的行索引
[2025-07-22 01:09:36,940] [DEBUG] 未找到账号 m171 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,940] [DEBUG] 更新账号 m173 的表格数据，是否有数据: True
[2025-07-22 01:09:36,940] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,940] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,940] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,941] [DEBUG] 开始查找账号 m173 对应的行索引
[2025-07-22 01:09:36,941] [DEBUG] 未找到账号 m173 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,941] [DEBUG] 更新账号 m18 的表格数据，是否有数据: True
[2025-07-22 01:09:36,941] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 01:09:36,941] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 01:09:36,942] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 01:09:36,942] [DEBUG] 开始查找账号 m18 对应的行索引
[2025-07-22 01:09:36,942] [DEBUG] 未找到账号 m18 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 01:09:36,982] [INFO] 用户取消后台加载
[2025-07-22 01:09:36,982] [INFO] 后台加载完成: 账号数据加载完成
[2025-07-22 01:09:37,187] [DEBUG] 批量数据处理完成后内存使用: 254.06 MB
[2025-07-22 01:09:37,188] [ERROR] 更新加载进度时出错: 'NoneType' object has no attribute 'setLabelText'
[2025-07-22 01:09:37,607] [INFO] account_loader.load_accounts_sync方法返回: success=True, accounts数量=305, error_msg=
[2025-07-22 01:09:37,607] [INFO] 同步加载账号数据完成，结果: True, 账号数量: 305, 错误信息: 
[2025-07-22 01:09:37,607] [INFO] 账号数据加载成功，共 305 个账号
[2025-07-22 01:09:37,608] [INFO] 账号加载完成回调开始时内存使用: 262.87 MB
[2025-07-22 01:09:37,608] [INFO] 已创建账号列表的浅拷贝，大小: 305
[2025-07-22 01:09:37,608] [INFO] 成功加载 305 个账号文件，延迟加载模式: True
[2025-07-22 01:09:37,637] [INFO] 账号列表大小: 305
[2025-07-22 01:09:37,637] [INFO] 第一个账号信息: {'file_path': 'E:/软件共享/账号合并\\***********.txt', 'account_id': '***********', 'data': {'accountId': '***********', 'remark': '***********', 'cookies': {'xigua_csrf_token': 'Rh9tB8w72kFdjHWXzmwLx4FN', 'is_staff_user': 'false', 'sessionid_ss': '68c4cc3f1c5dedd10b37702f8cfc5581', 'uid_tt_ss': '4138e04b7027f10e81ee57334a3871f1', 'store-region': 'cn-gd', 'uid_tt': '4138e04b7027f10e81ee57334a3871f1', 'passport_auth_status': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'passport_mfa_token': 'CjfDS1ognyyaR5OXvuo1ZhOlSC8orpJykXqU3XrL0%2F4LK3qwDRucEFU8wl5EeoQDFgQXMDSnVv64GkoKPIA87d76RVvezzRe1eiXyd81F%2FQYdzX0iWRhqgsPoaSXZlpDutIfPN2NISNG6lXHe%2BhFo3Alo9axe2OZMhDY3OsNGPax0WwgAiIBA6K42yM%3D', 'passport_auth_status_ss': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'sid_tt': '68c4cc3f1c5dedd10b37702f8cfc5581', 'odin_tt': 'f681b2fee0f84dd926c09d58e132e2328c436e9fcf004f758697a032da7adc66d0d445a80508f8d675d1357603c3efe11f5237f59ac2ca2893e80a3e5da52dcf', 'sessionid': '68c4cc3f1c5dedd10b37702f8cfc5581', 'gfgarrcache_8005812e': '1', 'ssid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'toutiao_sso_user_ss': 'c2032ee116a4c329079d18ddc99f62c6', 'gfgarrcache_6a2703f1': '1', 'sid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'store-region-src': 'uid', 'sid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'toutiao_sso_user': 'c2032ee116a4c329079d18ddc99f62c6', 'sso_uid_tt_ss': '707d66e7d745b6ca510877ff49d6deca', 'xg_p_tos_token': '79aa2d6e8307f3027308bb8063808201', 'sso_uid_tt': '707d66e7d745b6ca510877ff49d6deca', 'sso_auth_status_ss': '99d6be0fe40fbeccd26dbd011f7e4345', 'ssid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'sid_guard': '68c4cc3f1c5dedd10b37702f8cfc5581%7C1741687410%7C5184002%7CSat%2C+10-May-2025+10%3A03%3A32+GMT', 'sso_auth_status': '99d6be0fe40fbeccd26dbd011f7e4345', 'passport_csrf_token': '3d631c058164d798d48475833f13446e', 'n_mh': '30A9sphpGJEPcXGYEGo8hiNGyEbb5VtQ_Ov5DGw0tbA', 'ttwid': '1%7C0FfHeK6FyRv027NKpnZinttVopHTQty8nSXP9P7wnNw%7C1741687388%7C33b1d18a6f3d000bd20fd4a1d0f5d6b37682c37752a91a6e1265423a4b1e9968', 'gfkadpd': '1231,25897', 'd_ticket': '0da2a81adc5bb557ff718c555c0a4801d76e7', 'passport_csrf_token_default': '3d631c058164d798d48475833f13446e', 'csrf_session_id': 'e1a99642f2b9648ef30c79ef589a852a', 's_v_web_id': 'verify_m84bp59m_dED7JGij_AkV2_4J3T_B268_83sqBwAURoyE'}}, 'file_type': '.txt'}
[2025-07-22 01:09:37,638] [INFO] 使用延迟加载模式，准备更新表格基本信息
[2025-07-22 01:09:37,642] [DEBUG] 已导入高级内存管理器
[2025-07-22 01:09:37,655] [INFO] 更新表格开始时内存使用: 262.95MB, 可用: 1949.32MB/16236.06MB
[2025-07-22 01:09:37,685] [INFO] 准备设置表格行数: 305
[2025-07-22 01:09:37,685] [INFO] 已设置表格行数: 305，当前表格行数: 305
[2025-07-22 01:09:37,686] [DEBUG] 处理账号批次: 1-50/305
[2025-07-22 01:09:38,162] [DEBUG] 处理账号批次: 51-100/305
[2025-07-22 01:09:38,380] [DEBUG] 处理账号批次: 101-150/305
[2025-07-22 01:09:38,619] [DEBUG] 处理账号批次: 151-200/305
[2025-07-22 01:09:38,855] [DEBUG] 处理账号批次: 201-250/305
[2025-07-22 01:09:39,087] [DEBUG] 处理账号批次: 251-300/305
[2025-07-22 01:09:39,318] [DEBUG] 处理账号批次: 301-305/305
[2025-07-22 01:09:39,468] [INFO] 表格更新完成，耗时: 1.81秒，内存使用: 358.90MB
[2025-07-22 01:09:39,468] [INFO] 使用延迟加载模式，已更新表格基本信息
[2025-07-22 01:09:39,469] [INFO] 延迟加载模式下，表格行数: 305
[2025-07-22 01:09:39,520] [INFO] 不设置账号已加载标志，允许重复加载
[2025-07-22 01:09:39,520] [INFO] 开始计算收益统计，表格行数: 305, 列数: 19
[2025-07-22 01:09:39,521] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-22 01:09:39,522] [INFO] 七天总收益: ¥0.00
[2025-07-22 01:09:39,522] [INFO] 昨日总收益: ¥0.00
[2025-07-22 01:09:39,522] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-22 01:09:39,524] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-22 01:09:41,494] [INFO] AI文件名改写器初始化成功
[2025-07-22 01:09:41,571] [INFO] 已从文件加载设置: E:\toutiaoyuanma1\tou014\tou011\video_processor_settings.json
[2025-07-22 01:09:41,572] [DEBUG] 从智能体配置同步
[2025-07-22 01:09:41,573] [DEBUG] 已从AI改写器同步配置: agent_id=WXR2oUKfqs..., token=***
[2025-07-22 01:09:41,574] [INFO] 视频处理对话框已初始化
[2025-07-22 01:09:42,564] [DEBUG] 设置已保存到: E:\toutiaoyuanma1\tou014\tou011\video_processor_settings.json
[2025-07-22 01:09:44,595] [INFO] 开始计算收益统计，表格行数: 305, 列数: 19
[2025-07-22 01:09:44,596] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-22 01:09:44,596] [INFO] 七天总收益: ¥0.00
[2025-07-22 01:09:44,596] [INFO] 昨日总收益: ¥0.00
[2025-07-22 01:09:44,597] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-22 01:09:44,599] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-22 01:09:49,632] [DEBUG] 设置已保存到: E:\toutiaoyuanma1\tou014\tou011\video_processor_settings.json
[2025-07-22 01:09:51,112] [INFO] 正在停止所有活动线程...
[2025-07-22 01:09:51,112] [INFO] 成功停止了 0 个线程
[2025-07-22 21:53:30,704] [INFO] OpenCV库已加载，支持视频处理功能
[2025-07-22 21:53:30,789] [INFO] PIL库已加载，支持图像处理功能
[2025-07-22 21:53:36,795] [INFO] OpenCV库已加载，支持视频处理功能
[2025-07-22 21:53:36,871] [INFO] PIL库已加载，支持图像处理功能
[2025-07-22 21:53:43,516] [INFO] 自动化配置加载成功
[2025-07-22 21:53:43,516] [INFO] 自动化任务调度器初始化完成
[2025-07-22 21:53:43,534] [INFO] 心跳状态指示器初始化完成
[2025-07-22 21:53:43,609] [INFO] 跳过示例数据加载，等待真实账号数据
[2025-07-22 21:53:43,615] [ERROR] 连接表格信号时出错: 'AccountTab' object has no attribute 'on_cell_changed'
[2025-07-22 21:53:44,721] [INFO] 启动快速启动模式 - 后台加载账号数据
[2025-07-22 21:53:44,828] [DEBUG] 批量数据处理开始时内存使用: 246.37 MB，数据量: 100
[2025-07-22 21:53:44,829] [DEBUG] 更新账号 m24 的表格数据，是否有数据: True
[2025-07-22 21:53:44,829] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,830] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,830] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,830] [DEBUG] 开始查找账号 m24 对应的行索引
[2025-07-22 21:53:44,831] [DEBUG] 未找到账号 m24 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,831] [DEBUG] 更新账号 m35 的表格数据，是否有数据: True
[2025-07-22 21:53:44,831] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,831] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,831] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,832] [DEBUG] 开始查找账号 m35 对应的行索引
[2025-07-22 21:53:44,832] [DEBUG] 未找到账号 m35 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,833] [DEBUG] 更新账号 m25 的表格数据，是否有数据: True
[2025-07-22 21:53:44,833] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,833] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,833] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,834] [DEBUG] 开始查找账号 m25 对应的行索引
[2025-07-22 21:53:44,834] [DEBUG] 未找到账号 m25 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,834] [DEBUG] 更新账号 m37 的表格数据，是否有数据: True
[2025-07-22 21:53:44,834] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,835] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,835] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,836] [DEBUG] 开始查找账号 m37 对应的行索引
[2025-07-22 21:53:44,836] [DEBUG] 未找到账号 m37 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,836] [DEBUG] 更新账号 m29 的表格数据，是否有数据: True
[2025-07-22 21:53:44,836] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,836] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,837] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,837] [DEBUG] 开始查找账号 m29 对应的行索引
[2025-07-22 21:53:44,837] [DEBUG] 未找到账号 m29 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,837] [DEBUG] 更新账号 m3 的表格数据，是否有数据: True
[2025-07-22 21:53:44,837] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,838] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,838] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,838] [DEBUG] 开始查找账号 m3 对应的行索引
[2025-07-22 21:53:44,838] [DEBUG] 未找到账号 m3 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,839] [DEBUG] 更新账号 m30 的表格数据，是否有数据: True
[2025-07-22 21:53:44,839] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,839] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,839] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,839] [DEBUG] 开始查找账号 m30 对应的行索引
[2025-07-22 21:53:44,840] [DEBUG] 未找到账号 m30 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,840] [DEBUG] 更新账号 m33 的表格数据，是否有数据: True
[2025-07-22 21:53:44,840] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,840] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,841] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,841] [DEBUG] 开始查找账号 m33 对应的行索引
[2025-07-22 21:53:44,841] [DEBUG] 未找到账号 m33 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,841] [DEBUG] 更新账号 m34 的表格数据，是否有数据: True
[2025-07-22 21:53:44,841] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,842] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,842] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,842] [DEBUG] 开始查找账号 m34 对应的行索引
[2025-07-22 21:53:44,842] [DEBUG] 未找到账号 m34 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,842] [DEBUG] 更新账号 m36 的表格数据，是否有数据: True
[2025-07-22 21:53:44,843] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,843] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,843] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,843] [DEBUG] 开始查找账号 m36 对应的行索引
[2025-07-22 21:53:44,843] [DEBUG] 未找到账号 m36 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,843] [DEBUG] 更新账号 m38 的表格数据，是否有数据: True
[2025-07-22 21:53:44,844] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,844] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,844] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,844] [DEBUG] 开始查找账号 m38 对应的行索引
[2025-07-22 21:53:44,844] [DEBUG] 未找到账号 m38 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,845] [DEBUG] 更新账号 m39 的表格数据，是否有数据: True
[2025-07-22 21:53:44,845] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,845] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,845] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,845] [DEBUG] 开始查找账号 m39 对应的行索引
[2025-07-22 21:53:44,846] [DEBUG] 未找到账号 m39 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,846] [DEBUG] 更新账号 m40 的表格数据，是否有数据: True
[2025-07-22 21:53:44,846] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,846] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,848] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,848] [DEBUG] 开始查找账号 m40 对应的行索引
[2025-07-22 21:53:44,848] [DEBUG] 未找到账号 m40 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,848] [DEBUG] 更新账号 m41 的表格数据，是否有数据: True
[2025-07-22 21:53:44,848] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,848] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,849] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,849] [DEBUG] 开始查找账号 m41 对应的行索引
[2025-07-22 21:53:44,849] [DEBUG] 未找到账号 m41 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,849] [DEBUG] 更新账号 m52 的表格数据，是否有数据: True
[2025-07-22 21:53:44,849] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,850] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,850] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,850] [DEBUG] 开始查找账号 m52 对应的行索引
[2025-07-22 21:53:44,850] [DEBUG] 未找到账号 m52 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,851] [DEBUG] 更新账号 m55 的表格数据，是否有数据: True
[2025-07-22 21:53:44,851] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,851] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,851] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,851] [DEBUG] 开始查找账号 m55 对应的行索引
[2025-07-22 21:53:44,852] [DEBUG] 未找到账号 m55 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,852] [DEBUG] 更新账号 m58 的表格数据，是否有数据: True
[2025-07-22 21:53:44,852] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,852] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,853] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,853] [DEBUG] 开始查找账号 m58 对应的行索引
[2025-07-22 21:53:44,853] [DEBUG] 未找到账号 m58 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,853] [DEBUG] 更新账号 m43 的表格数据，是否有数据: True
[2025-07-22 21:53:44,853] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,853] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,854] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,854] [DEBUG] 开始查找账号 m43 对应的行索引
[2025-07-22 21:53:44,854] [DEBUG] 未找到账号 m43 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,854] [DEBUG] 更新账号 m61 的表格数据，是否有数据: True
[2025-07-22 21:53:44,855] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,855] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,855] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,855] [DEBUG] 开始查找账号 m61 对应的行索引
[2025-07-22 21:53:44,856] [DEBUG] 未找到账号 m61 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,856] [DEBUG] 更新账号 m44 的表格数据，是否有数据: True
[2025-07-22 21:53:44,856] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,856] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,856] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,857] [DEBUG] 开始查找账号 m44 对应的行索引
[2025-07-22 21:53:44,857] [DEBUG] 未找到账号 m44 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,857] [DEBUG] 更新账号 m64 的表格数据，是否有数据: True
[2025-07-22 21:53:44,857] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,857] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,858] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,858] [DEBUG] 开始查找账号 m64 对应的行索引
[2025-07-22 21:53:44,858] [DEBUG] 未找到账号 m64 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,858] [DEBUG] 更新账号 m65 的表格数据，是否有数据: True
[2025-07-22 21:53:44,858] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,859] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,859] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,859] [DEBUG] 开始查找账号 m65 对应的行索引
[2025-07-22 21:53:44,859] [DEBUG] 未找到账号 m65 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,859] [DEBUG] 更新账号 m66 的表格数据，是否有数据: True
[2025-07-22 21:53:44,860] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,860] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,860] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,861] [DEBUG] 开始查找账号 m66 对应的行索引
[2025-07-22 21:53:44,861] [DEBUG] 未找到账号 m66 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,861] [DEBUG] 更新账号 m47 的表格数据，是否有数据: True
[2025-07-22 21:53:44,862] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,862] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,862] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,863] [DEBUG] 开始查找账号 m47 对应的行索引
[2025-07-22 21:53:44,863] [DEBUG] 未找到账号 m47 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,863] [DEBUG] 更新账号 m48 的表格数据，是否有数据: True
[2025-07-22 21:53:44,863] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,863] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,864] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,864] [DEBUG] 开始查找账号 m48 对应的行索引
[2025-07-22 21:53:44,864] [DEBUG] 未找到账号 m48 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,864] [DEBUG] 更新账号 m5 的表格数据，是否有数据: True
[2025-07-22 21:53:44,864] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,865] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,865] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,865] [DEBUG] 开始查找账号 m5 对应的行索引
[2025-07-22 21:53:44,866] [DEBUG] 未找到账号 m5 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,866] [DEBUG] 更新账号 m54 的表格数据，是否有数据: True
[2025-07-22 21:53:44,866] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,866] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,866] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,867] [DEBUG] 开始查找账号 m54 对应的行索引
[2025-07-22 21:53:44,867] [DEBUG] 未找到账号 m54 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,867] [DEBUG] 更新账号 m60 的表格数据，是否有数据: True
[2025-07-22 21:53:44,867] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,867] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,868] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,868] [DEBUG] 开始查找账号 m60 对应的行索引
[2025-07-22 21:53:44,868] [DEBUG] 未找到账号 m60 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,868] [DEBUG] 更新账号 m6 的表格数据，是否有数据: True
[2025-07-22 21:53:44,868] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,868] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,869] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,869] [DEBUG] 开始查找账号 m6 对应的行索引
[2025-07-22 21:53:44,869] [DEBUG] 未找到账号 m6 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,869] [DEBUG] 更新账号 m62 的表格数据，是否有数据: True
[2025-07-22 21:53:44,869] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,870] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,870] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,870] [DEBUG] 开始查找账号 m62 对应的行索引
[2025-07-22 21:53:44,870] [DEBUG] 未找到账号 m62 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,870] [DEBUG] 更新账号 m63 的表格数据，是否有数据: True
[2025-07-22 21:53:44,871] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,871] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,871] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,871] [DEBUG] 开始查找账号 m63 对应的行索引
[2025-07-22 21:53:44,871] [DEBUG] 未找到账号 m63 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,872] [DEBUG] 更新账号 m76 的表格数据，是否有数据: True
[2025-07-22 21:53:44,872] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,872] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,872] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,873] [DEBUG] 开始查找账号 m76 对应的行索引
[2025-07-22 21:53:44,873] [DEBUG] 未找到账号 m76 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,873] [DEBUG] 更新账号 m7 的表格数据，是否有数据: True
[2025-07-22 21:53:44,873] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,873] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,874] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,874] [DEBUG] 开始查找账号 m7 对应的行索引
[2025-07-22 21:53:44,874] [DEBUG] 未找到账号 m7 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,874] [DEBUG] 更新账号 m70 的表格数据，是否有数据: True
[2025-07-22 21:53:44,875] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,875] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,875] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,876] [DEBUG] 开始查找账号 m70 对应的行索引
[2025-07-22 21:53:44,876] [DEBUG] 未找到账号 m70 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,876] [DEBUG] 更新账号 m71 的表格数据，是否有数据: True
[2025-07-22 21:53:44,876] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,876] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,877] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,877] [DEBUG] 开始查找账号 m71 对应的行索引
[2025-07-22 21:53:44,877] [DEBUG] 未找到账号 m71 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,877] [DEBUG] 更新账号 m72 的表格数据，是否有数据: True
[2025-07-22 21:53:44,878] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,878] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,879] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,879] [DEBUG] 开始查找账号 m72 对应的行索引
[2025-07-22 21:53:44,879] [DEBUG] 未找到账号 m72 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,879] [DEBUG] 更新账号 m74 的表格数据，是否有数据: True
[2025-07-22 21:53:44,879] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,879] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,880] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,880] [DEBUG] 开始查找账号 m74 对应的行索引
[2025-07-22 21:53:44,880] [DEBUG] 未找到账号 m74 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,880] [DEBUG] 更新账号 m73 的表格数据，是否有数据: True
[2025-07-22 21:53:44,881] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,881] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,881] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,881] [DEBUG] 开始查找账号 m73 对应的行索引
[2025-07-22 21:53:44,881] [DEBUG] 未找到账号 m73 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,882] [DEBUG] 更新账号 m75 的表格数据，是否有数据: True
[2025-07-22 21:53:44,882] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,882] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,882] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,882] [DEBUG] 开始查找账号 m75 对应的行索引
[2025-07-22 21:53:44,883] [DEBUG] 未找到账号 m75 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,883] [DEBUG] 更新账号 m8 的表格数据，是否有数据: True
[2025-07-22 21:53:44,883] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,883] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,883] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,884] [DEBUG] 开始查找账号 m8 对应的行索引
[2025-07-22 21:53:44,884] [DEBUG] 未找到账号 m8 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,884] [DEBUG] 更新账号 m86 的表格数据，是否有数据: True
[2025-07-22 21:53:44,884] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,885] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,885] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,885] [DEBUG] 开始查找账号 m86 对应的行索引
[2025-07-22 21:53:44,885] [DEBUG] 未找到账号 m86 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,885] [DEBUG] 更新账号 m88 的表格数据，是否有数据: True
[2025-07-22 21:53:44,886] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,886] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,886] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,886] [DEBUG] 开始查找账号 m88 对应的行索引
[2025-07-22 21:53:44,887] [DEBUG] 未找到账号 m88 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,887] [DEBUG] 更新账号 m9 的表格数据，是否有数据: True
[2025-07-22 21:53:44,887] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,887] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,887] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,888] [DEBUG] 开始查找账号 m9 对应的行索引
[2025-07-22 21:53:44,888] [DEBUG] 未找到账号 m9 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,888] [DEBUG] 更新账号 m91 的表格数据，是否有数据: True
[2025-07-22 21:53:44,888] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,888] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,888] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,888] [DEBUG] 开始查找账号 m91 对应的行索引
[2025-07-22 21:53:44,888] [DEBUG] 未找到账号 m91 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,888] [DEBUG] 更新账号 m92 的表格数据，是否有数据: True
[2025-07-22 21:53:44,889] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,889] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,889] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,889] [DEBUG] 开始查找账号 m92 对应的行索引
[2025-07-22 21:53:44,889] [DEBUG] 未找到账号 m92 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,890] [DEBUG] 更新账号 m93 的表格数据，是否有数据: True
[2025-07-22 21:53:44,890] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,890] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,890] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,891] [DEBUG] 开始查找账号 m93 对应的行索引
[2025-07-22 21:53:44,891] [DEBUG] 未找到账号 m93 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,891] [DEBUG] 更新账号 m94 的表格数据，是否有数据: True
[2025-07-22 21:53:44,892] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,892] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,892] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,892] [DEBUG] 开始查找账号 m94 对应的行索引
[2025-07-22 21:53:44,892] [DEBUG] 未找到账号 m94 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,893] [DEBUG] 更新账号 m95 的表格数据，是否有数据: True
[2025-07-22 21:53:44,893] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,893] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,893] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,893] [DEBUG] 开始查找账号 m95 对应的行索引
[2025-07-22 21:53:44,894] [DEBUG] 未找到账号 m95 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,894] [DEBUG] 更新账号 m96 的表格数据，是否有数据: True
[2025-07-22 21:53:44,894] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,894] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,894] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,895] [DEBUG] 开始查找账号 m96 对应的行索引
[2025-07-22 21:53:44,895] [DEBUG] 未找到账号 m96 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,895] [DEBUG] 更新账号 m97 的表格数据，是否有数据: True
[2025-07-22 21:53:44,895] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,896] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,896] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,896] [DEBUG] 开始查找账号 m97 对应的行索引
[2025-07-22 21:53:44,897] [DEBUG] 未找到账号 m97 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,897] [DEBUG] 更新账号 m1 的表格数据，是否有数据: True
[2025-07-22 21:53:44,897] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,897] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,898] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,898] [DEBUG] 开始查找账号 m1 对应的行索引
[2025-07-22 21:53:44,898] [DEBUG] 未找到账号 m1 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,898] [DEBUG] 更新账号 m10 的表格数据，是否有数据: True
[2025-07-22 21:53:44,898] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,898] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,898] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,898] [DEBUG] 开始查找账号 m10 对应的行索引
[2025-07-22 21:53:44,900] [DEBUG] 未找到账号 m10 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,900] [DEBUG] 更新账号 m100 的表格数据，是否有数据: True
[2025-07-22 21:53:44,900] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,900] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,901] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,901] [DEBUG] 开始查找账号 m100 对应的行索引
[2025-07-22 21:53:44,901] [DEBUG] 未找到账号 m100 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,901] [DEBUG] 更新账号 m101 的表格数据，是否有数据: True
[2025-07-22 21:53:44,901] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,902] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,902] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,902] [DEBUG] 开始查找账号 m101 对应的行索引
[2025-07-22 21:53:44,902] [DEBUG] 未找到账号 m101 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,902] [DEBUG] 更新账号 m102 的表格数据，是否有数据: True
[2025-07-22 21:53:44,903] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,903] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,903] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,903] [DEBUG] 开始查找账号 m102 对应的行索引
[2025-07-22 21:53:44,903] [DEBUG] 未找到账号 m102 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,904] [DEBUG] 更新账号 m103 的表格数据，是否有数据: True
[2025-07-22 21:53:44,904] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,904] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,904] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,904] [DEBUG] 开始查找账号 m103 对应的行索引
[2025-07-22 21:53:44,905] [DEBUG] 未找到账号 m103 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,905] [DEBUG] 更新账号 m104 的表格数据，是否有数据: True
[2025-07-22 21:53:44,905] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,905] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,905] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,906] [DEBUG] 开始查找账号 m104 对应的行索引
[2025-07-22 21:53:44,906] [DEBUG] 未找到账号 m104 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,906] [DEBUG] 更新账号 m12 的表格数据，是否有数据: True
[2025-07-22 21:53:44,907] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,907] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,907] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,907] [DEBUG] 开始查找账号 m12 对应的行索引
[2025-07-22 21:53:44,908] [DEBUG] 未找到账号 m12 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,908] [DEBUG] 更新账号 m13 的表格数据，是否有数据: True
[2025-07-22 21:53:44,908] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,908] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,908] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,909] [DEBUG] 开始查找账号 m13 对应的行索引
[2025-07-22 21:53:44,909] [DEBUG] 未找到账号 m13 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,909] [DEBUG] 更新账号 m141 的表格数据，是否有数据: True
[2025-07-22 21:53:44,909] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,909] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,909] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,909] [DEBUG] 开始查找账号 m141 对应的行索引
[2025-07-22 21:53:44,910] [DEBUG] 未找到账号 m141 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,910] [DEBUG] 更新账号 m142 的表格数据，是否有数据: True
[2025-07-22 21:53:44,910] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,910] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,911] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,911] [DEBUG] 开始查找账号 m142 对应的行索引
[2025-07-22 21:53:44,911] [DEBUG] 未找到账号 m142 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,911] [DEBUG] 更新账号 m143 的表格数据，是否有数据: True
[2025-07-22 21:53:44,911] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,912] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,912] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,912] [DEBUG] 开始查找账号 m143 对应的行索引
[2025-07-22 21:53:44,912] [DEBUG] 未找到账号 m143 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,912] [DEBUG] 更新账号 m15 的表格数据，是否有数据: True
[2025-07-22 21:53:44,913] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,913] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,913] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,913] [DEBUG] 开始查找账号 m15 对应的行索引
[2025-07-22 21:53:44,913] [DEBUG] 未找到账号 m15 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,914] [DEBUG] 更新账号 m151 的表格数据，是否有数据: True
[2025-07-22 21:53:44,914] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,914] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,914] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,915] [DEBUG] 开始查找账号 m151 对应的行索引
[2025-07-22 21:53:44,915] [DEBUG] 未找到账号 m151 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,915] [DEBUG] 更新账号 m152 的表格数据，是否有数据: True
[2025-07-22 21:53:44,915] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,915] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,916] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,916] [DEBUG] 开始查找账号 m152 对应的行索引
[2025-07-22 21:53:44,916] [DEBUG] 未找到账号 m152 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,916] [DEBUG] 更新账号 m153 的表格数据，是否有数据: True
[2025-07-22 21:53:44,917] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,917] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,917] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,917] [DEBUG] 开始查找账号 m153 对应的行索引
[2025-07-22 21:53:44,917] [DEBUG] 未找到账号 m153 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,918] [DEBUG] 更新账号 m154 的表格数据，是否有数据: True
[2025-07-22 21:53:44,918] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,918] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,918] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,918] [DEBUG] 开始查找账号 m154 对应的行索引
[2025-07-22 21:53:44,918] [DEBUG] 未找到账号 m154 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,919] [DEBUG] 更新账号 m155 的表格数据，是否有数据: True
[2025-07-22 21:53:44,919] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,919] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,919] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,920] [DEBUG] 开始查找账号 m155 对应的行索引
[2025-07-22 21:53:44,920] [DEBUG] 未找到账号 m155 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,920] [DEBUG] 更新账号 m156 的表格数据，是否有数据: True
[2025-07-22 21:53:44,920] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,921] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,921] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,921] [DEBUG] 开始查找账号 m156 对应的行索引
[2025-07-22 21:53:44,921] [DEBUG] 未找到账号 m156 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,923] [DEBUG] 更新账号 m157 的表格数据，是否有数据: True
[2025-07-22 21:53:44,923] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,923] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,923] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,924] [DEBUG] 开始查找账号 m157 对应的行索引
[2025-07-22 21:53:44,924] [DEBUG] 未找到账号 m157 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,924] [DEBUG] 更新账号 m158 的表格数据，是否有数据: True
[2025-07-22 21:53:44,924] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,924] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,924] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,925] [DEBUG] 开始查找账号 m158 对应的行索引
[2025-07-22 21:53:44,925] [DEBUG] 未找到账号 m158 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,925] [DEBUG] 更新账号 m159 的表格数据，是否有数据: True
[2025-07-22 21:53:44,925] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,925] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,926] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,926] [DEBUG] 开始查找账号 m159 对应的行索引
[2025-07-22 21:53:44,926] [DEBUG] 未找到账号 m159 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,926] [DEBUG] 更新账号 m16 的表格数据，是否有数据: True
[2025-07-22 21:53:44,926] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,927] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,927] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,927] [DEBUG] 开始查找账号 m16 对应的行索引
[2025-07-22 21:53:44,927] [DEBUG] 未找到账号 m16 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,927] [DEBUG] 更新账号 m161 的表格数据，是否有数据: True
[2025-07-22 21:53:44,928] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,928] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,928] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,928] [DEBUG] 开始查找账号 m161 对应的行索引
[2025-07-22 21:53:44,928] [DEBUG] 未找到账号 m161 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,929] [DEBUG] 更新账号 m162 的表格数据，是否有数据: True
[2025-07-22 21:53:44,929] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,929] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,929] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,929] [DEBUG] 开始查找账号 m162 对应的行索引
[2025-07-22 21:53:44,930] [DEBUG] 未找到账号 m162 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,930] [DEBUG] 更新账号 m163 的表格数据，是否有数据: True
[2025-07-22 21:53:44,930] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,930] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,930] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,931] [DEBUG] 开始查找账号 m163 对应的行索引
[2025-07-22 21:53:44,931] [DEBUG] 未找到账号 m163 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,931] [DEBUG] 更新账号 m164 的表格数据，是否有数据: True
[2025-07-22 21:53:44,931] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,931] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,932] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,932] [DEBUG] 开始查找账号 m164 对应的行索引
[2025-07-22 21:53:44,932] [DEBUG] 未找到账号 m164 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,932] [DEBUG] 更新账号 m165 的表格数据，是否有数据: True
[2025-07-22 21:53:44,932] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,933] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,933] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,933] [DEBUG] 开始查找账号 m165 对应的行索引
[2025-07-22 21:53:44,933] [DEBUG] 未找到账号 m165 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,933] [DEBUG] 更新账号 m166 的表格数据，是否有数据: True
[2025-07-22 21:53:44,934] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,934] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,934] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,934] [DEBUG] 开始查找账号 m166 对应的行索引
[2025-07-22 21:53:44,934] [DEBUG] 未找到账号 m166 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,935] [DEBUG] 更新账号 m167 的表格数据，是否有数据: True
[2025-07-22 21:53:44,935] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,935] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,935] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,935] [DEBUG] 开始查找账号 m167 对应的行索引
[2025-07-22 21:53:44,936] [DEBUG] 未找到账号 m167 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,936] [DEBUG] 更新账号 m168 的表格数据，是否有数据: True
[2025-07-22 21:53:44,936] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,936] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,936] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,936] [DEBUG] 开始查找账号 m168 对应的行索引
[2025-07-22 21:53:44,937] [DEBUG] 未找到账号 m168 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,937] [DEBUG] 更新账号 m17 的表格数据，是否有数据: True
[2025-07-22 21:53:44,937] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,938] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,938] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,938] [DEBUG] 开始查找账号 m17 对应的行索引
[2025-07-22 21:53:44,938] [DEBUG] 未找到账号 m17 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,939] [DEBUG] 更新账号 m171 的表格数据，是否有数据: True
[2025-07-22 21:53:44,939] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,939] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,939] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,939] [DEBUG] 开始查找账号 m171 对应的行索引
[2025-07-22 21:53:44,940] [DEBUG] 未找到账号 m171 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,940] [DEBUG] 更新账号 m172 的表格数据，是否有数据: True
[2025-07-22 21:53:44,940] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,940] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,941] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,941] [DEBUG] 开始查找账号 m172 对应的行索引
[2025-07-22 21:53:44,941] [DEBUG] 未找到账号 m172 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,941] [DEBUG] 更新账号 m173 的表格数据，是否有数据: True
[2025-07-22 21:53:44,941] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,941] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,941] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,942] [DEBUG] 开始查找账号 m173 对应的行索引
[2025-07-22 21:53:44,942] [DEBUG] 未找到账号 m173 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,942] [DEBUG] 更新账号 m174 的表格数据，是否有数据: True
[2025-07-22 21:53:44,942] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,942] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,943] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,943] [DEBUG] 开始查找账号 m174 对应的行索引
[2025-07-22 21:53:44,943] [DEBUG] 未找到账号 m174 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,943] [DEBUG] 更新账号 m175 的表格数据，是否有数据: True
[2025-07-22 21:53:44,943] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,944] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,944] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,944] [DEBUG] 开始查找账号 m175 对应的行索引
[2025-07-22 21:53:44,944] [DEBUG] 未找到账号 m175 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,945] [DEBUG] 更新账号 m176 的表格数据，是否有数据: True
[2025-07-22 21:53:44,945] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,945] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,945] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,946] [DEBUG] 开始查找账号 m176 对应的行索引
[2025-07-22 21:53:44,946] [DEBUG] 未找到账号 m176 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,946] [DEBUG] 更新账号 m177 的表格数据，是否有数据: True
[2025-07-22 21:53:44,946] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,946] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,947] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,947] [DEBUG] 开始查找账号 m177 对应的行索引
[2025-07-22 21:53:44,947] [DEBUG] 未找到账号 m177 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,947] [DEBUG] 更新账号 m178 的表格数据，是否有数据: True
[2025-07-22 21:53:44,948] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,948] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,948] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,948] [DEBUG] 开始查找账号 m178 对应的行索引
[2025-07-22 21:53:44,948] [DEBUG] 未找到账号 m178 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,949] [DEBUG] 更新账号 m179 的表格数据，是否有数据: True
[2025-07-22 21:53:44,949] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,949] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,949] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,949] [DEBUG] 开始查找账号 m179 对应的行索引
[2025-07-22 21:53:44,950] [DEBUG] 未找到账号 m179 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,950] [DEBUG] 更新账号 m18 的表格数据，是否有数据: True
[2025-07-22 21:53:44,950] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,950] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,950] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,951] [DEBUG] 开始查找账号 m18 对应的行索引
[2025-07-22 21:53:44,951] [DEBUG] 未找到账号 m18 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,951] [DEBUG] 更新账号 m19 的表格数据，是否有数据: True
[2025-07-22 21:53:44,951] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,951] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,951] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,953] [DEBUG] 开始查找账号 m19 对应的行索引
[2025-07-22 21:53:44,953] [DEBUG] 未找到账号 m19 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,953] [DEBUG] 更新账号 m2 的表格数据，是否有数据: True
[2025-07-22 21:53:44,953] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,954] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,954] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,954] [DEBUG] 开始查找账号 m2 对应的行索引
[2025-07-22 21:53:44,955] [DEBUG] 未找到账号 m2 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,955] [DEBUG] 更新账号 m20 的表格数据，是否有数据: True
[2025-07-22 21:53:44,955] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,955] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,956] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,956] [DEBUG] 开始查找账号 m20 对应的行索引
[2025-07-22 21:53:44,956] [DEBUG] 未找到账号 m20 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,957] [DEBUG] 更新账号 m22 的表格数据，是否有数据: True
[2025-07-22 21:53:44,957] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,957] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,958] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,958] [DEBUG] 开始查找账号 m22 对应的行索引
[2025-07-22 21:53:44,958] [DEBUG] 未找到账号 m22 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,959] [DEBUG] 更新账号 m26 的表格数据，是否有数据: True
[2025-07-22 21:53:44,959] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,959] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,960] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,960] [DEBUG] 开始查找账号 m26 对应的行索引
[2025-07-22 21:53:44,960] [DEBUG] 未找到账号 m26 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,961] [DEBUG] 更新账号 m27 的表格数据，是否有数据: True
[2025-07-22 21:53:44,961] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,961] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,962] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,962] [DEBUG] 开始查找账号 m27 对应的行索引
[2025-07-22 21:53:44,962] [DEBUG] 未找到账号 m27 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,962] [DEBUG] 更新账号 m28 的表格数据，是否有数据: True
[2025-07-22 21:53:44,962] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,963] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,963] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,963] [DEBUG] 开始查找账号 m28 对应的行索引
[2025-07-22 21:53:44,964] [DEBUG] 未找到账号 m28 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:44,964] [DEBUG] 更新账号 m46 的表格数据，是否有数据: True
[2025-07-22 21:53:44,965] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:44,965] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:44,965] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:44,966] [DEBUG] 开始查找账号 m46 对应的行索引
[2025-07-22 21:53:44,966] [DEBUG] 未找到账号 m46 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:45,009] [INFO] 用户取消后台加载
[2025-07-22 21:53:45,010] [INFO] 后台加载完成: 账号数据加载完成
[2025-07-22 21:53:45,224] [DEBUG] 批量数据处理完成后内存使用: 248.35 MB
[2025-07-22 21:53:45,226] [ERROR] 更新加载进度时出错: 'NoneType' object has no attribute 'setLabelText'
[2025-07-22 21:53:45,273] [INFO] 账号加载模式: 延迟加载
[2025-07-22 21:53:45,274] [INFO] 数据目录功能已移除
[2025-07-22 21:53:45,274] [INFO] 开始同步加载Cookie文件，路径: E:/软件共享/账号合并
[2025-07-22 21:53:45,278] [INFO] 路径 E:/软件共享/账号合并 下有 0 个JSON文件和 305 个TXT文件
[2025-07-22 21:53:45,279] [INFO] 调用account_loader.load_accounts_sync方法前
[2025-07-22 21:53:47,485] [INFO] account_loader.load_accounts_sync方法返回: success=True, accounts数量=305, error_msg=
[2025-07-22 21:53:47,485] [INFO] 同步加载账号数据完成，结果: True, 账号数量: 305, 错误信息: 
[2025-07-22 21:53:47,485] [INFO] 账号数据加载成功，共 305 个账号
[2025-07-22 21:53:47,485] [INFO] 账号加载完成回调开始时内存使用: 257.68 MB
[2025-07-22 21:53:47,486] [INFO] 已创建账号列表的浅拷贝，大小: 305
[2025-07-22 21:53:47,486] [INFO] 成功加载 305 个账号文件，延迟加载模式: True
[2025-07-22 21:53:47,518] [INFO] 账号列表大小: 305
[2025-07-22 21:53:47,518] [INFO] 第一个账号信息: {'file_path': 'E:/软件共享/账号合并\\***********.txt', 'account_id': '***********', 'data': {'accountId': '***********', 'remark': '***********', 'cookies': {'xigua_csrf_token': 'Rh9tB8w72kFdjHWXzmwLx4FN', 'is_staff_user': 'false', 'sessionid_ss': '68c4cc3f1c5dedd10b37702f8cfc5581', 'uid_tt_ss': '4138e04b7027f10e81ee57334a3871f1', 'store-region': 'cn-gd', 'uid_tt': '4138e04b7027f10e81ee57334a3871f1', 'passport_auth_status': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'passport_mfa_token': 'CjfDS1ognyyaR5OXvuo1ZhOlSC8orpJykXqU3XrL0%2F4LK3qwDRucEFU8wl5EeoQDFgQXMDSnVv64GkoKPIA87d76RVvezzRe1eiXyd81F%2FQYdzX0iWRhqgsPoaSXZlpDutIfPN2NISNG6lXHe%2BhFo3Alo9axe2OZMhDY3OsNGPax0WwgAiIBA6K42yM%3D', 'passport_auth_status_ss': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'sid_tt': '68c4cc3f1c5dedd10b37702f8cfc5581', 'odin_tt': 'f681b2fee0f84dd926c09d58e132e2328c436e9fcf004f758697a032da7adc66d0d445a80508f8d675d1357603c3efe11f5237f59ac2ca2893e80a3e5da52dcf', 'sessionid': '68c4cc3f1c5dedd10b37702f8cfc5581', 'gfgarrcache_8005812e': '1', 'ssid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'toutiao_sso_user_ss': 'c2032ee116a4c329079d18ddc99f62c6', 'gfgarrcache_6a2703f1': '1', 'sid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'store-region-src': 'uid', 'sid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'toutiao_sso_user': 'c2032ee116a4c329079d18ddc99f62c6', 'sso_uid_tt_ss': '707d66e7d745b6ca510877ff49d6deca', 'xg_p_tos_token': '79aa2d6e8307f3027308bb8063808201', 'sso_uid_tt': '707d66e7d745b6ca510877ff49d6deca', 'sso_auth_status_ss': '99d6be0fe40fbeccd26dbd011f7e4345', 'ssid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'sid_guard': '68c4cc3f1c5dedd10b37702f8cfc5581%7C1741687410%7C5184002%7CSat%2C+10-May-2025+10%3A03%3A32+GMT', 'sso_auth_status': '99d6be0fe40fbeccd26dbd011f7e4345', 'passport_csrf_token': '3d631c058164d798d48475833f13446e', 'n_mh': '30A9sphpGJEPcXGYEGo8hiNGyEbb5VtQ_Ov5DGw0tbA', 'ttwid': '1%7C0FfHeK6FyRv027NKpnZinttVopHTQty8nSXP9P7wnNw%7C1741687388%7C33b1d18a6f3d000bd20fd4a1d0f5d6b37682c37752a91a6e1265423a4b1e9968', 'gfkadpd': '1231,25897', 'd_ticket': '0da2a81adc5bb557ff718c555c0a4801d76e7', 'passport_csrf_token_default': '3d631c058164d798d48475833f13446e', 'csrf_session_id': 'e1a99642f2b9648ef30c79ef589a852a', 's_v_web_id': 'verify_m84bp59m_dED7JGij_AkV2_4J3T_B268_83sqBwAURoyE'}}, 'file_type': '.txt'}
[2025-07-22 21:53:47,519] [INFO] 使用延迟加载模式，准备更新表格基本信息
[2025-07-22 21:53:47,522] [DEBUG] 已导入高级内存管理器
[2025-07-22 21:53:47,538] [INFO] 更新表格开始时内存使用: 257.77MB, 可用: 899.57MB/16236.06MB
[2025-07-22 21:53:47,570] [INFO] 准备设置表格行数: 305
[2025-07-22 21:53:47,570] [INFO] 已设置表格行数: 305，当前表格行数: 305
[2025-07-22 21:53:47,571] [DEBUG] 处理账号批次: 1-50/305
[2025-07-22 21:53:47,816] [DEBUG] 处理账号批次: 51-100/305
[2025-07-22 21:53:48,063] [DEBUG] 处理账号批次: 101-150/305
[2025-07-22 21:53:48,294] [DEBUG] 处理账号批次: 151-200/305
[2025-07-22 21:53:48,542] [DEBUG] 处理账号批次: 201-250/305
[2025-07-22 21:53:48,802] [DEBUG] 处理账号批次: 251-300/305
[2025-07-22 21:53:49,049] [DEBUG] 处理账号批次: 301-305/305
[2025-07-22 21:53:49,208] [INFO] 表格更新完成，耗时: 1.67秒，内存使用: 353.76MB
[2025-07-22 21:53:49,208] [INFO] 使用延迟加载模式，已更新表格基本信息
[2025-07-22 21:53:49,208] [INFO] 延迟加载模式下，表格行数: 305
[2025-07-22 21:53:49,261] [INFO] 不设置账号已加载标志，允许重复加载
[2025-07-22 21:53:49,261] [INFO] 开始计算收益统计，表格行数: 305, 列数: 19
[2025-07-22 21:53:49,263] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-22 21:53:49,264] [INFO] 七天总收益: ¥0.00
[2025-07-22 21:53:49,264] [INFO] 昨日总收益: ¥0.00
[2025-07-22 21:53:49,265] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-22 21:53:49,267] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-22 21:53:52,546] [INFO] 自动化配置加载成功
[2025-07-22 21:53:52,546] [INFO] 自动化任务调度器初始化完成
[2025-07-22 21:53:52,553] [INFO] 心跳状态指示器初始化完成
[2025-07-22 21:53:52,604] [INFO] 跳过示例数据加载，等待真实账号数据
[2025-07-22 21:53:52,610] [ERROR] 连接表格信号时出错: 'AccountTab' object has no attribute 'on_cell_changed'
[2025-07-22 21:53:52,711] [INFO] 开始计算收益统计，表格行数: 305, 列数: 19
[2025-07-22 21:53:52,713] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-22 21:53:52,713] [INFO] 七天总收益: ¥0.00
[2025-07-22 21:53:52,714] [INFO] 昨日总收益: ¥0.00
[2025-07-22 21:53:52,714] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-22 21:53:52,719] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-22 21:53:53,619] [INFO] 账号加载模式: 延迟加载
[2025-07-22 21:53:53,620] [INFO] 数据目录功能已移除
[2025-07-22 21:53:53,620] [INFO] 开始同步加载Cookie文件，路径: E:/软件共享/账号合并
[2025-07-22 21:53:53,621] [INFO] 路径 E:/软件共享/账号合并 下有 0 个JSON文件和 305 个TXT文件
[2025-07-22 21:53:53,621] [INFO] 调用account_loader.load_accounts_sync方法前
[2025-07-22 21:53:53,624] [INFO] 启动快速启动模式 - 后台加载账号数据
[2025-07-22 21:53:53,748] [DEBUG] 批量数据处理开始时内存使用: 250.75 MB，数据量: 100
[2025-07-22 21:53:53,749] [DEBUG] 更新账号 m24 的表格数据，是否有数据: True
[2025-07-22 21:53:53,749] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,750] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,750] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,750] [DEBUG] 开始查找账号 m24 对应的行索引
[2025-07-22 21:53:53,750] [DEBUG] 未找到账号 m24 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,751] [DEBUG] 更新账号 m35 的表格数据，是否有数据: True
[2025-07-22 21:53:53,751] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,751] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,752] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,752] [DEBUG] 开始查找账号 m35 对应的行索引
[2025-07-22 21:53:53,752] [DEBUG] 未找到账号 m35 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,753] [DEBUG] 更新账号 m25 的表格数据，是否有数据: True
[2025-07-22 21:53:53,753] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,754] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,754] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,754] [DEBUG] 开始查找账号 m25 对应的行索引
[2025-07-22 21:53:53,755] [DEBUG] 未找到账号 m25 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,755] [DEBUG] 更新账号 m37 的表格数据，是否有数据: True
[2025-07-22 21:53:53,755] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,756] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,756] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,756] [DEBUG] 开始查找账号 m37 对应的行索引
[2025-07-22 21:53:53,756] [DEBUG] 未找到账号 m37 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,757] [DEBUG] 更新账号 m29 的表格数据，是否有数据: True
[2025-07-22 21:53:53,757] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,757] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,758] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,758] [DEBUG] 开始查找账号 m29 对应的行索引
[2025-07-22 21:53:53,758] [DEBUG] 未找到账号 m29 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,758] [DEBUG] 更新账号 m3 的表格数据，是否有数据: True
[2025-07-22 21:53:53,759] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,759] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,759] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,760] [DEBUG] 开始查找账号 m3 对应的行索引
[2025-07-22 21:53:53,760] [DEBUG] 未找到账号 m3 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,760] [DEBUG] 更新账号 m30 的表格数据，是否有数据: True
[2025-07-22 21:53:53,760] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,761] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,761] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,761] [DEBUG] 开始查找账号 m30 对应的行索引
[2025-07-22 21:53:53,762] [DEBUG] 未找到账号 m30 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,762] [DEBUG] 更新账号 m33 的表格数据，是否有数据: True
[2025-07-22 21:53:53,762] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,763] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,763] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,763] [DEBUG] 开始查找账号 m33 对应的行索引
[2025-07-22 21:53:53,763] [DEBUG] 未找到账号 m33 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,764] [DEBUG] 更新账号 m34 的表格数据，是否有数据: True
[2025-07-22 21:53:53,764] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,764] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,767] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,768] [DEBUG] 开始查找账号 m34 对应的行索引
[2025-07-22 21:53:53,768] [DEBUG] 未找到账号 m34 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,768] [DEBUG] 更新账号 m36 的表格数据，是否有数据: True
[2025-07-22 21:53:53,769] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,769] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,769] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,769] [DEBUG] 开始查找账号 m36 对应的行索引
[2025-07-22 21:53:53,770] [DEBUG] 未找到账号 m36 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,770] [DEBUG] 更新账号 m38 的表格数据，是否有数据: True
[2025-07-22 21:53:53,770] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,771] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,771] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,771] [DEBUG] 开始查找账号 m38 对应的行索引
[2025-07-22 21:53:53,771] [DEBUG] 未找到账号 m38 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,772] [DEBUG] 更新账号 m39 的表格数据，是否有数据: True
[2025-07-22 21:53:53,772] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,772] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,773] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,773] [DEBUG] 开始查找账号 m39 对应的行索引
[2025-07-22 21:53:53,773] [DEBUG] 未找到账号 m39 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,773] [DEBUG] 更新账号 m40 的表格数据，是否有数据: True
[2025-07-22 21:53:53,774] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,774] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,774] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,774] [DEBUG] 开始查找账号 m40 对应的行索引
[2025-07-22 21:53:53,774] [DEBUG] 未找到账号 m40 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,774] [DEBUG] 更新账号 m41 的表格数据，是否有数据: True
[2025-07-22 21:53:53,775] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,775] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,775] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,776] [DEBUG] 开始查找账号 m41 对应的行索引
[2025-07-22 21:53:53,776] [DEBUG] 未找到账号 m41 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,776] [DEBUG] 更新账号 m52 的表格数据，是否有数据: True
[2025-07-22 21:53:53,777] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,777] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,777] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,777] [DEBUG] 开始查找账号 m52 对应的行索引
[2025-07-22 21:53:53,777] [DEBUG] 未找到账号 m52 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,778] [DEBUG] 更新账号 m55 的表格数据，是否有数据: True
[2025-07-22 21:53:53,778] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,778] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,779] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,779] [DEBUG] 开始查找账号 m55 对应的行索引
[2025-07-22 21:53:53,779] [DEBUG] 未找到账号 m55 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,779] [DEBUG] 更新账号 m58 的表格数据，是否有数据: True
[2025-07-22 21:53:53,780] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,780] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,780] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,781] [DEBUG] 开始查找账号 m58 对应的行索引
[2025-07-22 21:53:53,781] [DEBUG] 未找到账号 m58 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,781] [DEBUG] 更新账号 m43 的表格数据，是否有数据: True
[2025-07-22 21:53:53,781] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,782] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,782] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,782] [DEBUG] 开始查找账号 m43 对应的行索引
[2025-07-22 21:53:53,783] [DEBUG] 未找到账号 m43 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,783] [DEBUG] 更新账号 m61 的表格数据，是否有数据: True
[2025-07-22 21:53:53,783] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,783] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,784] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,784] [DEBUG] 开始查找账号 m61 对应的行索引
[2025-07-22 21:53:53,784] [DEBUG] 未找到账号 m61 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,784] [DEBUG] 更新账号 m44 的表格数据，是否有数据: True
[2025-07-22 21:53:53,784] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,786] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,787] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,788] [DEBUG] 开始查找账号 m44 对应的行索引
[2025-07-22 21:53:53,789] [DEBUG] 未找到账号 m44 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,789] [DEBUG] 更新账号 m64 的表格数据，是否有数据: True
[2025-07-22 21:53:53,790] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,790] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,790] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,791] [DEBUG] 开始查找账号 m64 对应的行索引
[2025-07-22 21:53:53,791] [DEBUG] 未找到账号 m64 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,791] [DEBUG] 更新账号 m65 的表格数据，是否有数据: True
[2025-07-22 21:53:53,791] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,792] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,792] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,793] [DEBUG] 开始查找账号 m65 对应的行索引
[2025-07-22 21:53:53,793] [DEBUG] 未找到账号 m65 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,794] [DEBUG] 更新账号 m66 的表格数据，是否有数据: True
[2025-07-22 21:53:53,794] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,794] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,795] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,795] [DEBUG] 开始查找账号 m66 对应的行索引
[2025-07-22 21:53:53,795] [DEBUG] 未找到账号 m66 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,795] [DEBUG] 更新账号 m47 的表格数据，是否有数据: True
[2025-07-22 21:53:53,795] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,797] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,798] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,799] [DEBUG] 开始查找账号 m47 对应的行索引
[2025-07-22 21:53:53,800] [DEBUG] 未找到账号 m47 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,800] [DEBUG] 更新账号 m48 的表格数据，是否有数据: True
[2025-07-22 21:53:53,800] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,801] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,801] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,801] [DEBUG] 开始查找账号 m48 对应的行索引
[2025-07-22 21:53:53,802] [DEBUG] 未找到账号 m48 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,802] [DEBUG] 更新账号 m5 的表格数据，是否有数据: True
[2025-07-22 21:53:53,803] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,803] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,803] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,804] [DEBUG] 开始查找账号 m5 对应的行索引
[2025-07-22 21:53:53,805] [DEBUG] 未找到账号 m5 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,805] [DEBUG] 更新账号 m54 的表格数据，是否有数据: True
[2025-07-22 21:53:53,805] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,805] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,807] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,807] [DEBUG] 开始查找账号 m54 对应的行索引
[2025-07-22 21:53:53,807] [DEBUG] 未找到账号 m54 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,807] [DEBUG] 更新账号 m60 的表格数据，是否有数据: True
[2025-07-22 21:53:53,808] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,808] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,808] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,809] [DEBUG] 开始查找账号 m60 对应的行索引
[2025-07-22 21:53:53,809] [DEBUG] 未找到账号 m60 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,809] [DEBUG] 更新账号 m6 的表格数据，是否有数据: True
[2025-07-22 21:53:53,810] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,810] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,810] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,811] [DEBUG] 开始查找账号 m6 对应的行索引
[2025-07-22 21:53:53,811] [DEBUG] 未找到账号 m6 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,811] [DEBUG] 更新账号 m62 的表格数据，是否有数据: True
[2025-07-22 21:53:53,812] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,812] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,812] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,813] [DEBUG] 开始查找账号 m62 对应的行索引
[2025-07-22 21:53:53,813] [DEBUG] 未找到账号 m62 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,813] [DEBUG] 更新账号 m63 的表格数据，是否有数据: True
[2025-07-22 21:53:53,814] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,814] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,814] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,815] [DEBUG] 开始查找账号 m63 对应的行索引
[2025-07-22 21:53:53,815] [DEBUG] 未找到账号 m63 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,815] [DEBUG] 更新账号 m76 的表格数据，是否有数据: True
[2025-07-22 21:53:53,816] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,816] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,816] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,817] [DEBUG] 开始查找账号 m76 对应的行索引
[2025-07-22 21:53:53,817] [DEBUG] 未找到账号 m76 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,817] [DEBUG] 更新账号 m7 的表格数据，是否有数据: True
[2025-07-22 21:53:53,818] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,818] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,818] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,819] [DEBUG] 开始查找账号 m7 对应的行索引
[2025-07-22 21:53:53,819] [DEBUG] 未找到账号 m7 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,819] [DEBUG] 更新账号 m70 的表格数据，是否有数据: True
[2025-07-22 21:53:53,820] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,820] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,820] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,821] [DEBUG] 开始查找账号 m70 对应的行索引
[2025-07-22 21:53:53,821] [DEBUG] 未找到账号 m70 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,821] [DEBUG] 更新账号 m71 的表格数据，是否有数据: True
[2025-07-22 21:53:53,821] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,822] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,822] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,822] [DEBUG] 开始查找账号 m71 对应的行索引
[2025-07-22 21:53:53,823] [DEBUG] 未找到账号 m71 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,823] [DEBUG] 更新账号 m72 的表格数据，是否有数据: True
[2025-07-22 21:53:53,823] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,823] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,823] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,824] [DEBUG] 开始查找账号 m72 对应的行索引
[2025-07-22 21:53:53,824] [DEBUG] 未找到账号 m72 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,824] [DEBUG] 更新账号 m74 的表格数据，是否有数据: True
[2025-07-22 21:53:53,825] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,825] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,825] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,825] [DEBUG] 开始查找账号 m74 对应的行索引
[2025-07-22 21:53:53,826] [DEBUG] 未找到账号 m74 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,826] [DEBUG] 更新账号 m73 的表格数据，是否有数据: True
[2025-07-22 21:53:53,826] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,826] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,827] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,827] [DEBUG] 开始查找账号 m73 对应的行索引
[2025-07-22 21:53:53,827] [DEBUG] 未找到账号 m73 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,827] [DEBUG] 更新账号 m75 的表格数据，是否有数据: True
[2025-07-22 21:53:53,827] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,828] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,828] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,828] [DEBUG] 开始查找账号 m75 对应的行索引
[2025-07-22 21:53:53,828] [DEBUG] 未找到账号 m75 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,829] [DEBUG] 更新账号 m8 的表格数据，是否有数据: True
[2025-07-22 21:53:53,829] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,829] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,829] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,829] [DEBUG] 开始查找账号 m8 对应的行索引
[2025-07-22 21:53:53,830] [DEBUG] 未找到账号 m8 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,830] [DEBUG] 更新账号 m86 的表格数据，是否有数据: True
[2025-07-22 21:53:53,830] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,831] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,831] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,831] [DEBUG] 开始查找账号 m86 对应的行索引
[2025-07-22 21:53:53,831] [DEBUG] 未找到账号 m86 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,832] [DEBUG] 更新账号 m88 的表格数据，是否有数据: True
[2025-07-22 21:53:53,832] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,832] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,832] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,833] [DEBUG] 开始查找账号 m88 对应的行索引
[2025-07-22 21:53:53,833] [DEBUG] 未找到账号 m88 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,833] [DEBUG] 更新账号 m9 的表格数据，是否有数据: True
[2025-07-22 21:53:53,833] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,833] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,834] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,834] [DEBUG] 开始查找账号 m9 对应的行索引
[2025-07-22 21:53:53,834] [DEBUG] 未找到账号 m9 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,835] [DEBUG] 更新账号 m91 的表格数据，是否有数据: True
[2025-07-22 21:53:53,835] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,836] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,836] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,836] [DEBUG] 开始查找账号 m91 对应的行索引
[2025-07-22 21:53:53,837] [DEBUG] 未找到账号 m91 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,837] [DEBUG] 更新账号 m92 的表格数据，是否有数据: True
[2025-07-22 21:53:53,837] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,837] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,837] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,839] [DEBUG] 开始查找账号 m92 对应的行索引
[2025-07-22 21:53:53,839] [DEBUG] 未找到账号 m92 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,839] [DEBUG] 更新账号 m93 的表格数据，是否有数据: True
[2025-07-22 21:53:53,839] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,840] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,840] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,841] [DEBUG] 开始查找账号 m93 对应的行索引
[2025-07-22 21:53:53,841] [DEBUG] 未找到账号 m93 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,841] [DEBUG] 更新账号 m94 的表格数据，是否有数据: True
[2025-07-22 21:53:53,842] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,842] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,842] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,843] [DEBUG] 开始查找账号 m94 对应的行索引
[2025-07-22 21:53:53,843] [DEBUG] 未找到账号 m94 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,843] [DEBUG] 更新账号 m95 的表格数据，是否有数据: True
[2025-07-22 21:53:53,843] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,844] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,844] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,844] [DEBUG] 开始查找账号 m95 对应的行索引
[2025-07-22 21:53:53,844] [DEBUG] 未找到账号 m95 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,845] [DEBUG] 更新账号 m96 的表格数据，是否有数据: True
[2025-07-22 21:53:53,845] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,845] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,845] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,846] [DEBUG] 开始查找账号 m96 对应的行索引
[2025-07-22 21:53:53,846] [DEBUG] 未找到账号 m96 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,846] [DEBUG] 更新账号 m97 的表格数据，是否有数据: True
[2025-07-22 21:53:53,846] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,847] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,847] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,847] [DEBUG] 开始查找账号 m97 对应的行索引
[2025-07-22 21:53:53,847] [DEBUG] 未找到账号 m97 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,848] [DEBUG] 更新账号 m1 的表格数据，是否有数据: True
[2025-07-22 21:53:53,848] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,848] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,848] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,848] [DEBUG] 开始查找账号 m1 对应的行索引
[2025-07-22 21:53:53,848] [DEBUG] 未找到账号 m1 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,849] [DEBUG] 更新账号 m10 的表格数据，是否有数据: True
[2025-07-22 21:53:53,849] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,850] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,850] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,850] [DEBUG] 开始查找账号 m10 对应的行索引
[2025-07-22 21:53:53,850] [DEBUG] 未找到账号 m10 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,850] [DEBUG] 更新账号 m100 的表格数据，是否有数据: True
[2025-07-22 21:53:53,851] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,851] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,851] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,852] [DEBUG] 开始查找账号 m100 对应的行索引
[2025-07-22 21:53:53,852] [DEBUG] 未找到账号 m100 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,852] [DEBUG] 更新账号 m101 的表格数据，是否有数据: True
[2025-07-22 21:53:53,852] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,853] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,853] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,853] [DEBUG] 开始查找账号 m101 对应的行索引
[2025-07-22 21:53:53,853] [DEBUG] 未找到账号 m101 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,854] [DEBUG] 更新账号 m102 的表格数据，是否有数据: True
[2025-07-22 21:53:53,854] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,854] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,854] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,855] [DEBUG] 开始查找账号 m102 对应的行索引
[2025-07-22 21:53:53,855] [DEBUG] 未找到账号 m102 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,855] [DEBUG] 更新账号 m103 的表格数据，是否有数据: True
[2025-07-22 21:53:53,855] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,856] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,856] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,856] [DEBUG] 开始查找账号 m103 对应的行索引
[2025-07-22 21:53:53,857] [DEBUG] 未找到账号 m103 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,857] [DEBUG] 更新账号 m104 的表格数据，是否有数据: True
[2025-07-22 21:53:53,858] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,858] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,858] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,859] [DEBUG] 开始查找账号 m104 对应的行索引
[2025-07-22 21:53:53,859] [DEBUG] 未找到账号 m104 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,859] [DEBUG] 更新账号 m12 的表格数据，是否有数据: True
[2025-07-22 21:53:53,859] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,860] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,860] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,860] [DEBUG] 开始查找账号 m12 对应的行索引
[2025-07-22 21:53:53,860] [DEBUG] 未找到账号 m12 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,860] [DEBUG] 更新账号 m13 的表格数据，是否有数据: True
[2025-07-22 21:53:53,860] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,861] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,861] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,861] [DEBUG] 开始查找账号 m13 对应的行索引
[2025-07-22 21:53:53,861] [DEBUG] 未找到账号 m13 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,862] [DEBUG] 更新账号 m141 的表格数据，是否有数据: True
[2025-07-22 21:53:53,862] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,862] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,862] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,862] [DEBUG] 开始查找账号 m141 对应的行索引
[2025-07-22 21:53:53,863] [DEBUG] 未找到账号 m141 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,863] [DEBUG] 更新账号 m142 的表格数据，是否有数据: True
[2025-07-22 21:53:53,863] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,863] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,864] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,864] [DEBUG] 开始查找账号 m142 对应的行索引
[2025-07-22 21:53:53,864] [DEBUG] 未找到账号 m142 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,864] [DEBUG] 更新账号 m143 的表格数据，是否有数据: True
[2025-07-22 21:53:53,865] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,865] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,865] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,865] [DEBUG] 开始查找账号 m143 对应的行索引
[2025-07-22 21:53:53,866] [DEBUG] 未找到账号 m143 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,866] [DEBUG] 更新账号 m15 的表格数据，是否有数据: True
[2025-07-22 21:53:53,866] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,866] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,866] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,867] [DEBUG] 开始查找账号 m15 对应的行索引
[2025-07-22 21:53:53,867] [DEBUG] 未找到账号 m15 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,867] [DEBUG] 更新账号 m151 的表格数据，是否有数据: True
[2025-07-22 21:53:53,867] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,867] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,868] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,868] [DEBUG] 开始查找账号 m151 对应的行索引
[2025-07-22 21:53:53,868] [DEBUG] 未找到账号 m151 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,868] [DEBUG] 更新账号 m152 的表格数据，是否有数据: True
[2025-07-22 21:53:53,868] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,869] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,869] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,869] [DEBUG] 开始查找账号 m152 对应的行索引
[2025-07-22 21:53:53,869] [DEBUG] 未找到账号 m152 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,869] [DEBUG] 更新账号 m153 的表格数据，是否有数据: True
[2025-07-22 21:53:53,869] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,871] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,871] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,871] [DEBUG] 开始查找账号 m153 对应的行索引
[2025-07-22 21:53:53,872] [DEBUG] 未找到账号 m153 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,872] [DEBUG] 更新账号 m154 的表格数据，是否有数据: True
[2025-07-22 21:53:53,872] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,873] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,873] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,873] [DEBUG] 开始查找账号 m154 对应的行索引
[2025-07-22 21:53:53,874] [DEBUG] 未找到账号 m154 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,874] [DEBUG] 更新账号 m155 的表格数据，是否有数据: True
[2025-07-22 21:53:53,874] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,874] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,874] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,875] [DEBUG] 开始查找账号 m155 对应的行索引
[2025-07-22 21:53:53,875] [DEBUG] 未找到账号 m155 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,875] [DEBUG] 更新账号 m156 的表格数据，是否有数据: True
[2025-07-22 21:53:53,875] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,876] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,876] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,876] [DEBUG] 开始查找账号 m156 对应的行索引
[2025-07-22 21:53:53,876] [DEBUG] 未找到账号 m156 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,877] [DEBUG] 更新账号 m157 的表格数据，是否有数据: True
[2025-07-22 21:53:53,877] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,877] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,877] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,877] [DEBUG] 开始查找账号 m157 对应的行索引
[2025-07-22 21:53:53,878] [DEBUG] 未找到账号 m157 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,878] [DEBUG] 更新账号 m158 的表格数据，是否有数据: True
[2025-07-22 21:53:53,878] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,878] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,878] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,878] [DEBUG] 开始查找账号 m158 对应的行索引
[2025-07-22 21:53:53,879] [DEBUG] 未找到账号 m158 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,879] [DEBUG] 更新账号 m159 的表格数据，是否有数据: True
[2025-07-22 21:53:53,879] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,879] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,879] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,880] [DEBUG] 开始查找账号 m159 对应的行索引
[2025-07-22 21:53:53,880] [DEBUG] 未找到账号 m159 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,880] [DEBUG] 更新账号 m16 的表格数据，是否有数据: True
[2025-07-22 21:53:53,880] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,880] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,880] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,880] [DEBUG] 开始查找账号 m16 对应的行索引
[2025-07-22 21:53:53,881] [DEBUG] 未找到账号 m16 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,881] [DEBUG] 更新账号 m161 的表格数据，是否有数据: True
[2025-07-22 21:53:53,881] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,881] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,882] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,882] [DEBUG] 开始查找账号 m161 对应的行索引
[2025-07-22 21:53:53,882] [DEBUG] 未找到账号 m161 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,882] [DEBUG] 更新账号 m162 的表格数据，是否有数据: True
[2025-07-22 21:53:53,883] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,883] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,883] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,883] [DEBUG] 开始查找账号 m162 对应的行索引
[2025-07-22 21:53:53,884] [DEBUG] 未找到账号 m162 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,884] [DEBUG] 更新账号 m163 的表格数据，是否有数据: True
[2025-07-22 21:53:53,884] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,884] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,885] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,885] [DEBUG] 开始查找账号 m163 对应的行索引
[2025-07-22 21:53:53,885] [DEBUG] 未找到账号 m163 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,885] [DEBUG] 更新账号 m164 的表格数据，是否有数据: True
[2025-07-22 21:53:53,885] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,886] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,886] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,886] [DEBUG] 开始查找账号 m164 对应的行索引
[2025-07-22 21:53:53,886] [DEBUG] 未找到账号 m164 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,887] [DEBUG] 更新账号 m165 的表格数据，是否有数据: True
[2025-07-22 21:53:53,887] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,887] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,887] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,888] [DEBUG] 开始查找账号 m165 对应的行索引
[2025-07-22 21:53:53,888] [DEBUG] 未找到账号 m165 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,888] [DEBUG] 更新账号 m166 的表格数据，是否有数据: True
[2025-07-22 21:53:53,888] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,888] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,889] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,889] [DEBUG] 开始查找账号 m166 对应的行索引
[2025-07-22 21:53:53,889] [DEBUG] 未找到账号 m166 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,889] [DEBUG] 更新账号 m167 的表格数据，是否有数据: True
[2025-07-22 21:53:53,890] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,890] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,890] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,890] [DEBUG] 开始查找账号 m167 对应的行索引
[2025-07-22 21:53:53,890] [DEBUG] 未找到账号 m167 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,890] [DEBUG] 更新账号 m168 的表格数据，是否有数据: True
[2025-07-22 21:53:53,890] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,890] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,892] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,892] [DEBUG] 开始查找账号 m168 对应的行索引
[2025-07-22 21:53:53,892] [DEBUG] 未找到账号 m168 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,892] [DEBUG] 更新账号 m17 的表格数据，是否有数据: True
[2025-07-22 21:53:53,893] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,893] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,893] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,893] [DEBUG] 开始查找账号 m17 对应的行索引
[2025-07-22 21:53:53,894] [DEBUG] 未找到账号 m17 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,894] [DEBUG] 更新账号 m171 的表格数据，是否有数据: True
[2025-07-22 21:53:53,894] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,894] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,894] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,895] [DEBUG] 开始查找账号 m171 对应的行索引
[2025-07-22 21:53:53,895] [DEBUG] 未找到账号 m171 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,895] [DEBUG] 更新账号 m172 的表格数据，是否有数据: True
[2025-07-22 21:53:53,895] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,896] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,896] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,896] [DEBUG] 开始查找账号 m172 对应的行索引
[2025-07-22 21:53:53,896] [DEBUG] 未找到账号 m172 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,897] [DEBUG] 更新账号 m173 的表格数据，是否有数据: True
[2025-07-22 21:53:53,897] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,897] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,897] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,898] [DEBUG] 开始查找账号 m173 对应的行索引
[2025-07-22 21:53:53,898] [DEBUG] 未找到账号 m173 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,898] [DEBUG] 更新账号 m174 的表格数据，是否有数据: True
[2025-07-22 21:53:53,898] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,898] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,899] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,899] [DEBUG] 开始查找账号 m174 对应的行索引
[2025-07-22 21:53:53,899] [DEBUG] 未找到账号 m174 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,899] [DEBUG] 更新账号 m175 的表格数据，是否有数据: True
[2025-07-22 21:53:53,899] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,900] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,900] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,900] [DEBUG] 开始查找账号 m175 对应的行索引
[2025-07-22 21:53:53,900] [DEBUG] 未找到账号 m175 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,901] [DEBUG] 更新账号 m176 的表格数据，是否有数据: True
[2025-07-22 21:53:53,901] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,901] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,901] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,902] [DEBUG] 开始查找账号 m176 对应的行索引
[2025-07-22 21:53:53,902] [DEBUG] 未找到账号 m176 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,902] [DEBUG] 更新账号 m177 的表格数据，是否有数据: True
[2025-07-22 21:53:53,902] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,902] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,903] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,903] [DEBUG] 开始查找账号 m177 对应的行索引
[2025-07-22 21:53:53,903] [DEBUG] 未找到账号 m177 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,903] [DEBUG] 更新账号 m178 的表格数据，是否有数据: True
[2025-07-22 21:53:53,903] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,903] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,904] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,904] [DEBUG] 开始查找账号 m178 对应的行索引
[2025-07-22 21:53:53,904] [DEBUG] 未找到账号 m178 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,904] [DEBUG] 更新账号 m179 的表格数据，是否有数据: True
[2025-07-22 21:53:53,904] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,905] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,905] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,905] [DEBUG] 开始查找账号 m179 对应的行索引
[2025-07-22 21:53:53,905] [DEBUG] 未找到账号 m179 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,905] [DEBUG] 更新账号 m18 的表格数据，是否有数据: True
[2025-07-22 21:53:53,906] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,906] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,906] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,906] [DEBUG] 开始查找账号 m18 对应的行索引
[2025-07-22 21:53:53,907] [DEBUG] 未找到账号 m18 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,907] [DEBUG] 更新账号 m19 的表格数据，是否有数据: True
[2025-07-22 21:53:53,907] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,907] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,907] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,907] [DEBUG] 开始查找账号 m19 对应的行索引
[2025-07-22 21:53:53,908] [DEBUG] 未找到账号 m19 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,908] [DEBUG] 更新账号 m2 的表格数据，是否有数据: True
[2025-07-22 21:53:53,908] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,908] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,908] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,909] [DEBUG] 开始查找账号 m2 对应的行索引
[2025-07-22 21:53:53,909] [DEBUG] 未找到账号 m2 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,909] [DEBUG] 更新账号 m20 的表格数据，是否有数据: True
[2025-07-22 21:53:53,909] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,909] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,910] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,910] [DEBUG] 开始查找账号 m20 对应的行索引
[2025-07-22 21:53:53,910] [DEBUG] 未找到账号 m20 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,910] [DEBUG] 更新账号 m22 的表格数据，是否有数据: True
[2025-07-22 21:53:53,910] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,911] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,911] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,911] [DEBUG] 开始查找账号 m22 对应的行索引
[2025-07-22 21:53:53,911] [DEBUG] 未找到账号 m22 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,911] [DEBUG] 更新账号 m26 的表格数据，是否有数据: True
[2025-07-22 21:53:53,912] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,912] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,912] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,912] [DEBUG] 开始查找账号 m26 对应的行索引
[2025-07-22 21:53:53,913] [DEBUG] 未找到账号 m26 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,913] [DEBUG] 更新账号 m27 的表格数据，是否有数据: True
[2025-07-22 21:53:53,913] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,913] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,913] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,915] [DEBUG] 开始查找账号 m27 对应的行索引
[2025-07-22 21:53:53,915] [DEBUG] 未找到账号 m27 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,915] [DEBUG] 更新账号 m28 的表格数据，是否有数据: True
[2025-07-22 21:53:53,915] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,915] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,916] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,916] [DEBUG] 开始查找账号 m28 对应的行索引
[2025-07-22 21:53:53,916] [DEBUG] 未找到账号 m28 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,916] [DEBUG] 更新账号 m46 的表格数据，是否有数据: True
[2025-07-22 21:53:53,917] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-07-22 21:53:53,917] [DEBUG] 表格结构: 0行 x 19列
[2025-07-22 21:53:53,917] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-07-22 21:53:53,917] [DEBUG] 开始查找账号 m46 对应的行索引
[2025-07-22 21:53:53,918] [DEBUG] 未找到账号 m46 对应的行索引，尝试记录所有行的账号ID
[2025-07-22 21:53:53,961] [INFO] 用户取消后台加载
[2025-07-22 21:53:53,961] [INFO] 后台加载完成: 账号数据加载完成
[2025-07-22 21:53:54,167] [DEBUG] 批量数据处理完成后内存使用: 250.82 MB
[2025-07-22 21:53:54,168] [ERROR] 更新加载进度时出错: 'NoneType' object has no attribute 'setLabelText'
[2025-07-22 21:53:54,890] [INFO] account_loader.load_accounts_sync方法返回: success=True, accounts数量=305, error_msg=
[2025-07-22 21:53:54,890] [INFO] 同步加载账号数据完成，结果: True, 账号数量: 305, 错误信息: 
[2025-07-22 21:53:54,891] [INFO] 账号数据加载成功，共 305 个账号
[2025-07-22 21:53:54,891] [INFO] 账号加载完成回调开始时内存使用: 260.31 MB
[2025-07-22 21:53:54,891] [INFO] 已创建账号列表的浅拷贝，大小: 305
[2025-07-22 21:53:54,891] [INFO] 成功加载 305 个账号文件，延迟加载模式: True
[2025-07-22 21:53:54,922] [INFO] 账号列表大小: 305
[2025-07-22 21:53:54,923] [INFO] 第一个账号信息: {'file_path': 'E:/软件共享/账号合并\\***********.txt', 'account_id': '***********', 'data': {'accountId': '***********', 'remark': '***********', 'cookies': {'xigua_csrf_token': 'Rh9tB8w72kFdjHWXzmwLx4FN', 'is_staff_user': 'false', 'sessionid_ss': '68c4cc3f1c5dedd10b37702f8cfc5581', 'uid_tt_ss': '4138e04b7027f10e81ee57334a3871f1', 'store-region': 'cn-gd', 'uid_tt': '4138e04b7027f10e81ee57334a3871f1', 'passport_auth_status': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'passport_mfa_token': 'CjfDS1ognyyaR5OXvuo1ZhOlSC8orpJykXqU3XrL0%2F4LK3qwDRucEFU8wl5EeoQDFgQXMDSnVv64GkoKPIA87d76RVvezzRe1eiXyd81F%2FQYdzX0iWRhqgsPoaSXZlpDutIfPN2NISNG6lXHe%2BhFo3Alo9axe2OZMhDY3OsNGPax0WwgAiIBA6K42yM%3D', 'passport_auth_status_ss': '8cf13a7fbf7f9917564f9b51ef186b6f%2C15d1159db41dc3b851d62af1ece85a23', 'sid_tt': '68c4cc3f1c5dedd10b37702f8cfc5581', 'odin_tt': 'f681b2fee0f84dd926c09d58e132e2328c436e9fcf004f758697a032da7adc66d0d445a80508f8d675d1357603c3efe11f5237f59ac2ca2893e80a3e5da52dcf', 'sessionid': '68c4cc3f1c5dedd10b37702f8cfc5581', 'gfgarrcache_8005812e': '1', 'ssid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'toutiao_sso_user_ss': 'c2032ee116a4c329079d18ddc99f62c6', 'gfgarrcache_6a2703f1': '1', 'sid_ucp_sso_v1': '1.0.0-KDI4MTYxNTI4ODFiNDYyMmU2ZWI4OWFmYWU0MTUwNjQ5YjZiMTEyMWMKHwiz3oCmsozXBhDylMC-BhjPCSAMMOycyZkGOAJA8QcaAmhsIiBjMjAzMmVlMTE2YTRjMzI5MDc5ZDE4ZGRjOTlmNjJjNg', 'store-region-src': 'uid', 'sid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'toutiao_sso_user': 'c2032ee116a4c329079d18ddc99f62c6', 'sso_uid_tt_ss': '707d66e7d745b6ca510877ff49d6deca', 'xg_p_tos_token': '79aa2d6e8307f3027308bb8063808201', 'sso_uid_tt': '707d66e7d745b6ca510877ff49d6deca', 'sso_auth_status_ss': '99d6be0fe40fbeccd26dbd011f7e4345', 'ssid_ucp_v1': '1.0.0-KDdkMmMzNzc3ZjFmMWYzMzYxMTgyYjVkNWE3NmY1ZjY3YTY0ODUxNWEKGQiz3oCmsozXBhDylMC-BhjPCSAMOAJA8QcaAmxxIiA2OGM0Y2MzZjFjNWRlZGQxMGIzNzcwMmY4Y2ZjNTU4MQ', 'sid_guard': '68c4cc3f1c5dedd10b37702f8cfc5581%7C1741687410%7C5184002%7CSat%2C+10-May-2025+10%3A03%3A32+GMT', 'sso_auth_status': '99d6be0fe40fbeccd26dbd011f7e4345', 'passport_csrf_token': '3d631c058164d798d48475833f13446e', 'n_mh': '30A9sphpGJEPcXGYEGo8hiNGyEbb5VtQ_Ov5DGw0tbA', 'ttwid': '1%7C0FfHeK6FyRv027NKpnZinttVopHTQty8nSXP9P7wnNw%7C1741687388%7C33b1d18a6f3d000bd20fd4a1d0f5d6b37682c37752a91a6e1265423a4b1e9968', 'gfkadpd': '1231,25897', 'd_ticket': '0da2a81adc5bb557ff718c555c0a4801d76e7', 'passport_csrf_token_default': '3d631c058164d798d48475833f13446e', 'csrf_session_id': 'e1a99642f2b9648ef30c79ef589a852a', 's_v_web_id': 'verify_m84bp59m_dED7JGij_AkV2_4J3T_B268_83sqBwAURoyE'}}, 'file_type': '.txt'}
[2025-07-22 21:53:54,923] [INFO] 使用延迟加载模式，准备更新表格基本信息
[2025-07-22 21:53:54,925] [DEBUG] 已导入高级内存管理器
[2025-07-22 21:53:54,942] [INFO] 更新表格开始时内存使用: 260.32MB, 可用: 1038.74MB/16236.06MB
[2025-07-22 21:53:54,976] [INFO] 准备设置表格行数: 305
[2025-07-22 21:53:54,977] [INFO] 已设置表格行数: 305，当前表格行数: 305
[2025-07-22 21:53:54,977] [DEBUG] 处理账号批次: 1-50/305
[2025-07-22 21:53:55,208] [DEBUG] 处理账号批次: 51-100/305
[2025-07-22 21:53:55,500] [DEBUG] 处理账号批次: 101-150/305
[2025-07-22 21:53:55,763] [DEBUG] 处理账号批次: 151-200/305
[2025-07-22 21:53:56,010] [DEBUG] 处理账号批次: 201-250/305
[2025-07-22 21:53:56,256] [DEBUG] 处理账号批次: 251-300/305
[2025-07-22 21:53:56,505] [DEBUG] 处理账号批次: 301-305/305
[2025-07-22 21:53:56,675] [INFO] 表格更新完成，耗时: 1.73秒，内存使用: 355.75MB
[2025-07-22 21:53:56,675] [INFO] 使用延迟加载模式，已更新表格基本信息
[2025-07-22 21:53:56,676] [INFO] 延迟加载模式下，表格行数: 305
[2025-07-22 21:53:56,710] [INFO] 不设置账号已加载标志，允许重复加载
[2025-07-22 21:53:56,710] [INFO] 开始计算收益统计，表格行数: 305, 列数: 19
[2025-07-22 21:53:56,711] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-22 21:53:56,712] [INFO] 七天总收益: ¥0.00
[2025-07-22 21:53:56,712] [INFO] 昨日总收益: ¥0.00
[2025-07-22 21:53:56,712] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-22 21:53:56,716] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-22 21:54:01,453] [INFO] 开始计算收益统计，表格行数: 305, 列数: 19
[2025-07-22 21:54:01,455] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-07-22 21:54:01,455] [INFO] 七天总收益: ¥0.00
[2025-07-22 21:54:01,455] [INFO] 昨日总收益: ¥0.00
[2025-07-22 21:54:01,456] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-22 21:54:01,458] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-22 21:54:01,763] [INFO] 开始强制清理批量存稿对象...
[2025-07-22 21:54:01,764] [INFO] 已清理活跃存稿任务标记
[2025-07-22 21:54:01,766] [INFO] 已清理主窗口中的_batch_draft_worker引用
[2025-07-22 21:54:01,766] [INFO] 已清理主窗口中的_batch_draft_thread引用
[2025-07-22 21:54:01,766] [INFO] 批量存稿状态标志重置完成
[2025-07-22 21:54:02,304] [INFO] 强制清理批量存稿对象完成
[2025-07-22 21:54:02,305] [DEBUG] 未检测到正在运行的批量存稿任务
[2025-07-22 21:54:02,305] [INFO] 进行预防性清理，确保没有残留对象...
[2025-07-22 21:54:02,306] [INFO] 开始强制清理批量存稿对象...
[2025-07-22 21:54:02,306] [INFO] 已清理活跃存稿任务标记
[2025-07-22 21:54:02,306] [INFO] 已清理主窗口中的_batch_draft_worker引用
[2025-07-22 21:54:02,307] [INFO] 已清理主窗口中的_batch_draft_thread引用
[2025-07-22 21:54:02,307] [INFO] 批量存稿状态标志重置完成
[2025-07-22 21:54:02,909] [INFO] 强制清理批量存稿对象完成
[2025-07-22 21:54:24,296] [INFO] 用户取消了批量存稿操作
[2025-07-22 21:54:25,596] [INFO] AI文件名改写器初始化成功
[2025-07-22 21:54:25,672] [INFO] 已从文件加载设置: e:\toutiaoyuanma1\tou014\tou011\video_processor_settings.json
[2025-07-22 21:54:25,673] [DEBUG] 从智能体配置同步
[2025-07-22 21:54:25,673] [DEBUG] 已从AI改写器同步配置: agent_id=WXR2oUKfqs..., token=***
[2025-07-22 21:54:25,674] [INFO] 视频处理对话框已初始化
[2025-07-22 21:54:26,674] [DEBUG] 设置已保存到: e:\toutiaoyuanma1\tou014\tou011\video_processor_settings.json
[2025-07-22 21:54:28,578] [DEBUG] 更新智能体配置: agent_id=WXR2oUKfqs...
[2025-07-22 21:54:29,578] [DEBUG] 设置已保存到: e:\toutiaoyuanma1\tou014\tou011\video_processor_settings.json
[2025-07-22 21:54:30,363] [DEBUG] 更新智能体配置: agent_id=WXR2oUKfqs...
[2025-07-22 21:54:32,393] [DEBUG] 设置已保存到: e:\toutiaoyuanma1\tou014\tou011\video_processor_settings.json
[2025-07-22 21:54:40,910] [DEBUG] 设置已保存到: e:\toutiaoyuanma1\tou014\tou011\video_processor_settings.json
[2025-07-22 21:54:57,261] [DEBUG] 设置已保存到: e:\toutiaoyuanma1\tou014\tou011\video_processor_settings.json
[2025-07-22 21:56:04,927] [INFO] 已打开头条视频爬虫对话框
[2025-07-22 21:56:45,975] [DEBUG] 未检测到正在运行的批量存稿任务
[2025-07-22 21:56:45,976] [DEBUG] 准备为账号执行存稿: ***********, Cookie文件: E:/软件共享/账号合并\***********.txt
[2025-07-22 21:57:13,056] [INFO] AI文件名改写器初始化成功
[2025-07-22 21:57:13,091] [DEBUG] UI中的智能体配置为空，跳过更新
[2025-07-22 21:57:13,092] [INFO] 已从文件加载设置: e:\toutiaoyuanma1\tou014\tou011\video_processor_settings.json
[2025-07-22 21:57:13,093] [DEBUG] 从智能体配置同步
[2025-07-22 21:57:13,094] [DEBUG] 已从AI改写器同步配置: agent_id=WXR2oUKfqs..., token=***
[2025-07-22 21:57:13,095] [INFO] 视频处理对话框已初始化
[2025-07-22 21:57:14,095] [DEBUG] 设置已保存到: e:\toutiaoyuanma1\tou014\tou011\video_processor_settings.json
[2025-07-22 21:57:27,360] [DEBUG] 设置已保存到: e:\toutiaoyuanma1\tou014\tou011\video_processor_settings.json
[2025-07-22 21:57:42,912] [DEBUG] 已导入高级内存管理器
[2025-07-22 21:57:42,913] [INFO] 账号加载模式: 延迟加载
[2025-07-22 21:57:42,978] [INFO] 数据目录功能已移除
[2025-07-22 21:57:42,979] [INFO] 开始异步加载Cookie文件，路径: E:/软件共享/测试账号/111111111，延迟加载模式: True
[2025-07-22 21:57:45,063] [INFO] 自动化配置保存成功
[2025-07-22 21:57:45,063] [INFO] 自动化配置更新成功
[2025-07-22 21:57:45,064] [INFO] 心跳指示器已停用
[2025-07-22 21:57:46,997] [INFO] 🔄 UI收到批量数据：98个账号，当前表格行数：0
[2025-07-22 21:57:46,998] [INFO] 📊 表格行数已更新：0 → 98
[2025-07-22 21:57:47,038] [INFO] 批量处理 98 个账号数据，耗时: 0.04秒
[2025-07-22 21:57:47,459] [INFO] 🔄 UI收到批量数据：94个账号，当前表格行数：98
[2025-07-22 21:57:47,460] [INFO] 📊 表格行数已更新：98 → 192
[2025-07-22 21:57:47,496] [INFO] 批量处理 94 个账号数据，耗时: 0.04秒
[2025-07-22 21:57:47,917] [INFO] 🔄 UI收到批量数据：98个账号，当前表格行数：192
[2025-07-22 21:57:47,918] [INFO] 📊 表格行数已更新：192 → 290
[2025-07-22 21:57:47,955] [INFO] 批量处理 98 个账号数据，耗时: 0.04秒
[2025-07-22 21:57:48,357] [INFO] 🔄 UI收到批量数据：99个账号，当前表格行数：290
[2025-07-22 21:57:48,358] [INFO] 📊 表格行数已更新：290 → 389
[2025-07-22 21:57:48,394] [INFO] 批量处理 99 个账号数据，耗时: 0.04秒
[2025-07-22 21:57:48,836] [INFO] 🔄 UI收到批量数据：97个账号，当前表格行数：389
[2025-07-22 21:57:48,837] [INFO] 📊 表格行数已更新：389 → 486
[2025-07-22 21:57:48,875] [INFO] 批量处理 97 个账号数据，耗时: 0.04秒
[2025-07-22 21:57:49,290] [INFO] 🔄 UI收到批量数据：97个账号，当前表格行数：486
[2025-07-22 21:57:49,291] [INFO] 📊 表格行数已更新：486 → 583
[2025-07-22 21:57:49,328] [INFO] 批量处理 97 个账号数据，耗时: 0.04秒
[2025-07-22 21:57:49,753] [INFO] 🔄 UI收到批量数据：99个账号，当前表格行数：583
[2025-07-22 21:57:49,753] [INFO] 📊 表格行数已更新：583 → 682
[2025-07-22 21:57:49,790] [INFO] 批量处理 99 个账号数据，耗时: 0.04秒
[2025-07-22 21:57:50,216] [INFO] 🔄 UI收到批量数据：98个账号，当前表格行数：682
[2025-07-22 21:57:50,217] [INFO] 📊 表格行数已更新：682 → 780
[2025-07-22 21:57:50,254] [INFO] 批量处理 98 个账号数据，耗时: 0.04秒
[2025-07-22 21:57:50,667] [INFO] 🔄 UI收到批量数据：96个账号，当前表格行数：780
[2025-07-22 21:57:50,668] [INFO] 📊 表格行数已更新：780 → 876
[2025-07-22 21:57:50,706] [INFO] 批量处理 96 个账号数据，耗时: 0.04秒
[2025-07-22 21:57:50,830] [INFO] 用户取消了账号加载
[2025-07-22 21:57:50,935] [INFO] 账号加载完成回调开始时内存使用: 387.91 MB
[2025-07-22 21:57:50,937] [ERROR] 加载账号失败: 用户取消了加载
[2025-07-22 21:58:03,753] [DEBUG] 已导入高级内存管理器
[2025-07-22 21:58:03,753] [INFO] 账号加载模式: 延迟加载
[2025-07-22 21:58:03,803] [INFO] 数据目录功能已移除
[2025-07-22 21:58:03,803] [INFO] 开始异步加载Cookie文件，路径: E:/软件共享/测试账号/新建文件夹 (2)，延迟加载模式: True
[2025-07-22 21:58:03,807] [INFO] 自动化配置保存成功
[2025-07-22 21:58:03,808] [INFO] 自动化配置更新成功
[2025-07-22 21:58:03,808] [INFO] 心跳指示器已停用
[2025-07-22 21:58:03,854] [INFO] 账号加载完成回调开始时内存使用: 381.74 MB
[2025-07-22 21:58:03,854] [ERROR] 加载账号失败: 账号目录为空: E:/软件共享/测试账号/新建文件夹 (2)
[2025-07-22 21:58:07,526] [INFO] 开始添加新账号
[2025-07-22 21:58:11,099] [INFO] 用户取消添加账号
[2025-07-22 21:58:12,807] [INFO] 开始添加新账号
[2025-07-22 21:58:15,582] [DEBUG] 将使用文件路径保存Cookie: E:/软件共享/测试账号/新建文件夹 (2)\测试.txt
[2025-07-22 21:58:15,582] [INFO] 启动浏览器以添加新账号: 测试, 备注: 测试
[2025-07-22 21:58:15,585] [INFO] 准备为账号 测试 添加Cookie
[2025-07-22 21:58:15,585] [DEBUG] Cookie保存路径: E:/软件共享/测试账号/新建文件夹 (2)
[2025-07-22 21:58:15,585] [INFO] 检查网络连接...
[2025-07-22 21:58:15,617] [INFO] 网络连接检查结果: 网络连接正常
[2025-07-22 21:58:15,618] [DEBUG] Cookie文件路径: E:/软件共享/测试账号/新建文件夹 (2)\测试.txt
[2025-07-22 21:58:15,618] [INFO] 尝试使用ChromeDriver管理器创建浏览器...
[2025-07-22 21:58:19,118] [INFO] 已成功初始化Chrome浏览器
[2025-07-22 21:58:19,120] [INFO] 正在打开头条媒体平台登录页面...
[2025-07-22 21:58:19,121] [INFO] 第 1 次尝试打开头条媒体平台...
[2025-07-22 21:58:21,536] [INFO] 成功打开头条媒体平台
[2025-07-22 21:58:21,537] [INFO] 等待用户登录，最多等待5分钟...
[2025-07-22 21:58:41,983] [DEBUG] 通过XPath检测登录元素失败: Message: 
Stacktrace:
	GetHandleVerifier [0x0x7ff6ab07fea5+79173]
	GetHandleVerifier [0x0x7ff6ab07ff00+79264]
	(No symbol) [0x0x7ff6aae39e5a]
	(No symbol) [0x0x7ff6aae90586]
	(No symbol) [0x0x7ff6aae9083c]
	(No symbol) [0x0x7ff6aaee4247]
	(No symbol) [0x0x7ff6aaeb89af]
	(No symbol) [0x0x7ff6aaee100d]
	(No symbol) [0x0x7ff6aaeb8743]
	(No symbol) [0x0x7ff6aae814c1]
	(No symbol) [0x0x7ff6aae82253]
	GetHandleVerifier [0x0x7ff6ab34a2dd+3004797]
	GetHandleVerifier [0x0x7ff6ab34472d+2981325]
	GetHandleVerifier [0x0x7ff6ab363380+3107360]
	GetHandleVerifier [0x0x7ff6ab09aa2e+188622]
	GetHandleVerifier [0x0x7ff6ab0a22bf+219487]
	GetHandleVerifier [0x0x7ff6ab088df4+115860]
	GetHandleVerifier [0x0x7ff6ab088fa9+116297]
	GetHandleVerifier [0x0x7ff6ab06f558+11256]
	BaseThreadInitThunk [0x0x7ffa510ae8d7+23]
	RtlUserThreadStart [0x0x7ffa520dc34c+44]
，尝试备用方法
[2025-07-22 22:00:33,184] [INFO] 检测到用户已登录 (XPath验证)
[2025-07-22 22:00:33,184] [INFO] 显示确认对话框...
[2025-07-22 22:00:42,202] [INFO] 获取cookies...
[2025-07-22 22:00:42,255] [INFO] 保存cookies到文件: E:/软件共享/测试账号/新建文件夹 (2)\测试.txt
[2025-07-22 22:00:42,256] [INFO] [SUCCESS] 账号 测试 添加成功, 文件路径: E:/软件共享/测试账号/新建文件夹 (2)\测试.txt
[2025-07-22 22:00:42,257] [INFO] 关闭浏览器...
[2025-07-22 22:00:44,613] [INFO] [SUCCESS] 成功添加账号，Cookie已保存到: E:/软件共享/测试账号/新建文件夹 (2)\测试.txt
[2025-07-22 22:00:44,618] [DEBUG] 更新账号 0 的表格数据，是否有数据: True
[2025-07-22 22:00:44,618] [ERROR] 账号ID无效: 0
[2025-07-22 22:00:49,192] [DEBUG] 准备登录账号: 测试, Cookie文件: E:/软件共享/测试账号/新建文件夹 (2)\测试.txt
[2025-07-22 22:00:49,193] [INFO] 使用登录设置: 指纹浏览器=False, 手机端模式=False, 禁用JS=False, 清除Cookie=True
[2025-07-22 22:00:49,195] [INFO] 🚀 开始Cookie登录流程（重构版本）
[2025-07-22 22:00:49,195] [INFO] 🔧 配置Chrome浏览器选项...
[2025-07-22 22:00:49,195] [INFO] 使用与批量存稿相同的标准浏览器配置
[2025-07-22 22:00:49,197] [INFO] 使用独立用户数据目录: C:\Users\<USER>\AppData\Local\Temp\chrome_user_data_single_1753192849
[2025-07-22 22:00:49,198] [INFO] ✅ Chrome浏览器选项配置完成
[2025-07-22 22:00:52,590] [INFO] 🔑 开始登录账号 测试
[2025-07-22 22:00:52,593] [DEBUG] JSON格式解析成功，提取到 5 个关键Cookie
[2025-07-22 22:00:52,593] [INFO] 📋 解析到 5 个关键Cookie
[2025-07-22 22:00:52,593] [INFO] 🌐 导航到头条媒体平台
[2025-07-22 22:00:54,050] [INFO] 🔑 添加 5 个关键Cookie
[2025-07-22 22:00:54,080] [INFO] ✅ 成功添加 5 个Cookie
[2025-07-22 22:00:54,080] [INFO] 🔄 刷新页面应用Cookie
[2025-07-22 22:00:57,119] [INFO] 🔍 验证登录状态
[2025-07-22 22:00:57,124] [DEBUG] URL验证通过 (0.00s)
[2025-07-22 22:00:57,125] [INFO] [SUCCESS] ✅ 账号 测试 登录成功
[2025-07-22 22:00:57,128] [INFO] 🔍 开始检测 账号 测试 的实名认证状态...
[2025-07-22 22:00:57,143] [INFO] 📋 文本检测结果: 页面文本检测通过
[2025-07-22 22:00:57,268] [INFO] 🔍 元素检测结果: 元素检测通过
[2025-07-22 22:00:57,273] [INFO] 🌐 URL检测结果: URL检测通过
[2025-07-22 22:00:57,273] [INFO] ✅ 账号 测试 已实名认证
[2025-07-22 22:00:57,273] [INFO] ✅ 账号 测试 实名认证状态: 已实名
[2025-07-22 22:00:57,275] [INFO] 账号 测试 的浏览器实例已添加到管理器，当前管理 1 个浏览器
[2025-07-22 22:00:57,275] [INFO] [SUCCESS] ✅ 账号 测试 登录成功，已停留在头条媒体平台首页
[2025-07-22 22:00:57,279] [INFO] [SUCCESS] 账号 测试 登录成功
[2025-07-22 22:00:57,280] [INFO] 账号 测试 登录成功: 账号 测试 登录成功，已停留在头条媒体平台首页
[2025-07-22 22:01:12,170] [INFO] 指定了特定行: 0
[2025-07-22 22:01:12,171] [INFO] 准备处理的行数: 1
[2025-07-22 22:01:12,172] [INFO] 行索引: [0]
[2025-07-22 22:01:15,368] [INFO] 数据采集设置: 线程数=2, 清理缓存=True
[2025-07-22 22:01:15,368] [INFO] 创建数据采集器，线程数: 2
[2025-07-22 22:01:15,370] [INFO] 已设置数据采集器线程数: 2
[2025-07-22 22:01:15,370] [INFO] 开始启动数据采集，处理 1 个账号，使用 2 个线程
[2025-07-22 22:01:15,371] [INFO] 数据采集: 使用Cookie目录: E:/软件共享/测试账号/新建文件夹 (2)
[2025-07-22 22:01:15,371] [INFO] 数据采集: 开始准备账号队列，总行数: 1
[2025-07-22 22:01:15,372] [INFO] 数据采集: 处理第 1 行
[2025-07-22 22:01:15,372] [INFO] 数据采集: 找到账号: 测试
[2025-07-22 22:01:15,373] [INFO] 数据采集: 找到账号 测试 的Cookie文件: E:/软件共享/测试账号/新建文件夹 (2)\测试.txt
[2025-07-22 22:01:15,373] [INFO] 数据采集: 已将账号 测试 添加到队列，当前队列长度: 1
[2025-07-22 22:01:15,373] [DEBUG] 数据采集状态更新: 更新已完成计数:0:1
[2025-07-22 22:01:15,375] [DEBUG] 已完成计数更新: 0/1
[2025-07-22 22:01:15,375] [DEBUG] 数据采集状态更新: 更新总体进度:准备中:0:1:正在准备数据采集
[2025-07-22 22:01:15,375] [DEBUG] 总体进度更新: 账号=准备中, 进度=0/1, 状态=正在准备数据采集
[2025-07-22 22:01:15,376] [INFO] 数据采集: 准备采集 1 个账号的数据，按账号顺序依次处理
[2025-07-22 22:01:15,376] [INFO] 数据采集: 将启动 1 个线程处理 1 个账号
[2025-07-22 22:01:15,377] [INFO] 数据采集: 账号队列: 测试
[2025-07-22 22:01:15,377] [INFO] 数据采集: 启动 采集线程-1
[2025-07-22 22:01:15,378] [INFO] 数据采集: 已创建工作线程: 采集线程-1
[2025-07-22 22:01:15,487] [INFO] 已启动数据采集，处理 1 个账号，使用 2 个线程
[2025-07-22 22:01:15,490] [DEBUG] 数据采集状态更新: ℹ️ [采集线程-1] 启动数据采集流程...
[2025-07-22 22:01:15,491] [INFO] 数据采集: [采集线程-1] 启动数据采集流程...
[2025-07-22 22:01:19,540] [DEBUG] 数据采集状态更新: ✅ 成功创建Chrome浏览器
[2025-07-22 22:01:19,540] [INFO] [SUCCESS] 数据采集: 成功创建Chrome浏览器
[2025-07-22 22:01:19,542] [INFO] 数据采集: [采集线程-1] 尝试获取下一个账号，当前索引: 0，队列长度: 1
[2025-07-22 22:01:19,542] [INFO] 数据采集: [采集线程-1] 获取到账号: 测试，行索引: 0，当前索引更新为: 1
[2025-07-22 22:01:19,543] [DEBUG] 数据采集状态更新: ℹ️ [采集线程-1] 获取到账号: 测试
[2025-07-22 22:01:19,544] [INFO] 数据采集: [采集线程-1] 获取到账号: 测试
[2025-07-22 22:01:19,544] [DEBUG] 数据采集状态更新: ℹ️ 正在处理账号 1/1 (100.0%): 测试
[2025-07-22 22:01:19,544] [INFO] 数据采集: 正在处理账号 1/1 (100.0%): 测试
[2025-07-22 22:01:19,545] [DEBUG] 数据采集状态更新: 更新浏览器进度:采集线程-1:测试:100.0
[2025-07-22 22:01:19,545] [DEBUG] 浏览器进度更新: 线程=采集线程-1, 账号=测试, 进度=100.0%
[2025-07-22 22:01:19,545] [DEBUG] 数据采集状态更新: 更新总体进度:测试:1:1:正在查询数据
[2025-07-22 22:01:19,546] [DEBUG] 总体进度更新: 账号=测试, 进度=1/1, 状态=正在查询数据
[2025-07-22 22:01:20,571] [INFO] 🔑 开始登录账号 测试
[2025-07-22 22:01:20,571] [DEBUG] 数据采集状态更新: ℹ️ 已清理浏览器会话，准备处理账号 测试
[2025-07-22 22:01:20,571] [INFO] 数据采集: 已清理浏览器会话，准备处理账号 测试
[2025-07-22 22:01:20,572] [DEBUG] JSON格式解析成功，提取到 5 个关键Cookie
[2025-07-22 22:01:20,572] [INFO] 📋 解析到 5 个关键Cookie
[2025-07-22 22:01:20,572] [INFO] 🌐 导航到头条媒体平台
[2025-07-22 22:01:20,573] [DEBUG] 数据采集状态更新: ℹ️ 🔑 使用统一登录管理器登录账号 测试
[2025-07-22 22:01:20,573] [INFO] 数据采集: 🔑 使用统一登录管理器登录账号 测试
[2025-07-22 22:01:22,496] [INFO] 🔑 添加 5 个关键Cookie
[2025-07-22 22:01:22,518] [INFO] ✅ 成功添加 5 个Cookie
[2025-07-22 22:01:22,518] [INFO] 🔄 刷新页面应用Cookie
[2025-07-22 22:01:25,509] [INFO] 🔍 验证登录状态
[2025-07-22 22:01:25,513] [DEBUG] URL验证通过 (0.00s)
[2025-07-22 22:01:25,513] [INFO] [SUCCESS] ✅ 账号 测试 登录成功
[2025-07-22 22:01:25,514] [INFO] 🔍 开始检测 账号 测试 的实名认证状态...
[2025-07-22 22:01:25,514] [DEBUG] 数据采集状态更新: ✅ ✅ 账号 测试 登录成功: 登录成功
[2025-07-22 22:01:25,516] [INFO] [SUCCESS] 数据采集: ✅ 账号 测试 登录成功: 登录成功
[2025-07-22 22:01:25,530] [INFO] 📋 文本检测结果: 页面文本检测通过
[2025-07-22 22:01:33,727] [INFO] 双击表格单元格: 行=0, 列=0
[2025-07-22 22:01:33,728] [DEBUG] 双击账号列表不加载数据，功能已禁用
[2025-07-22 22:02:29,170] [INFO] 开始强制清理批量存稿对象...
[2025-07-22 22:02:29,171] [INFO] 已清理活跃存稿任务标记
[2025-07-22 22:02:29,172] [INFO] 已清理主窗口中的_batch_draft_worker引用
[2025-07-22 22:02:29,172] [INFO] 已清理主窗口中的_batch_draft_thread引用
[2025-07-22 22:02:29,172] [INFO] 批量存稿状态标志重置完成
[2025-07-22 22:02:29,722] [INFO] 强制清理批量存稿对象完成
[2025-07-22 22:02:29,723] [DEBUG] 未检测到正在运行的批量存稿任务
[2025-07-22 22:02:29,723] [INFO] 进行预防性清理，确保没有残留对象...
[2025-07-22 22:02:29,723] [INFO] 开始强制清理批量存稿对象...
[2025-07-22 22:02:29,724] [INFO] 已清理活跃存稿任务标记
[2025-07-22 22:02:29,724] [INFO] 已清理主窗口中的_batch_draft_worker引用
[2025-07-22 22:02:29,724] [INFO] 已清理主窗口中的_batch_draft_thread引用
[2025-07-22 22:02:29,725] [INFO] 批量存稿状态标志重置完成
[2025-07-22 22:02:30,291] [INFO] 强制清理批量存稿对象完成
[2025-07-22 22:02:31,098] [INFO] 🔍 元素检测结果: 元素检测通过
[2025-07-22 22:02:31,104] [INFO] 🌐 URL检测结果: URL检测通过
[2025-07-22 22:02:31,104] [INFO] ✅ 账号 测试 已实名认证
[2025-07-22 22:02:31,123] [DEBUG] 数据采集状态更新: ✅ ✅ 账号 测试 已实名认证，继续数据采集
[2025-07-22 22:02:31,123] [INFO] [SUCCESS] 数据采集: ✅ 账号 测试 已实名认证，继续数据采集
[2025-07-22 22:02:31,124] [DEBUG] 数据采集状态更新: ✅ 账号 测试 登录成功，开始采集数据
[2025-07-22 22:02:31,125] [INFO] [SUCCESS] 数据采集: 账号 测试 登录成功，开始采集数据
[2025-07-22 22:02:31,126] [DEBUG] 数据采集状态更新: 🔄 正在访问数据概览页面...
[2025-07-22 22:02:35,027] [INFO] 用户取消了批量存稿操作
[2025-07-22 22:02:40,071] [DEBUG] 数据采集状态更新: ℹ️ 账号 测试 实名状态: 已实名
[2025-07-22 22:02:40,072] [INFO] 数据采集: 账号 测试 实名状态: 已实名
[2025-07-22 22:02:40,102] [DEBUG] 数据采集状态更新: ✅ 已获取昵称: 林深见鹿
[2025-07-22 22:02:40,124] [DEBUG] 数据采集状态更新: ✅ 已获取总粉丝数: 0
[2025-07-22 22:02:40,147] [DEBUG] 数据采集状态更新: ⚠️ 无法从文本中提取昨日粉丝数: 昨日无变化
[2025-07-22 22:02:40,148] [WARNING] 数据采集: 无法从文本中提取昨日粉丝数: 昨日无变化
[2025-07-22 22:02:40,169] [DEBUG] 数据采集状态更新: ✅ 已获取累计播放量: 0
[2025-07-22 22:02:40,207] [DEBUG] 数据采集状态更新: ⚠️ 无法从文本中提取昨日播放量: 昨日无变化
[2025-07-22 22:02:40,208] [WARNING] 数据采集: 无法从文本中提取昨日播放量: 昨日无变化
[2025-07-22 22:02:40,242] [DEBUG] 数据采集状态更新: ✅ 已获取累计收益: 0元
[2025-07-22 22:02:40,299] [DEBUG] 数据采集状态更新: ⚠️ 无法从文本中提取昨日收益: 昨日无变化
[2025-07-22 22:02:40,300] [WARNING] 数据采集: 无法从文本中提取昨日收益: 昨日无变化
[2025-07-22 22:02:40,333] [DEBUG] 数据采集状态更新: ✅ 已获取账号注册天数: 16 天
[2025-07-22 22:02:40,334] [DEBUG] 数据采集状态更新: 🔄 正在跳转到创作者计划页面采集信用分...
[2025-07-22 22:02:44,186] [DEBUG] 数据采集状态更新: ✅ 已获取信用分: 100
[2025-07-22 22:02:44,187] [DEBUG] 数据采集状态更新: 🔄 正在跳转到草稿界面采集草稿数量...
[2025-07-22 22:02:48,063] [DEBUG] 数据采集状态更新: ✅ 已获取草稿数量: 1
[2025-07-22 22:02:48,064] [DEBUG] 数据采集状态更新: 🔄 正在跳转到收益数据界面采集可提现金额...
[2025-07-22 22:02:51,906] [DEBUG] 数据采集状态更新: ✅ 已获取可提现金额: 0
[2025-07-22 22:02:51,907] [DEBUG] 数据采集状态更新: 🔄 正在采集七天收益数据...
[2025-07-22 22:02:51,933] [DEBUG] 数据采集状态更新: ✅ 第1天收益: 0
[2025-07-22 22:02:51,956] [DEBUG] 数据采集状态更新: ✅ 第2天收益: 0
[2025-07-22 22:02:51,981] [DEBUG] 数据采集状态更新: ✅ 第3天收益: 0
[2025-07-22 22:02:52,007] [DEBUG] 数据采集状态更新: ✅ 第4天收益: 0
[2025-07-22 22:02:52,030] [DEBUG] 数据采集状态更新: ✅ 第5天收益: 0
[2025-07-22 22:02:52,053] [DEBUG] 数据采集状态更新: ✅ 第6天收益: 0
[2025-07-22 22:02:52,074] [DEBUG] 数据采集状态更新: ✅ 第7天收益: 0
[2025-07-22 22:02:52,075] [DEBUG] 数据采集状态更新: ✅ 七天总收益: 0.00
[2025-07-22 22:02:52,076] [DEBUG] 数据采集状态更新: 🔄 正在跳转到提现界面采集提现相关数据...
[2025-07-22 22:02:55,930] [DEBUG] 数据采集状态更新: ✅ 已获取总提现金额: 0
[2025-07-22 22:03:00,942] [DEBUG] 数据采集状态更新: ⚠️ 采集提现记录失败: Message: 
Stacktrace:
	GetHandleVerifier [0x0x7ff6ab07fea5+79173]
	GetHandleVerifier [0x0x7ff6ab07ff00+79264]
	(No symbol) [0x0x7ff6aae39e5a]
	(No symbol) [0x0x7ff6aae90586]
	(No symbol) [0x0x7ff6aae9083c]
	(No symbol) [0x0x7ff6aaee4247]
	(No symbol) [0x0x7ff6aaeb89af]
	(No symbol) [0x0x7ff6aaee100d]
	(No symbol) [0x0x7ff6aaeb8743]
	(No symbol) [0x0x7ff6aae814c1]
	(No symbol) [0x0x7ff6aae82253]
	GetHandleVerifier [0x0x7ff6ab34a2dd+3004797]
	GetHandleVerifier [0x0x7ff6ab34472d+2981325]
	GetHandleVerifier [0x0x7ff6ab363380+3107360]
	GetHandleVerifier [0x0x7ff6ab09aa2e+188622]
	GetHandleVerifier [0x0x7ff6ab0a22bf+219487]
	GetHandleVerifier [0x0x7ff6ab088df4+115860]
	GetHandleVerifier [0x0x7ff6ab088fa9+116297]
	GetHandleVerifier [0x0x7ff6ab06f558+11256]
	BaseThreadInitThunk [0x0x7ffa510ae8d7+23]
	RtlUserThreadStart [0x0x7ffa520dc34c+44]

[2025-07-22 22:03:00,943] [WARNING] 数据采集: 采集提现记录失败: Message: 
Stacktrace:
	GetHandleVerifier [0x0x7ff6ab07fea5+79173]
	GetHandleVerifier [0x0x7ff6ab07ff00+79264]
	(No symbol) [0x0x7ff6aae39e5a]
	(No symbol) [0x0x7ff6aae90586]
	(No symbol) [0x0x7ff6aae9083c]
	(No symbol) [0x0x7ff6aaee4247]
	(No symbol) [0x0x7ff6aaeb89af]
	(No symbol) [0x0x7ff6aaee100d]
	(No symbol) [0x0x7ff6aaeb8743]
	(No symbol) [0x0x7ff6aae814c1]
	(No symbol) [0x0x7ff6aae82253]
	GetHandleVerifier [0x0x7ff6ab34a2dd+3004797]
	GetHandleVerifier [0x0x7ff6ab34472d+2981325]
	GetHandleVerifier [0x0x7ff6ab363380+3107360]
	GetHandleVerifier [0x0x7ff6ab09aa2e+188622]
	GetHandleVerifier [0x0x7ff6ab0a22bf+219487]
	GetHandleVerifier [0x0x7ff6ab088df4+115860]
	GetHandleVerifier [0x0x7ff6ab088fa9+116297]
	GetHandleVerifier [0x0x7ff6ab06f558+11256]
	BaseThreadInitThunk [0x0x7ffa510ae8d7+23]
	RtlUserThreadStart [0x0x7ffa520dc34c+44]

[2025-07-22 22:03:00,947] [DEBUG] 数据采集状态更新: ✅ 已通过JavaScript获取提现日期: 无记录
[2025-07-22 22:03:00,947] [DEBUG] 数据采集状态更新: ✅ 已通过JavaScript获取最近提现金额: 0
[2025-07-22 22:03:00,949] [DEBUG] 数据采集状态更新: ℹ️ 更新账号 测试 实名状态: 已实名
[2025-07-22 22:03:00,950] [INFO] 数据采集: 更新账号 测试 实名状态: 已实名
[2025-07-22 22:03:00,951] [DEBUG] 数据采集状态更新: ✅ 账号 测试 数据采集成功
[2025-07-22 22:03:00,952] [INFO] [SUCCESS] 数据采集: 账号 测试 数据采集成功
[2025-07-22 22:03:00,971] [INFO] 数据采集: 已将账号 测试 的数据保存到统一JSON文件
[2025-07-22 22:03:00,972] [DEBUG] 数据采集状态更新: ✅ 已实时保存账号 测试 的数据
[2025-07-22 22:03:00,973] [INFO] [SUCCESS] 数据采集: 已实时保存账号 测试 的数据
[2025-07-22 22:03:00,973] [DEBUG] 数据采集状态更新: 更新浏览器进度:采集线程-1:测试:100
[2025-07-22 22:03:00,973] [DEBUG] 浏览器进度更新: 线程=采集线程-1, 账号=测试, 进度=100.0%
[2025-07-22 22:03:00,974] [DEBUG] 数据采集状态更新: 更新总体进度:测试:1:1:数据采集成功
[2025-07-22 22:03:00,974] [DEBUG] 总体进度更新: 账号=测试, 进度=1/1, 状态=数据采集成功
[2025-07-22 22:03:00,974] [DEBUG] 数据采集状态更新: 更新已完成计数:1:1
[2025-07-22 22:03:00,975] [DEBUG] 已完成计数更新: 1/1
[2025-07-22 22:03:00,975] [DEBUG] 数据采集状态更新: 更新总体进度:测试:1:1:数据采集成功
[2025-07-22 22:03:00,975] [DEBUG] 总体进度更新: 账号=测试, 进度=1/1, 状态=数据采集成功
[2025-07-22 22:03:01,474] [INFO] 数据采集: [采集线程-1] 尝试获取下一个账号，当前索引: 1，队列长度: 1
[2025-07-22 22:03:01,476] [INFO] 数据采集: [采集线程-1] 没有更多账号可处理，当前索引: 1，队列长度: 1
[2025-07-22 22:03:01,476] [DEBUG] 数据采集状态更新: ℹ️ [采集线程-1] 没有更多账号需要处理
[2025-07-22 22:03:01,477] [INFO] 数据采集: [采集线程-1] 没有更多账号需要处理
[2025-07-22 22:03:01,477] [DEBUG] 数据采集状态更新: ✅ 已完成所有账号的数据采集
[2025-07-22 22:03:01,478] [INFO] [SUCCESS] 数据采集: 已完成所有账号的数据采集
[2025-07-22 22:03:03,737] [DEBUG] 数据采集状态更新: ℹ️ 已关闭浏览器
[2025-07-22 22:03:03,738] [INFO] 数据采集: 已关闭浏览器
[2025-07-22 22:03:03,738] [DEBUG] 数据采集状态更新: ℹ️ 采集线程已完成
[2025-07-22 22:03:03,738] [INFO] 数据采集: 采集线程已完成
[2025-07-22 22:03:03,739] [INFO] 数据采集: 一个采集线程已完成，剩余 0 个线程
[2025-07-22 22:03:03,739] [INFO] [SUCCESS] 数据采集: 所有采集线程已完成。成功: 1 账号，失败: 0 账号
[2025-07-22 22:03:03,739] [DEBUG] 数据采集状态更新: 更新已完成计数:1:1
[2025-07-22 22:03:03,739] [DEBUG] 已完成计数更新: 1/1
[2025-07-22 22:03:03,740] [DEBUG] 数据采集状态更新: 更新总体进度:完成:1:1:采集完成
[2025-07-22 22:03:03,740] [DEBUG] 总体进度更新: 账号=完成, 进度=1/1, 状态=采集完成
[2025-07-22 22:03:03,749] [INFO] 数据采集: 已读取现有账号数据，共 101 个账号
[2025-07-22 22:03:03,773] [INFO] [SUCCESS] 数据采集: 已将所有账号数据保存到统一JSON文件: D:/头条全自动/数据\accounts_data.json
[2025-07-22 22:03:05,249] [INFO] 数据采集: 正在导出数据到Excel文件...
[2025-07-22 22:03:05,451] [INFO] [SUCCESS] 数据采集: 数据已成功导出到Excel文件: D:/头条全自动/数据\头条账号数据_20250722_220305.xlsx
[2025-07-22 22:03:05,452] [INFO] 数据采集全部完成
[2025-07-22 22:03:06,731] [INFO] 开始自动统计七天收益和昨日收益...
[2025-07-22 22:03:06,732] [INFO] 开始计算收益统计，表格行数: 1, 列数: 19
[2025-07-22 22:03:06,732] [INFO] 收益统计完成 - 统计了 1 个账号
[2025-07-22 22:03:06,733] [INFO] 七天总收益: ¥0.00
[2025-07-22 22:03:06,733] [INFO] 昨日总收益: ¥0.00
[2025-07-22 22:03:06,733] [INFO] 更新7天总收益统计: ¥0.00
[2025-07-22 22:03:06,736] [INFO] 更新昨日总收益统计: ¥0.00
[2025-07-22 22:03:06,739] [INFO] ✅ 收益统计已完成并更新到主界面顶部
[2025-07-22 22:03:24,601] [INFO] 开始强制清理批量存稿对象...
[2025-07-22 22:03:24,602] [INFO] 已清理活跃存稿任务标记
[2025-07-22 22:03:24,602] [INFO] 已清理主窗口中的_batch_draft_worker引用
[2025-07-22 22:03:24,602] [INFO] 已清理主窗口中的_batch_draft_thread引用
[2025-07-22 22:03:24,603] [INFO] 批量存稿状态标志重置完成
[2025-07-22 22:03:25,193] [INFO] 强制清理批量存稿对象完成
[2025-07-22 22:03:25,194] [DEBUG] 未检测到正在运行的批量存稿任务
[2025-07-22 22:03:25,194] [INFO] 进行预防性清理，确保没有残留对象...
[2025-07-22 22:03:25,195] [INFO] 开始强制清理批量存稿对象...
[2025-07-22 22:03:25,195] [INFO] 已清理活跃存稿任务标记
[2025-07-22 22:03:25,196] [INFO] 已清理主窗口中的_batch_draft_worker引用
[2025-07-22 22:03:25,196] [INFO] 已清理主窗口中的_batch_draft_thread引用
[2025-07-22 22:03:25,196] [INFO] 批量存稿状态标志重置完成
[2025-07-22 22:03:25,753] [INFO] 强制清理批量存稿对象完成
[2025-07-22 22:03:46,127] [INFO] 启动批量存稿任务 - 无头模式: True, 并发数: 1, 存稿次数: 5, 视频公平分配: True, 清理缓存: True, 去除活动弹窗: False, 处理发布限制弹窗: False
[2025-07-22 22:03:46,127] [DEBUG] 未检测到正在运行的批量存稿任务
[2025-07-22 22:03:46,128] [INFO] 进行预防性清理，确保没有残留对象...
[2025-07-22 22:03:46,128] [INFO] 开始清理存稿任务对象...
[2025-07-22 22:03:46,129] [INFO] 已清理主窗口中的批量存稿任务引用
[2025-07-22 22:03:46,314] [INFO] 存稿任务对象清理完成
[2025-07-22 22:03:46,315] [INFO] 已从设置中获取视频目录: D:/头条全自动/视频搬运/已处理视频
[2025-07-22 22:03:46,316] [INFO] 已从设置中获取封面目录: D:/头条全自动/视频搬运/已处理封面
[2025-07-22 22:03:46,327] [INFO] 找到 474 个视频文件: 一口气看完为什么中国战机突然领先美国全集.mp4, 一句恒河以北归中国印度人彻底破防 2.mp4, 一句恒河以北归中国又一次让印度三哥彻底破防了.mp4...
[2025-07-22 22:03:46,339] [INFO] 找到 474 个封面文件: 一口气看完为什么中国战机突然领先美国全集.jpg, 一句恒河以北归中国印度人彻底破防 2.jpg, 一句恒河以北归中国又一次让印度三哥彻底破防了.jpg...
[2025-07-22 22:03:47,996] [INFO] 创建批量存稿任务 - 无头模式: True, 并发数: 1, 存稿次数: 5
[2025-07-22 22:03:47,997] [INFO] 批量存稿线程已启动
[2025-07-22 22:03:47,999] [INFO] 开始执行批量存稿任务
[2025-07-22 22:03:47,999] [DEBUG] 重试管理器初始化完成 - L1即时重试: 登录2次/网络1次, L2操作重试: 视频上传1次/页面加载1次, L3批次重试: 启用, 最大1轮
[2025-07-22 22:03:47,999] [INFO] 重试管理器初始化完成
[2025-07-22 22:03:48,000] [INFO] 运行时间统计定时器已启动
[2025-07-22 22:03:48,001] [INFO] 获取到账号列表，共 1 个账号
[2025-07-22 22:03:48,002] [INFO] 账号 1: 测试, Cookie文件: E:/软件共享/测试账号/新建文件夹 (2)\测试.txt
[2025-07-22 22:03:48,006] [INFO] 批量存稿任务线程已启动
[2025-07-22 22:03:48,007] [INFO] 开始执行批量存稿，账号数量: 1
[2025-07-22 22:03:48,007] [INFO] 工作窃取模式: 1 个线程处理 1 个账号
[2025-07-22 22:03:48,012] [INFO] ✅ 工作窃取线程 #0 已启动
[2025-07-22 22:03:48,012] [INFO] 所有工作窃取线程已启动，共 1 个线程
[2025-07-22 22:03:48,013] [INFO] 等待工作窃取线程 #0 完成...
[2025-07-22 22:03:48,028] [INFO] [SUCCESS] 批量存稿任务已启动
[2025-07-22 22:03:48,029] [INFO] [SUCCESS] 批量存稿设置已确认，开始执行任务
[2025-07-22 22:03:51,332] [INFO] 批量存稿状态更新: ✅ 工作窃取线程 #0 浏览器初始化成功，开始处理任务
[2025-07-22 22:03:51,333] [INFO] 🔑 开始登录账号 测试
[2025-07-22 22:03:51,333] [INFO] 批量存稿状态更新: 🔄 工作窃取线程 #0 开始处理账号 测试 (第1个)
[2025-07-22 22:03:51,336] [DEBUG] JSON格式解析成功，提取到 5 个关键Cookie
[2025-07-22 22:03:51,336] [INFO] 📋 解析到 5 个关键Cookie
[2025-07-22 22:03:51,337] [INFO] 🌐 导航到头条媒体平台
[2025-07-22 22:03:53,033] [INFO] 🔑 添加 5 个关键Cookie
[2025-07-22 22:03:53,069] [INFO] ✅ 成功添加 5 个Cookie
[2025-07-22 22:03:53,070] [INFO] 🔄 刷新页面应用Cookie
[2025-07-22 22:03:56,123] [INFO] 🔍 验证登录状态
[2025-07-22 22:03:56,129] [DEBUG] URL验证通过 (0.01s)
[2025-07-22 22:03:56,129] [INFO] [SUCCESS] ✅ 账号 测试 登录成功
[2025-07-22 22:03:56,131] [INFO] 🔍 开始检测 账号 测试 的实名认证状态...
[2025-07-22 22:03:56,132] [INFO] 批量存稿状态更新: ✅ 账号 测试 登录成功
[2025-07-22 22:03:56,153] [INFO] 📋 文本检测结果: 页面文本检测通过
[2025-07-22 22:03:56,286] [INFO] 🔍 元素检测结果: 元素检测通过
[2025-07-22 22:03:56,291] [INFO] 🌐 URL检测结果: URL检测通过
[2025-07-22 22:03:56,291] [INFO] ✅ 账号 测试 已实名认证
[2025-07-22 22:03:56,294] [INFO] ✅ 已更新账号 测试 的实名状态为: 已实名
[2025-07-22 22:03:56,295] [INFO] ✅ 账号 测试 已实名认证，继续任务
[2025-07-22 22:03:56,295] [INFO] 批量存稿状态更新: ✅ 账号 测试 登录成功
[2025-07-22 22:03:56,302] [INFO] 批量存稿状态更新: 账号 测试 开始执行存稿任务...
[2025-07-22 22:03:56,311] [INFO] 批量存稿状态更新: 账号 测试 开始第 1/5 次存稿
[2025-07-22 22:03:57,305] [INFO] 批量存稿状态更新: 🔄 标准模式：正在导航到视频上传页面...
[2025-07-22 22:04:00,321] [INFO] 批量存稿状态更新: 账号 测试 选择视频文件: 中国女篮变动宫鲁鸣李梦事件未完竟遭多国联赛哄抢.mp4
[2025-07-22 22:04:00,322] [INFO] 批量存稿状态更新: 🔍 检测目标元素是否存在...
[2025-07-22 22:04:00,331] [INFO] 批量存稿状态更新: ✅ 检测到目标元素，立即开始视频上传...
[2025-07-22 22:04:00,332] [INFO] 批量存稿状态更新: 🔍 查找文件输入元素(方法1: CSS选择器)...
[2025-07-22 22:04:00,340] [INFO] 批量存稿状态更新: ✅ 方法1成功找到文件输入元素
[2025-07-22 22:04:00,342] [INFO] 批量存稿状态更新: ✅ 准备发送文件路径...
[2025-07-22 22:04:00,342] [INFO] 批量存稿状态更新: 📂 文件绝对路径: D:\头条全自动\视频搬运\已处理视频\中国女篮变动宫鲁鸣李梦事件未完竟遭多国联赛哄抢.mp4
[2025-07-22 22:04:01,391] [INFO] 批量存稿状态更新: ✅ 已通过传统方式发送文件路径
[2025-07-22 22:04:01,401] [INFO] 批量存稿状态更新: ✅ 已滚动到页面底部
[2025-07-22 22:04:01,401] [INFO] 批量存稿状态更新: ⏳ 等待视频开始上传...
[2025-07-22 22:04:02,902] [INFO] 批量存稿状态更新: 🔍 准备处理封面，立即查找封面元素...
[2025-07-22 22:04:03,946] [INFO] 批量存稿状态更新: ✅ 找到封面元素，使用JavaScript点击...
[2025-07-22 22:04:04,039] [INFO] 批量存稿状态更新: ✅ 普通点击成功
[2025-07-22 22:04:04,041] [INFO] 批量存稿状态更新: ✅ 已点击封面元素
[2025-07-22 22:04:04,042] [INFO] 批量存稿状态更新: 🔍 账号 测试: 等待封面上传界面出现...
[2025-07-22 22:04:04,043] [INFO] 批量存稿状态更新: ⏳ 账号 测试: 等待封面上传对话框显示...
[2025-07-22 22:04:04,052] [INFO] 批量存稿状态更新: ✅ 账号 测试: 封面上传对话框已显示
[2025-07-22 22:04:04,054] [INFO] 批量存稿状态更新: 🔍 账号 测试: 查找本地封面选项...
[2025-07-22 22:04:04,074] [INFO] 批量存稿状态更新: ✅ 找到本地封面选项，立即点击...
[2025-07-22 22:04:04,212] [INFO] 批量存稿状态更新: ✅ 已点击本地封面选项
[2025-07-22 22:04:04,214] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备上传封面...
[2025-07-22 22:04:04,217] [INFO] 批量存稿状态更新: ✅ 账号 测试: 找到封面文件: 中国女篮变动宫鲁鸣李梦事件未完竟遭多国联赛哄抢.jpg
[2025-07-22 22:04:04,219] [INFO] 批量存稿状态更新: 🔍 等待文件输入元素出现...
[2025-07-22 22:04:04,220] [INFO] 批量存稿状态更新: 🔍 在封面上传对话框中查找文件输入元素...
[2025-07-22 22:04:04,244] [INFO] 批量存稿状态更新: ✅ 方法2成功：通过XPath在对话框中找到 1 个文件输入元素
[2025-07-22 22:04:04,245] [INFO] 批量存稿状态更新: ✅ 找到封面上传对话框中的文件输入元素
[2025-07-22 22:04:04,247] [INFO] 批量存稿状态更新: ✅ 发送封面文件路径...
[2025-07-22 22:04:04,272] [INFO] 批量存稿状态更新: ✅ 已发送封面文件路径
[2025-07-22 22:04:04,274] [INFO] 批量存稿状态更新: ✅ 封面上传成功
[2025-07-22 22:04:04,276] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备点击封面确认按钮...
[2025-07-22 22:04:05,276] [INFO] 批量存稿状态更新: 🔍 账号 测试: 尝试点击第一个封面确认按钮(方法1)...
[2025-07-22 22:04:05,465] [INFO] 批量存稿状态更新: 第一个按钮结果(方法1): 第一个按钮点击成功(方法1)
[2025-07-22 22:04:05,468] [INFO] 批量存稿状态更新: 🔍 尝试点击第一个封面确认按钮(方法2)...
[2025-07-22 22:04:05,481] [INFO] 批量存稿状态更新: 第一个按钮结果(方法2): 第一个按钮未找到(方法2)
[2025-07-22 22:04:05,483] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法1)...
[2025-07-22 22:04:05,547] [INFO] 批量存稿状态更新: 第二个按钮结果(方法1): 第二个按钮点击成功(方法1)
[2025-07-22 22:04:05,549] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法2)...
[2025-07-22 22:04:05,558] [INFO] 批量存稿状态更新: 第二个按钮结果(方法2): 第二个按钮未找到(方法2)
[2025-07-22 22:04:05,561] [INFO] 批量存稿状态更新: ✅ 所有封面确认按钮点击成功
[2025-07-22 22:04:05,563] [INFO] 批量存稿状态更新: 🔍 等待弹窗消失...
[2025-07-22 22:04:14,147] [INFO] 批量存稿状态更新: ✅ 第一个弹窗已消失
[2025-07-22 22:04:14,171] [INFO] 批量存稿状态更新: ✅ 第二个弹窗已消失
[2025-07-22 22:04:14,173] [INFO] 批量存稿状态更新: 🔍 检测封面上传成功元素...
[2025-07-22 22:04:14,701] [INFO] 批量存稿状态更新: ✅ 已检测到封面上传成功元素
[2025-07-22 22:04:14,703] [INFO] 批量存稿状态更新: ✅ 确认检测到封面上传成功元素!
[2025-07-22 22:04:14,704] [INFO] 批量存稿状态更新: 🔍 立即检测上传成功文本...
[2025-07-22 22:04:18,932] [INFO] 批量存稿状态更新: ✅ 已检测到上传成功提示 (XPath方式)
[2025-07-22 22:04:18,933] [INFO] 批量存稿状态更新: ✅ 确认检测到上传成功提示!
[2025-07-22 22:04:18,934] [INFO] 批量存稿状态更新: ✅ 封面提交完成
[2025-07-22 22:04:18,936] [INFO] 批量存稿状态更新: ✅ 封面处理完成，开始监测视频上传状态...
[2025-07-22 22:04:18,938] [INFO] 批量存稿状态更新: 🔍 账号 测试 开始并行监测视频上传状态...
[2025-07-22 22:04:19,571] [DEBUG] 视频上传失败检测收到停止信号，退出
[2025-07-22 22:04:19,574] [INFO] 批量存稿状态更新: ✅ 账号 测试 视频上传成功检测完成
[2025-07-22 22:04:19,576] [INFO] 批量存稿状态更新: ✅ 视频上传成功确认
[2025-07-22 22:04:21,577] [INFO] 批量存稿状态更新: 🎯 存稿模式: 尝试点击存稿按钮...
[2025-07-22 22:04:21,589] [INFO] 批量存稿状态更新: 📝 存稿按钮点击结果: 存稿按钮点击成功
[2025-07-22 22:04:21,590] [INFO] 批量存稿状态更新: ✅ 已点击存稿按钮，开始检测保存状态...
[2025-07-22 22:04:21,591] [INFO] 批量存稿状态更新: 🔍 开始检测'全部保存成功'文字...
[2025-07-22 22:04:22,745] [INFO] 批量存稿状态更新: ✅ 在页面源代码中检测到'全部保存成功'
[2025-07-22 22:04:22,745] [DEBUG] 账号 测试 第 1/5 次存稿完成
[2025-07-22 22:04:22,747] [INFO] 批量存稿状态更新: ✅ 检测到'全部保存成功'，存稿操作完成
[2025-07-22 22:04:22,747] [INFO] 批量存稿状态更新: 🚀 立即开始下一个任务
[2025-07-22 22:04:22,748] [INFO] 批量存稿状态更新: ✅ 账号 测试 第1次存稿成功
[2025-07-22 22:04:24,756] [INFO] 批量存稿状态更新: 账号 测试 开始第 2/5 次存稿
[2025-07-22 22:04:24,763] [INFO] 批量存稿状态更新: 直接导航到上传界面
[2025-07-22 22:04:25,722] [INFO] 批量存稿状态更新: 账号 测试 选择视频文件: 志新观天下某国稍微支持下大俄就真支棱起来了.mp4
[2025-07-22 22:04:25,723] [INFO] 批量存稿状态更新: 🔍 检测目标元素是否存在...
[2025-07-22 22:04:27,333] [INFO] 批量存稿状态更新: ✅ 检测到目标元素，立即开始视频上传...
[2025-07-22 22:04:27,335] [INFO] 批量存稿状态更新: 🔍 查找文件输入元素(方法1: CSS选择器)...
[2025-07-22 22:04:27,346] [INFO] 批量存稿状态更新: ✅ 方法1成功找到文件输入元素
[2025-07-22 22:04:27,346] [INFO] 批量存稿状态更新: ✅ 准备发送文件路径...
[2025-07-22 22:04:27,348] [INFO] 批量存稿状态更新: 📂 文件绝对路径: D:\头条全自动\视频搬运\已处理视频\志新观天下某国稍微支持下大俄就真支棱起来了.mp4
[2025-07-22 22:04:28,401] [INFO] 批量存稿状态更新: ✅ 已通过传统方式发送文件路径
[2025-07-22 22:04:28,407] [INFO] 批量存稿状态更新: ✅ 已滚动到页面底部
[2025-07-22 22:04:28,408] [INFO] 批量存稿状态更新: ⏳ 等待视频开始上传...
[2025-07-22 22:04:29,909] [INFO] 批量存稿状态更新: 🔍 准备处理封面，立即查找封面元素...
[2025-07-22 22:04:30,952] [INFO] 批量存稿状态更新: ✅ 找到封面元素，使用JavaScript点击...
[2025-07-22 22:04:31,023] [INFO] 批量存稿状态更新: ✅ 普通点击成功
[2025-07-22 22:04:31,024] [INFO] 批量存稿状态更新: ✅ 已点击封面元素
[2025-07-22 22:04:31,025] [INFO] 批量存稿状态更新: 🔍 账号 测试: 等待封面上传界面出现...
[2025-07-22 22:04:31,026] [INFO] 批量存稿状态更新: ⏳ 账号 测试: 等待封面上传对话框显示...
[2025-07-22 22:04:31,037] [INFO] 批量存稿状态更新: ✅ 账号 测试: 封面上传对话框已显示
[2025-07-22 22:04:31,040] [INFO] 批量存稿状态更新: 🔍 账号 测试: 查找本地封面选项...
[2025-07-22 22:04:31,057] [INFO] 批量存稿状态更新: ✅ 找到本地封面选项，立即点击...
[2025-07-22 22:04:31,165] [INFO] 批量存稿状态更新: ✅ 已点击本地封面选项
[2025-07-22 22:04:31,166] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备上传封面...
[2025-07-22 22:04:31,169] [INFO] 批量存稿状态更新: ✅ 账号 测试: 找到封面文件: 志新观天下某国稍微支持下大俄就真支棱起来了.jpg
[2025-07-22 22:04:31,171] [INFO] 批量存稿状态更新: 🔍 等待文件输入元素出现...
[2025-07-22 22:04:31,172] [INFO] 批量存稿状态更新: 🔍 在封面上传对话框中查找文件输入元素...
[2025-07-22 22:04:31,203] [INFO] 批量存稿状态更新: ✅ 方法2成功：通过XPath在对话框中找到 1 个文件输入元素
[2025-07-22 22:04:31,205] [INFO] 批量存稿状态更新: ✅ 找到封面上传对话框中的文件输入元素
[2025-07-22 22:04:31,206] [INFO] 批量存稿状态更新: ✅ 发送封面文件路径...
[2025-07-22 22:04:31,226] [INFO] 批量存稿状态更新: ✅ 已发送封面文件路径
[2025-07-22 22:04:31,227] [INFO] 批量存稿状态更新: ✅ 封面上传成功
[2025-07-22 22:04:31,229] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备点击封面确认按钮...
[2025-07-22 22:04:32,229] [INFO] 批量存稿状态更新: 🔍 账号 测试: 尝试点击第一个封面确认按钮(方法1)...
[2025-07-22 22:04:32,456] [INFO] 批量存稿状态更新: 第一个按钮结果(方法1): 第一个按钮点击成功(方法1)
[2025-07-22 22:04:32,459] [INFO] 批量存稿状态更新: 🔍 尝试点击第一个封面确认按钮(方法2)...
[2025-07-22 22:04:32,471] [INFO] 批量存稿状态更新: 第一个按钮结果(方法2): 第一个按钮未找到(方法2)
[2025-07-22 22:04:32,475] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法1)...
[2025-07-22 22:04:32,514] [INFO] 批量存稿状态更新: 第二个按钮结果(方法1): 第二个按钮点击成功(方法1)
[2025-07-22 22:04:32,516] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法2)...
[2025-07-22 22:04:32,523] [INFO] 批量存稿状态更新: 第二个按钮结果(方法2): 第二个按钮未找到(方法2)
[2025-07-22 22:04:32,525] [INFO] 批量存稿状态更新: ✅ 所有封面确认按钮点击成功
[2025-07-22 22:04:32,526] [INFO] 批量存稿状态更新: 🔍 等待弹窗消失...
[2025-07-22 22:04:35,258] [INFO] 批量存稿状态更新: ✅ 第一个弹窗已消失
[2025-07-22 22:04:35,269] [INFO] 批量存稿状态更新: ✅ 第二个弹窗已消失
[2025-07-22 22:04:35,272] [INFO] 批量存稿状态更新: 🔍 检测封面上传成功元素...
[2025-07-22 22:04:35,802] [INFO] 批量存稿状态更新: ✅ 已检测到封面上传成功元素
[2025-07-22 22:04:35,805] [INFO] 批量存稿状态更新: ✅ 确认检测到封面上传成功元素!
[2025-07-22 22:04:35,806] [INFO] 批量存稿状态更新: 🔍 立即检测上传成功文本...
[2025-07-22 22:04:48,423] [INFO] 批量存稿状态更新: ✅ 已检测到上传成功提示 (XPath方式)
[2025-07-22 22:04:48,424] [INFO] 批量存稿状态更新: ✅ 确认检测到上传成功提示!
[2025-07-22 22:04:48,424] [INFO] 批量存稿状态更新: ✅ 封面提交完成
[2025-07-22 22:04:48,427] [INFO] 批量存稿状态更新: ✅ 封面处理完成，开始监测视频上传状态...
[2025-07-22 22:04:48,428] [INFO] 批量存稿状态更新: 🔍 账号 测试 开始并行监测视频上传状态...
[2025-07-22 22:04:49,031] [DEBUG] 视频上传失败检测收到停止信号，退出
[2025-07-22 22:04:49,032] [INFO] 批量存稿状态更新: ✅ 账号 测试 视频上传成功检测完成
[2025-07-22 22:04:49,034] [INFO] 批量存稿状态更新: ✅ 视频上传成功确认
[2025-07-22 22:04:51,040] [INFO] 批量存稿状态更新: 🎯 存稿模式: 尝试点击存稿按钮...
[2025-07-22 22:04:51,050] [INFO] 批量存稿状态更新: 📝 存稿按钮点击结果: 存稿按钮点击成功
[2025-07-22 22:04:51,051] [INFO] 批量存稿状态更新: ✅ 已点击存稿按钮，开始检测保存状态...
[2025-07-22 22:04:51,053] [INFO] 批量存稿状态更新: 🔍 开始检测'全部保存成功'文字...
[2025-07-22 22:04:52,252] [DEBUG] 账号 测试 第 2/5 次存稿完成
[2025-07-22 22:04:52,252] [INFO] 批量存稿状态更新: ✅ 在页面源代码中检测到'全部保存成功'
[2025-07-22 22:04:52,253] [INFO] 批量存稿状态更新: ✅ 检测到'全部保存成功'，存稿操作完成
[2025-07-22 22:04:52,253] [INFO] 批量存稿状态更新: 🚀 立即开始下一个任务
[2025-07-22 22:04:52,255] [INFO] 批量存稿状态更新: ✅ 账号 测试 第2次存稿成功
[2025-07-22 22:04:54,263] [INFO] 批量存稿状态更新: 账号 测试 开始第 3/5 次存稿
[2025-07-22 22:04:54,273] [INFO] 批量存稿状态更新: 直接导航到上传界面
[2025-07-22 22:04:55,175] [INFO] 批量存稿状态更新: 账号 测试 选择视频文件: 杨瀚森未出战成赢家开拓者遭 22-0 他带毒瘤拔高 杨瀚森 .mp4
[2025-07-22 22:04:55,176] [INFO] 批量存稿状态更新: 🔍 检测目标元素是否存在...
[2025-07-22 22:04:56,472] [INFO] 批量存稿状态更新: ✅ 检测到目标元素，立即开始视频上传...
[2025-07-22 22:04:56,474] [INFO] 批量存稿状态更新: 🔍 查找文件输入元素(方法1: CSS选择器)...
[2025-07-22 22:04:56,486] [INFO] 批量存稿状态更新: ✅ 方法1成功找到文件输入元素
[2025-07-22 22:04:56,488] [INFO] 批量存稿状态更新: ✅ 准备发送文件路径...
[2025-07-22 22:04:56,488] [INFO] 批量存稿状态更新: 📂 文件绝对路径: D:\头条全自动\视频搬运\已处理视频\杨瀚森未出战成赢家开拓者遭 22-0 他带毒瘤拔高 杨瀚森 .mp4
[2025-07-22 22:04:57,548] [INFO] 批量存稿状态更新: ✅ 已通过传统方式发送文件路径
[2025-07-22 22:04:57,651] [INFO] 批量存稿状态更新: ✅ 已滚动到页面底部
[2025-07-22 22:04:57,655] [INFO] 批量存稿状态更新: ⏳ 等待视频开始上传...
[2025-07-22 22:04:59,152] [INFO] 批量存稿状态更新: 🔍 准备处理封面，立即查找封面元素...
[2025-07-22 22:05:00,183] [INFO] 批量存稿状态更新: ✅ 找到封面元素，使用JavaScript点击...
[2025-07-22 22:05:00,235] [INFO] 批量存稿状态更新: ✅ 普通点击成功
[2025-07-22 22:05:00,237] [INFO] 批量存稿状态更新: ✅ 已点击封面元素
[2025-07-22 22:05:00,237] [INFO] 批量存稿状态更新: 🔍 账号 测试: 等待封面上传界面出现...
[2025-07-22 22:05:00,238] [INFO] 批量存稿状态更新: ⏳ 账号 测试: 等待封面上传对话框显示...
[2025-07-22 22:05:00,248] [INFO] 批量存稿状态更新: ✅ 账号 测试: 封面上传对话框已显示
[2025-07-22 22:05:00,249] [INFO] 批量存稿状态更新: 🔍 账号 测试: 查找本地封面选项...
[2025-07-22 22:05:00,267] [INFO] 批量存稿状态更新: ✅ 找到本地封面选项，立即点击...
[2025-07-22 22:05:00,372] [INFO] 批量存稿状态更新: ✅ 已点击本地封面选项
[2025-07-22 22:05:00,374] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备上传封面...
[2025-07-22 22:05:00,376] [INFO] 批量存稿状态更新: ✅ 账号 测试: 找到封面文件: 杨瀚森未出战成赢家开拓者遭 22-0 他带毒瘤拔高 杨瀚森 .jpg
[2025-07-22 22:05:00,377] [INFO] 批量存稿状态更新: 🔍 等待文件输入元素出现...
[2025-07-22 22:05:00,377] [INFO] 批量存稿状态更新: 🔍 在封面上传对话框中查找文件输入元素...
[2025-07-22 22:05:00,409] [INFO] 批量存稿状态更新: ✅ 方法2成功：通过XPath在对话框中找到 1 个文件输入元素
[2025-07-22 22:05:00,412] [INFO] 批量存稿状态更新: ✅ 找到封面上传对话框中的文件输入元素
[2025-07-22 22:05:00,414] [INFO] 批量存稿状态更新: ✅ 发送封面文件路径...
[2025-07-22 22:05:00,446] [INFO] 批量存稿状态更新: ✅ 已发送封面文件路径
[2025-07-22 22:05:00,448] [INFO] 批量存稿状态更新: ✅ 封面上传成功
[2025-07-22 22:05:00,451] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备点击封面确认按钮...
[2025-07-22 22:05:01,449] [INFO] 批量存稿状态更新: 🔍 账号 测试: 尝试点击第一个封面确认按钮(方法1)...
[2025-07-22 22:05:01,667] [INFO] 批量存稿状态更新: 第一个按钮结果(方法1): 第一个按钮点击成功(方法1)
[2025-07-22 22:05:01,670] [INFO] 批量存稿状态更新: 🔍 尝试点击第一个封面确认按钮(方法2)...
[2025-07-22 22:05:01,699] [INFO] 批量存稿状态更新: 第一个按钮结果(方法2): 第一个按钮未找到(方法2)
[2025-07-22 22:05:01,701] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法1)...
[2025-07-22 22:05:01,738] [INFO] 批量存稿状态更新: 第二个按钮结果(方法1): 第二个按钮点击成功(方法1)
[2025-07-22 22:05:01,741] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法2)...
[2025-07-22 22:05:01,750] [INFO] 批量存稿状态更新: 第二个按钮结果(方法2): 第二个按钮未找到(方法2)
[2025-07-22 22:05:01,753] [INFO] 批量存稿状态更新: ✅ 所有封面确认按钮点击成功
[2025-07-22 22:05:01,754] [INFO] 批量存稿状态更新: 🔍 等待弹窗消失...
[2025-07-22 22:05:03,401] [INFO] 批量存稿状态更新: ✅ 第一个弹窗已消失
[2025-07-22 22:05:03,413] [INFO] 批量存稿状态更新: ✅ 第二个弹窗已消失
[2025-07-22 22:05:03,415] [INFO] 批量存稿状态更新: 🔍 检测封面上传成功元素...
[2025-07-22 22:05:03,948] [INFO] 批量存稿状态更新: ✅ 已检测到封面上传成功元素
[2025-07-22 22:05:03,950] [INFO] 批量存稿状态更新: ✅ 确认检测到封面上传成功元素!
[2025-07-22 22:05:03,952] [INFO] 批量存稿状态更新: 🔍 立即检测上传成功文本...
[2025-07-22 22:05:03,965] [INFO] 批量存稿状态更新: ✅ 已检测到上传成功提示 (XPath方式)
[2025-07-22 22:05:03,968] [INFO] 批量存稿状态更新: ✅ 确认检测到上传成功提示!
[2025-07-22 22:05:03,971] [INFO] 批量存稿状态更新: ✅ 封面提交完成
[2025-07-22 22:05:03,973] [INFO] 批量存稿状态更新: ✅ 封面处理完成，开始监测视频上传状态...
[2025-07-22 22:05:03,975] [INFO] 批量存稿状态更新: 🔍 账号 测试 开始并行监测视频上传状态...
[2025-07-22 22:05:04,589] [DEBUG] 视频上传失败检测收到停止信号，退出
[2025-07-22 22:05:04,590] [INFO] 批量存稿状态更新: ✅ 账号 测试 视频上传成功检测完成
[2025-07-22 22:05:04,592] [INFO] 批量存稿状态更新: ✅ 视频上传成功确认
[2025-07-22 22:05:06,597] [INFO] 批量存稿状态更新: 🎯 存稿模式: 尝试点击存稿按钮...
[2025-07-22 22:05:06,608] [INFO] 批量存稿状态更新: 📝 存稿按钮点击结果: 存稿按钮点击成功
[2025-07-22 22:05:06,610] [INFO] 批量存稿状态更新: ✅ 已点击存稿按钮，开始检测保存状态...
[2025-07-22 22:05:06,611] [INFO] 批量存稿状态更新: 🔍 开始检测'全部保存成功'文字...
[2025-07-22 22:05:08,520] [DEBUG] 账号 测试 第 3/5 次存稿完成
[2025-07-22 22:05:08,520] [INFO] 批量存稿状态更新: ✅ 在页面源代码中检测到'全部保存成功'
[2025-07-22 22:05:08,521] [INFO] 批量存稿状态更新: ✅ 检测到'全部保存成功'，存稿操作完成
[2025-07-22 22:05:08,521] [INFO] 批量存稿状态更新: 🚀 立即开始下一个任务
[2025-07-22 22:05:08,522] [INFO] 批量存稿状态更新: ✅ 账号 测试 第3次存稿成功
[2025-07-22 22:05:10,536] [INFO] 批量存稿状态更新: 账号 测试 开始第 4/5 次存稿
[2025-07-22 22:05:10,545] [INFO] 批量存稿状态更新: 直接导航到上传界面
[2025-07-22 22:05:11,525] [INFO] 批量存稿状态更新: 账号 测试 选择视频文件: 分鹅肉阿疆哥一马当先青春二拍俄乌战争局势1119集.mp4
[2025-07-22 22:05:11,526] [INFO] 批量存稿状态更新: 🔍 检测目标元素是否存在...
[2025-07-22 22:05:13,248] [INFO] 批量存稿状态更新: ✅ 检测到目标元素，立即开始视频上传...
[2025-07-22 22:05:13,249] [INFO] 批量存稿状态更新: 🔍 查找文件输入元素(方法1: CSS选择器)...
[2025-07-22 22:05:13,259] [INFO] 批量存稿状态更新: ✅ 方法1成功找到文件输入元素
[2025-07-22 22:05:13,261] [INFO] 批量存稿状态更新: ✅ 准备发送文件路径...
[2025-07-22 22:05:13,262] [INFO] 批量存稿状态更新: 📂 文件绝对路径: D:\头条全自动\视频搬运\已处理视频\分鹅肉阿疆哥一马当先青春二拍俄乌战争局势1119集.mp4
[2025-07-22 22:05:14,306] [INFO] 批量存稿状态更新: ✅ 已通过传统方式发送文件路径
[2025-07-22 22:05:14,316] [INFO] 批量存稿状态更新: ✅ 已滚动到页面底部
[2025-07-22 22:05:14,317] [INFO] 批量存稿状态更新: ⏳ 等待视频开始上传...
[2025-07-22 22:05:15,817] [INFO] 批量存稿状态更新: 🔍 准备处理封面，立即查找封面元素...
[2025-07-22 22:05:16,864] [INFO] 批量存稿状态更新: ✅ 找到封面元素，使用JavaScript点击...
[2025-07-22 22:05:16,920] [INFO] 批量存稿状态更新: ✅ 普通点击成功
[2025-07-22 22:05:16,921] [INFO] 批量存稿状态更新: ✅ 已点击封面元素
[2025-07-22 22:05:16,922] [INFO] 批量存稿状态更新: 🔍 账号 测试: 等待封面上传界面出现...
[2025-07-22 22:05:16,923] [INFO] 批量存稿状态更新: ⏳ 账号 测试: 等待封面上传对话框显示...
[2025-07-22 22:05:16,931] [INFO] 批量存稿状态更新: ✅ 账号 测试: 封面上传对话框已显示
[2025-07-22 22:05:16,932] [INFO] 批量存稿状态更新: 🔍 账号 测试: 查找本地封面选项...
[2025-07-22 22:05:16,949] [INFO] 批量存稿状态更新: ✅ 找到本地封面选项，立即点击...
[2025-07-22 22:05:17,020] [INFO] 批量存稿状态更新: ✅ 已点击本地封面选项
[2025-07-22 22:05:17,022] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备上传封面...
[2025-07-22 22:05:17,025] [INFO] 批量存稿状态更新: ✅ 账号 测试: 找到封面文件: 分鹅肉阿疆哥一马当先青春二拍俄乌战争局势1119集.jpg
[2025-07-22 22:05:17,026] [INFO] 批量存稿状态更新: 🔍 等待文件输入元素出现...
[2025-07-22 22:05:17,027] [INFO] 批量存稿状态更新: 🔍 在封面上传对话框中查找文件输入元素...
[2025-07-22 22:05:17,059] [INFO] 批量存稿状态更新: ✅ 方法2成功：通过XPath在对话框中找到 1 个文件输入元素
[2025-07-22 22:05:17,061] [INFO] 批量存稿状态更新: ✅ 找到封面上传对话框中的文件输入元素
[2025-07-22 22:05:17,062] [INFO] 批量存稿状态更新: ✅ 发送封面文件路径...
[2025-07-22 22:05:17,081] [INFO] 批量存稿状态更新: ✅ 已发送封面文件路径
[2025-07-22 22:05:17,083] [INFO] 批量存稿状态更新: ✅ 封面上传成功
[2025-07-22 22:05:17,085] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备点击封面确认按钮...
[2025-07-22 22:05:18,097] [INFO] 批量存稿状态更新: 🔍 账号 测试: 尝试点击第一个封面确认按钮(方法1)...
[2025-07-22 22:05:18,255] [INFO] 批量存稿状态更新: 第一个按钮结果(方法1): 第一个按钮点击成功(方法1)
[2025-07-22 22:05:18,259] [INFO] 批量存稿状态更新: 🔍 尝试点击第一个封面确认按钮(方法2)...
[2025-07-22 22:05:18,293] [INFO] 批量存稿状态更新: 第一个按钮结果(方法2): 第一个按钮未找到(方法2)
[2025-07-22 22:05:18,295] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法1)...
[2025-07-22 22:05:18,326] [INFO] 批量存稿状态更新: 第二个按钮结果(方法1): 第二个按钮点击成功(方法1)
[2025-07-22 22:05:18,329] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法2)...
[2025-07-22 22:05:18,369] [INFO] 批量存稿状态更新: 第二个按钮结果(方法2): 第二个按钮未找到(方法2)
[2025-07-22 22:05:18,372] [INFO] 批量存稿状态更新: ✅ 所有封面确认按钮点击成功
[2025-07-22 22:05:18,374] [INFO] 批量存稿状态更新: 🔍 等待弹窗消失...
[2025-07-22 22:05:19,701] [INFO] 批量存稿状态更新: ✅ 第一个弹窗已消失
[2025-07-22 22:05:19,710] [INFO] 批量存稿状态更新: ✅ 第二个弹窗已消失
[2025-07-22 22:05:19,713] [INFO] 批量存稿状态更新: 🔍 检测封面上传成功元素...
[2025-07-22 22:05:20,241] [INFO] 批量存稿状态更新: ✅ 已检测到封面上传成功元素
[2025-07-22 22:05:20,243] [INFO] 批量存稿状态更新: ✅ 确认检测到封面上传成功元素!
[2025-07-22 22:05:20,244] [INFO] 批量存稿状态更新: 🔍 立即检测上传成功文本...
[2025-07-22 22:05:20,252] [INFO] 批量存稿状态更新: ✅ 已检测到上传成功提示 (XPath方式)
[2025-07-22 22:05:20,253] [INFO] 批量存稿状态更新: ✅ 确认检测到上传成功提示!
[2025-07-22 22:05:20,254] [INFO] 批量存稿状态更新: ✅ 封面提交完成
[2025-07-22 22:05:20,256] [INFO] 批量存稿状态更新: ✅ 封面处理完成，开始监测视频上传状态...
[2025-07-22 22:05:20,259] [INFO] 批量存稿状态更新: 🔍 账号 测试 开始并行监测视频上传状态...
[2025-07-22 22:05:20,847] [DEBUG] 视频上传失败检测收到停止信号，退出
[2025-07-22 22:05:20,848] [INFO] 批量存稿状态更新: ✅ 账号 测试 视频上传成功检测完成
[2025-07-22 22:05:20,850] [INFO] 批量存稿状态更新: ✅ 视频上传成功确认
[2025-07-22 22:05:22,850] [INFO] 批量存稿状态更新: 🎯 存稿模式: 尝试点击存稿按钮...
[2025-07-22 22:05:22,861] [INFO] 批量存稿状态更新: 📝 存稿按钮点击结果: 存稿按钮点击成功
[2025-07-22 22:05:22,862] [INFO] 批量存稿状态更新: ✅ 已点击存稿按钮，开始检测保存状态...
[2025-07-22 22:05:22,863] [INFO] 批量存稿状态更新: 🔍 开始检测'全部保存成功'文字...
[2025-07-22 22:05:24,053] [DEBUG] 账号 测试 第 4/5 次存稿完成
[2025-07-22 22:05:24,053] [INFO] 批量存稿状态更新: ✅ 通过XPath检测到'全部保存成功'元素
[2025-07-22 22:05:24,054] [INFO] 批量存稿状态更新: ✅ 检测到'全部保存成功'，存稿操作完成
[2025-07-22 22:05:24,055] [INFO] 批量存稿状态更新: 🚀 立即开始下一个任务
[2025-07-22 22:05:24,055] [INFO] 批量存稿状态更新: ✅ 账号 测试 第4次存稿成功
[2025-07-22 22:05:26,061] [INFO] 批量存稿状态更新: 账号 测试 开始第 5/5 次存稿
[2025-07-22 22:05:26,067] [INFO] 批量存稿状态更新: 直接导航到上传界面
[2025-07-22 22:05:27,032] [INFO] 批量存稿状态更新: 账号 测试 选择视频文件: 中印边境重兵云集印出动大军打到雅鲁藏布江.mp4
[2025-07-22 22:05:27,034] [INFO] 批量存稿状态更新: 🔍 检测目标元素是否存在...
[2025-07-22 22:05:28,286] [INFO] 批量存稿状态更新: ✅ 检测到目标元素，立即开始视频上传...
[2025-07-22 22:05:28,288] [INFO] 批量存稿状态更新: 🔍 查找文件输入元素(方法1: CSS选择器)...
[2025-07-22 22:05:28,295] [INFO] 批量存稿状态更新: ✅ 方法1成功找到文件输入元素
[2025-07-22 22:05:28,297] [INFO] 批量存稿状态更新: ✅ 准备发送文件路径...
[2025-07-22 22:05:28,297] [INFO] 批量存稿状态更新: 📂 文件绝对路径: D:\头条全自动\视频搬运\已处理视频\中印边境重兵云集印出动大军打到雅鲁藏布江.mp4
[2025-07-22 22:05:29,343] [INFO] 批量存稿状态更新: ✅ 已通过传统方式发送文件路径
[2025-07-22 22:05:29,349] [INFO] 批量存稿状态更新: ✅ 已滚动到页面底部
[2025-07-22 22:05:29,350] [INFO] 批量存稿状态更新: ⏳ 等待视频开始上传...
[2025-07-22 22:05:30,855] [INFO] 批量存稿状态更新: 🔍 准备处理封面，立即查找封面元素...
[2025-07-22 22:05:31,885] [INFO] 批量存稿状态更新: ✅ 找到封面元素，使用JavaScript点击...
[2025-07-22 22:05:31,935] [INFO] 批量存稿状态更新: ✅ 普通点击成功
[2025-07-22 22:05:31,936] [INFO] 批量存稿状态更新: ✅ 已点击封面元素
[2025-07-22 22:05:31,937] [INFO] 批量存稿状态更新: 🔍 账号 测试: 等待封面上传界面出现...
[2025-07-22 22:05:31,938] [INFO] 批量存稿状态更新: ⏳ 账号 测试: 等待封面上传对话框显示...
[2025-07-22 22:05:31,947] [INFO] 批量存稿状态更新: ✅ 账号 测试: 封面上传对话框已显示
[2025-07-22 22:05:31,948] [INFO] 批量存稿状态更新: 🔍 账号 测试: 查找本地封面选项...
[2025-07-22 22:05:31,966] [INFO] 批量存稿状态更新: ✅ 找到本地封面选项，立即点击...
[2025-07-22 22:05:32,066] [INFO] 批量存稿状态更新: ✅ 已点击本地封面选项
[2025-07-22 22:05:32,067] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备上传封面...
[2025-07-22 22:05:32,069] [INFO] 批量存稿状态更新: ✅ 账号 测试: 找到封面文件: 中印边境重兵云集印出动大军打到雅鲁藏布江.jpg
[2025-07-22 22:05:32,070] [INFO] 批量存稿状态更新: 🔍 等待文件输入元素出现...
[2025-07-22 22:05:32,070] [INFO] 批量存稿状态更新: 🔍 在封面上传对话框中查找文件输入元素...
[2025-07-22 22:05:32,102] [INFO] 批量存稿状态更新: ✅ 方法2成功：通过XPath在对话框中找到 1 个文件输入元素
[2025-07-22 22:05:32,103] [INFO] 批量存稿状态更新: ✅ 找到封面上传对话框中的文件输入元素
[2025-07-22 22:05:32,104] [INFO] 批量存稿状态更新: ✅ 发送封面文件路径...
[2025-07-22 22:05:32,129] [INFO] 批量存稿状态更新: ✅ 已发送封面文件路径
[2025-07-22 22:05:32,130] [INFO] 批量存稿状态更新: ✅ 封面上传成功
[2025-07-22 22:05:32,133] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备点击封面确认按钮...
[2025-07-22 22:05:33,144] [INFO] 批量存稿状态更新: 🔍 账号 测试: 尝试点击第一个封面确认按钮(方法1)...
[2025-07-22 22:05:33,310] [INFO] 批量存稿状态更新: 第一个按钮结果(方法1): 第一个按钮点击成功(方法1)
[2025-07-22 22:05:33,312] [INFO] 批量存稿状态更新: 🔍 尝试点击第一个封面确认按钮(方法2)...
[2025-07-22 22:05:33,318] [INFO] 批量存稿状态更新: 第一个按钮结果(方法2): 第一个按钮未找到(方法2)
[2025-07-22 22:05:33,320] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法1)...
[2025-07-22 22:05:33,338] [INFO] 批量存稿状态更新: 第二个按钮结果(方法1): 第二个按钮点击成功(方法1)
[2025-07-22 22:05:33,341] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法2)...
[2025-07-22 22:05:33,352] [INFO] 批量存稿状态更新: 第二个按钮结果(方法2): 第二个按钮未找到(方法2)
[2025-07-22 22:05:33,355] [INFO] 批量存稿状态更新: ✅ 所有封面确认按钮点击成功
[2025-07-22 22:05:33,356] [INFO] 批量存稿状态更新: 🔍 等待弹窗消失...
[2025-07-22 22:05:35,485] [INFO] 批量存稿状态更新: ✅ 第一个弹窗已消失
[2025-07-22 22:05:35,500] [INFO] 批量存稿状态更新: ✅ 第二个弹窗已消失
[2025-07-22 22:05:35,502] [INFO] 批量存稿状态更新: 🔍 检测封面上传成功元素...
[2025-07-22 22:05:36,038] [INFO] 批量存稿状态更新: ✅ 已检测到封面上传成功元素
[2025-07-22 22:05:36,040] [INFO] 批量存稿状态更新: ✅ 确认检测到封面上传成功元素!
[2025-07-22 22:05:36,041] [INFO] 批量存稿状态更新: 🔍 立即检测上传成功文本...
[2025-07-22 22:05:37,751] [INFO] 批量存稿状态更新: ✅ 已检测到上传成功提示 (XPath方式)
[2025-07-22 22:05:37,752] [INFO] 批量存稿状态更新: ✅ 确认检测到上传成功提示!
[2025-07-22 22:05:37,753] [INFO] 批量存稿状态更新: ✅ 封面提交完成
[2025-07-22 22:05:37,754] [INFO] 批量存稿状态更新: ✅ 封面处理完成，开始监测视频上传状态...
[2025-07-22 22:05:37,755] [INFO] 批量存稿状态更新: 🔍 账号 测试 开始并行监测视频上传状态...
[2025-07-22 22:05:38,374] [DEBUG] 视频上传失败检测收到停止信号，退出
[2025-07-22 22:05:38,375] [INFO] 批量存稿状态更新: ✅ 账号 测试 视频上传成功检测完成
[2025-07-22 22:05:38,377] [INFO] 批量存稿状态更新: ✅ 视频上传成功确认
[2025-07-22 22:05:40,382] [INFO] 批量存稿状态更新: 🎯 存稿模式: 尝试点击存稿按钮...
[2025-07-22 22:05:40,392] [INFO] 批量存稿状态更新: 📝 存稿按钮点击结果: 存稿按钮点击成功
[2025-07-22 22:05:40,393] [INFO] 批量存稿状态更新: ✅ 已点击存稿按钮，开始检测保存状态...
[2025-07-22 22:05:40,394] [INFO] 批量存稿状态更新: 🔍 开始检测'全部保存成功'文字...
[2025-07-22 22:05:41,551] [INFO] 账号 测试: 存稿完成 (5/5)
[2025-07-22 22:05:41,552] [INFO] 批量存稿状态更新: ✅ 在页面源代码中检测到'全部保存成功'
[2025-07-22 22:05:41,553] [INFO] 批量存稿状态更新: ✅ 检测到'全部保存成功'，存稿操作完成
[2025-07-22 22:05:41,553] [INFO] 批量存稿状态更新: 🚀 立即开始下一个任务
[2025-07-22 22:05:41,554] [INFO] 批量存稿状态更新: ✅ 账号 测试 第5次存稿成功
[2025-07-22 22:05:41,563] [INFO] 批量存稿状态更新: 📊 进度: 1/1 (100%)
[2025-07-22 22:05:41,564] [INFO] 批量存稿状态更新: ✅ 工作窃取线程 #0 账号 测试 处理成功
[2025-07-22 22:05:42,577] [DEBUG] 工作窃取线程 #0 队列为空或收到停止信号，准备退出
[2025-07-22 22:05:42,577] [DEBUG] 工作窃取线程 #0 完成，共处理 1 个账号
[2025-07-22 22:05:44,811] [DEBUG] 工作窃取线程 #0 浏览器已关闭
[2025-07-22 22:05:44,813] [INFO] ✅ 线程 #0 已完成
[2025-07-22 22:05:44,813] [INFO] 批量存稿状态更新: ✅ 线程 #0 已完成
[2025-07-22 22:05:44,814] [INFO] 所有线程已完成，准备发送完成信号
[2025-07-22 22:05:44,814] [INFO] 已设置停止标志，所有工作窃取线程将停止
[2025-07-22 22:05:44,816] [INFO] 批量存稿任务总运行时间: 00:01:56
[2025-07-22 22:05:44,817] [INFO] 批量存稿任务统计 - 总计: 1, 成功: 1, 登录失败: 0, 存稿失败: 0, 重试轮次: 0, 重试成功: 0, 视频重试: {'retry_accounts': 0, 'total_retries': 0, 'retry_success': 0, 'final_failed': 0}
[2025-07-22 22:05:44,818] [INFO] 批量存稿状态更新: ⏱️ 总运行时间: 00:01:56
[2025-07-22 22:05:44,820] [INFO] 批量存稿状态更新: 📊 任务统计: 总账号 1 个
[2025-07-22 22:05:44,821] [INFO] 批量存稿状态更新: 📊 存稿成功: 1 个账号
[2025-07-22 22:05:45,126] [DEBUG] 已清理临时目录: C:\Users\<USER>\AppData\Local\Temp\chrome_user_data_0
[2025-07-22 22:05:45,134] [DEBUG] 共清理了 1 个临时Chrome用户数据目录
[2025-07-22 22:05:45,134] [DEBUG] 已断开信号连接: status_signal
[2025-07-22 22:05:45,135] [DEBUG] 已断开信号连接: progress_signal
[2025-07-22 22:05:45,135] [DEBUG] 已断开信号连接: finished_signal
[2025-07-22 22:05:45,186] [INFO] 批量存稿任务正常完成
[2025-07-22 22:06:10,956] [DEBUG] 准备登录账号: 测试, Cookie文件: E:/软件共享/测试账号/新建文件夹 (2)\测试.txt
[2025-07-22 22:06:10,957] [INFO] 使用登录设置: 指纹浏览器=False, 手机端模式=False, 禁用JS=False, 清除Cookie=True
[2025-07-22 22:06:10,959] [INFO] 🚀 开始Cookie登录流程（重构版本）
[2025-07-22 22:06:10,960] [INFO] 🔧 配置Chrome浏览器选项...
[2025-07-22 22:06:10,960] [INFO] 使用与批量存稿相同的标准浏览器配置
[2025-07-22 22:06:10,960] [INFO] 使用独立用户数据目录: C:\Users\<USER>\AppData\Local\Temp\chrome_user_data_single_1753193170
[2025-07-22 22:06:10,960] [INFO] ✅ Chrome浏览器选项配置完成
[2025-07-22 22:06:15,185] [INFO] 🔑 开始登录账号 测试
[2025-07-22 22:06:15,186] [DEBUG] JSON格式解析成功，提取到 5 个关键Cookie
[2025-07-22 22:06:15,187] [INFO] 📋 解析到 5 个关键Cookie
[2025-07-22 22:06:15,187] [INFO] 🌐 导航到头条媒体平台
[2025-07-22 22:06:15,655] [INFO] 🔑 添加 5 个关键Cookie
[2025-07-22 22:06:17,205] [INFO] ✅ 成功添加 5 个Cookie
[2025-07-22 22:06:17,207] [INFO] 🔄 刷新页面应用Cookie
[2025-07-22 22:06:20,729] [INFO] 🔍 验证登录状态
[2025-07-22 22:06:20,737] [DEBUG] URL验证通过 (0.01s)
[2025-07-22 22:06:20,738] [INFO] [SUCCESS] ✅ 账号 测试 登录成功
[2025-07-22 22:06:20,738] [INFO] 🔍 开始检测 账号 测试 的实名认证状态...
[2025-07-22 22:06:20,782] [INFO] 📋 文本检测结果: 页面文本检测通过
[2025-07-22 22:06:21,008] [INFO] 🔍 元素检测结果: 元素检测通过
[2025-07-22 22:06:21,124] [INFO] 🌐 URL检测结果: URL检测通过
[2025-07-22 22:06:21,125] [INFO] ✅ 账号 测试 已实名认证
[2025-07-22 22:06:21,125] [INFO] ✅ 账号 测试 实名认证状态: 已实名
[2025-07-22 22:06:21,126] [INFO] 账号 测试 的浏览器实例已添加到管理器，当前管理 1 个浏览器
[2025-07-22 22:06:21,126] [INFO] [SUCCESS] ✅ 账号 测试 登录成功，已停留在头条媒体平台首页
[2025-07-22 22:06:21,135] [INFO] [SUCCESS] 账号 测试 登录成功
[2025-07-22 22:06:21,137] [INFO] 账号 测试 登录成功: 账号 测试 登录成功，已停留在头条媒体平台首页
[2025-07-22 22:06:55,895] [INFO] 开始强制清理批量存稿对象...
[2025-07-22 22:06:55,897] [INFO] 已强制清理batch_worker
[2025-07-22 22:06:55,901] [INFO] 已强制清理batch_thread
[2025-07-22 22:06:55,901] [INFO] 已重置批量存稿完成处理标志
[2025-07-22 22:06:55,904] [INFO] 已清理活跃存稿任务标记
[2025-07-22 22:06:55,905] [INFO] 已清理主窗口中的_batch_draft_worker引用
[2025-07-22 22:06:55,905] [INFO] 已清理主窗口中的_batch_draft_thread引用
[2025-07-22 22:06:55,905] [INFO] 批量存稿状态标志重置完成
[2025-07-22 22:06:58,639] [INFO] 强制清理批量存稿对象完成
[2025-07-22 22:06:58,640] [DEBUG] 未检测到正在运行的批量存稿任务
[2025-07-22 22:06:58,640] [INFO] 进行预防性清理，确保没有残留对象...
[2025-07-22 22:06:58,641] [INFO] 开始强制清理批量存稿对象...
[2025-07-22 22:06:58,641] [INFO] 已强制清理batch_worker
[2025-07-22 22:06:58,641] [INFO] 已强制清理batch_thread
[2025-07-22 22:06:58,642] [INFO] 已重置批量存稿完成处理标志
[2025-07-22 22:06:58,643] [INFO] 已清理活跃存稿任务标记
[2025-07-22 22:06:58,643] [INFO] 已清理主窗口中的_batch_draft_worker引用
[2025-07-22 22:06:58,644] [INFO] 已清理主窗口中的_batch_draft_thread引用
[2025-07-22 22:06:58,644] [INFO] 批量存稿状态标志重置完成
[2025-07-22 22:06:59,211] [INFO] 强制清理批量存稿对象完成
[2025-07-22 22:07:09,925] [INFO] 启动批量存稿任务 - 无头模式: True, 并发数: 1, 存稿次数: 5, 视频公平分配: True, 清理缓存: True, 去除活动弹窗: False, 处理发布限制弹窗: False
[2025-07-22 22:07:09,926] [DEBUG] 未检测到正在运行的批量存稿任务
[2025-07-22 22:07:09,926] [INFO] 进行预防性清理，确保没有残留对象...
[2025-07-22 22:07:09,927] [INFO] 开始清理存稿任务对象...
[2025-07-22 22:07:09,927] [INFO] 已清理主窗口中的批量存稿任务引用
[2025-07-22 22:07:10,105] [INFO] 存稿任务对象清理完成
[2025-07-22 22:07:10,107] [INFO] 已从设置中获取视频目录: D:/头条全自动/视频搬运/已处理视频
[2025-07-22 22:07:10,107] [INFO] 已从设置中获取封面目录: D:/头条全自动/视频搬运/已处理封面
[2025-07-22 22:07:10,109] [INFO] 找到 474 个视频文件: 一口气看完为什么中国战机突然领先美国全集.mp4, 一句恒河以北归中国印度人彻底破防 2.mp4, 一句恒河以北归中国又一次让印度三哥彻底破防了.mp4...
[2025-07-22 22:07:10,118] [INFO] 找到 474 个封面文件: 一口气看完为什么中国战机突然领先美国全集.jpg, 一句恒河以北归中国印度人彻底破防 2.jpg, 一句恒河以北归中国又一次让印度三哥彻底破防了.jpg...
[2025-07-22 22:07:15,779] [INFO] [SUCCESS] 批量存稿设置已确认，开始执行任务
[2025-07-22 22:07:16,499] [INFO] 开始强制清理批量存稿对象...
[2025-07-22 22:07:16,500] [INFO] 已强制清理batch_worker
[2025-07-22 22:07:16,500] [INFO] 已强制清理batch_thread
[2025-07-22 22:07:16,501] [INFO] 已重置批量存稿完成处理标志
[2025-07-22 22:07:16,501] [INFO] 已清理活跃存稿任务标记
[2025-07-22 22:07:16,502] [INFO] 已清理主窗口中的_batch_draft_worker引用
[2025-07-22 22:07:16,502] [INFO] 已清理主窗口中的_batch_draft_thread引用
[2025-07-22 22:07:16,502] [INFO] 批量存稿状态标志重置完成
[2025-07-22 22:07:17,079] [INFO] 强制清理批量存稿对象完成
[2025-07-22 22:07:17,079] [DEBUG] 未检测到正在运行的批量存稿任务
[2025-07-22 22:07:17,079] [INFO] 进行预防性清理，确保没有残留对象...
[2025-07-22 22:07:17,080] [INFO] 开始强制清理批量存稿对象...
[2025-07-22 22:07:17,080] [INFO] 已强制清理batch_worker
[2025-07-22 22:07:17,080] [INFO] 已强制清理batch_thread
[2025-07-22 22:07:17,081] [INFO] 已重置批量存稿完成处理标志
[2025-07-22 22:07:17,082] [INFO] 已清理活跃存稿任务标记
[2025-07-22 22:07:17,082] [INFO] 已清理主窗口中的_batch_draft_worker引用
[2025-07-22 22:07:17,082] [INFO] 已清理主窗口中的_batch_draft_thread引用
[2025-07-22 22:07:17,083] [INFO] 批量存稿状态标志重置完成
[2025-07-22 22:07:17,647] [INFO] 强制清理批量存稿对象完成
[2025-07-22 22:07:32,709] [INFO] 启动批量存稿任务 - 无头模式: True, 并发数: 1, 存稿次数: 3, 视频公平分配: True, 清理缓存: True, 去除活动弹窗: False, 处理发布限制弹窗: False
[2025-07-22 22:07:32,710] [DEBUG] 未检测到正在运行的批量存稿任务
[2025-07-22 22:07:32,710] [INFO] 进行预防性清理，确保没有残留对象...
[2025-07-22 22:07:32,711] [INFO] 开始清理存稿任务对象...
[2025-07-22 22:07:32,711] [INFO] 已清理主窗口中的批量存稿任务引用
[2025-07-22 22:07:32,886] [INFO] 存稿任务对象清理完成
[2025-07-22 22:07:32,887] [INFO] 已从设置中获取视频目录: D:/头条全自动/视频搬运/已处理视频
[2025-07-22 22:07:32,888] [INFO] 已从设置中获取封面目录: D:/头条全自动/视频搬运/已处理封面
[2025-07-22 22:07:32,889] [INFO] 找到 474 个视频文件: 一口气看完为什么中国战机突然领先美国全集.mp4, 一句恒河以北归中国印度人彻底破防 2.mp4, 一句恒河以北归中国又一次让印度三哥彻底破防了.mp4...
[2025-07-22 22:07:32,890] [INFO] 找到 474 个封面文件: 一口气看完为什么中国战机突然领先美国全集.jpg, 一句恒河以北归中国印度人彻底破防 2.jpg, 一句恒河以北归中国又一次让印度三哥彻底破防了.jpg...
[2025-07-22 22:07:33,628] [INFO] 创建批量存稿任务 - 无头模式: True, 并发数: 1, 存稿次数: 3
[2025-07-22 22:07:33,629] [INFO] 批量存稿线程已启动
[2025-07-22 22:07:33,629] [INFO] 开始执行批量存稿任务
[2025-07-22 22:07:33,630] [DEBUG] 重试管理器初始化完成 - L1即时重试: 登录2次/网络0次, L2操作重试: 视频上传1次/页面加载1次, L3批次重试: 启用, 最大1轮
[2025-07-22 22:07:33,630] [INFO] 重试管理器初始化完成
[2025-07-22 22:07:33,630] [INFO] 运行时间统计定时器已启动
[2025-07-22 22:07:33,631] [INFO] 获取到账号列表，共 1 个账号
[2025-07-22 22:07:33,631] [INFO] 账号 1: 测试, Cookie文件: E:/软件共享/测试账号/新建文件夹 (2)\测试.txt
[2025-07-22 22:07:33,635] [INFO] 批量存稿任务线程已启动
[2025-07-22 22:07:33,636] [INFO] 开始执行批量存稿，账号数量: 1
[2025-07-22 22:07:33,636] [INFO] 工作窃取模式: 1 个线程处理 1 个账号
[2025-07-22 22:07:33,640] [INFO] ✅ 工作窃取线程 #0 已启动
[2025-07-22 22:07:33,640] [INFO] 所有工作窃取线程已启动，共 1 个线程
[2025-07-22 22:07:33,640] [INFO] 等待工作窃取线程 #0 完成...
[2025-07-22 22:07:33,654] [INFO] [SUCCESS] 批量存稿任务已启动
[2025-07-22 22:07:33,655] [INFO] [SUCCESS] 批量存稿设置已确认，开始执行任务
[2025-07-22 22:07:37,898] [INFO] 🔑 开始登录账号 测试
[2025-07-22 22:07:37,898] [INFO] 批量存稿状态更新: ✅ 工作窃取线程 #0 浏览器初始化成功，开始处理任务
[2025-07-22 22:07:37,899] [INFO] 批量存稿状态更新: 🔄 工作窃取线程 #0 开始处理账号 测试 (第1个)
[2025-07-22 22:07:37,902] [DEBUG] JSON格式解析成功，提取到 5 个关键Cookie
[2025-07-22 22:07:37,902] [INFO] 📋 解析到 5 个关键Cookie
[2025-07-22 22:07:37,902] [INFO] 🌐 导航到头条媒体平台
[2025-07-22 22:07:38,978] [INFO] 🔑 添加 5 个关键Cookie
[2025-07-22 22:07:39,020] [INFO] ✅ 成功添加 5 个Cookie
[2025-07-22 22:07:39,020] [INFO] 🔄 刷新页面应用Cookie
[2025-07-22 22:07:42,249] [INFO] 🔍 验证登录状态
[2025-07-22 22:07:42,254] [DEBUG] URL验证通过 (0.00s)
[2025-07-22 22:07:42,255] [INFO] [SUCCESS] ✅ 账号 测试 登录成功
[2025-07-22 22:07:42,255] [INFO] 🔍 开始检测 账号 测试 的实名认证状态...
[2025-07-22 22:07:42,256] [INFO] 批量存稿状态更新: ✅ 账号 测试 登录成功
[2025-07-22 22:07:42,284] [INFO] 📋 文本检测结果: 页面文本检测通过
[2025-07-22 22:07:42,411] [INFO] 🔍 元素检测结果: 元素检测通过
[2025-07-22 22:07:42,415] [INFO] 🌐 URL检测结果: URL检测通过
[2025-07-22 22:07:42,416] [INFO] ✅ 账号 测试 已实名认证
[2025-07-22 22:07:42,418] [INFO] ✅ 已更新账号 测试 的实名状态为: 已实名
[2025-07-22 22:07:42,419] [INFO] ✅ 账号 测试 已实名认证，继续任务
[2025-07-22 22:07:42,420] [INFO] 批量存稿状态更新: ✅ 账号 测试 登录成功
[2025-07-22 22:07:42,422] [INFO] 批量存稿状态更新: 账号 测试 开始执行存稿任务...
[2025-07-22 22:07:42,425] [INFO] 批量存稿状态更新: 账号 测试 开始第 1/3 次存稿
[2025-07-22 22:07:43,309] [INFO] 批量存稿状态更新: 🔄 标准模式：正在导航到视频上传页面...
[2025-07-22 22:07:46,394] [INFO] 批量存稿状态更新: 账号 测试 选择视频文件: 欧盟取消北京峰会报复中国强烈反制对等反制即日生效.mp4
[2025-07-22 22:07:46,395] [INFO] 批量存稿状态更新: 🔍 检测目标元素是否存在...
[2025-07-22 22:07:46,437] [INFO] 批量存稿状态更新: ✅ 检测到目标元素，立即开始视频上传...
[2025-07-22 22:07:46,438] [INFO] 批量存稿状态更新: 🔍 查找文件输入元素(方法1: CSS选择器)...
[2025-07-22 22:07:46,448] [INFO] 批量存稿状态更新: ✅ 方法1成功找到文件输入元素
[2025-07-22 22:07:46,449] [INFO] 批量存稿状态更新: ✅ 准备发送文件路径...
[2025-07-22 22:07:46,450] [INFO] 批量存稿状态更新: 📂 文件绝对路径: D:\头条全自动\视频搬运\已处理视频\欧盟取消北京峰会报复中国强烈反制对等反制即日生效.mp4
[2025-07-22 22:07:47,497] [INFO] 批量存稿状态更新: ✅ 已通过传统方式发送文件路径
[2025-07-22 22:07:47,504] [INFO] 批量存稿状态更新: ✅ 已滚动到页面底部
[2025-07-22 22:07:47,506] [INFO] 批量存稿状态更新: ⏳ 等待视频开始上传...
[2025-07-22 22:07:49,011] [INFO] 批量存稿状态更新: 🔍 准备处理封面，立即查找封面元素...
[2025-07-22 22:07:50,057] [INFO] 批量存稿状态更新: ✅ 找到封面元素，使用JavaScript点击...
[2025-07-22 22:07:50,127] [INFO] 批量存稿状态更新: ✅ 普通点击成功
[2025-07-22 22:07:50,128] [INFO] 批量存稿状态更新: ✅ 已点击封面元素
[2025-07-22 22:07:50,129] [INFO] 批量存稿状态更新: 🔍 账号 测试: 等待封面上传界面出现...
[2025-07-22 22:07:50,130] [INFO] 批量存稿状态更新: ⏳ 账号 测试: 等待封面上传对话框显示...
[2025-07-22 22:07:50,142] [INFO] 批量存稿状态更新: ✅ 账号 测试: 封面上传对话框已显示
[2025-07-22 22:07:50,143] [INFO] 批量存稿状态更新: 🔍 账号 测试: 查找本地封面选项...
[2025-07-22 22:07:50,162] [INFO] 批量存稿状态更新: ✅ 找到本地封面选项，立即点击...
[2025-07-22 22:07:50,244] [INFO] 批量存稿状态更新: ✅ 已点击本地封面选项
[2025-07-22 22:07:50,245] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备上传封面...
[2025-07-22 22:07:50,248] [INFO] 批量存稿状态更新: ✅ 账号 测试: 找到封面文件: 欧盟取消北京峰会报复中国强烈反制对等反制即日生效.jpg
[2025-07-22 22:07:50,248] [INFO] 批量存稿状态更新: 🔍 等待文件输入元素出现...
[2025-07-22 22:07:50,249] [INFO] 批量存稿状态更新: 🔍 在封面上传对话框中查找文件输入元素...
[2025-07-22 22:07:50,276] [INFO] 批量存稿状态更新: ✅ 方法2成功：通过XPath在对话框中找到 1 个文件输入元素
[2025-07-22 22:07:50,277] [INFO] 批量存稿状态更新: ✅ 找到封面上传对话框中的文件输入元素
[2025-07-22 22:07:50,279] [INFO] 批量存稿状态更新: ✅ 发送封面文件路径...
[2025-07-22 22:07:50,303] [INFO] 批量存稿状态更新: ✅ 已发送封面文件路径
[2025-07-22 22:07:50,304] [INFO] 批量存稿状态更新: ✅ 封面上传成功
[2025-07-22 22:07:50,306] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备点击封面确认按钮...
[2025-07-22 22:07:51,315] [INFO] 批量存稿状态更新: 🔍 账号 测试: 尝试点击第一个封面确认按钮(方法1)...
[2025-07-22 22:07:51,474] [INFO] 批量存稿状态更新: 第一个按钮结果(方法1): 第一个按钮点击成功(方法1)
[2025-07-22 22:07:51,476] [INFO] 批量存稿状态更新: 🔍 尝试点击第一个封面确认按钮(方法2)...
[2025-07-22 22:07:51,485] [INFO] 批量存稿状态更新: 第一个按钮结果(方法2): 第一个按钮未找到(方法2)
[2025-07-22 22:07:51,487] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法1)...
[2025-07-22 22:07:51,505] [INFO] 批量存稿状态更新: 第二个按钮结果(方法1): 第二个按钮点击成功(方法1)
[2025-07-22 22:07:51,507] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法2)...
[2025-07-22 22:07:51,512] [INFO] 批量存稿状态更新: 第二个按钮结果(方法2): 第二个按钮未找到(方法2)
[2025-07-22 22:07:51,515] [INFO] 批量存稿状态更新: ✅ 所有封面确认按钮点击成功
[2025-07-22 22:07:51,516] [INFO] 批量存稿状态更新: 🔍 等待弹窗消失...
[2025-07-22 22:07:55,457] [INFO] 批量存稿状态更新: ✅ 第一个弹窗已消失
[2025-07-22 22:07:55,469] [INFO] 批量存稿状态更新: ✅ 第二个弹窗已消失
[2025-07-22 22:07:55,470] [INFO] 批量存稿状态更新: 🔍 检测封面上传成功元素...
[2025-07-22 22:07:56,008] [INFO] 批量存稿状态更新: ✅ 已检测到封面上传成功元素
[2025-07-22 22:07:56,010] [INFO] 批量存稿状态更新: ✅ 确认检测到封面上传成功元素!
[2025-07-22 22:07:56,011] [INFO] 批量存稿状态更新: 🔍 立即检测上传成功文本...
[2025-07-22 22:07:56,020] [INFO] 批量存稿状态更新: ✅ 已检测到上传成功提示 (XPath方式)
[2025-07-22 22:07:56,022] [INFO] 批量存稿状态更新: ✅ 确认检测到上传成功提示!
[2025-07-22 22:07:56,022] [INFO] 批量存稿状态更新: ✅ 封面提交完成
[2025-07-22 22:07:56,024] [INFO] 批量存稿状态更新: ✅ 封面处理完成，开始监测视频上传状态...
[2025-07-22 22:07:56,026] [INFO] 批量存稿状态更新: 🔍 账号 测试 开始并行监测视频上传状态...
[2025-07-22 22:07:56,617] [DEBUG] 视频上传失败检测收到停止信号，退出
[2025-07-22 22:07:56,619] [INFO] 批量存稿状态更新: ✅ 账号 测试 视频上传成功检测完成
[2025-07-22 22:07:56,621] [INFO] 批量存稿状态更新: ✅ 视频上传成功确认
[2025-07-22 22:07:58,628] [INFO] 批量存稿状态更新: 🎯 封号模式: 尝试点击发布按钮...
[2025-07-22 22:07:58,639] [DEBUG] 账号 测试 第 1/3 次存稿完成
[2025-07-22 22:07:58,640] [INFO] 批量存稿状态更新: 📝 发布按钮点击结果: 发布按钮点击成功
[2025-07-22 22:07:58,641] [INFO] 批量存稿状态更新: ✅ 已点击发布按钮，封号模式操作完成
[2025-07-22 22:07:58,642] [INFO] 批量存稿状态更新: 🚀 立即开始下一个任务
[2025-07-22 22:07:58,642] [INFO] 批量存稿状态更新: ✅ 账号 测试 第1次发布成功
[2025-07-22 22:08:00,648] [INFO] 批量存稿状态更新: 账号 测试 开始第 2/3 次存稿
[2025-07-22 22:08:00,651] [INFO] 批量存稿状态更新: 直接导航到上传界面
[2025-07-22 22:08:01,528] [INFO] 批量存稿状态更新: 账号 测试 选择视频文件: 年轻的时候一定要对自己媳妇好到老就知道了.mp4
[2025-07-22 22:08:01,529] [INFO] 批量存稿状态更新: 🔍 检测目标元素是否存在...
[2025-07-22 22:08:02,790] [INFO] 批量存稿状态更新: ✅ 检测到目标元素，立即开始视频上传...
[2025-07-22 22:08:02,791] [INFO] 批量存稿状态更新: 🔍 查找文件输入元素(方法1: CSS选择器)...
[2025-07-22 22:08:02,799] [INFO] 批量存稿状态更新: ✅ 方法1成功找到文件输入元素
[2025-07-22 22:08:02,800] [INFO] 批量存稿状态更新: ✅ 准备发送文件路径...
[2025-07-22 22:08:02,802] [INFO] 批量存稿状态更新: 📂 文件绝对路径: D:\头条全自动\视频搬运\已处理视频\年轻的时候一定要对自己媳妇好到老就知道了.mp4
[2025-07-22 22:08:03,862] [INFO] 批量存稿状态更新: ✅ 已通过传统方式发送文件路径
[2025-07-22 22:08:03,891] [INFO] 批量存稿状态更新: ✅ 已滚动到页面底部
[2025-07-22 22:08:03,892] [INFO] 批量存稿状态更新: ⏳ 等待视频开始上传...
[2025-07-22 22:08:05,394] [INFO] 批量存稿状态更新: 🔍 准备处理封面，立即查找封面元素...
[2025-07-22 22:08:06,424] [INFO] 批量存稿状态更新: ✅ 找到封面元素，使用JavaScript点击...
[2025-07-22 22:08:06,469] [INFO] 批量存稿状态更新: ✅ 普通点击成功
[2025-07-22 22:08:06,470] [INFO] 批量存稿状态更新: ✅ 已点击封面元素
[2025-07-22 22:08:06,471] [INFO] 批量存稿状态更新: 🔍 账号 测试: 等待封面上传界面出现...
[2025-07-22 22:08:06,472] [INFO] 批量存稿状态更新: ⏳ 账号 测试: 等待封面上传对话框显示...
[2025-07-22 22:08:06,480] [INFO] 批量存稿状态更新: ✅ 账号 测试: 封面上传对话框已显示
[2025-07-22 22:08:06,481] [INFO] 批量存稿状态更新: 🔍 账号 测试: 查找本地封面选项...
[2025-07-22 22:08:06,493] [INFO] 批量存稿状态更新: ✅ 找到本地封面选项，立即点击...
[2025-07-22 22:08:06,583] [INFO] 批量存稿状态更新: ✅ 已点击本地封面选项
[2025-07-22 22:08:06,584] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备上传封面...
[2025-07-22 22:08:06,587] [INFO] 批量存稿状态更新: ✅ 账号 测试: 找到封面文件: 年轻的时候一定要对自己媳妇好到老就知道了.jpg
[2025-07-22 22:08:06,588] [INFO] 批量存稿状态更新: 🔍 等待文件输入元素出现...
[2025-07-22 22:08:06,589] [INFO] 批量存稿状态更新: 🔍 在封面上传对话框中查找文件输入元素...
[2025-07-22 22:08:06,609] [INFO] 批量存稿状态更新: ✅ 方法2成功：通过XPath在对话框中找到 1 个文件输入元素
[2025-07-22 22:08:06,611] [INFO] 批量存稿状态更新: ✅ 找到封面上传对话框中的文件输入元素
[2025-07-22 22:08:06,612] [INFO] 批量存稿状态更新: ✅ 发送封面文件路径...
[2025-07-22 22:08:06,658] [INFO] 批量存稿状态更新: ✅ 已发送封面文件路径
[2025-07-22 22:08:06,659] [INFO] 批量存稿状态更新: ✅ 封面上传成功
[2025-07-22 22:08:06,661] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备点击封面确认按钮...
[2025-07-22 22:08:07,662] [INFO] 批量存稿状态更新: 🔍 账号 测试: 尝试点击第一个封面确认按钮(方法1)...
[2025-07-22 22:08:07,837] [INFO] 批量存稿状态更新: 第一个按钮结果(方法1): 第一个按钮点击成功(方法1)
[2025-07-22 22:08:07,839] [INFO] 批量存稿状态更新: 🔍 尝试点击第一个封面确认按钮(方法2)...
[2025-07-22 22:08:07,844] [INFO] 批量存稿状态更新: 第一个按钮结果(方法2): 第一个按钮未找到(方法2)
[2025-07-22 22:08:07,846] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法1)...
[2025-07-22 22:08:07,864] [INFO] 批量存稿状态更新: 第二个按钮结果(方法1): 第二个按钮点击成功(方法1)
[2025-07-22 22:08:07,866] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法2)...
[2025-07-22 22:08:07,873] [INFO] 批量存稿状态更新: 第二个按钮结果(方法2): 第二个按钮未找到(方法2)
[2025-07-22 22:08:07,875] [INFO] 批量存稿状态更新: ✅ 所有封面确认按钮点击成功
[2025-07-22 22:08:07,877] [INFO] 批量存稿状态更新: 🔍 等待弹窗消失...
[2025-07-22 22:08:09,395] [INFO] 批量存稿状态更新: ✅ 第一个弹窗已消失
[2025-07-22 22:08:09,401] [INFO] 批量存稿状态更新: ✅ 第二个弹窗已消失
[2025-07-22 22:08:09,403] [INFO] 批量存稿状态更新: 🔍 检测封面上传成功元素...
[2025-07-22 22:08:09,922] [INFO] 批量存稿状态更新: ✅ 已检测到封面上传成功元素
[2025-07-22 22:08:09,923] [INFO] 批量存稿状态更新: ✅ 确认检测到封面上传成功元素!
[2025-07-22 22:08:09,924] [INFO] 批量存稿状态更新: 🔍 立即检测上传成功文本...
[2025-07-22 22:08:22,732] [INFO] 批量存稿状态更新: ✅ 已检测到上传成功提示 (XPath方式)
[2025-07-22 22:08:22,733] [INFO] 批量存稿状态更新: ✅ 确认检测到上传成功提示!
[2025-07-22 22:08:22,734] [INFO] 批量存稿状态更新: ✅ 封面提交完成
[2025-07-22 22:08:22,735] [INFO] 批量存稿状态更新: ✅ 封面处理完成，开始监测视频上传状态...
[2025-07-22 22:08:22,738] [INFO] 批量存稿状态更新: 🔍 账号 测试 开始并行监测视频上传状态...
[2025-07-22 22:08:23,324] [DEBUG] 视频上传失败检测收到停止信号，退出
[2025-07-22 22:08:23,325] [INFO] 批量存稿状态更新: ✅ 账号 测试 视频上传成功检测完成
[2025-07-22 22:08:23,327] [INFO] 批量存稿状态更新: ✅ 视频上传成功确认
[2025-07-22 22:08:25,336] [INFO] 批量存稿状态更新: 🎯 封号模式: 尝试点击发布按钮...
[2025-07-22 22:08:25,347] [DEBUG] 账号 测试 第 2/3 次存稿完成
[2025-07-22 22:08:25,347] [INFO] 批量存稿状态更新: 📝 发布按钮点击结果: 发布按钮点击成功
[2025-07-22 22:08:25,347] [INFO] 批量存稿状态更新: ✅ 已点击发布按钮，封号模式操作完成
[2025-07-22 22:08:25,348] [INFO] 批量存稿状态更新: 🚀 立即开始下一个任务
[2025-07-22 22:08:25,348] [INFO] 批量存稿状态更新: ✅ 账号 测试 第2次发布成功
[2025-07-22 22:08:27,352] [INFO] 批量存稿状态更新: 账号 测试 开始第 3/3 次存稿
[2025-07-22 22:08:27,358] [INFO] 批量存稿状态更新: 直接导航到上传界面
[2025-07-22 22:08:28,187] [INFO] 批量存稿状态更新: 账号 测试 选择视频文件: 朝鲜正式出兵援助俄罗斯规模达12万人为何对我国不利.mp4
[2025-07-22 22:08:28,188] [INFO] 批量存稿状态更新: 🔍 检测目标元素是否存在...
[2025-07-22 22:08:29,754] [INFO] 批量存稿状态更新: ✅ 检测到目标元素，立即开始视频上传...
[2025-07-22 22:08:29,756] [INFO] 批量存稿状态更新: 🔍 查找文件输入元素(方法1: CSS选择器)...
[2025-07-22 22:08:29,764] [INFO] 批量存稿状态更新: ✅ 方法1成功找到文件输入元素
[2025-07-22 22:08:29,765] [INFO] 批量存稿状态更新: ✅ 准备发送文件路径...
[2025-07-22 22:08:29,766] [INFO] 批量存稿状态更新: 📂 文件绝对路径: D:\头条全自动\视频搬运\已处理视频\朝鲜正式出兵援助俄罗斯规模达12万人为何对我国不利.mp4
[2025-07-22 22:08:30,805] [INFO] 批量存稿状态更新: ✅ 已通过传统方式发送文件路径
[2025-07-22 22:08:30,810] [INFO] 批量存稿状态更新: ✅ 已滚动到页面底部
[2025-07-22 22:08:30,811] [INFO] 批量存稿状态更新: ⏳ 等待视频开始上传...
[2025-07-22 22:08:32,313] [INFO] 批量存稿状态更新: 🔍 准备处理封面，立即查找封面元素...
[2025-07-22 22:08:33,340] [INFO] 批量存稿状态更新: ✅ 找到封面元素，使用JavaScript点击...
[2025-07-22 22:08:33,394] [INFO] 批量存稿状态更新: ✅ 普通点击成功
[2025-07-22 22:08:33,395] [INFO] 批量存稿状态更新: ✅ 已点击封面元素
[2025-07-22 22:08:33,395] [INFO] 批量存稿状态更新: 🔍 账号 测试: 等待封面上传界面出现...
[2025-07-22 22:08:33,396] [INFO] 批量存稿状态更新: ⏳ 账号 测试: 等待封面上传对话框显示...
[2025-07-22 22:08:33,406] [INFO] 批量存稿状态更新: ✅ 账号 测试: 封面上传对话框已显示
[2025-07-22 22:08:33,408] [INFO] 批量存稿状态更新: 🔍 账号 测试: 查找本地封面选项...
[2025-07-22 22:08:33,426] [INFO] 批量存稿状态更新: ✅ 找到本地封面选项，立即点击...
[2025-07-22 22:08:33,522] [INFO] 批量存稿状态更新: ✅ 已点击本地封面选项
[2025-07-22 22:08:33,524] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备上传封面...
[2025-07-22 22:08:33,526] [INFO] 批量存稿状态更新: ✅ 账号 测试: 找到封面文件: 朝鲜正式出兵援助俄罗斯规模达12万人为何对我国不利.jpg
[2025-07-22 22:08:33,527] [INFO] 批量存稿状态更新: 🔍 等待文件输入元素出现...
[2025-07-22 22:08:33,528] [INFO] 批量存稿状态更新: 🔍 在封面上传对话框中查找文件输入元素...
[2025-07-22 22:08:33,550] [INFO] 批量存稿状态更新: ✅ 方法2成功：通过XPath在对话框中找到 1 个文件输入元素
[2025-07-22 22:08:33,552] [INFO] 批量存稿状态更新: ✅ 找到封面上传对话框中的文件输入元素
[2025-07-22 22:08:33,553] [INFO] 批量存稿状态更新: ✅ 发送封面文件路径...
[2025-07-22 22:08:33,581] [INFO] 批量存稿状态更新: ✅ 已发送封面文件路径
[2025-07-22 22:08:33,582] [INFO] 批量存稿状态更新: ✅ 封面上传成功
[2025-07-22 22:08:33,586] [INFO] 批量存稿状态更新: 🔍 账号 测试: 准备点击封面确认按钮...
[2025-07-22 22:08:34,583] [INFO] 批量存稿状态更新: 🔍 账号 测试: 尝试点击第一个封面确认按钮(方法1)...
[2025-07-22 22:08:34,799] [INFO] 批量存稿状态更新: 第一个按钮结果(方法1): 第一个按钮点击成功(方法1)
[2025-07-22 22:08:34,801] [INFO] 批量存稿状态更新: 🔍 尝试点击第一个封面确认按钮(方法2)...
[2025-07-22 22:08:34,806] [INFO] 批量存稿状态更新: 第一个按钮结果(方法2): 第一个按钮未找到(方法2)
[2025-07-22 22:08:34,808] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法1)...
[2025-07-22 22:08:34,834] [INFO] 批量存稿状态更新: 第二个按钮结果(方法1): 第二个按钮点击成功(方法1)
[2025-07-22 22:08:34,837] [INFO] 批量存稿状态更新: 🔍 尝试点击第二个封面确认按钮(方法2)...
[2025-07-22 22:08:34,844] [INFO] 批量存稿状态更新: 第二个按钮结果(方法2): 第二个按钮未找到(方法2)
[2025-07-22 22:08:34,846] [INFO] 批量存稿状态更新: ✅ 所有封面确认按钮点击成功
[2025-07-22 22:08:34,847] [INFO] 批量存稿状态更新: 🔍 等待弹窗消失...
[2025-07-22 22:08:40,238] [INFO] 批量存稿状态更新: ✅ 第一个弹窗已消失
[2025-07-22 22:08:40,249] [INFO] 批量存稿状态更新: ✅ 第二个弹窗已消失
[2025-07-22 22:08:40,250] [INFO] 批量存稿状态更新: 🔍 检测封面上传成功元素...
[2025-07-22 22:08:40,774] [INFO] 批量存稿状态更新: ✅ 已检测到封面上传成功元素
[2025-07-22 22:08:40,776] [INFO] 批量存稿状态更新: ✅ 确认检测到封面上传成功元素!
[2025-07-22 22:08:40,777] [INFO] 批量存稿状态更新: 🔍 立即检测上传成功文本...
[2025-07-22 22:08:51,223] [INFO] 批量存稿状态更新: ✅ 已检测到上传成功提示 (XPath方式)
[2025-07-22 22:08:51,223] [INFO] 批量存稿状态更新: ✅ 确认检测到上传成功提示!
[2025-07-22 22:08:51,224] [INFO] 批量存稿状态更新: ✅ 封面提交完成
[2025-07-22 22:08:51,225] [INFO] 批量存稿状态更新: ✅ 封面处理完成，开始监测视频上传状态...
[2025-07-22 22:08:51,228] [INFO] 批量存稿状态更新: 🔍 账号 测试 开始并行监测视频上传状态...
[2025-07-22 22:08:51,816] [DEBUG] 视频上传失败检测收到停止信号，退出
[2025-07-22 22:08:51,817] [INFO] 批量存稿状态更新: ✅ 账号 测试 视频上传成功检测完成
[2025-07-22 22:08:51,819] [INFO] 批量存稿状态更新: ✅ 视频上传成功确认
[2025-07-22 22:08:53,829] [INFO] 批量存稿状态更新: 🎯 封号模式: 尝试点击发布按钮...
[2025-07-22 22:08:53,840] [INFO] 账号 测试: 存稿完成 (3/3)
[2025-07-22 22:08:53,840] [INFO] 批量存稿状态更新: 📝 发布按钮点击结果: 发布按钮点击成功
[2025-07-22 22:08:53,841] [INFO] 批量存稿状态更新: ✅ 已点击发布按钮，封号模式操作完成
[2025-07-22 22:08:53,842] [INFO] 批量存稿状态更新: 🚀 立即开始下一个任务
[2025-07-22 22:08:53,842] [INFO] 批量存稿状态更新: ✅ 账号 测试 第3次发布成功
[2025-07-22 22:08:53,847] [INFO] 批量存稿状态更新: 📊 进度: 1/1 (100%)
[2025-07-22 22:08:53,847] [INFO] 批量存稿状态更新: ✅ 工作窃取线程 #0 账号 测试 处理成功
[2025-07-22 22:08:54,245] [DEBUG] 准备登录账号: 测试, Cookie文件: E:/软件共享/测试账号/新建文件夹 (2)\测试.txt
[2025-07-22 22:08:54,246] [INFO] 使用登录设置: 指纹浏览器=False, 手机端模式=False, 禁用JS=False, 清除Cookie=True
[2025-07-22 22:08:54,248] [INFO] 🚀 开始Cookie登录流程（重构版本）
[2025-07-22 22:08:54,248] [INFO] 🔧 配置Chrome浏览器选项...
[2025-07-22 22:08:54,248] [INFO] 使用与批量存稿相同的标准浏览器配置
[2025-07-22 22:08:54,248] [INFO] 使用独立用户数据目录: C:\Users\<USER>\AppData\Local\Temp\chrome_user_data_single_1753193334
[2025-07-22 22:08:54,249] [INFO] ✅ Chrome浏览器选项配置完成
[2025-07-22 22:08:54,841] [DEBUG] 工作窃取线程 #0 队列为空或收到停止信号，准备退出
[2025-07-22 22:08:54,841] [DEBUG] 工作窃取线程 #0 完成，共处理 1 个账号
[2025-07-22 22:08:57,037] [DEBUG] 工作窃取线程 #0 浏览器已关闭
[2025-07-22 22:08:57,040] [INFO] ✅ 线程 #0 已完成
[2025-07-22 22:08:57,040] [INFO] 所有线程已完成，准备发送完成信号
[2025-07-22 22:08:57,041] [INFO] 批量存稿状态更新: ✅ 线程 #0 已完成
[2025-07-22 22:08:57,041] [INFO] 已设置停止标志，所有工作窃取线程将停止
[2025-07-22 22:08:57,044] [INFO] 批量存稿任务总运行时间: 00:01:23
[2025-07-22 22:08:57,045] [INFO] 批量存稿状态更新: ⏱️ 总运行时间: 00:01:23
[2025-07-22 22:08:57,045] [INFO] 批量存稿任务统计 - 总计: 1, 成功: 1, 登录失败: 0, 发布失败: 0, 重试轮次: 0, 重试成功: 0, 视频重试: {'retry_accounts': 0, 'total_retries': 0, 'retry_success': 0, 'final_failed': 0}
[2025-07-22 22:08:57,046] [INFO] 批量存稿状态更新: 📊 任务统计: 总账号 1 个
[2025-07-22 22:08:57,047] [INFO] 批量存稿状态更新: 📊 发布成功: 1 个账号
[2025-07-22 22:08:57,432] [DEBUG] 已清理临时目录: C:\Users\<USER>\AppData\Local\Temp\chrome_user_data_0
[2025-07-22 22:08:57,455] [DEBUG] 共清理了 1 个临时Chrome用户数据目录
[2025-07-22 22:08:57,460] [DEBUG] 已断开信号连接: status_signal
[2025-07-22 22:08:57,461] [DEBUG] 已断开信号连接: progress_signal
[2025-07-22 22:08:57,461] [DEBUG] 已断开信号连接: finished_signal
[2025-07-22 22:08:57,532] [INFO] 批量存稿任务正常完成
[2025-07-22 22:08:57,665] [INFO] 🔑 开始登录账号 测试
[2025-07-22 22:08:57,667] [DEBUG] JSON格式解析成功，提取到 5 个关键Cookie
[2025-07-22 22:08:57,667] [INFO] 📋 解析到 5 个关键Cookie
[2025-07-22 22:08:57,667] [INFO] 🌐 导航到头条媒体平台
[2025-07-22 22:08:58,700] [INFO] 🔑 添加 5 个关键Cookie
[2025-07-22 22:08:58,729] [INFO] ✅ 成功添加 5 个Cookie
[2025-07-22 22:08:58,729] [INFO] 🔄 刷新页面应用Cookie
[2025-07-22 22:09:01,735] [INFO] 🔍 验证登录状态
[2025-07-22 22:09:01,740] [DEBUG] URL验证通过 (0.01s)
[2025-07-22 22:09:01,741] [INFO] [SUCCESS] ✅ 账号 测试 登录成功
[2025-07-22 22:09:01,741] [INFO] 🔍 开始检测 账号 测试 的实名认证状态...
[2025-07-22 22:09:01,756] [INFO] 📋 文本检测结果: 页面文本检测通过
[2025-07-22 22:09:01,891] [INFO] 🔍 元素检测结果: 元素检测通过
[2025-07-22 22:09:01,895] [INFO] 🌐 URL检测结果: URL检测通过
[2025-07-22 22:09:01,895] [INFO] ✅ 账号 测试 已实名认证
[2025-07-22 22:09:01,896] [INFO] ✅ 账号 测试 实名认证状态: 已实名
[2025-07-22 22:09:01,896] [INFO] 账号 测试 的浏览器实例已添加到管理器，当前管理 1 个浏览器
[2025-07-22 22:09:01,896] [INFO] [SUCCESS] ✅ 账号 测试 登录成功，已停留在头条媒体平台首页
[2025-07-22 22:09:01,901] [INFO] [SUCCESS] 账号 测试 登录成功
[2025-07-22 22:09:01,902] [INFO] 账号 测试 登录成功: 账号 测试 登录成功，已停留在头条媒体平台首页
