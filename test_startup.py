#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试程序启动
"""

import os
import sys

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_account_tab_import():
    """测试AccountTab导入"""
    try:
        print("测试AccountTab导入...")
        from app.tabs.account_tab import AccountTab
        print("✅ AccountTab导入成功")
        return True
    except Exception as e:
        print(f"❌ AccountTab导入失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_cookie_encryption_import():
    """测试Cookie加密模块导入"""
    try:
        print("\n测试Cookie加密模块导入...")
        from app.utils.cookie_encryption import get_cookie_encryptor, CRYPTOGRAPHY_AVAILABLE
        print(f"✅ Cookie加密模块导入成功")
        print(f"📦 cryptography库可用: {CRYPTOGRAPHY_AVAILABLE}")
        
        encryptor = get_cookie_encryptor()
        print(f"🔧 加密器可用: {encryptor.available}")
        return True
    except Exception as e:
        print(f"❌ Cookie加密模块导入失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("程序启动测试")
    print("=" * 50)
    
    # 测试Cookie加密模块
    encryption_ok = test_cookie_encryption_import()
    
    # 测试AccountTab导入
    account_tab_ok = test_account_tab_import()
    
    print("\n" + "=" * 50)
    if account_tab_ok:
        print("🎉 程序启动测试通过！")
        if encryption_ok:
            print("✅ Cookie加密功能完全可用")
        else:
            print("⚠️  Cookie加密功能不可用，但不影响程序启动")
    else:
        print("❌ 程序启动测试失败")
    print("=" * 50)
