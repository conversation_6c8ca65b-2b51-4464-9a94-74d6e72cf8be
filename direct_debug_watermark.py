#!/usr/bin/env python3
"""
直接调试水印字体大小问题
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QDialog, QHBoxLayout
from PyQt5.QtCore import QTimer

# 添加项目路径
sys.path.append('.')

def direct_debug_watermark():
    """直接调试水印问题"""
    try:
        from app.dialogs.video_processor_dialog import WatermarkConfigDialog, WatermarkConfig
        from PIL import Image
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("直接调试水印字体大小")
        main_window.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("""
🔧 直接调试水印字体大小问题

这个工具会：
1. 创建水印配置对话框
2. 自动设置测试参数
3. 对比预览和封面生成的字体大小
4. 显示详细的调试信息
5. 找出问题所在

请点击按钮开始自动调试...
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #fff8dc;
                padding: 20px;
                border-radius: 8px;
                font-size: 16px;
                line-height: 1.6;
                border: 2px solid #daa520;
                color: #8b4513;
            }
        """)
        layout.addWidget(info_label)
        
        # 创建按钮布局
        button_layout = QHBoxLayout()
        
        # 单行测试按钮
        single_test_button = QPushButton("🔍 测试单行水印")
        single_test_button.setStyleSheet("""
            QPushButton {
                background-color: #32cd32;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #228b22;
            }
        """)
        
        # 多行测试按钮
        multi_test_button = QPushButton("🔍 测试多行水印")
        multi_test_button.setStyleSheet("""
            QPushButton {
                background-color: #4169e1;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #1e90ff;
            }
        """)
        
        button_layout.addWidget(single_test_button)
        button_layout.addWidget(multi_test_button)
        layout.addLayout(button_layout)
        
        # 结果显示区域
        result_label = QLabel("等待测试结果...")
        result_label.setStyleSheet("""
            QLabel {
                background-color: #f5f5f5;
                padding: 20px;
                border-radius: 8px;
                font-size: 14px;
                font-family: 'Consolas', 'Monaco', monospace;
                border: 1px solid #ccc;
                min-height: 300px;
            }
        """)
        layout.addWidget(result_label)
        
        def test_single_watermark():
            """测试单行水印"""
            print("=" * 80)
            print("开始单行水印调试测试")
            print("=" * 80)
            
            result_text = "🔍 单行水印调试结果:\n\n"
            
            try:
                # 1. 创建配置
                config = WatermarkConfig()
                config.enabled = True
                config.multi_line_enabled = False
                result_text += f"1. 配置创建: ✅\n"
                result_text += f"   - 水印启用: {config.enabled}\n"
                result_text += f"   - 多行模式: {config.multi_line_enabled}\n"
                result_text += f"   - 默认字体大小: {config.font_size}px\n\n"
                
                # 2. 创建对话框
                dialog = WatermarkConfigDialog(config, main_window)
                result_text += f"2. 对话框创建: ✅\n"
                
                # 3. 设置测试参数
                test_font_size = 60
                dialog.font_size_spinbox.setValue(test_font_size)
                result_text += f"3. 设置测试字体大小: {test_font_size}px\n\n"
                
                # 4. 获取界面当前值
                ui_font_size = dialog.font_size_spinbox.value()
                ui_font_family = dialog.font_combo.currentText()
                result_text += f"4. 界面当前值:\n"
                result_text += f"   - 字体大小: {ui_font_size}px\n"
                result_text += f"   - 字体名称: {ui_font_family}\n\n"
                
                # 5. 测试预览字体大小
                preview_scale = 640 / 1920
                preview_font_size = int(ui_font_size * preview_scale)
                result_text += f"5. 预览字体计算:\n"
                result_text += f"   - 缩放比例: {preview_scale:.3f}\n"
                result_text += f"   - 预览字体大小: {preview_font_size}px\n\n"
                
                # 6. 测试封面字体大小
                config_font_size = config.font_size
                result_text += f"6. 配置对象字体大小: {config_font_size}px\n\n"
                
                # 7. 模拟封面生成
                test_img = Image.new('RGB', (1920, 1080), color='#2c3e50')
                result_text += f"7. 模拟封面生成:\n"
                result_text += f"   - 测试图像: 1920x1080\n"
                
                # 调用封面水印添加方法
                watermarked = dialog.add_watermark_to_pil_image(test_img, "测试文字")
                if watermarked:
                    result_text += f"   - 水印添加: ✅\n"
                else:
                    result_text += f"   - 水印添加: ❌\n"
                
                result_text += f"\n8. 问题分析:\n"
                if ui_font_size != config_font_size:
                    result_text += f"   ❌ 界面值({ui_font_size}px) ≠ 配置值({config_font_size}px)\n"
                    result_text += f"   🔧 需要同步界面值到配置对象\n"
                else:
                    result_text += f"   ✅ 界面值与配置值一致\n"
                
                result_text += f"\n9. 建议解决方案:\n"
                result_text += f"   - 封面生成前调用: config.font_size = ui_font_size\n"
                result_text += f"   - 或者封面生成时直接使用界面值\n"
                
            except Exception as e:
                result_text += f"❌ 测试过程出错: {e}\n"
                import traceback
                result_text += traceback.format_exc()
            
            result_label.setText(result_text)
            print(result_text)
        
        def test_multi_watermark():
            """测试多行水印"""
            print("=" * 80)
            print("开始多行水印调试测试")
            print("=" * 80)
            
            result_text = "🔍 多行水印调试结果:\n\n"
            
            try:
                # 1. 创建配置
                config = WatermarkConfig()
                config.enabled = True
                config.multi_line_enabled = True
                result_text += f"1. 配置创建: ✅\n"
                result_text += f"   - 水印启用: {config.enabled}\n"
                result_text += f"   - 多行模式: {config.multi_line_enabled}\n"
                result_text += f"   - 多行配置数量: {len(config.multi_line_configs)}\n\n"
                
                # 2. 创建对话框
                dialog = WatermarkConfigDialog(config, main_window)
                result_text += f"2. 对话框创建: ✅\n"
                
                # 3. 设置测试参数
                test_font_size = 72
                dialog.font_size_spinbox.setValue(test_font_size)
                result_text += f"3. 设置测试字体大小: {test_font_size}px\n\n"
                
                # 4. 获取界面当前值
                ui_font_size = dialog.font_size_spinbox.value()
                ui_font_family = dialog.font_combo.currentText()
                current_line_index = dialog.line_selector.currentIndex() if hasattr(dialog, 'line_selector') else 0
                result_text += f"4. 界面当前值:\n"
                result_text += f"   - 字体大小: {ui_font_size}px\n"
                result_text += f"   - 字体名称: {ui_font_family}\n"
                result_text += f"   - 当前选中行: {current_line_index + 1}\n\n"
                
                # 5. 检查多行配置
                result_text += f"5. 多行配置检查:\n"
                for i, line_config in enumerate(config.multi_line_configs):
                    result_text += f"   第{i+1}行: {line_config.font_size}px (启用: {line_config.enabled})\n"
                result_text += f"\n"
                
                # 6. 强制保存当前配置
                if hasattr(dialog, 'save_ui_config_to_line'):
                    dialog.save_ui_config_to_line(current_line_index)
                    result_text += f"6. 强制保存配置到第{current_line_index + 1}行: ✅\n"
                    
                    # 检查保存后的配置
                    saved_font_size = config.multi_line_configs[current_line_index].font_size
                    result_text += f"   保存后字体大小: {saved_font_size}px\n\n"
                else:
                    result_text += f"6. 保存配置方法不存在: ❌\n\n"
                
                # 7. 模拟封面生成
                test_img = Image.new('RGB', (1920, 1080), color='#2c3e50')
                result_text += f"7. 模拟多行封面生成:\n"
                result_text += f"   - 测试图像: 1920x1080\n"
                
                # 调用封面水印添加方法
                watermarked = dialog.add_watermark_to_pil_image(test_img, "测试多行文字")
                if watermarked:
                    result_text += f"   - 多行水印添加: ✅\n"
                else:
                    result_text += f"   - 多行水印添加: ❌\n"
                
                result_text += f"\n8. 问题分析:\n"
                current_config = config.multi_line_configs[current_line_index]
                if ui_font_size != current_config.font_size:
                    result_text += f"   ❌ 界面值({ui_font_size}px) ≠ 当前行配置({current_config.font_size}px)\n"
                    result_text += f"   🔧 需要在封面生成前同步配置\n"
                else:
                    result_text += f"   ✅ 界面值与当前行配置一致\n"
                
                result_text += f"\n9. 建议解决方案:\n"
                result_text += f"   - 封面生成前强制保存所有行配置\n"
                result_text += f"   - 或者封面生成时直接使用界面值\n"
                result_text += f"   - 确保多行配置实时同步\n"
                
            except Exception as e:
                result_text += f"❌ 测试过程出错: {e}\n"
                import traceback
                result_text += traceback.format_exc()
            
            result_label.setText(result_text)
            print(result_text)
        
        single_test_button.clicked.connect(test_single_watermark)
        multi_test_button.clicked.connect(test_multi_watermark)
        
        main_window.show()
        
        print("直接调试工具启动成功")
        print("请点击按钮开始测试")
        
        return app.exec_()
        
    except Exception as e:
        print(f"调试工具启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("启动直接调试工具...")
    return direct_debug_watermark()

if __name__ == "__main__":
    sys.exit(main())
