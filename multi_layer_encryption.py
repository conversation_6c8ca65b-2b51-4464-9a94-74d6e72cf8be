#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多层加密Cookie保护方案
"""

import os
import json
import base64
import hashlib
import hmac
import time
import secrets
from typing import Optional, Dict, Tuple

try:
    from cryptography.fernet import <PERSON>rnet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PB<PERSON><PERSON>2HMAC
    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False

class MultiLayerCookieEncryption:
    """多层加密Cookie保护"""
    
    def __init__(self, master_password: str = "zhengyang.0924"):
        self.master_password = master_password
        self.available = CRYPTOGRAPHY_AVAILABLE
        
    def _generate_session_key(self) -> bytes:
        """生成会话密钥"""
        return secrets.token_bytes(32)
    
    def _derive_key(self, password: str, salt: bytes, iterations: int = 200000) -> bytes:
        """派生密钥"""
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=iterations,
        )
        return kdf.derive(password.encode('utf-8'))
    
    def _encrypt_layer1(self, data: bytes, key: bytes) -> bytes:
        """第一层加密：AES-256-GCM"""
        from cryptography.hazmat.primitives.ciphers.aead import AESGCM
        
        aesgcm = AESGCM(key)
        nonce = secrets.token_bytes(12)  # GCM推荐12字节nonce
        ciphertext = aesgcm.encrypt(nonce, data, None)
        
        return nonce + ciphertext
    
    def _decrypt_layer1(self, encrypted_data: bytes, key: bytes) -> bytes:
        """第一层解密：AES-256-GCM"""
        from cryptography.hazmat.primitives.ciphers.aead import AESGCM
        
        nonce = encrypted_data[:12]
        ciphertext = encrypted_data[12:]
        
        aesgcm = AESGCM(key)
        return aesgcm.decrypt(nonce, ciphertext, None)
    
    def _encrypt_layer2(self, data: bytes, key: bytes) -> bytes:
        """第二层加密：ChaCha20-Poly1305"""
        from cryptography.hazmat.primitives.ciphers.aead import ChaCha20Poly1305
        
        chacha = ChaCha20Poly1305(key)
        nonce = secrets.token_bytes(12)
        ciphertext = chacha.encrypt(nonce, data, None)
        
        return nonce + ciphertext
    
    def _decrypt_layer2(self, encrypted_data: bytes, key: bytes) -> bytes:
        """第二层解密：ChaCha20-Poly1305"""
        from cryptography.hazmat.primitives.ciphers.aead import ChaCha20Poly1305
        
        nonce = encrypted_data[:12]
        ciphertext = encrypted_data[12:]
        
        chacha = ChaCha20Poly1305(key)
        return chacha.decrypt(nonce, ciphertext, None)
    
    def _generate_hmac(self, data: bytes, key: bytes) -> bytes:
        """生成HMAC签名"""
        return hmac.new(key, data, hashlib.sha256).digest()
    
    def _verify_hmac(self, data: bytes, signature: bytes, key: bytes) -> bool:
        """验证HMAC签名"""
        expected = hmac.new(key, data, hashlib.sha256).digest()
        return hmac.compare_digest(expected, signature)
    
    def encrypt_cookie_data(self, cookie_data: dict, user_context: dict = None) -> dict:
        """多层加密Cookie数据"""
        if not self.available:
            raise RuntimeError("cryptography库未安装")
        
        try:
            # 准备数据
            timestamp = int(time.time())
            session_id = secrets.token_hex(16)
            
            enhanced_data = {
                "original_data": cookie_data,
                "timestamp": timestamp,
                "session_id": session_id,
                "user_context": user_context or {},
                "version": "3.0"
            }
            
            # 转换为JSON
            json_data = json.dumps(enhanced_data, ensure_ascii=False, separators=(',', ':')).encode('utf-8')
            
            # 生成密钥
            salt1 = secrets.token_bytes(32)
            salt2 = secrets.token_bytes(32)
            hmac_salt = secrets.token_bytes(32)
            
            key1 = self._derive_key(self.master_password, salt1)
            key2 = self._derive_key(f"{self.master_password}_{session_id}", salt2)
            hmac_key = self._derive_key(f"{self.master_password}_hmac", hmac_salt)
            
            # 第一层加密
            layer1_encrypted = self._encrypt_layer1(json_data, key1)
            
            # 第二层加密
            layer2_encrypted = self._encrypt_layer2(layer1_encrypted, key2)
            
            # 生成HMAC签名
            signature = self._generate_hmac(layer2_encrypted, hmac_key)
            
            # 组装最终数据
            final_data = {
                "encrypted": True,
                "version": "3.0",
                "algorithm": "AES-256-GCM + ChaCha20-Poly1305",
                "data": base64.b64encode(layer2_encrypted).decode('utf-8'),
                "signature": base64.b64encode(signature).decode('utf-8'),
                "salt1": base64.b64encode(salt1).decode('utf-8'),
                "salt2": base64.b64encode(salt2).decode('utf-8'),
                "hmac_salt": base64.b64encode(hmac_salt).decode('utf-8'),
                "session_id": session_id,
                "created_time": timestamp,
                "multi_layer": True,
                "account_id": cookie_data.get("accountId", ""),
                "remark": cookie_data.get("remark", "")
            }
            
            return final_data
            
        except Exception as e:
            raise Exception(f"多层加密失败: {str(e)}")
    
    def decrypt_cookie_data(self, encrypted_file_data: dict) -> dict:
        """多层解密Cookie数据"""
        if not self.available:
            raise RuntimeError("cryptography库未安装")
        
        try:
            # 验证是否为多层加密文件
            if not encrypted_file_data.get("multi_layer", False):
                raise Exception("此文件不是多层加密文件")
            
            # 提取数据
            encrypted_data = base64.b64decode(encrypted_file_data["data"])
            signature = base64.b64decode(encrypted_file_data["signature"])
            salt1 = base64.b64decode(encrypted_file_data["salt1"])
            salt2 = base64.b64decode(encrypted_file_data["salt2"])
            hmac_salt = base64.b64decode(encrypted_file_data["hmac_salt"])
            session_id = encrypted_file_data["session_id"]
            
            # 重新生成密钥
            key1 = self._derive_key(self.master_password, salt1)
            key2 = self._derive_key(f"{self.master_password}_{session_id}", salt2)
            hmac_key = self._derive_key(f"{self.master_password}_hmac", hmac_salt)
            
            # 验证HMAC签名
            if not self._verify_hmac(encrypted_data, signature, hmac_key):
                raise Exception("数据完整性验证失败，文件可能被篡改")
            
            # 第二层解密
            layer1_data = self._decrypt_layer2(encrypted_data, key2)
            
            # 第一层解密
            json_data = self._decrypt_layer1(layer1_data, key1)
            
            # 解析JSON
            enhanced_data = json.loads(json_data.decode('utf-8'))
            
            # 验证时间戳（可选）
            timestamp = enhanced_data.get("timestamp", 0)
            current_time = int(time.time())
            max_age = 30 * 24 * 3600  # 30天
            
            if current_time - timestamp > max_age:
                raise Exception("Cookie文件已过期")
            
            return enhanced_data.get("original_data", {})
            
        except Exception as e:
            raise Exception(f"多层解密失败: {str(e)}")

# 使用示例
def test_multi_layer_encryption():
    """测试多层加密"""
    encryptor = MultiLayerCookieEncryption()
    
    test_data = {
        "accountId": "test123",
        "remark": "测试账号",
        "cookies": {
            "sessionid": "test_session",
            "csrf_token": "test_csrf"
        }
    }
    
    user_context = {
        "user_id": "user123",
        "device_id": "device456"
    }
    
    try:
        # 加密
        encrypted = encryptor.encrypt_cookie_data(test_data, user_context)
        print("✅ 多层加密成功")
        
        # 解密
        decrypted = encryptor.decrypt_cookie_data(encrypted)
        print("✅ 多层解密成功")
        print(f"数据匹配: {decrypted == test_data}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    test_multi_layer_encryption()
