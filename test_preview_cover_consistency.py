#!/usr/bin/env python3
"""
测试预览与封面尺寸一致性修复的脚本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QDialog

# 添加项目路径
sys.path.append('.')

def test_preview_cover_consistency():
    """测试预览与封面尺寸一致性修复"""
    try:
        from app.dialogs.video_processor_dialog import WatermarkConfigDialog, WatermarkConfig
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("预览与封面尺寸一致性修复测试")
        main_window.setGeometry(100, 100, 1000, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("""
📐 预览与封面尺寸一致性修复测试

问题描述：
预览区域使用640x360尺寸，但生成的封面是1920x1080，
导致预览中的水印大小和位置与最终封面不一致

修复内容：
✅ 预览内部使用1920x1080尺寸创建水印
✅ 缩放到640x360显示，保持比例一致
✅ 位置计算基于封面实际尺寸(1920x1080)
✅ 拖拽位置转换考虑封面与预览的比例关系
✅ 水印大小按封面尺寸计算，确保预览准确

测试步骤：
1. 打开水印配置对话框
2. 设置水印参数（字体大小、位置等）
3. 观察预览区域的水印效果
4. 拖拽调整水印位置
5. 观察控制台输出的尺寸转换信息

预期效果：
- 预览中的水印大小与封面中的比例一致
- 拖拽位置能正确转换为封面坐标
- 控制台显示详细的尺寸转换信息
- 预览效果与最终生成的封面完全一致

技术细节：
- 预览显示尺寸: 640x360
- 封面实际尺寸: 1920x1080  
- 缩放比例: 1:3 (预览:封面)
- 位置计算基于封面尺寸
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f0f8ff;
                padding: 20px;
                border-radius: 8px;
                font-size: 14px;
                line-height: 1.6;
                border: 2px solid #4169e1;
                color: #191970;
            }
        """)
        layout.addWidget(info_label)
        
        # 添加测试按钮
        test_button = QPushButton("📐 测试预览与封面尺寸一致性修复")
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #4169e1;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #1e90ff;
            }
        """)
        
        def open_dialog():
            print("=" * 80)
            print("开始预览与封面尺寸一致性修复测试")
            print("=" * 80)
            
            try:
                # 创建水印配置
                config = WatermarkConfig()
                config.enabled = True
                
                print("✅ 水印配置创建成功")
                print(f"水印启用: {config.enabled}")
                
                # 打开配置对话框
                dialog = WatermarkConfigDialog(config, main_window)
                print("✅ 对话框创建成功")
                
                # 检查预览标签的尺寸设置
                if hasattr(dialog, 'preview_label'):
                    preview_label = dialog.preview_label
                    print(f"\n📐 预览标签尺寸信息:")
                    print(f"预览显示尺寸: {preview_label.width()}x{preview_label.height()}")
                    if hasattr(preview_label, 'cover_width'):
                        print(f"封面实际尺寸: {preview_label.cover_width}x{preview_label.cover_height}")
                        scale_x = preview_label.width() / preview_label.cover_width
                        scale_y = preview_label.height() / preview_label.cover_height
                        print(f"缩放比例: X={scale_x:.3f}, Y={scale_y:.3f}")
                
                print("\n🎯 请按以下步骤测试:")
                print("1. 观察预览区域的初始水印效果")
                print("2. 调整字体大小，观察水印大小变化")
                print("3. 拖拽水印到不同位置")
                print("4. 观察控制台的位置转换信息")
                print("5. 设置不同的位置百分比")
                
                print("\n🔍 观察要点:")
                print("- 控制台会显示详细的尺寸转换信息")
                print("- 拖拽时会显示预览坐标→封面坐标→百分比的转换")
                print("- 水印大小应该与封面中的比例一致")
                print("- 位置设置应该准确反映在预览中")
                
                result = dialog.exec_()
                
                if result == QDialog.Accepted:
                    print("\n✅ 预览与封面一致性测试完成")
                    print("最终配置:")
                    print(f"水印启用: {config.enabled}")
                    print(f"字体: {config.font_family} {config.font_size}px")
                    print(f"位置: ({config.position_x}%, {config.position_y}%)")
                    print(f"颜色: {config.text_color}")
                    print(f"透明度: {config.opacity}%")
                    
                    # 验证尺寸一致性
                    print(f"\n📊 尺寸一致性验证:")
                    print("修复效果:")
                    print("✅ 预览使用1920x1080内部尺寸创建水印")
                    print("✅ 缩放到640x360显示，保持比例")
                    print("✅ 位置计算基于封面实际尺寸")
                    print("✅ 拖拽位置正确转换")
                    
                    if hasattr(config, 'multi_line_enabled') and config.multi_line_enabled:
                        print("\n多行模式配置:")
                        for i, line_config in enumerate(config.multi_line_configs):
                            print(f"第{i+1}行: 位置({line_config.position_x}%, {line_config.position_y}%)")
                        
                else:
                    print("\n❌ 预览与封面一致性测试已取消")
                
            except Exception as e:
                print(f"❌ 测试过程中出现错误: {e}")
                import traceback
                traceback.print_exc()
            
            print("=" * 80)
        
        test_button.clicked.connect(open_dialog)
        layout.addWidget(test_button)
        
        # 添加修复详情
        fix_details_label = QLabel("""
🔧 修复详情：

修复前的问题：
❌ 预览区域: 640x360
❌ 封面生成: 1920x1080
❌ 水印大小和位置不一致

修复后的改进：
✅ 预览内部使用1920x1080创建水印
✅ 缩放到640x360显示
✅ 位置计算基于封面尺寸

技术实现：
1. 预览容器尺寸设置:
   - preview_width = 640
   - preview_height = 360
   - cover_width = 1920
   - cover_height = 1080
   - preview_scale = 640/1920 = 1/3

2. 水印位置计算:
   - 基于封面尺寸计算实际位置
   - 缩放到预览尺寸显示
   - 拖拽时反向转换回封面坐标

3. 图像创建流程:
   - 创建1920x1080的水印图像
   - 添加水印效果
   - 缩放到640x360用于预览显示

4. 位置转换公式:
   - 预览→封面: cover_pos = preview_pos * (1920/640)
   - 封面→预览: preview_pos = cover_pos * (640/1920)
   - 百分比: percent = (cover_pos / cover_size) * 100
        """)
        fix_details_label.setStyleSheet("""
            QLabel {
                background-color: #f5f5f5;
                padding: 15px;
                border-radius: 8px;
                font-size: 12px;
                color: #333333;
                border-left: 4px solid #4169e1;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        layout.addWidget(fix_details_label)
        
        main_window.show()
        
        print("预览与封面尺寸一致性修复测试应用启动成功")
        print("请点击按钮开始测试")
        
        return app.exec_()
        
    except Exception as e:
        print(f"测试启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("开始预览与封面尺寸一致性修复测试...")
    return test_preview_cover_consistency()

if __name__ == "__main__":
    sys.exit(main())
