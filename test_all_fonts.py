#!/usr/bin/env python3
"""
全面检查所有字体分类和映射的脚本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QTextEdit, QScrollArea

# 添加项目路径
sys.path.append('.')

def check_all_fonts():
    """检查所有字体"""
    try:
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("全面字体检查工具")
        main_window.setGeometry(100, 100, 1200, 800)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_window.setCentralWidget(scroll_area)
        
        layout = QVBoxLayout(scroll_widget)
        
        # 标题
        title_label = QLabel("🔍 全面字体检查工具")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
                padding: 20px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        layout.addWidget(title_label)
        
        # 字体分类定义
        font_categories = {
            "现代字体": {
                "fonts": ["Microsoft YaHei", "PingFang SC", "Noto Sans CJK SC", "Source Han Sans SC"],
                "icon": "🔤"
            },
            "创意字体": {
                "fonts": ["造字工房悦黑", "造字工房朗倩", "造字工房版黑", "造字工房尚雅"],
                "icon": "🎨"
            },
            "有趣字体": {
                "fonts": ["华文彩云", "华文琥珀", "华文新魏", "方正舒体"],
                "icon": "😊"
            },
            "书法字体": {
                "fonts": ["楷体", "行书", "隶书", "华文行楷", "华文新魏"],
                "icon": "✍️"
            },
            "力量字体": {
                "fonts": ["黑体", "Microsoft YaHei Bold", "方正粗黑宋简体", "华文中宋"],
                "icon": "💪"
            },
            "卡通字体": {
                "fonts": ["华文彩云", "KaiTi", "华文行楷", "SimSun"],
                "icon": "🎪"
            }
        }
        
        # 字体文件映射
        font_files = {
            # 现代字体
            "Microsoft YaHei": "msyh.ttc",
            "PingFang SC": "msyh.ttc",
            "Noto Sans CJK SC": "msyh.ttc",
            "Source Han Sans SC": "msyh.ttc",
            
            # 创意字体
            "造字工房悦黑": "msyh.ttc",
            "造字工房朗倩": "simkai.ttf",
            "造字工房版黑": "simhei.ttf",
            "造字工房尚雅": "msyh.ttc",
            
            # 有趣字体
            "华文彩云": "STCAIYUN.TTF",
            "华文琥珀": "STHUPO.TTF",
            "华文新魏": "STXINWEI.TTF",
            "方正舒体": "simkai.ttf",
            
            # 书法字体
            "楷体": "simkai.ttf",
            "行书": "STXINGKA.TTF",
            "隶书": "STLITI.TTF",
            "华文行楷": "STXINGKA.TTF",
            "华文新魏": "STXINWEI.TTF",
            
            # 力量字体
            "黑体": "simhei.ttf",
            "Microsoft YaHei Bold": "msyhbd.ttc",
            "方正粗黑宋简体": "simhei.ttf",
            "华文中宋": "STZHONGS.TTF",
            
            # 卡通字体
            "华文彩云": "STCAIYUN.TTF",
            "KaiTi": "simkai.ttf",
            "华文行楷": "STXINGKA.TTF",
            "SimSun": "simsun.ttc"
        }
        
        # 检查结果显示
        def create_font_check_report():
            report = []
            total_fonts = 0
            available_fonts = 0
            
            for category_name, category_info in font_categories.items():
                report.append(f"\n{'='*60}")
                report.append(f"{category_info['icon']} {category_name}")
                report.append(f"{'='*60}")
                
                for font_name in category_info['fonts']:
                    total_fonts += 1
                    
                    # 检查是否有映射
                    if font_name in font_files:
                        font_file = font_files[font_name]
                        font_path = f"C:/Windows/Fonts/{font_file}"
                        
                        # 检查文件是否存在
                        if os.path.exists(font_path):
                            status = "✅ 可用"
                            available_fonts += 1
                        else:
                            status = "❌ 文件不存在"
                        
                        report.append(f"  {font_name}")
                        report.append(f"    映射: {font_file}")
                        report.append(f"    路径: {font_path}")
                        report.append(f"    状态: {status}")
                    else:
                        report.append(f"  {font_name}")
                        report.append(f"    映射: ❌ 无映射")
                        report.append(f"    状态: ❌ 无法使用")
                    
                    report.append("")
            
            # 添加统计信息
            report.insert(0, f"📊 字体检查统计")
            report.insert(1, f"总字体数: {total_fonts}")
            report.insert(2, f"可用字体: {available_fonts}")
            report.insert(3, f"可用率: {available_fonts/total_fonts*100:.1f}%")
            report.insert(4, "")
            
            return "\n".join(report)
        
        # 显示检查报告
        report_text = create_font_check_report()
        
        report_display = QTextEdit()
        report_display.setPlainText(report_text)
        report_display.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 2px solid #34495e;
                border-radius: 8px;
                padding: 10px;
            }
        """)
        report_display.setMinimumHeight(500)
        layout.addWidget(report_display)
        
        # 测试按钮
        test_button = QPushButton("🎨 打开水印配置对话框进行实际测试")
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        def open_watermark_dialog():
            try:
                from app.dialogs.video_processor_dialog import CoverWatermarkDialog
                dialog = CoverWatermarkDialog(main_window)
                
                print("=" * 80)
                print("开始实际字体测试")
                print("=" * 80)
                print("请在对话框中测试每个字体分类的字体切换效果")
                print("观察预览区域的字体变化和控制台输出")
                
                dialog.exec_()
            except Exception as e:
                print(f"打开对话框失败: {e}")
        
        test_button.clicked.connect(open_watermark_dialog)
        layout.addWidget(test_button)
        
        # 使用说明
        usage_label = QLabel("""
📋 使用说明：

1. 上方显示了所有字体分类的详细检查结果
2. ✅ 可用：字体文件存在，可以正常使用
3. ❌ 文件不存在：有映射但字体文件不存在，会使用替代字体
4. ❌ 无映射：字体没有文件映射，无法使用

点击测试按钮可以打开水印配置对话框进行实际测试。
建议重点测试标记为"可用"的字体，这些字体应该能显示不同的效果。
        """)
        usage_label.setStyleSheet("""
            QLabel {
                background-color: #f39c12;
                color: white;
                padding: 15px;
                border-radius: 8px;
                font-size: 14px;
                margin: 10px;
            }
        """)
        layout.addWidget(usage_label)
        
        main_window.show()
        
        print("全面字体检查工具启动成功")
        print("请查看窗口中的详细检查报告")
        
        return app.exec_()
        
    except Exception as e:
        print(f"检查失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("开始全面检查所有字体...")
    return check_all_fonts()

if __name__ == "__main__":
    sys.exit(main())
