#!/usr/bin/env python3
"""
测试多行水印控件启用修复的脚本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QDialog

# 添加项目路径
sys.path.append('.')

def test_multiline_controls_fix():
    """测试多行水印控件启用修复"""
    try:
        from app.dialogs.video_processor_dialog import WatermarkConfigDialog, WatermarkConfig
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("多行水印控件启用修复测试")
        main_window.setGeometry(100, 100, 1000, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("""
🔧 多行水印控件启用修复测试

问题描述：
启动多行水印后调整不了参数（颜色、字体、位置等）

问题原因：
update_multiline_ui_state 方法只启用了多行相关控件，
没有启用基本的水印参数控件

修复内容：
✅ 在多行模式启用时，确保所有水印参数控件都可用
✅ 字体设置控件：字体选择、字体大小
✅ 颜色设置控件：文字颜色、阴影颜色、描边颜色
✅ 位置设置控件：X/Y位置数值框和滑块
✅ 效果设置控件：透明度、阴影、描边
✅ 多行专用控件：行选择器、添加/删除行

测试步骤：
1. 打开水印配置对话框
2. 启用"多行水印模式"
3. 检查所有控件是否可用（不是灰色禁用状态）
4. 尝试调整各种参数：
   - 字体和字体大小
   - 文字颜色
   - 位置设置
   - 透明度
   - 阴影和描边效果
5. 观察预览是否正确更新

预期效果：
- 所有水印参数控件都应该可用
- 参数调整应该立即反映在预览中
- 每行可以独立设置不同参数
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #fff8dc;
                padding: 20px;
                border-radius: 8px;
                font-size: 14px;
                line-height: 1.6;
                border: 2px solid #daa520;
                color: #8b4513;
            }
        """)
        layout.addWidget(info_label)
        
        # 添加测试按钮
        test_button = QPushButton("🔧 测试多行水印控件启用修复")
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #daa520;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #b8860b;
            }
        """)
        
        def open_dialog():
            print("=" * 80)
            print("开始多行水印控件启用修复测试")
            print("=" * 80)
            
            try:
                # 创建水印配置
                config = WatermarkConfig()
                config.enabled = True
                config.multi_line_enabled = False  # 先设置为单行模式
                
                print("✅ 水印配置创建成功")
                print(f"初始模式: {'多行' if config.multi_line_enabled else '单行'}")
                
                # 打开配置对话框
                dialog = WatermarkConfigDialog(config, main_window)
                print("✅ 对话框创建成功")
                
                print("\n🎯 请按以下步骤测试控件启用:")
                print("1. 观察当前单行模式下所有控件都可用")
                print("2. 勾选'启用多行水印模式'")
                print("3. 检查以下控件是否仍然可用（不是灰色）:")
                
                print("\n📋 需要检查的控件:")
                print("   🔤 字体设置:")
                print("     - 字体选择下拉框")
                print("     - 字体大小数值框和滑块")
                
                print("   🎨 颜色设置:")
                print("     - 文字颜色按钮")
                print("     - 阴影颜色按钮")
                print("     - 描边颜色按钮")
                
                print("   📍 位置设置:")
                print("     - X位置数值框和滑块")
                print("     - Y位置数值框和滑块")
                
                print("   ✨ 效果设置:")
                print("     - 透明度滑块")
                print("     - 阴影开关和参数")
                print("     - 描边开关和参数")
                
                print("   📝 多行设置:")
                print("     - 行选择器")
                print("     - 添加行按钮")
                print("     - 删除行按钮")
                print("     - 字符数设置")
                
                print("\n4. 尝试调整各种参数，观察预览变化")
                print("5. 切换不同行，为每行设置不同参数")
                
                result = dialog.exec_()
                
                if result == QDialog.Accepted:
                    print("\n✅ 控件启用测试完成")
                    
                    if config.multi_line_enabled:
                        print("最终配置（多行模式）:")
                        for i, line_config in enumerate(config.multi_line_configs):
                            print(f"第{i+1}行:")
                            print(f"  字体: {line_config.font_family} {line_config.font_size}px")
                            print(f"  颜色: {line_config.text_color}")
                            print(f"  位置: ({line_config.position_x}%, {line_config.position_y}%)")
                            print(f"  透明度: {line_config.opacity}%")
                            print(f"  阴影: {'启用' if line_config.shadow_enabled else '禁用'}")
                            print(f"  描边: {'启用' if line_config.stroke_enabled else '禁用'}")
                    else:
                        print("最终配置（单行模式）:")
                        print(f"字体: {config.font_family} {config.font_size}px")
                        print(f"颜色: {config.text_color}")
                        print(f"位置: ({config.position_x}%, {config.position_y}%)")
                        print(f"透明度: {config.opacity}%")
                        print(f"阴影: {'启用' if config.shadow_enabled else '禁用'}")
                        print(f"描边: {'启用' if config.stroke_enabled else '禁用'}")
                        
                else:
                    print("\n❌ 控件启用测试已取消")
                
            except Exception as e:
                print(f"❌ 测试过程中出现错误: {e}")
                import traceback
                traceback.print_exc()
            
            print("=" * 80)
        
        test_button.clicked.connect(open_dialog)
        layout.addWidget(test_button)
        
        # 添加修复详情
        fix_details_label = QLabel("""
🔧 修复详情：

修复前的问题：
❌ update_multiline_ui_state 只启用多行专用控件
❌ 基本水印参数控件在多行模式下被禁用
❌ 用户无法调整字体、颜色、位置等参数

修复后的改进：
✅ 明确启用所有基本水印参数控件
✅ 字体控件：font_combo, font_size_spinbox, font_size_slider
✅ 颜色控件：text_color_button, text_color_label
✅ 位置控件：position_x/y_spinbox, position_x/y_slider
✅ 透明度控件：opacity_slider
✅ 阴影控件：shadow_checkbox, shadow_color_button, shadow_offset_x/y_spinbox
✅ 描边控件：stroke_checkbox, stroke_color_button, stroke_width_spinbox/slider

修复原理：
在多行模式启用时，除了启用多行专用控件外，
还要确保所有基本水印参数控件都设置为可用状态。
这样用户就可以为每一行独立调整所有参数。

技术实现：
通过 hasattr 检查控件是否存在，然后调用 setEnabled(True)
确保在多行模式下所有参数控件都可用。
        """)
        fix_details_label.setStyleSheet("""
            QLabel {
                background-color: #f5f5f5;
                padding: 15px;
                border-radius: 8px;
                font-size: 12px;
                color: #333333;
                border-left: 4px solid #daa520;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        layout.addWidget(fix_details_label)
        
        main_window.show()
        
        print("多行水印控件启用修复测试应用启动成功")
        print("请点击按钮开始测试")
        
        return app.exec_()
        
    except Exception as e:
        print(f"测试启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("开始多行水印控件启用修复测试...")
    return test_multiline_controls_fix()

if __name__ == "__main__":
    sys.exit(main())
