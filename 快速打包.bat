@echo off
chcp 65001 >nul
title 头条内容社交工具 - 快速打包

echo.
echo ████████╗ ██████╗ ██╗   ██╗████████╗██╗ █████╗  ██████╗ 
echo ╚══██╔══╝██╔═══██╗██║   ██║╚══██╔══╝██║██╔══██╗██╔═══██╗
echo    ██║   ██║   ██║██║   ██║   ██║   ██║███████║██║   ██║
echo    ██║   ██║   ██║██║   ██║   ██║   ██║██╔══██║██║   ██║
echo    ██║   ╚██████╔╝╚██████╔╝   ██║   ██║██║  ██║╚██████╔╝
echo    ╚═╝    ╚═════╝  ╚═════╝    ╚═╝   ╚═╝╚═╝  ╚═╝ ╚═════╝ 
echo.
echo                    头条内容社交工具 - 快速打包
echo                    ========================
echo                   优化版本：排除用户数据，减小文件大小
echo.

echo 📍 当前目录: %CD%
echo ⏰ 开始时间: %DATE% %TIME%
echo.

REM 检查Python环境
echo 🔍 检查Python环境...
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Python未安装或未添加到PATH环境变量
    echo 请安装Python 3.7+并确保添加到PATH
    goto :error
)

python --version
echo.

REM 检查必要文件
echo 🔍 检查必要文件...
if not exist "main.py" (
    echo ❌ 找不到main.py文件
    goto :error
)
echo ✅ main.py

if not exist "app\main_window.py" (
    echo ❌ 找不到app\main_window.py文件
    goto :error
)
echo ✅ app\main_window.py

if not exist "requirements.txt" (
    echo ❌ 找不到requirements.txt文件
    goto :error
)
echo ✅ requirements.txt
echo.

REM 检查PyInstaller
echo 📦 检查PyInstaller...
python -c "import PyInstaller; print('PyInstaller版本:', PyInstaller.__version__)" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ PyInstaller未安装，正在安装...
    python -m pip install pyinstaller
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ PyInstaller安装失败
        goto :error
    )
    echo ✅ PyInstaller安装成功
) else (
    echo ✅ PyInstaller已安装
)
echo.

REM 开始打包
echo 🚀 开始打包...
echo ================================================
python quick_build.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ================================================
    echo 🎉 打包完成！
    echo ⏰ 完成时间: %DATE% %TIME%
    echo 📁 输出目录: %CD%\dist
    echo.
    echo 打包成功！可执行文件已生成到 dist 目录中。
) else (
    echo.
    echo ================================================
    echo ❌ 打包失败！
    echo 请检查上面的错误信息。
    goto :error
)

goto :end

:error
echo.
echo ================================================
echo ❌ 操作失败！
echo 请检查错误信息并重试。

:end
echo.
echo 按任意键退出...
pause >nul
