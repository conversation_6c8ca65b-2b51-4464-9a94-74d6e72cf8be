{"input_dir": "E:/CRTubeGet Downloaded", "output_dir": "D:/头条全自动/视频搬运/已处理视频", "cover_dir": "D:/头条全自动/视频搬运/已处理封面", "thread_count": "16", "overwrite_files": true, "generate_cover": true, "watermark_enabled": true, "watermark_config": {"enabled": true, "font_family": "华文彩云", "font_category": "有趣字体", "font_size": 56, "text_color": "#FFFF00", "shadow_enabled": true, "shadow_color": "#000000", "shadow_offset_x": 6, "shadow_offset_y": 2, "stroke_enabled": false, "stroke_color": "#000000", "stroke_width": 2, "position_x": 8, "position_y": 98, "opacity": 86, "single_char_count": 6, "multi_line_enabled": true, "multi_line_configs": [{"enabled": true, "char_count": 5, "font_family": "华文彩云", "font_size": 56, "text_color": "#FFFF00", "shadow_enabled": true, "shadow_color": "#000000", "shadow_offset_x": 6, "shadow_offset_y": 2, "stroke_enabled": false, "stroke_color": "#000000", "stroke_width": 2, "position_x": 8, "position_y": 90, "opacity": 86}]}, "duration_filter_enabled": true, "min_duration": 3, "max_duration": 0, "ai_rewrite_enabled": false, "agent_id": "WXR2oUKfqsAm", "token": "O4tOE7n389DRVpFtHfwUyoNwzPRvGJVY", "save_settings": true, "auto_scroll": false, "last_saved": "2025-07-26 13:09:07"}