#!/usr/bin/env python3
"""
测试模糊半径功能已删除的脚本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QDialog

# 添加项目路径
sys.path.append('.')

def test_no_blur_radius():
    """测试模糊半径功能已删除"""
    try:
        from app.dialogs.video_processor_dialog import WatermarkConfigDialog, WatermarkConfig
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("模糊半径功能删除验证")
        main_window.setGeometry(100, 100, 900, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("""
🗑️ 模糊半径功能删除验证

删除内容：
✅ 删除了配置类中的 shadow_blur 属性
✅ 删除了界面中的模糊半径控件
✅ 删除了模糊半径滑块和数值框
✅ 删除了模糊效果的绘制逻辑
✅ 简化了阴影绘制为简单偏移

验证要点：
1. 阴影设置组中不应该有"模糊半径"控件
2. 阴影效果应该是简单的偏移，没有模糊
3. 配置保存和加载不应该包含模糊半径
4. 多行模式下每行的阴影也不应该有模糊

测试步骤：
1. 打开水印配置对话框
2. 检查阴影设置组，确认没有模糊半径控件
3. 启用阴影效果，观察预览
4. 切换到多行模式，测试每行的阴影效果
5. 确认阴影是清晰的偏移，没有模糊效果
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #ffe4e1;
                padding: 20px;
                border-radius: 8px;
                font-size: 14px;
                line-height: 1.6;
                border: 2px solid #ff6347;
                color: #8b0000;
            }
        """)
        layout.addWidget(info_label)
        
        # 添加测试按钮
        test_button = QPushButton("🔍 验证模糊半径功能已删除")
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #ff6347;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #ff4500;
            }
        """)
        
        def open_dialog():
            print("=" * 80)
            print("开始模糊半径功能删除验证")
            print("=" * 80)
            
            try:
                # 创建水印配置
                config = WatermarkConfig()
                config.enabled = True
                
                print("✅ 水印配置创建成功")
                
                # 检查配置类是否还有shadow_blur属性
                has_blur_attr = hasattr(config, 'shadow_blur')
                print(f"配置类是否还有shadow_blur属性: {'是' if has_blur_attr else '否'}")
                
                if has_blur_attr:
                    print("❌ 警告: 配置类中仍然存在shadow_blur属性")
                else:
                    print("✅ 确认: 配置类中已删除shadow_blur属性")
                
                # 检查多行配置类
                if len(config.multi_line_configs) > 0:
                    line_config = config.multi_line_configs[0]
                    has_line_blur_attr = hasattr(line_config, 'shadow_blur')
                    print(f"多行配置类是否还有shadow_blur属性: {'是' if has_line_blur_attr else '否'}")
                    
                    if has_line_blur_attr:
                        print("❌ 警告: 多行配置类中仍然存在shadow_blur属性")
                    else:
                        print("✅ 确认: 多行配置类中已删除shadow_blur属性")
                
                # 打开配置对话框
                dialog = WatermarkConfigDialog(config, main_window)
                print("✅ 对话框创建成功")
                
                # 检查界面控件
                has_blur_spinbox = hasattr(dialog, 'shadow_blur_spinbox')
                has_blur_slider = hasattr(dialog, 'shadow_blur_slider')
                
                print(f"界面是否还有shadow_blur_spinbox控件: {'是' if has_blur_spinbox else '否'}")
                print(f"界面是否还有shadow_blur_slider控件: {'是' if has_blur_slider else '否'}")
                
                if has_blur_spinbox or has_blur_slider:
                    print("❌ 警告: 界面中仍然存在模糊半径控件")
                else:
                    print("✅ 确认: 界面中已删除模糊半径控件")
                
                print("\n请在对话框中验证:")
                print("1. 阴影设置组中没有'模糊半径'相关控件")
                print("2. 启用阴影效果，观察预览中的阴影是清晰的偏移")
                print("3. 在多行模式下，每行的阴影也是清晰的偏移")
                print("4. 没有任何模糊效果")
                
                result = dialog.exec_()
                
                if result == QDialog.Accepted:
                    print("\n✅ 验证完成")
                    print("阴影效果应该是:")
                    print("- 清晰的文字偏移")
                    print("- 没有模糊边缘")
                    print("- 简单高效的渲染")
                else:
                    print("\n❌ 验证已取消")
                
            except Exception as e:
                print(f"❌ 验证过程中出现错误: {e}")
                import traceback
                traceback.print_exc()
            
            print("=" * 80)
        
        test_button.clicked.connect(open_dialog)
        layout.addWidget(test_button)
        
        # 添加删除详情
        details_label = QLabel("""
📋 删除详情：

🗑️ 配置类删除项：
  ❌ WatermarkConfig.shadow_blur
  ❌ MultiLineWatermarkConfig.shadow_blur
  ❌ to_dict() 中的 shadow_blur 字段
  ❌ from_dict() 中的 shadow_blur 处理

🗑️ 界面控件删除项：
  ❌ shadow_blur_spinbox (数值框)
  ❌ shadow_blur_slider (滑块)
  ❌ 模糊半径标签和布局
  ❌ 控件启用/禁用逻辑

🗑️ 绘制逻辑删除项：
  ❌ GaussianBlur 模糊滤镜
  ❌ 模糊阴影图层创建
  ❌ ImageFilter 导入
  ❌ 复杂的模糊渲染逻辑

✅ 保留的功能：
  ✅ 简单阴影偏移
  ✅ 阴影颜色设置
  ✅ 阴影透明度
  ✅ X/Y 偏移调整

🎯 简化效果：
  - 阴影渲染更快
  - 代码更简洁
  - 界面更简单
  - 功能更专注
        """)
        details_label.setStyleSheet("""
            QLabel {
                background-color: #f0f8ff;
                padding: 15px;
                border-radius: 8px;
                font-size: 12px;
                color: #191970;
                border-left: 4px solid #4169e1;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        layout.addWidget(details_label)
        
        main_window.show()
        
        print("模糊半径功能删除验证应用启动成功")
        print("请点击按钮开始验证")
        
        return app.exec_()
        
    except Exception as e:
        print(f"验证启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("开始模糊半径功能删除验证...")
    return test_no_blur_radius()

if __name__ == "__main__":
    sys.exit(main())
