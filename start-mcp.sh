#!/bin/bash

echo "========================================"
echo "       启动 MCP 服务测试"
echo "========================================"
echo

echo "选择要启动的 MCP 服务："
echo "1. mcp-feedback-enhanced (桌面应用)"
echo "2. mcp-feedback-enhanced (Web UI)"
echo "3. Context7 文档查询"
echo "4. 同时测试两个服务"
echo "5. 检查 MCP 工具状态"
echo

read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        echo
        echo "启动 mcp-feedback-enhanced 桌面应用..."
        export MCP_DESKTOP_MODE=true
        export MCP_LANGUAGE=zh-CN
        export MCP_DEBUG=false
        uvx mcp-feedback-enhanced@latest test --desktop
        ;;
    2)
        echo
        echo "启动 mcp-feedback-enhanced Web UI..."
        export MCP_DESKTOP_MODE=false
        export MCP_LANGUAGE=zh-CN
        export MCP_DEBUG=false
        uvx mcp-feedback-enhanced@latest test --web
        ;;
    3)
        echo
        echo "启动 Context7 文档查询服务..."
        npx -y @upstash/context7-mcp@latest
        ;;
    4)
        echo
        echo "检查两个服务的版本信息..."
        echo
        echo "=== mcp-feedback-enhanced ==="
        uvx mcp-feedback-enhanced@latest version
        echo
        echo "=== Context7 ==="
        echo "Context7 MCP 服务可用"
        echo
        echo "请在 AI 平台中测试 MCP 工具连接"
        ;;
    5)
        echo
        echo "检查 MCP 工具状态..."
        echo
        echo "=== 检查 mcp-feedback-enhanced ==="
        uvx mcp-feedback-enhanced@latest version
        echo
        echo "=== 检查端口占用 ==="
        lsof -i :8765 2>/dev/null || echo "端口 8765 未被占用"
        echo
        echo "=== 检查 Node.js (Context7 需要) ==="
        node --version
        npm --version
        ;;
    *)
        echo "无效选择，请重新运行脚本"
        ;;
esac

echo
read -p "按 Enter 键退出..."
