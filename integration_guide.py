#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
集成指南：如何将增强加密集成到现有项目
"""

import os
import json
from typing import Optional, Dict

# 导入增强加密模块
from enhanced_cookie_encryption import EnhancedCookieEncryption
from multi_layer_encryption import MultiLayerCookieEncryption
from server_validated_encryption import ServerValidatedEncryption

class CookieEncryptionManager:
    """统一的Cookie加密管理器"""
    
    def __init__(self, encryption_level: str = "enhanced"):
        """
        初始化加密管理器
        
        Args:
            encryption_level: 加密级别
                - "basic": 原始加密（向后兼容）
                - "enhanced": 硬件绑定加密
                - "multi_layer": 多层加密
                - "server_validated": 服务器验证加密
        """
        self.encryption_level = encryption_level
        self.encryptors = {
            "enhanced": EnhancedCookieEncryption(),
            "multi_layer": MultiLayerCookieEncryption(),
            "server_validated": ServerValidatedEncryption()
        }
        
        # 向后兼容：导入原始加密器
        try:
            from app.utils.cookie_encryption import CookieEncryption
            self.encryptors["basic"] = CookieEncryption()
        except ImportError:
            pass
    
    def encrypt_cookie_file(self, input_file: str, output_file: Optional[str] = None,
                           encryption_level: Optional[str] = None) -> bool:
        """
        加密Cookie文件
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
            encryption_level: 指定加密级别（覆盖默认设置）
        """
        level = encryption_level or self.encryption_level
        encryptor = self.encryptors.get(level)
        
        if not encryptor:
            raise ValueError(f"不支持的加密级别: {level}")
        
        return encryptor.encrypt_cookie_file(input_file, output_file)
    
    def decrypt_cookie_file(self, input_file: str) -> Optional[Dict]:
        """
        智能解密Cookie文件（自动检测加密类型）
        
        Args:
            input_file: 加密文件路径
            
        Returns:
            解密后的Cookie数据
        """
        try:
            # 读取文件
            with open(input_file, 'r', encoding='utf-8') as f:
                file_data = json.load(f)
            
            # 检测加密类型
            if file_data.get("server_validated", False):
                encryptor = self.encryptors["server_validated"]
            elif file_data.get("multi_layer", False):
                encryptor = self.encryptors["multi_layer"]
            elif file_data.get("machine_bound", False):
                encryptor = self.encryptors["enhanced"]
            elif file_data.get("encrypted", False):
                encryptor = self.encryptors.get("basic")
            else:
                # 未加密文件
                return file_data
            
            if not encryptor:
                raise Exception("找不到对应的解密器")
            
            return encryptor.decrypt_cookie_data(file_data)
            
        except Exception as e:
            print(f"解密失败: {str(e)}")
            return None
    
    def batch_upgrade_encryption(self, cookie_dir: str, target_level: str = "enhanced") -> Dict:
        """
        批量升级Cookie文件加密级别
        
        Args:
            cookie_dir: Cookie文件目录
            target_level: 目标加密级别
            
        Returns:
            升级结果统计
        """
        results = {
            "total": 0,
            "success": 0,
            "failed": 0,
            "skipped": 0,
            "errors": []
        }
        
        if not os.path.exists(cookie_dir):
            results["errors"].append(f"目录不存在: {cookie_dir}")
            return results
        
        # 创建备份目录
        backup_dir = os.path.join(cookie_dir, "encryption_upgrade_backup")
        os.makedirs(backup_dir, exist_ok=True)
        
        # 遍历Cookie文件
        for filename in os.listdir(cookie_dir):
            if not filename.endswith(('.json', '.txt')):
                continue
            
            file_path = os.path.join(cookie_dir, filename)
            backup_path = os.path.join(backup_dir, filename)
            
            results["total"] += 1
            
            try:
                # 备份原文件
                import shutil
                shutil.copy2(file_path, backup_path)
                
                # 读取并检测当前加密级别
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                
                if not content:
                    results["skipped"] += 1
                    continue
                
                try:
                    file_data = json.loads(content)
                    current_level = self._detect_encryption_level(file_data)
                except json.JSONDecodeError:
                    # 文本格式Cookie，需要加密
                    current_level = "none"
                    file_data = {"cookies": {"raw": content}}
                
                # 检查是否需要升级
                if current_level == target_level:
                    results["skipped"] += 1
                    continue
                
                # 解密原数据
                if current_level != "none":
                    original_data = self.decrypt_cookie_file(file_path)
                    if not original_data:
                        results["failed"] += 1
                        results["errors"].append(f"解密失败: {filename}")
                        continue
                else:
                    original_data = file_data
                
                # 使用新级别加密
                success = self.encrypt_cookie_file_data(original_data, file_path, target_level)
                
                if success:
                    results["success"] += 1
                    print(f"✅ 升级成功: {filename} ({current_level} -> {target_level})")
                else:
                    results["failed"] += 1
                    results["errors"].append(f"加密失败: {filename}")
                    # 恢复备份
                    shutil.copy2(backup_path, file_path)
                
            except Exception as e:
                results["failed"] += 1
                results["errors"].append(f"{filename}: {str(e)}")
                
                # 恢复备份
                try:
                    shutil.copy2(backup_path, file_path)
                except:
                    pass
        
        return results
    
    def encrypt_cookie_file_data(self, cookie_data: Dict, output_file: str, 
                                encryption_level: str) -> bool:
        """加密Cookie数据并保存到文件"""
        try:
            encryptor = self.encryptors.get(encryption_level)
            if not encryptor:
                return False
            
            encrypted_data = encryptor.encrypt_cookie_data(cookie_data)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(encrypted_data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"加密数据失败: {str(e)}")
            return False
    
    def _detect_encryption_level(self, file_data: Dict) -> str:
        """检测文件的加密级别"""
        if not file_data.get("encrypted", False):
            return "none"
        elif file_data.get("server_validated", False):
            return "server_validated"
        elif file_data.get("multi_layer", False):
            return "multi_layer"
        elif file_data.get("machine_bound", False):
            return "enhanced"
        else:
            return "basic"

# 集成示例
def integrate_with_existing_project():
    """集成到现有项目的示例"""
    
    # 1. 创建加密管理器
    encryption_manager = CookieEncryptionManager(encryption_level="enhanced")
    
    # 2. 批量升级现有Cookie文件
    cookie_dir = "accounts"  # 替换为实际路径
    if os.path.exists(cookie_dir):
        print("🔄 开始批量升级Cookie加密...")
        results = encryption_manager.batch_upgrade_encryption(cookie_dir, "enhanced")
        
        print(f"📊 升级结果:")
        print(f"  总文件数: {results['total']}")
        print(f"  成功升级: {results['success']}")
        print(f"  跳过文件: {results['skipped']}")
        print(f"  失败文件: {results['failed']}")
        
        if results['errors']:
            print("❌ 错误详情:")
            for error in results['errors'][:5]:  # 只显示前5个错误
                print(f"  - {error}")
    
    # 3. 测试新加密功能
    test_cookie_data = {
        "accountId": "test_account",
        "remark": "测试账号",
        "cookies": {
            "sessionid": "test_session_value",
            "csrf_token": "test_csrf_value"
        }
    }
    
    # 测试不同加密级别
    for level in ["enhanced", "multi_layer"]:
        try:
            print(f"\n🧪 测试 {level} 加密...")
            
            # 加密
            test_file = f"test_{level}.json"
            success = encryption_manager.encrypt_cookie_file_data(
                test_cookie_data, test_file, level
            )
            
            if success:
                print(f"✅ {level} 加密成功")
                
                # 解密验证
                decrypted = encryption_manager.decrypt_cookie_file(test_file)
                if decrypted and decrypted == test_cookie_data:
                    print(f"✅ {level} 解密验证成功")
                else:
                    print(f"❌ {level} 解密验证失败")
                
                # 清理测试文件
                os.remove(test_file)
            else:
                print(f"❌ {level} 加密失败")
                
        except Exception as e:
            print(f"❌ {level} 测试失败: {str(e)}")

# 修改现有代码的建议
def modify_existing_code_example():
    """修改现有代码的示例"""
    
    # 原始代码（示例）
    """
    # 原来的代码
    from app.utils.cookie_encryption import CookieEncryption
    
    encryptor = CookieEncryption()
    encrypted_data = encryptor.encrypt_cookie_data(cookie_data)
    """
    
    # 新代码（建议）
    """
    # 新的代码
    from integration_guide import CookieEncryptionManager
    
    # 创建管理器，默认使用增强加密
    encryption_manager = CookieEncryptionManager(encryption_level="enhanced")
    
    # 加密（自动使用硬件绑定）
    success = encryption_manager.encrypt_cookie_file_data(
        cookie_data, output_file, "enhanced"
    )
    
    # 解密（自动检测加密类型）
    decrypted_data = encryption_manager.decrypt_cookie_file(input_file)
    """

if __name__ == "__main__":
    print("🚀 开始集成测试...")
    integrate_with_existing_project()
    print("\n📝 代码修改建议:")
    modify_existing_code_example()
