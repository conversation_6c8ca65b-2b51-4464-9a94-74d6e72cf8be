@echo off
chcp 65001 >nul
title 头条内容社交工具 - 环境更新

echo.
echo ========================================
echo    头条内容社交工具 - 环境更新脚本
echo ========================================
echo.

:: 检查虚拟环境是否存在
if not exist "venv\Scripts\activate.bat" (
    echo ❌ 未找到虚拟环境，请先创建虚拟环境
    echo.
    echo 创建虚拟环境命令:
    echo python -m venv venv
    echo.
    pause
    exit /b 1
)

:: 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat

:: 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python不可用，请检查虚拟环境
    pause
    exit /b 1
)

echo ✅ 虚拟环境激活成功
echo.

:: 显示当前Python版本
echo 🐍 当前Python版本:
python --version
echo.

:: 更新pip
echo 🔧 更新pip...
python -m pip install --upgrade pip
echo.

:: 显示选项
echo 📦 请选择更新方式:
echo 1. 快速更新 (仅更新已变更的包)
echo 2. 完整更新 (重新安装所有依赖)
echo 3. 环境检查 (检查当前环境状态)
echo 4. 退出
echo.

set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" goto quick_update
if "%choice%"=="2" goto full_update
if "%choice%"=="3" goto env_check
if "%choice%"=="4" goto end
goto invalid_choice

:quick_update
echo.
echo 🔄 开始快速更新...
echo.

echo 📦 更新PyQt5...
python -m pip install PyQt5==5.15.11

echo 📦 更新selenium...
python -m pip install selenium==4.27.1

echo 📦 更新openpyxl...
python -m pip install openpyxl==3.1.5

echo 📦 更新numpy...
python -m pip install numpy==2.2.1

echo.
echo ✅ 快速更新完成
goto check_result

:full_update
echo.
echo 🔄 开始完整更新...
echo.

if not exist "requirements.txt" (
    echo ❌ 未找到requirements.txt文件
    pause
    exit /b 1
)

echo 📦 安装所有依赖包...
python -m pip install -r requirements.txt

echo.
echo ✅ 完整更新完成
goto check_result

:env_check
echo.
echo 🔍 开始环境检查...
echo.

if exist "quick_env_check.py" (
    python quick_env_check.py
) else (
    echo ❌ 未找到环境检查脚本
)

echo.
pause
goto menu

:check_result
echo.
echo 🔍 验证更新结果...
echo.

if exist "quick_env_check.py" (
    python quick_env_check.py
) else (
    echo 📋 手动检查关键包:
    echo.
    
    echo 检查PyQt5...
    python -c "import PyQt5.QtCore; print('✅ PyQt5:', PyQt5.QtCore.PYQT_VERSION_STR)" 2>nul || echo "❌ PyQt5 导入失败"
    
    echo 检查selenium...
    python -c "import selenium; print('✅ selenium:', selenium.__version__)" 2>nul || echo "❌ selenium 导入失败"
    
    echo 检查openpyxl...
    python -c "import openpyxl; print('✅ openpyxl:', openpyxl.__version__)" 2>nul || echo "❌ openpyxl 导入失败"
    
    echo 检查numpy...
    python -c "import numpy; print('✅ numpy:', numpy.__version__)" 2>nul || echo "❌ numpy 导入失败"
)

echo.
echo 🎉 更新完成！
echo 💡 建议重启应用程序以确保更新生效
echo.
pause
goto end

:invalid_choice
echo.
echo ❌ 无效选择，请重新输入
echo.
pause
goto menu

:menu
cls
echo.
echo ========================================
echo    头条内容社交工具 - 环境更新脚本
echo ========================================
echo.
goto quick_update

:end
echo.
echo 👋 感谢使用环境更新脚本
pause
