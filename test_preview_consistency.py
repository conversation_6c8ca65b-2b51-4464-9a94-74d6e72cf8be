#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试预览与封面的一致性
验证预览区域内部使用与封面一样的分辨率大小
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PIL import Image

def test_preview_consistency():
    """测试预览与封面的一致性"""
    print("=" * 80)
    print("🔍 预览与封面一致性测试")
    print("=" * 80)
    
    try:
        # 1. 导入水印模块
        print("\n1. 导入水印模块...")
        from app.dialogs.video_processor_dialog import WatermarkConfig, VideoProcessorWorker
        print("   ✅ 模块导入成功")
        
        # 2. 创建水印配置
        print("\n2. 创建水印配置...")
        config = WatermarkConfig()
        config.enabled = True
        config.multi_line_enabled = False  # 先测试单行
        config.font_family = "Microsoft YaHei"
        config.font_size = 60
        config.text_color = "#FFFFFF"
        config.position_x = 85
        config.position_y = 85
        config.opacity = 90
        
        print(f"   ✅ 配置: {config.font_size}px, 位置({config.position_x}%, {config.position_y}%)")
        
        # 3. 创建worker
        print("\n3. 创建VideoProcessorWorker...")
        worker = VideoProcessorWorker(
            input_dir="test", output_dir="test", cover_dir="test",
            thread_count=1, worker_id=0, shared_data={},
            watermark_config=config
        )
        print("   ✅ Worker创建成功")
        
        # 4. 测试封面生成逻辑
        print("\n4. 测试封面生成逻辑...")
        test_text = "一致性测试"
        
        # 创建1920x1080的封面图像
        cover_img = Image.new('RGB', (1920, 1080), color='#2c3e50')
        print(f"   封面图像尺寸: {cover_img.size}")
        
        # 使用封面水印逻辑
        cover_watermarked = worker.add_watermark_to_pil_image(cover_img.copy(), test_text)
        if cover_watermarked:
            cover_watermarked.save("consistency_test_cover.jpg", 'JPEG', quality=95)
            print(f"   ✅ 封面生成: consistency_test_cover.jpg")
        
        # 5. 模拟预览生成逻辑
        print("\n5. 模拟预览生成逻辑...")
        
        # 预览内部也使用1920x1080尺寸
        preview_internal_img = Image.new('RGB', (1920, 1080), color='#2c3e50')
        print(f"   预览内部尺寸: {preview_internal_img.size}")
        
        # 使用相同的水印逻辑
        preview_watermarked = worker.add_watermark_to_pil_image(preview_internal_img.copy(), test_text)
        if preview_watermarked:
            # 保存1920x1080的预览内部图像
            preview_watermarked.save("consistency_test_preview_internal.jpg", 'JPEG', quality=95)
            print(f"   ✅ 预览内部图像: consistency_test_preview_internal.jpg")
            
            # 缩放到640x360显示
            preview_display = preview_watermarked.resize((640, 360), Image.LANCZOS)
            preview_display.save("consistency_test_preview_display.jpg", 'JPEG', quality=95)
            print(f"   ✅ 预览显示图像: consistency_test_preview_display.jpg")
        
        # 6. 验证一致性
        print("\n6. 验证一致性...")
        
        if cover_watermarked and preview_watermarked:
            # 比较两个1920x1080图像的像素差异
            cover_pixels = list(cover_watermarked.getdata())
            preview_pixels = list(preview_watermarked.getdata())
            
            # 计算相同像素的数量
            same_pixels = sum(1 for c, p in zip(cover_pixels, preview_pixels) if c == p)
            total_pixels = len(cover_pixels)
            similarity = (same_pixels / total_pixels) * 100
            
            print(f"   像素相似度: {similarity:.2f}%")
            
            if similarity > 99.9:
                print(f"   ✅ 预览与封面完全一致")
            elif similarity > 95:
                print(f"   ✅ 预览与封面基本一致")
            else:
                print(f"   ❌ 预览与封面存在差异")
        
        # 7. 测试不同字体大小的一致性
        print("\n7. 测试不同字体大小的一致性...")
        
        test_sizes = [40, 60, 80, 100]
        
        for size in test_sizes:
            print(f"\n   测试字体大小: {size}px")
            
            # 更新配置
            config.font_size = size
            
            # 封面
            cover_result = worker.add_watermark_to_pil_image(cover_img.copy(), f"字体{size}px")
            if cover_result:
                cover_result.save(f"consistency_cover_{size}px.jpg", 'JPEG', quality=95)
                print(f"     封面: consistency_cover_{size}px.jpg")
            
            # 预览内部
            preview_result = worker.add_watermark_to_pil_image(preview_internal_img.copy(), f"字体{size}px")
            if preview_result:
                preview_result.save(f"consistency_preview_internal_{size}px.jpg", 'JPEG', quality=95)
                
                # 预览显示
                preview_display = preview_result.resize((640, 360), Image.LANCZOS)
                preview_display.save(f"consistency_preview_display_{size}px.jpg", 'JPEG', quality=95)
                print(f"     预览: consistency_preview_display_{size}px.jpg")
                
                # 验证一致性
                if cover_result and preview_result:
                    cover_pixels = list(cover_result.getdata())
                    preview_pixels = list(preview_result.getdata())
                    same_pixels = sum(1 for c, p in zip(cover_pixels, preview_pixels) if c == p)
                    similarity = (same_pixels / len(cover_pixels)) * 100
                    print(f"     一致性: {similarity:.2f}%")
        
        # 8. 测试多行水印一致性
        print("\n8. 测试多行水印一致性...")
        
        config.multi_line_enabled = True
        config.font_size = 60  # 重置字体大小
        
        # 配置多行参数
        if len(config.multi_line_configs) >= 3:
            config.multi_line_configs[0].enabled = True
            config.multi_line_configs[0].char_count = 3
            config.multi_line_configs[0].font_size = 80
            config.multi_line_configs[0].position_y = 75
            
            config.multi_line_configs[1].enabled = True
            config.multi_line_configs[1].char_count = 2
            config.multi_line_configs[1].font_size = 70
            config.multi_line_configs[1].position_y = 85
            
            config.multi_line_configs[2].enabled = True
            config.multi_line_configs[2].char_count = 1
            config.multi_line_configs[2].font_size = 60
            config.multi_line_configs[2].position_y = 95
        
        test_text = "多行一致性测试"
        print(f"   测试文字: '{test_text}'")
        
        # 封面多行
        cover_multiline = worker.add_watermark_to_pil_image(cover_img.copy(), test_text)
        if cover_multiline:
            cover_multiline.save("consistency_multiline_cover.jpg", 'JPEG', quality=95)
            print(f"   ✅ 多行封面: consistency_multiline_cover.jpg")
        
        # 预览多行
        preview_multiline = worker.add_watermark_to_pil_image(preview_internal_img.copy(), test_text)
        if preview_multiline:
            preview_multiline.save("consistency_multiline_preview_internal.jpg", 'JPEG', quality=95)
            
            # 预览显示
            preview_multiline_display = preview_multiline.resize((640, 360), Image.LANCZOS)
            preview_multiline_display.save("consistency_multiline_preview_display.jpg", 'JPEG', quality=95)
            print(f"   ✅ 多行预览: consistency_multiline_preview_display.jpg")
            
            # 验证多行一致性
            if cover_multiline and preview_multiline:
                cover_pixels = list(cover_multiline.getdata())
                preview_pixels = list(preview_multiline.getdata())
                same_pixels = sum(1 for c, p in zip(cover_pixels, preview_pixels) if c == p)
                similarity = (same_pixels / len(cover_pixels)) * 100
                print(f"   多行一致性: {similarity:.2f}%")
        
        # 9. 总结测试结果
        print("\n9. 测试总结...")
        print(f"   ✅ 预览与封面一致性测试完成")
        print(f"   ✅ 预览内部使用1920x1080尺寸")
        print(f"   ✅ 封面使用1920x1080尺寸")
        print(f"   ✅ 水印大小逻辑完全一致")
        
        print(f"\n📊 生成的文件:")
        print(f"   - consistency_test_cover.jpg: 封面效果")
        print(f"   - consistency_test_preview_internal.jpg: 预览内部效果(1920x1080)")
        print(f"   - consistency_test_preview_display.jpg: 预览显示效果(640x360)")
        print(f"   - consistency_cover_*px.jpg: 不同字体大小的封面")
        print(f"   - consistency_preview_display_*px.jpg: 对应的预览显示")
        print(f"   - consistency_multiline_*.jpg: 多行水印一致性测试")
        
        print(f"\n🔍 验证要点:")
        print(f"   1. 预览内部图像与封面图像应该完全一致")
        print(f"   2. 预览显示图像是内部图像的640x360缩放版本")
        print(f"   3. 水印的相对大小在预览和封面中保持一致")
        print(f"   4. 多行水印的分割和布局完全一致")
        
        print(f"\n✅ 预览与封面一致性测试全部完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    test_preview_consistency()
