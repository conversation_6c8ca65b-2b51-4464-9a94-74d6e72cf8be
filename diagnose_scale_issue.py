#!/usr/bin/env python3
"""
诊断字体大小倍率问题
"""

import sys
import os

# 添加项目路径
sys.path.append('.')

def diagnose_scale_issue():
    """诊断倍率问题"""
    print("=" * 80)
    print("诊断字体大小倍率问题")
    print("=" * 80)
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # 1. 测试不同字体大小的实际渲染效果
        print("1. 测试字体大小渲染效果...")
        
        test_sizes = [36, 48, 60, 72, 84, 96, 100]
        test_text = "测试文字"
        
        for size in test_sizes:
            try:
                # 创建测试图像
                img = Image.new('RGB', (1920, 1080), color='white')
                draw = ImageDraw.Draw(img)
                
                # 加载字体
                try:
                    font = ImageFont.truetype("C:/Windows/Fonts/msyh.ttc", size)
                except:
                    font = ImageFont.load_default()
                
                # 获取文字尺寸
                bbox = draw.textbbox((0, 0), test_text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                
                # 计算相对大小
                relative_width = (text_width / 1920) * 100
                relative_height = (text_height / 1080) * 100
                
                print(f"   {size}px字体: 实际尺寸{text_width}x{text_height}, 相对大小{relative_width:.1f}%x{relative_height:.1f}%")
                
            except Exception as e:
                print(f"   {size}px字体测试失败: {e}")
        
        # 2. 检查系统DPI设置
        print(f"\n2. 检查系统DPI设置...")
        try:
            import tkinter as tk
            root = tk.Tk()
            dpi = root.winfo_fpixels('1i')
            root.destroy()
            print(f"   系统DPI: {dpi:.1f}")
            
            if dpi != 96:
                scale_factor = dpi / 96
                print(f"   DPI缩放倍数: {scale_factor:.2f}")
                print(f"   这可能是字体看起来小的原因！")
            else:
                print(f"   DPI正常 (96)")
                
        except Exception as e:
            print(f"   DPI检查失败: {e}")
        
        # 3. 计算理想的字体大小
        print(f"\n3. 字体大小建议...")
        
        # 如果100px才正常，那么计算倍率
        normal_size = 100
        default_size = 36
        scale_factor = normal_size / default_size
        
        print(f"   当前情况: {default_size}px太小，{normal_size}px正常")
        print(f"   计算倍率: {scale_factor:.2f}倍")
        
        # 建议的字体大小
        suggested_sizes = {
            "小水印": int(48 * scale_factor),
            "中等水印": int(60 * scale_factor), 
            "大水印": int(72 * scale_factor),
            "超大水印": int(84 * scale_factor)
        }
        
        print(f"   建议的字体大小:")
        for name, size in suggested_sizes.items():
            print(f"   - {name}: {size}px")
        
        # 4. 检查可能的解决方案
        print(f"\n4. 可能的解决方案...")
        
        print(f"   方案1: 调整默认字体大小")
        print(f"   - 将默认36px改为{int(36 * scale_factor)}px")
        print(f"   - 这样用户不需要手动调整")
        
        print(f"   方案2: 添加字体大小预设")
        print(f"   - 添加'小、中、大、超大'预设按钮")
        print(f"   - 分别对应{suggested_sizes['小水印']}px、{suggested_sizes['中等水印']}px、{suggested_sizes['大水印']}px、{suggested_sizes['超大水印']}px")
        
        print(f"   方案3: 自动DPI适配")
        print(f"   - 根据系统DPI自动调整字体大小")
        print(f"   - 字体大小 = 设置值 × DPI倍率")
        
        # 5. 检查是否需要修改默认配置
        print(f"\n5. 建议修改...")
        
        if scale_factor > 2:
            print(f"   ⚠️ 倍率过高({scale_factor:.2f}倍)，建议:")
            print(f"   1. 修改默认字体大小为{int(36 * scale_factor)}px")
            print(f"   2. 或者检查是否有其他缩放问题")
        else:
            print(f"   ✅ 倍率正常({scale_factor:.2f}倍)")
            print(f"   可以修改默认字体大小为{int(36 * scale_factor)}px")
        
        print(f"\n✅ 倍率问题诊断完成")
        print(f"主要发现: 需要{scale_factor:.2f}倍的字体大小才正常")
        
    except Exception as e:
        print(f"❌ 诊断过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    diagnose_scale_issue()
