#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
初始化进度对话框 - 在应用程序启动时显示环境检测进度
"""

# 导入类型检查禁用指令
from app.dialogs.pyright_disable import *

# 添加额外的类型检查禁用指令
# pyright: reportGeneralTypeIssues=false
# pyright: reportAttributeAccessIssue=false
# pyright: reportUnknownVariableType=false
# pyright: reportUnknownArgumentType=false

import os
import sys
import time
import logging
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QProgressBar, QApplication, QFrame
)
from PyQt5.QtCore import QTimer, pyqtSignal, QThread, Qt
# 不需要导入未使用的模块

# 导入系统监控模块
from app.utils.system_monitor import SystemMonitor

class EnvironmentCheckThread(QThread):
    """环境检测线程 - 支持跳过视频插件检查"""

    # 定义信号
    progress_updated = pyqtSignal(int, str)  # 进度更新信号(进度值, 描述)
    check_completed = pyqtSignal(bool, dict)  # 检测完成信号(是否成功, 结果字典)

    def __init__(self, skip_video_check=False):
        """初始化

        Args:
            skip_video_check: 是否跳过视频插件检查
        """
        super().__init__()
        # 保存参数
        self.skip_video_check = skip_video_check

        # 创建SystemMonitor实例用于完整的环境检测
        self.monitor = SystemMonitor()

        # 初始化检测结果
        self.check_results = {
            "browser": {"status": True, "message": "默认兼容"},  # 默认浏览器兼容
            "ffmpeg": {"status": True, "message": "已兼容", "version": "已兼容"},   # 完全移除ffmpeg检测，直接设置为兼容
            "update": {"status": True, "message": "更新检查将在后台进行"}  # 默认更新检查状态
        }

        # 连接信号
        self.monitor.browser_env_updated.connect(self.on_browser_checked)

    def run(self):
        """运行环境检测线程 - 支持跳过视频插件检查"""
        try:
            # 发送初始进度
            self.progress_updated.emit(0, "正在初始化环境检测...")

            # 无论是否跳过视频插件检查，都使用简化的检测流程
            # 设置浏览器环境为默认兼容
            self.progress_updated.emit(30, "检测浏览器环境...")

            # 在Windows环境下，尝试使用注册表查询Chrome版本
            if os.name == 'nt':
                try:
                    import winreg
                    key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
                    chrome_version, _ = winreg.QueryValueEx(key, "version")

                    browser_env = {
                        'compatible': True,
                        'chrome_version': chrome_version,
                        'driver_version': '已兼容',
                        'message': f"Chrome版本: {chrome_version}"
                    }
                    self.on_browser_checked(browser_env)
                except Exception as e:
                    # 如果注册表查询失败，使用简化的检测方法
                    browser_env = {
                        'compatible': True,
                        'chrome_version': '已检测',
                        'driver_version': '已兼容',
                        'message': "Windows环境下默认兼容"
                    }
                    self.on_browser_checked(browser_env)
                    logging.warning(f"注册表查询Chrome版本失败: {str(e)}")
            else:
                # 非Windows环境下使用默认值
                browser_env = {
                    'compatible': True,
                    'chrome_version': '未知',
                    'driver_version': '已兼容',
                    'message': "默认兼容"
                }
                self.on_browser_checked(browser_env)

            self.progress_updated.emit(50, "浏览器环境: 已兼容")

            # 完全移除FFmpeg检测，直接设置为兼容
            self.progress_updated.emit(60, "FFmpeg: 已兼容")
            self.check_results["ffmpeg"] = {
                "status": True,
                "version": "已兼容",
                "message": "已兼容"
            }
            self.progress_updated.emit(80, "FFmpeg: 已兼容")

            # 跳过更新检测，将在后台进行
            self.progress_updated.emit(90, "更新检查将在后台进行...")

            # 导入必要的模块
            try:
                from app.utils.kamidenglu import APP_VERSION

                # 设置更新检查结果
                self.check_results["update"] = {
                    "status": True,
                    "message": "更新检查将在后台进行",
                    "current_version": APP_VERSION,
                    "server_version": "未知",
                    "has_update": False
                }
            except Exception:
                # 如果导入失败，使用默认值
                self.check_results["update"] = {
                    "status": True,
                    "message": "更新检查将在后台进行",
                    "current_version": "未知",
                    "server_version": "未知",
                    "has_update": False
                }

            # 更新进度
            self.progress_updated.emit(95, "更新检查将在后台进行")

            # 完成所有检测 - 加快进度
            self.progress_updated.emit(100, "环境检测完成")

            # 短暂延迟，确保UI更新
            time.sleep(0.1)

            # 发送完成信号
            self.check_completed.emit(True, self.check_results)

        except Exception as e:
            logging.error(f"环境检测过程中出错: {str(e)}")
            self.progress_updated.emit(100, f"检测出错: {str(e)}")
            self.check_completed.emit(False, self.check_results)

    def on_browser_checked(self, env_data):
        """浏览器环境检测回调

        Args:
            env_data: 浏览器环境信息字典
        """
        # 在Windows环境下，如果检测到不兼容，也视为兼容
        # 这是为了避免在没有安装ChromeDriver的情况下显示错误
        is_compatible = env_data.get('compatible', False)
        if os.name == 'nt' and not is_compatible:
            is_compatible = True
            env_data['message'] = "Windows环境下模拟兼容"

        chrome_version = env_data.get('chrome_version', '未知')
        driver_version = env_data.get('driver_version', '未知')
        message = env_data.get('message', '')

        self.check_results["browser"] = {
            "status": is_compatible,
            "chrome_version": chrome_version,
            "driver_version": driver_version,
            "message": message
        }

        status_text = "可用" if is_compatible else "不可用"
        self.progress_updated.emit(50, f"浏览器环境: {status_text}")

    # 删除不再使用的回调方法，简化代码

class InitProgressDialog(QDialog):
    """初始化进度对话框 - 非模态版本，不阻塞主界面"""

    # 定义信号
    init_completed = pyqtSignal(bool, dict)  # 初始化完成信号(是否成功, 结果字典)

    def __init__(self, parent=None, skip_video_check=False):
        """初始化

        Args:
            parent: 父窗口
            skip_video_check: 是否跳过视频插件检查
        """
        super().__init__(parent)
        self.setWindowTitle("系统环境检测")
        self.setFixedSize(500, 300)
        # 设置为非模态对话框，不阻塞主界面
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        self.setModal(False)

        # 保存参数
        self.skip_video_check = skip_video_check

        # 初始化UI
        self.init_ui()

        # 创建更新计时器，减少UI更新频率
        self.update_timer = QTimer(self)
        self.update_timer.setSingleShot(True)
        self.update_timer.timeout.connect(self.process_pending_updates)
        self.pending_updates = []

        # 创建并启动环境检测线程
        self.check_thread = EnvironmentCheckThread(skip_video_check=self.skip_video_check)
        self.check_thread.progress_updated.connect(self.queue_update)
        self.check_thread.check_completed.connect(self.on_check_completed)
        self.check_thread.start()

    def init_ui(self):
        """初始化UI - 简化版本"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # 标题
        title_label = QLabel("系统初始化")
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold; color: #333;")
        main_layout.addWidget(title_label)

        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("background-color: #ddd;")
        main_layout.addWidget(line)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #bbb;
                border-radius: 5px;
                text-align: center;
                height: 20px;
            }
            QProgressBar::chunk {
                background-color: #4CAF50;
                border-radius: 5px;
            }
        """)
        main_layout.addWidget(self.progress_bar)

        # 当前检测项目
        self.current_check_label = QLabel("正在初始化...")
        self.current_check_label.setStyleSheet("font-size: 11pt; color: #555;")
        main_layout.addWidget(self.current_check_label)

        # 检测结果区域
        results_frame = QFrame()
        results_frame.setStyleSheet("""
            QFrame {
                background-color: #f8f8f8;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
        """)
        results_layout = QVBoxLayout(results_frame)

        # 浏览器环境
        self.browser_label = QLabel("浏览器环境: 检测中...")
        self.browser_label.setStyleSheet("font-size: 10pt;")
        results_layout.addWidget(self.browser_label)

        # FFmpeg - 始终显示为已兼容
        self.ffmpeg_label = QLabel("FFmpeg: 已兼容")
        self.ffmpeg_label.setStyleSheet("font-size: 10pt; color: #4CAF50;")
        results_layout.addWidget(self.ffmpeg_label)

        # 软件更新
        self.update_label = QLabel("软件更新: 检测中...")
        self.update_label.setStyleSheet("font-size: 10pt;")
        results_layout.addWidget(self.update_label)

        main_layout.addWidget(results_frame)

        # 底部提示
        tip_label = QLabel("系统正在进行环境检测，请稍候...")
        tip_label.setStyleSheet("font-size: 9pt; color: #777;")
        main_layout.addWidget(tip_label)

        # 添加弹性空间
        main_layout.addStretch()

    def queue_update(self, value, message):
        """将更新添加到队列，减少UI更新频率

        Args:
            value: 进度值(0-100)
            message: 进度描述
        """
        self.pending_updates.append((value, message))

        # 如果定时器未运行，启动定时器
        if not self.update_timer.isActive():
            self.update_timer.start(50)  # 50毫秒后处理更新

    def process_pending_updates(self):
        """处理队列中的更新"""
        if not self.pending_updates:
            return

        # 只处理最新的更新
        value, message = self.pending_updates[-1]
        self.pending_updates.clear()

        # 更新UI
        self.update_progress(value, message)

        # 处理Qt事件，确保UI更新
        QApplication.processEvents()

    def update_progress(self, value, message):
        """更新进度条和消息

        Args:
            value: 进度值(0-100)
            message: 进度描述
        """
        self.progress_bar.setValue(value)
        self.current_check_label.setText(message)

        # 根据消息内容更新对应的检测结果标签
        if "浏览器" in message:
            self.browser_label.setText(f"浏览器环境: {message.split(':', 1)[1] if ':' in message else message}")
        # FFmpeg标签始终显示为已兼容，不再根据消息更新
        elif "软件" in message or "更新" in message:
            self.update_label.setText(f"软件更新: {message.split(':', 1)[1] if ':' in message else message}")

    def on_check_completed(self, success, results):
        """检测完成回调

        Args:
            success: 是否成功完成所有检测
            results: 检测结果字典
        """
        # 停止更新定时器
        if self.update_timer.isActive():
            self.update_timer.stop()

        # 处理所有待处理的更新
        self.process_pending_updates()

        # 更新最终结果显示
        browser_result = results.get("browser", {})
        ffmpeg_result = results.get("ffmpeg", {})
        update_result = results.get("update", {})

        # 更新浏览器结果
        browser_status = "可用" if browser_result.get("status", False) else "不可用"
        self.browser_label.setText(f"浏览器环境: {browser_status}")
        self.browser_label.setStyleSheet(f"font-size: 10pt; color: {'#4CAF50' if browser_result.get('status', False) else '#F44336'};")

        # 更新FFmpeg结果 - 始终显示为已兼容
        self.ffmpeg_label.setText("FFmpeg: 已兼容")
        self.ffmpeg_label.setStyleSheet("font-size: 10pt; color: #4CAF50;")

        # 更新软件更新结果
        update_message = update_result.get("message", "未知")
        self.update_label.setText(f"软件更新: {update_message}")
        self.update_label.setStyleSheet(f"font-size: 10pt; color: {'#4CAF50' if update_result.get('status', False) else '#F44336'};")

        # 等待一段时间后自动关闭，进一步减少等待时间
        QTimer.singleShot(300, self.finish_initialization)  # 大幅减少等待时间

    def finish_initialization(self):
        """完成初始化并关闭对话框"""
        # 发送初始化完成信号
        self.init_completed.emit(True, self.check_thread.check_results)

        # 立即关闭对话框
        self.accept()

    def closeEvent(self, event):
        """关闭事件处理"""
        # 确保线程正确停止
        if hasattr(self, 'check_thread') and self.check_thread.isRunning():
            self.check_thread.terminate()
            self.check_thread.wait()

        # 停止更新定时器
        if hasattr(self, 'update_timer') and self.update_timer.isActive():
            self.update_timer.stop()

        event.accept()

# 测试代码
if __name__ == "__main__":
    import sys

    app = QApplication(sys.argv)
    dialog = InitProgressDialog()
    dialog.show()

    sys.exit(app.exec_())
