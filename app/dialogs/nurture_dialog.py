#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
头条号养号设置对话框模块
"""

# 类型检查禁用指令
# pyright: reportUnusedImport=false
# pyright: reportAttributeAccessIssue=false
# pyright: reportUnknownArgumentType=false
# pyright: reportOptionalMemberAccess=false
# pyright: reportGeneralTypeIssues=false
# pyright: reportArgumentType=false
# pyright: reportMissingImports=false

import os
import logging
import json
import random
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QSpinBox, QTimeEdit,
    QCheckBox, QComboBox, QPushButton, QGroupBox, QTextEdit, QListWidget,
    QMessageBox, QTabWidget, QWidget, QFormLayout, QGridLayout, QRadioButton,
    QScrollArea, QApplication, QDesktopWidget
)
from PyQt5.QtCore import Qt, QTime, pyqtSignal, QSettings, QRect
from PyQt5.QtGui import QFont

class NurtureSettingsDialog(QDialog):
    """头条号养号设置对话框"""

    def __init__(self, parent=None):
        """初始化设置对话框

        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.setWindowTitle("🤖 头条号智能养号设置")

        # 自适应屏幕尺寸设置
        self.setup_adaptive_window_size()

        # 创建主滚动区域
        self.scroll_area = QScrollArea(self)
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 创建内容容器
        self.content_widget = QWidget()
        self.scroll_area.setWidget(self.content_widget)

        # 设置对话框的主布局
        dialog_layout = QVBoxLayout(self)
        dialog_layout.setContentsMargins(0, 0, 0, 0)
        dialog_layout.addWidget(self.scroll_area)

        # 创建内容布局
        main_layout = QVBoxLayout(self.content_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # 添加顶部说明区域
        self.create_header_info(main_layout)

        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #C0C0C0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #F0F0F0;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #3498DB;
            }
            QTabBar::tab:hover {
                background-color: #E8F4FD;
            }
        """)
        main_layout.addWidget(self.tab_widget)

        # 创建各个标签页内容
        self.create_time_tab()
        self.create_behavior_tab()
        self.create_comment_tab()
        self.create_camouflage_tab()  # 添加伪装设置标签页

        # 账号选择选项区域
        self.create_account_selection_area(main_layout)

        # 底部按钮
        button_layout = QHBoxLayout()
        self.start_now_btn = QPushButton("立即开始养号")
        self.start_now_btn.setStyleSheet("""
            QPushButton {
                background-color: #2980B9;
                color: white;
                border-radius: 4px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3498DB;
            }
            QPushButton:pressed {
                background-color: #1F618D;
            }
        """)
        self.start_now_btn.clicked.connect(self.accept)

        self.schedule_btn = QPushButton("定时养号计划")
        self.schedule_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border-radius: 4px;
                padding: 8px 15px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ECC71;
            }
            QPushButton:pressed {
                background-color: #1E8449;
            }
        """)
        self.schedule_btn.clicked.connect(self.schedule_nurture)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addWidget(self.start_now_btn)
        button_layout.addWidget(self.schedule_btn)
        button_layout.addWidget(self.cancel_btn)

        main_layout.addLayout(button_layout)

        # 创建设置对象
        self.settings = QSettings("ToutiaoHelper", "NurtureSettings")

        # 从设置中加载保存的值
        self.load_saved_settings()

    def setup_adaptive_window_size(self):
        """设置自适应窗口尺寸"""
        try:
            # 获取屏幕信息
            desktop = QApplication.desktop()
            screen_rect = desktop.availableGeometry()  # 获取可用屏幕区域（排除任务栏）

            # 计算窗口尺寸
            screen_width = screen_rect.width()
            screen_height = screen_rect.height()

            # 设置窗口宽度（最小700，最大1200，屏幕宽度的70%）
            window_width = max(700, min(1200, int(screen_width * 0.7)))

            # 设置窗口高度（屏幕可用高度的85%，但不超过1000）
            window_height = min(1000, int(screen_height * 0.85))

            # 设置最小尺寸
            min_width = 700
            min_height = min(600, int(screen_height * 0.6))  # 最小高度为屏幕高度的60%

            self.setMinimumSize(min_width, min_height)
            self.resize(window_width, window_height)

            # 允许最大化
            self.setWindowFlags(self.windowFlags() | Qt.WindowMaximizeButtonHint)

            # 将窗口居中显示
            self.center_window(screen_rect, window_width, window_height)

            # 记录窗口尺寸信息（用于调试）
            print(f"屏幕可用区域: {screen_width}x{screen_height}")
            print(f"窗口尺寸: {window_width}x{window_height}")
            print(f"最小尺寸: {min_width}x{min_height}")

        except Exception as e:
            # 如果出错，使用默认尺寸
            print(f"设置自适应窗口尺寸时出错: {e}")
            self.setMinimumSize(700, 600)
            self.resize(800, 750)
            self.setWindowFlags(self.windowFlags() | Qt.WindowMaximizeButtonHint)

    def center_window(self, screen_rect, window_width, window_height):
        """将窗口居中显示在屏幕上

        Args:
            screen_rect: 屏幕可用区域
            window_width: 窗口宽度
            window_height: 窗口高度
        """
        try:
            # 计算居中位置
            x = screen_rect.x() + (screen_rect.width() - window_width) // 2
            y = screen_rect.y() + (screen_rect.height() - window_height) // 2

            # 确保窗口不会超出屏幕边界
            x = max(screen_rect.x(), min(x, screen_rect.x() + screen_rect.width() - window_width))
            y = max(screen_rect.y(), min(y, screen_rect.y() + screen_rect.height() - window_height))

            # 移动窗口到计算的位置
            self.move(x, y)

            print(f"窗口位置: ({x}, {y})")

        except Exception as e:
            print(f"居中窗口时出错: {e}")
            # 如果出错，使用默认居中方式
            self.move(100, 100)

    def create_header_info(self, main_layout):
        """创建顶部信息区域"""
        header_group = QGroupBox("📋 功能说明")
        header_layout = QVBoxLayout(header_group)

        info_text = QLabel("""
<b>🎯 养号功能说明：</b><br>
• <b>自动浏览</b>：模拟真实用户浏览头条内容，保持账号活跃度<br>
• <b>智能互动</b>：根据设定概率进行点赞、评论、关注等操作<br>
• <b>防检测机制</b>：随机延迟、页面滚动、浏览器指纹伪装<br>
• <b>代理支持</b>：支持HTTP/SOCKS代理，可轮换IP地址<br><br>
<font color="#E74C3C"><b>⚠️ 注意：</b>请合理设置互动频率，避免过度操作导致账号异常</font>
        """)
        info_text.setWordWrap(True)
        info_text.setStyleSheet("""
            QLabel {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 6px;
                padding: 12px;
                font-size: 13px;
                line-height: 1.4;
            }
        """)
        header_layout.addWidget(info_text)
        main_layout.addWidget(header_group)

    def create_account_selection_area(self, main_layout):
        """创建账号选择区域"""
        account_group = QGroupBox("🎯 账号选择")
        account_layout = QVBoxLayout(account_group)

        # 账号选择选项
        self.select_all_accounts = QCheckBox("🔄 自动养号所有账号（忽略账号表格中的选中状态）")
        self.select_all_accounts.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #E74C3C;
                font-size: 14px;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        self.select_all_accounts.setChecked(True)  # 默认选中
        account_layout.addWidget(self.select_all_accounts)

        # 添加说明文字
        account_info = QLabel("💡 <b>提示：</b>建议选择此选项，系统将自动对所有可用账号进行养号操作")
        account_info.setStyleSheet("""
            QLabel {
                color: #6C757D;
                font-size: 12px;
                padding: 5px 20px;
            }
        """)
        account_layout.addWidget(account_info)

        main_layout.addWidget(account_group)

    def create_time_tab(self):
        """创建时间设置标签页"""
        time_tab = QWidget()
        time_layout = QVBoxLayout(time_tab)
        time_layout.setSpacing(15)

        # 添加标签页说明
        tab_info = QLabel("⏰ <b>设置养号执行的时间段，支持多时段和时间随机化</b>")
        tab_info.setStyleSheet("color: #495057; font-size: 13px; padding: 5px;")
        time_layout.addWidget(tab_info)

        # 创建时间段设置组
        schedule_group = QGroupBox("🕐 养号时间段设置")
        schedule_layout = QGridLayout(schedule_group)
        schedule_layout.setSpacing(10)

        # 时间段样式
        time_style = """
            QTimeEdit {
                padding: 6px;
                border: 1px solid #CED4DA;
                border-radius: 4px;
                font-size: 13px;
                min-width: 80px;
            }
            QTimeEdit:focus {
                border-color: #3498DB;
                outline: none;
            }
        """

        # 早上时段
        morning_enable = QCheckBox("🌅 早上时段")
        morning_enable.setChecked(True)
        self.enable_morning = morning_enable
        schedule_layout.addWidget(morning_enable, 0, 0)

        morning_layout = QHBoxLayout()
        self.morning_start = QTimeEdit(QTime(9, 0))
        self.morning_start.setStyleSheet(time_style)
        self.morning_end = QTimeEdit(QTime(11, 0))
        self.morning_end.setStyleSheet(time_style)
        morning_layout.addWidget(self.morning_start)
        morning_layout.addWidget(QLabel("至"))
        morning_layout.addWidget(self.morning_end)
        morning_layout.addWidget(QLabel("(推荐: 9:00-11:00)"))
        morning_layout.addStretch()
        schedule_layout.addLayout(morning_layout, 0, 1)

        # 下午时段
        afternoon_enable = QCheckBox("🌞 下午时段")
        afternoon_enable.setChecked(True)
        self.enable_afternoon = afternoon_enable
        schedule_layout.addWidget(afternoon_enable, 1, 0)

        afternoon_layout = QHBoxLayout()
        self.afternoon_start = QTimeEdit(QTime(14, 0))
        self.afternoon_start.setStyleSheet(time_style)
        self.afternoon_end = QTimeEdit(QTime(17, 0))
        self.afternoon_end.setStyleSheet(time_style)
        afternoon_layout.addWidget(self.afternoon_start)
        afternoon_layout.addWidget(QLabel("至"))
        afternoon_layout.addWidget(self.afternoon_end)
        afternoon_layout.addWidget(QLabel("(推荐: 14:00-17:00)"))
        afternoon_layout.addStretch()
        schedule_layout.addLayout(afternoon_layout, 1, 1)

        # 晚上时段
        evening_enable = QCheckBox("🌙 晚上时段")
        evening_enable.setChecked(True)
        self.enable_evening = evening_enable
        schedule_layout.addWidget(evening_enable, 2, 0)

        evening_layout = QHBoxLayout()
        self.evening_start = QTimeEdit(QTime(20, 0))
        self.evening_start.setStyleSheet(time_style)
        self.evening_end = QTimeEdit(QTime(22, 0))
        self.evening_end.setStyleSheet(time_style)
        evening_layout.addWidget(self.evening_start)
        evening_layout.addWidget(QLabel("至"))
        evening_layout.addWidget(self.evening_end)
        evening_layout.addWidget(QLabel("(推荐: 20:00-22:00)"))
        evening_layout.addStretch()
        schedule_layout.addLayout(evening_layout, 2, 1)

        time_layout.addWidget(schedule_group)

        # 时间选项设置
        options_group = QGroupBox("⚙️ 时间选项")
        options_layout = QVBoxLayout(options_group)

        self.randomize_time = QCheckBox("🎲 启用时间随机化（在设定时间范围内随机执行，更自然）")
        self.randomize_time.setChecked(True)
        self.randomize_time.setStyleSheet("font-weight: bold; color: #28A745;")
        options_layout.addWidget(self.randomize_time)

        # 添加时间设置提示
        time_tip = QLabel("""
💡 <b>时间设置建议：</b><br>
• 选择用户活跃度较高的时间段进行养号<br>
• 启用时间随机化可以避免固定时间执行被检测<br>
• 建议至少启用2个时间段，分散养号行为
        """)
        time_tip.setWordWrap(True)
        time_tip.setStyleSheet("""
            QLabel {
                background-color: #E8F5E8;
                border: 1px solid #C3E6CB;
                border-radius: 4px;
                padding: 10px;
                font-size: 12px;
                color: #155724;
            }
        """)
        options_layout.addWidget(time_tip)

        time_layout.addWidget(options_group)
        time_layout.addStretch()

        self.tab_widget.addTab(time_tab, "⏰ 时间设置")

    def create_behavior_tab(self):
        """创建行为设置标签页"""
        behavior_tab = QWidget()
        behavior_layout = QVBoxLayout(behavior_tab)
        behavior_layout.setSpacing(10)

        # 创建滚动区域以适应小屏幕
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(15)

        # 浏览行为设置
        browse_group = QGroupBox("📖 浏览行为设置")
        browse_layout = QGridLayout(browse_group)
        browse_layout.setSpacing(8)

        self.browse_count = QSpinBox()
        self.browse_count.setRange(3, 20)
        self.browse_count.setValue(8)
        self.browse_count.setMinimumWidth(100)
        browse_layout.addWidget(QLabel("浏览文章数量:"), 0, 0)
        browse_layout.addWidget(self.browse_count, 0, 1)
        browse_layout.addWidget(QLabel("篇"), 0, 2)

        self.browse_duration = QSpinBox()
        self.browse_duration.setRange(10, 300)
        self.browse_duration.setValue(60)
        self.browse_duration.setSuffix(" 秒")
        self.browse_duration.setMinimumWidth(100)
        browse_layout.addWidget(QLabel("平均浏览时长:"), 1, 0)
        browse_layout.addWidget(self.browse_duration, 1, 1)
        browse_layout.addWidget(QLabel("(建议30-120秒)"), 1, 2)

        self.enable_scroll = QCheckBox("🖱️ 模拟滚动页面（增强真实性）")
        self.enable_scroll.setChecked(True)
        self.enable_scroll.setStyleSheet("font-weight: bold; color: #007BFF;")
        browse_layout.addWidget(self.enable_scroll, 2, 0, 1, 3)

        scroll_layout.addWidget(browse_group)

        # 交互行为设置
        interaction_group = QGroupBox("👍 交互行为设置")
        interaction_layout = QGridLayout(interaction_group)
        interaction_layout.setSpacing(8)

        # 点赞设置
        self.like_probability = QSpinBox()
        self.like_probability.setRange(0, 100)
        self.like_probability.setValue(70)
        self.like_probability.setSuffix(" %")
        self.like_probability.setMinimumWidth(100)
        interaction_layout.addWidget(QLabel("👍 点赞概率:"), 0, 0)
        interaction_layout.addWidget(self.like_probability, 0, 1)
        interaction_layout.addWidget(QLabel("(建议60-80%)"), 0, 2)

        self.like_count = QSpinBox()
        self.like_count.setRange(1, 10)
        self.like_count.setValue(3)
        self.like_count.setMinimumWidth(100)
        interaction_layout.addWidget(QLabel("点赞数量:"), 1, 0)
        interaction_layout.addWidget(self.like_count, 1, 1)
        interaction_layout.addWidget(QLabel("篇/次"), 1, 2)

        # 评论设置
        self.comment_probability = QSpinBox()
        self.comment_probability.setRange(0, 100)
        self.comment_probability.setValue(30)
        self.comment_probability.setSuffix(" %")
        self.comment_probability.setMinimumWidth(100)
        interaction_layout.addWidget(QLabel("💬 评论概率:"), 2, 0)
        interaction_layout.addWidget(self.comment_probability, 2, 1)
        interaction_layout.addWidget(QLabel("(建议20-40%)"), 2, 2)

        self.comment_count = QSpinBox()
        self.comment_count.setRange(1, 5)
        self.comment_count.setValue(1)
        self.comment_count.setMinimumWidth(100)
        self.comment_count.setToolTip("设置每篇文章最多评论的次数")
        interaction_layout.addWidget(QLabel("每篇文章评论次数:"), 3, 0)
        interaction_layout.addWidget(self.comment_count, 3, 1)
        interaction_layout.addWidget(QLabel("次"), 3, 2)

        # 关注设置
        self.follow_probability = QSpinBox()
        self.follow_probability.setRange(0, 100)
        self.follow_probability.setValue(20)
        self.follow_probability.setSuffix(" %")
        self.follow_probability.setMinimumWidth(100)
        interaction_layout.addWidget(QLabel("➕ 关注概率:"), 4, 0)
        interaction_layout.addWidget(self.follow_probability, 4, 1)
        interaction_layout.addWidget(QLabel("(建议10-30%)"), 4, 2)

        self.follow_count = QSpinBox()
        self.follow_count.setRange(1, 5)
        self.follow_count.setValue(1)
        self.follow_count.setMinimumWidth(100)
        interaction_layout.addWidget(QLabel("关注数量:"), 5, 0)
        interaction_layout.addWidget(self.follow_count, 5, 1)
        interaction_layout.addWidget(QLabel("个/次"), 5, 2)

        scroll_layout.addWidget(interaction_group)

        # 防检测设置
        detection_group = QGroupBox("🛡️ 防检测设置")
        detection_layout = QGridLayout(detection_group)
        detection_layout.setSpacing(8)

        self.random_delay = QCheckBox("⏱️ 操作间随机延迟（模拟真实用户行为）")
        self.random_delay.setChecked(True)
        self.random_delay.setStyleSheet("font-weight: bold; color: #28A745;")
        detection_layout.addWidget(self.random_delay, 0, 0, 1, 3)

        self.min_delay = QSpinBox()
        self.min_delay.setRange(1, 60)
        self.min_delay.setValue(3)
        self.min_delay.setSuffix(" 秒")
        self.min_delay.setMinimumWidth(100)
        detection_layout.addWidget(QLabel("最小延迟:"), 1, 0)
        detection_layout.addWidget(self.min_delay, 1, 1)
        detection_layout.addWidget(QLabel("(建议2-5秒)"), 1, 2)

        self.max_delay = QSpinBox()
        self.max_delay.setRange(5, 120)
        self.max_delay.setValue(10)
        self.max_delay.setSuffix(" 秒")
        self.max_delay.setMinimumWidth(100)
        detection_layout.addWidget(QLabel("最大延迟:"), 2, 0)
        detection_layout.addWidget(self.max_delay, 2, 1)
        detection_layout.addWidget(QLabel("(建议8-15秒)"), 2, 2)

        self.headless_mode = QCheckBox("🔍 无头模式运行（后台运行，不显示浏览器窗口）")
        self.headless_mode.setChecked(False)
        detection_layout.addWidget(self.headless_mode, 3, 0, 1, 3)

        # 添加强制关闭浏览器进程的选项
        self.force_close_browser = QCheckBox("🔄 强制关闭浏览器进程（切换账号时清理内存）")
        self.force_close_browser.setChecked(False)  # 默认改为False，减少不必要的进程终止
        self.force_close_browser.setToolTip("切换账号时是否强制关闭所有Chrome浏览器进程，关闭此选项可能导致内存占用增加")
        detection_layout.addWidget(self.force_close_browser, 4, 0, 1, 3)

        # 多线程设置
        self.max_threads = QSpinBox()
        self.max_threads.setRange(1, 10)
        self.max_threads.setValue(3)
        self.max_threads.setMinimumWidth(100)
        self.max_threads.setToolTip("设置同时处理账号的最大线程数，建议不超过5")
        detection_layout.addWidget(QLabel("🧵 并发线程数:"), 5, 0)
        detection_layout.addWidget(self.max_threads, 5, 1)
        detection_layout.addWidget(QLabel("(建议1-5个)"), 5, 2)

        scroll_layout.addWidget(detection_group)

        # 性能优化设置
        performance_group = QGroupBox("⚡ 性能优化设置")
        performance_layout = QGridLayout(performance_group)
        performance_layout.setSpacing(8)

        # 智能内存管理
        self.smart_memory_management = QCheckBox("🧠 智能内存管理（优化浏览器内存使用）")
        self.smart_memory_management.setChecked(True)
        self.smart_memory_management.setStyleSheet("font-weight: bold; color: #007BFF;")
        self.smart_memory_management.setToolTip("启用智能内存管理，优化浏览器内存使用，减少崩溃概率")
        performance_layout.addWidget(self.smart_memory_management, 0, 0, 1, 3)

        # 浏览器缓存清理
        self.clear_browser_cache = QCheckBox("🗑️ 定期清理浏览器缓存（减少内存占用）")
        self.clear_browser_cache.setChecked(True)
        self.clear_browser_cache.setToolTip("定期清理浏览器缓存，减少内存占用")
        performance_layout.addWidget(self.clear_browser_cache, 1, 0, 1, 3)

        # 线程启动间隔
        self.thread_start_interval = QSpinBox()
        self.thread_start_interval.setRange(1, 30)
        self.thread_start_interval.setValue(1)  # 默认值改为1秒
        self.thread_start_interval.setSuffix(" 秒")
        self.thread_start_interval.setMinimumWidth(100)
        self.thread_start_interval.setToolTip("设置线程启动间隔，避免同时启动多个浏览器实例导致系统资源竞争")
        performance_layout.addWidget(QLabel("线程启动间隔:"), 2, 0)
        performance_layout.addWidget(self.thread_start_interval, 2, 1)
        performance_layout.addWidget(QLabel("(建议1-5秒)"), 2, 2)

        # 自动重试次数
        self.max_retries = QSpinBox()
        self.max_retries.setRange(0, 5)
        self.max_retries.setValue(3)
        self.max_retries.setMinimumWidth(100)
        self.max_retries.setToolTip("设置操作失败时的最大重试次数")
        performance_layout.addWidget(QLabel("最大重试次数:"), 3, 0)
        performance_layout.addWidget(self.max_retries, 3, 1)
        performance_layout.addWidget(QLabel("次"), 3, 2)

        # 自然行为模拟
        self.natural_behavior = QCheckBox("🎭 增强自然行为模拟（随机停顿、回看等）")
        self.natural_behavior.setChecked(True)
        self.natural_behavior.setStyleSheet("font-weight: bold; color: #28A745;")
        self.natural_behavior.setToolTip("启用更自然的浏览行为模拟，包括随机停顿、回看等")
        performance_layout.addWidget(self.natural_behavior, 4, 0, 1, 3)

        scroll_layout.addWidget(performance_group)

        # 设置滚动区域
        scroll_area.setWidget(scroll_content)
        behavior_layout.addWidget(scroll_area)

        self.tab_widget.addTab(behavior_tab, "🎯 行为设置")

    def create_comment_tab(self):
        """创建评论设置标签页"""
        comment_tab = QWidget()
        comment_layout = QVBoxLayout(comment_tab)
        comment_layout.setSpacing(15)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(15)

        # 评论池设置
        comment_group = QGroupBox("💬 评论内容设置")
        comment_group_layout = QVBoxLayout(comment_group)
        comment_group_layout.setSpacing(10)

        # 添加说明信息
        comment_info = QLabel("""
<b>📝 评论设置说明：</b><br>
• 每行输入一条评论内容，系统将随机选择使用<br>
• 已内置150+条多样化评论，涵盖各种表达方式<br>
• 包含表达赞同、感谢分享、学习收获、互动交流等类型<br>
• 评论内容积极正面，自然真实，提高养号效果<br>
• 支持表情符号，让评论更生动自然<br>
• 可根据需要添加或修改评论内容
        """)
        comment_info.setWordWrap(True)
        comment_info.setStyleSheet("""
            QLabel {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                padding: 10px;
                font-size: 12px;
                color: #495057;
            }
        """)
        comment_group_layout.addWidget(comment_info)

        comment_hint = QLabel("💡 已内置150+条多样化评论，每行一条，系统随机选择使用")
        comment_hint.setStyleSheet("color: #007BFF; font-weight: bold; margin: 5px 0;")
        comment_group_layout.addWidget(comment_hint)

        self.comment_pool = QTextEdit()
        self.comment_pool.setPlaceholderText("输入评论内容，每行一条...")
        self.comment_pool.setMinimumHeight(200)
        # 设置扩展的评论池，包含150+条多样化评论
        expanded_comments = """👍 不错的文章，学习了
🙏 感谢分享，很有用
✨ 写得真好，点赞
📚 感谢作者的分享
💾 收藏了，下次再看
❤️ 内容很棒，已关注
💡 这个观点很有见地
📖 文章写得很全面
🎯 分析得很到位
⭐ 值得一读的好文章
🔥 干货满满，受益匪浅
👏 作者辛苦了，支持
🌟 内容质量很高
💪 继续加油，期待更新
🎉 终于找到这样的好文
😊 看完心情很好
🤝 说得太对了
💯 百分百赞同
🎊 内容很精彩
🌈 视角很独特
🔍 分析很深入
💝 这就是我想要的
🚀 思路很清晰
🎨 表达很生动
🌸 文笔很优美
🎭 角度很新颖
🔮 见解很深刻
🎪 内容很丰富
🌺 读起来很舒服
🎵 节奏把握得好
🌻 正能量满满
🎸 引人深思
🌙 很有启发性
⚡ 观点很犀利
🌊 逻辑很清楚
🎈 很实用的内容
🌷 温暖人心
🎀 细节很到位
🌴 格局很大
🎯 重点很突出
🌱 很有成长性
🎪 趣味性很强
🌟 闪闪发光的观点
🎨 创意十足
🌈 多彩的思维
🔥 热门话题
💎 珍贵的分享
🎊 庆祝好内容
🌸 春风化雨般的文字
🎭 戏剧性的转折
🔮 预见性很强
🎪 娱乐性十足
🌺 美好的阅读体验
🎵 韵律感很强
🌻 阳光般的态度
🎸 震撼人心
🌙 宁静致远
⚡ 电光火石般的灵感
🌊 波澜壮阔的视野
🎈 轻松愉快的阅读
🌷 芬芳的智慧
🎀 精致的表达
🌴 茁壮成长的思想
感谢楼主的用心分享
这个话题很有意思
学到了新知识
原来如此，涨知识了
说到心坎里了
深有同感
确实是这样的
很有道理
受教了
醍醐灌顶
茅塞顿开
眼前一亮
豁然开朗
心有戚戚焉
感同身受
引起共鸣
说得很中肯
分析得很透彻
见解独到
思路清晰
逻辑严密
条理清楚
层次分明
重点突出
主题鲜明
内容充实
信息量很大
干货很多
很实用
很有价值
很有意义
很有帮助
很受启发
很有收获
获益良多
受益匪浅
收获满满
满载而归
不虚此行
值得一看
值得收藏
值得推荐
值得学习
值得思考
值得借鉴
值得参考
值得关注
继续关注
持续关注
长期关注
一直关注
支持作者
为作者点赞
给作者加油
作者加油
作者辛苦了
作者用心了
作者很棒
作者厉害
作者专业
作者有才
期待更新
期待下一篇
等待续集
坐等更新
催更
快更新
更新太慢了
内容太少了
意犹未尽
还想看更多
希望详细一点
能否展开说说
可以深入一点
想了解更多
求详细解答
求科普
求指教
求分享
求推荐
求链接
求资源
马克一下
标记收藏
先收藏再看
收藏慢慢看
转发分享
分享给朋友
推荐给同事
安利给大家
必须点赞
忍不住点赞
不得不赞
必须支持
强烈推荐
五星好评
满分推荐
神作
佳作
力作
精品
经典
典型
标杆
范例
模板"""

        self.comment_pool.setText(expanded_comments)
        self.comment_pool.setStyleSheet("""
            QTextEdit {
                border: 2px solid #E9ECEF;
                border-radius: 6px;
                padding: 8px;
                font-size: 13px;
                line-height: 1.4;
            }
            QTextEdit:focus {
                border-color: #007BFF;
            }
        """)
        comment_group_layout.addWidget(self.comment_pool)

        scroll_layout.addWidget(comment_group)
        scroll_layout.addStretch()

        # 设置滚动区域
        scroll_area.setWidget(scroll_content)
        comment_layout.addWidget(scroll_area)

        self.tab_widget.addTab(comment_tab, "💬 评论设置")

    def create_camouflage_tab(self):
        """创建伪装设置标签页"""
        camouflage_tab = QWidget()
        camouflage_layout = QVBoxLayout(camouflage_tab)
        camouflage_layout.setSpacing(15)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(15)

        # IP代理设置
        proxy_group = QGroupBox("🌐 IP代理设置")
        proxy_layout = QGridLayout(proxy_group)
        proxy_layout.setSpacing(10)

        # 添加代理说明
        proxy_info = QLabel("""
<b>🔒 代理设置说明：</b><br>
• <b>批量存稿代理</b>：使用主程序的代理配置，推荐选择<br>
• <b>全局代理</b>：使用系统全局代理设置<br>
• <b>本地代理</b>：为养号功能单独配置代理<br>
• 建议优先使用批量存稿代理，确保IP一致性
        """)
        proxy_info.setWordWrap(True)
        proxy_info.setStyleSheet("""
            QLabel {
                background-color: #E3F2FD;
                border: 1px solid #BBDEFB;
                border-radius: 4px;
                padding: 10px;
                font-size: 12px;
                color: #0D47A1;
            }
        """)
        proxy_layout.addWidget(proxy_info, 0, 0, 1, 3)

        # 使用批量存稿代理设置
        self.use_batch_proxy = QCheckBox("🔗 使用批量存稿代理设置（推荐）")
        self.use_batch_proxy.setChecked(True)  # 默认选中
        self.use_batch_proxy.setStyleSheet("font-weight: bold; color: #28A745;")
        self.use_batch_proxy.stateChanged.connect(self.toggle_batch_proxy_settings)
        proxy_layout.addWidget(self.use_batch_proxy, 1, 0, 1, 3)

        # 使用全局代理设置
        self.use_global_proxy = QCheckBox("🌍 使用全局代理设置")
        self.use_global_proxy.setChecked(False)
        self.use_global_proxy.stateChanged.connect(self.toggle_proxy_settings)
        proxy_layout.addWidget(self.use_global_proxy, 2, 0, 1, 2)

        # 同步按钮
        sync_btn = QPushButton("🔄 同步全局代理")
        sync_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border-radius: 4px;
                padding: 8px 15px;
                font-weight: bold;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)
        sync_btn.clicked.connect(self.sync_global_proxy)
        proxy_layout.addWidget(sync_btn, 2, 2)

        # 启用本地代理
        self.enable_proxy = QCheckBox("⚙️ 启用本地代理配置")
        self.enable_proxy.setChecked(False)
        self.enable_proxy.stateChanged.connect(self.toggle_local_proxy_settings)
        proxy_layout.addWidget(self.enable_proxy, 3, 0, 1, 3)

        # 代理类型选择
        proxy_layout.addWidget(QLabel("代理类型:"), 4, 0)
        self.proxy_type = QComboBox()
        self.proxy_type.addItems(["HTTP", "SOCKS5", "动态代理池"])
        self.proxy_type.setMinimumWidth(120)
        proxy_layout.addWidget(self.proxy_type, 4, 1)

        # 代理地址设置
        self.proxy_address = QTextEdit()
        self.proxy_address.setMaximumHeight(80)
        self.proxy_address.setMinimumHeight(60)
        self.proxy_address.setPlaceholderText("输入代理地址，格式: ip:port\n多个代理地址请每行输入一个\n或输入代理API地址")
        self.proxy_address.setStyleSheet("""
            QTextEdit {
                border: 2px solid #E9ECEF;
                border-radius: 4px;
                padding: 5px;
                font-family: 'Consolas', 'Monaco', monospace;
            }
            QTextEdit:focus {
                border-color: #007BFF;
            }
        """)
        proxy_layout.addWidget(QLabel("代理地址:"), 5, 0, Qt.AlignmentFlag.AlignTop)
        proxy_layout.addWidget(self.proxy_address, 5, 1, 1, 2)

        # 代理自动切换设置
        self.auto_switch_proxy = QCheckBox("🔄 自动切换代理（提高隐蔽性）")
        self.auto_switch_proxy.setChecked(True)
        self.auto_switch_proxy.setStyleSheet("font-weight: bold; color: #007BFF;")
        proxy_layout.addWidget(self.auto_switch_proxy, 6, 0, 1, 3)

        # 切换频率设置
        proxy_layout.addWidget(QLabel("切换频率:"), 7, 0)
        self.proxy_switch_interval = QComboBox()
        self.proxy_switch_interval.addItems(["每账号切换一次", "每操作切换一次", "每30分钟切换一次", "每60分钟切换一次"])
        self.proxy_switch_interval.setMinimumWidth(150)
        proxy_layout.addWidget(self.proxy_switch_interval, 7, 1)

        scroll_layout.addWidget(proxy_group)

        # User-Agent设置
        ua_group = QGroupBox("🌐 User-Agent设置")
        ua_layout = QGridLayout(ua_group)
        ua_layout.setSpacing(10)

        self.enable_random_ua = QCheckBox("🎲 启用随机User-Agent（模拟不同浏览器）")
        self.enable_random_ua.setChecked(True)
        self.enable_random_ua.setStyleSheet("font-weight: bold; color: #28A745;")
        ua_layout.addWidget(self.enable_random_ua, 0, 0, 1, 3)

        # 设备类型选择
        ua_layout.addWidget(QLabel("设备类型:"), 1, 0)
        self.device_type = QComboBox()
        self.device_type.addItems(["自动随机", "仅PC", "仅移动设备", "PC和移动混合"])
        self.device_type.setMinimumWidth(150)
        ua_layout.addWidget(self.device_type, 1, 1)

        # 浏览器类型选择
        ua_layout.addWidget(QLabel("浏览器类型:"), 2, 0)
        self.browser_type = QComboBox()
        self.browser_type.addItems(["自动随机", "Chrome", "Firefox", "Edge", "Safari"])
        self.browser_type.setMinimumWidth(150)
        ua_layout.addWidget(self.browser_type, 2, 1)

        # 自定义User-Agent池
        self.custom_ua = QTextEdit()
        self.custom_ua.setMaximumHeight(80)
        self.custom_ua.setMinimumHeight(60)
        self.custom_ua.setPlaceholderText("输入自定义User-Agent，每行一个\n留空则使用系统内置UA池")
        self.custom_ua.setStyleSheet("""
            QTextEdit {
                border: 2px solid #E9ECEF;
                border-radius: 4px;
                padding: 5px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
            }
            QTextEdit:focus {
                border-color: #28A745;
            }
        """)
        ua_layout.addWidget(QLabel("自定义UA:"), 3, 0, Qt.AlignmentFlag.AlignTop)
        ua_layout.addWidget(self.custom_ua, 3, 1, 1, 2)

        scroll_layout.addWidget(ua_group)

        # 浏览器指纹设置
        fingerprint_group = QGroupBox("🔒 浏览器指纹设置")
        fingerprint_layout = QGridLayout(fingerprint_group)
        fingerprint_layout.setSpacing(10)

        self.enable_fingerprint = QCheckBox("🎭 启用浏览器指纹伪装（高级反检测）")
        self.enable_fingerprint.setChecked(False)  # 默认禁用指纹浏览器
        self.enable_fingerprint.setStyleSheet("font-weight: bold; color: #DC3545;")
        fingerprint_layout.addWidget(self.enable_fingerprint, 0, 0, 1, 3)

        # 分辨率设置
        fingerprint_layout.addWidget(QLabel("分辨率模式:"), 1, 0)
        self.resolution_type = QComboBox()
        self.resolution_type.addItems(["随机分辨率", "固定分辨率", "常见分辨率随机"])
        self.resolution_type.setMinimumWidth(150)
        fingerprint_layout.addWidget(self.resolution_type, 1, 1)

        resolution_layout_widget = QWidget()
        resolution_layout = QHBoxLayout(resolution_layout_widget)
        resolution_layout.setContentsMargins(0, 0, 0, 0)
        self.resolution_width = QSpinBox()
        self.resolution_width.setRange(800, 3840)
        self.resolution_width.setValue(1920)
        self.resolution_width.setMinimumWidth(80)
        self.resolution_height = QSpinBox()
        self.resolution_height.setRange(600, 2160)
        self.resolution_height.setValue(1080)
        self.resolution_height.setMinimumWidth(80)
        resolution_layout.addWidget(self.resolution_width)
        resolution_layout.addWidget(QLabel("×"))
        resolution_layout.addWidget(self.resolution_height)
        resolution_layout.addStretch()
        fingerprint_layout.addWidget(QLabel("自定义分辨率:"), 2, 0)
        fingerprint_layout.addWidget(resolution_layout_widget, 2, 1, 1, 2)

        # 设备像素比
        fingerprint_layout.addWidget(QLabel("设备像素比:"), 3, 0)
        self.pixel_ratio = QComboBox()
        self.pixel_ratio.addItems(["随机", "1.0", "1.5", "2.0", "2.5", "3.0"])
        self.pixel_ratio.setMinimumWidth(100)
        fingerprint_layout.addWidget(self.pixel_ratio, 3, 1)

        # 语言设置
        fingerprint_layout.addWidget(QLabel("浏览器语言:"), 4, 0)
        self.browser_language = QComboBox()
        self.browser_language.addItems(["中文", "随机中英文", "仅英文"])
        self.browser_language.setMinimumWidth(120)
        fingerprint_layout.addWidget(self.browser_language, 4, 1)

        # Canvas指纹
        self.randomize_canvas = QCheckBox("🎨 随机Canvas指纹（防止指纹追踪）")
        self.randomize_canvas.setChecked(True)
        self.randomize_canvas.setStyleSheet("color: #007BFF;")
        fingerprint_layout.addWidget(self.randomize_canvas, 5, 0, 1, 3)

        # WebRTC设置
        self.disable_webrtc = QCheckBox("🚫 禁用WebRTC（防止真实IP泄露）")
        self.disable_webrtc.setChecked(True)
        self.disable_webrtc.setStyleSheet("color: #DC3545; font-weight: bold;")
        fingerprint_layout.addWidget(self.disable_webrtc, 6, 0, 1, 3)

        scroll_layout.addWidget(fingerprint_group)
        scroll_layout.addStretch()

        # 设置滚动区域
        scroll_area.setWidget(scroll_content)
        camouflage_layout.addWidget(scroll_area)

        self.tab_widget.addTab(camouflage_tab, "🎭 伪装设置")

    def schedule_nurture(self):
        """设置定时养号计划"""
        # 显示确认对话框
        reply = QMessageBox.question(
            self,
            "定时养号计划",
            "确定要设置定时养号计划吗？\n系统将按照设定的时间自动执行养号任务。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.accept()

    def add_select_all_accounts_option(self):
        """添加选择所有账号选项"""
        # 方法已在初始化时创建了选项，这里用于兼容性
        pass

    def toggle_batch_proxy_settings(self, state):
        """切换批量存稿代理设置

        Args:
            state: 复选框状态
        """
        # 如果使用批量存稿代理，禁用其他代理设置
        use_batch = state == Qt.CheckState.Checked

        # 禁用或启用其他代理设置
        self.use_global_proxy.setEnabled(not use_batch)
        self.enable_proxy.setEnabled(not use_batch)
        self.proxy_type.setEnabled(not use_batch and self.enable_proxy.isChecked())
        self.proxy_address.setEnabled(not use_batch and self.enable_proxy.isChecked())
        self.auto_switch_proxy.setEnabled(not use_batch and self.enable_proxy.isChecked())
        self.proxy_switch_interval.setEnabled(not use_batch and self.enable_proxy.isChecked())

        # 如果启用批量存稿代理，自动禁用其他代理
        if use_batch:
            self.use_global_proxy.setChecked(False)
            self.enable_proxy.setChecked(False)

    def toggle_proxy_settings(self, state):
        """切换全局代理设置

        Args:
            state: 复选框状态
        """
        # 如果使用全局代理，禁用本地代理设置
        use_global = state == Qt.CheckState.Checked

        # 禁用或启用本地代理设置
        self.enable_proxy.setEnabled(not use_global)
        self.proxy_type.setEnabled(not use_global and self.enable_proxy.isChecked())
        self.proxy_address.setEnabled(not use_global and self.enable_proxy.isChecked())
        self.auto_switch_proxy.setEnabled(not use_global and self.enable_proxy.isChecked())
        self.proxy_switch_interval.setEnabled(not use_global and self.enable_proxy.isChecked())

        # 如果启用全局代理，自动禁用本地代理
        if use_global:
            self.enable_proxy.setChecked(False)

        # 如果启用全局代理，自动禁用批量存稿代理
        if use_global:
            self.use_batch_proxy.setChecked(False)

    def toggle_local_proxy_settings(self, state):
        """切换本地代理设置

        Args:
            state: 复选框状态
        """
        # 只有在未使用全局代理时才生效
        if not self.use_global_proxy.isChecked():
            enabled = state == Qt.CheckState.Checked
            self.proxy_type.setEnabled(enabled)
            self.proxy_address.setEnabled(enabled)
            self.auto_switch_proxy.setEnabled(enabled)
            self.proxy_switch_interval.setEnabled(enabled)

    def sync_global_proxy(self):
        """同步全局代理设置"""
        try:
            # 导入代理工具
            from app.utils.proxy_utils import get_proxy_settings

            # 获取全局代理设置
            try:
                # 尝试使用新参数
                global_settings = get_proxy_settings(check_enabled=False)
            except TypeError:
                # 如果旧版本不支持check_enabled参数，使用默认调用
                global_settings = get_proxy_settings()

            if not global_settings:
                QMessageBox.warning(self, "警告", "未找到全局代理设置，请先在代理设置中配置代理。")
                return

            # 检查代理设置是否有效
            has_proxy_list = bool(global_settings.get("enable_rotation", False) and global_settings.get("proxy_list", "").strip())
            has_single_proxy = bool(global_settings.get("proxy_host", "").strip())

            if not has_proxy_list and not has_single_proxy:
                QMessageBox.warning(self, "警告", "全局代理设置不完整，请先配置代理服务器地址或代理列表。")
                return

            # 自动勾选使用全局代理
            self.use_global_proxy.setChecked(True)

            # 显示同步成功消息
            proxy_type = global_settings.get("proxy_type", "HTTP")
            has_rotation = global_settings.get("enable_rotation", False)
            proxy_list = global_settings.get("proxy_list", "").strip()
            proxy_count = len(proxy_list.split(",")) if proxy_list else 0

            message = f"已同步全局代理设置:\n\n"
            message += f"- 代理类型: {proxy_type}\n"

            if has_rotation and proxy_count > 0:
                message += f"- IP轮换: 已启用\n"
                message += f"- 代理数量: {proxy_count}个\n"
            else:
                host = global_settings.get("proxy_host", "")
                port = global_settings.get("proxy_port", "")
                message += f"- 服务器: {host}:{port}\n"

            if global_settings.get("require_auth", False):
                message += "- 认证: 已启用\n"

            QMessageBox.information(self, "同步成功", message)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"同步全局代理设置失败: {str(e)}")

    def load_saved_settings(self):
        """从设置中加载保存的值"""
        try:
            # 时间设置
            self.morning_start.setTime(QTime.fromString(self.settings.value("morning_start", "09:00"), 'HH:mm'))
            self.morning_end.setTime(QTime.fromString(self.settings.value("morning_end", "11:00"), 'HH:mm'))
            self.afternoon_start.setTime(QTime.fromString(self.settings.value("afternoon_start", "14:00"), 'HH:mm'))
            self.afternoon_end.setTime(QTime.fromString(self.settings.value("afternoon_end", "17:00"), 'HH:mm'))
            self.evening_start.setTime(QTime.fromString(self.settings.value("evening_start", "20:00"), 'HH:mm'))
            self.evening_end.setTime(QTime.fromString(self.settings.value("evening_end", "22:00"), 'HH:mm'))
            self.enable_morning.setChecked(self.settings.value("enable_morning", True, type=bool))
            self.enable_afternoon.setChecked(self.settings.value("enable_afternoon", True, type=bool))
            self.enable_evening.setChecked(self.settings.value("enable_evening", True, type=bool))
            self.randomize_time.setChecked(self.settings.value("randomize_time", True, type=bool))

            # 行为设置
            self.browse_count.setValue(self.settings.value("browse_count", 8, type=int))
            self.browse_duration.setValue(self.settings.value("browse_duration", 60, type=int))
            self.enable_scroll.setChecked(self.settings.value("enable_scroll", True, type=bool))
            self.like_probability.setValue(self.settings.value("like_probability", 70, type=int))
            self.like_count.setValue(self.settings.value("like_count", 3, type=int))
            self.comment_probability.setValue(self.settings.value("comment_probability", 30, type=int))
            self.comment_count.setValue(self.settings.value("comment_count", 1, type=int))
            self.follow_probability.setValue(self.settings.value("follow_probability", 20, type=int))
            self.follow_count.setValue(self.settings.value("follow_count", 1, type=int))
            self.random_delay.setChecked(self.settings.value("random_delay", True, type=bool))
            self.min_delay.setValue(self.settings.value("min_delay", 3, type=int))
            self.max_delay.setValue(self.settings.value("max_delay", 10, type=int))
            self.headless_mode.setChecked(self.settings.value("headless_mode", False, type=bool))
            self.force_close_browser.setChecked(self.settings.value("force_close_browser", False, type=bool))
            self.max_threads.setValue(self.settings.value("max_threads", 3, type=int))

            # 性能优化设置
            self.smart_memory_management.setChecked(self.settings.value("smart_memory_management", True, type=bool))
            self.clear_browser_cache.setChecked(self.settings.value("clear_browser_cache", True, type=bool))
            self.thread_start_interval.setValue(self.settings.value("thread_start_interval", 1, type=int))
            self.max_retries.setValue(self.settings.value("max_retries", 3, type=int))
            self.natural_behavior.setChecked(self.settings.value("natural_behavior", True, type=bool))

            # 评论设置
            saved_comments = self.settings.value("comment_pool", "")
            if saved_comments:
                self.comment_pool.setText(saved_comments)

            # 账号选择设置
            self.select_all_accounts.setChecked(self.settings.value("select_all_accounts", False, type=bool))

            # 伪装设置
            self.use_batch_proxy.setChecked(self.settings.value("use_batch_proxy", True, type=bool))
            self.use_global_proxy.setChecked(self.settings.value("use_global_proxy", False, type=bool))
            self.enable_proxy.setChecked(self.settings.value("enable_proxy", False, type=bool))
            self.proxy_type.setCurrentText(self.settings.value("proxy_type", "HTTP"))
            self.proxy_address.setText(self.settings.value("proxy_address", ""))
            self.auto_switch_proxy.setChecked(self.settings.value("auto_switch_proxy", True, type=bool))
            self.proxy_switch_interval.setCurrentText(self.settings.value("proxy_switch_interval", "每账号切换一次"))

            # 更新UI状态
            self.toggle_batch_proxy_settings(self.use_batch_proxy.checkState())
            self.toggle_proxy_settings(self.use_global_proxy.checkState())

            self.enable_random_ua.setChecked(self.settings.value("enable_random_ua", True, type=bool))
            self.device_type.setCurrentText(self.settings.value("device_type", "自动随机"))
            self.browser_type.setCurrentText(self.settings.value("browser_type", "自动随机"))
            self.custom_ua.setText(self.settings.value("custom_ua", ""))

            self.enable_fingerprint.setChecked(self.settings.value("enable_fingerprint", False, type=bool))
            self.resolution_type.setCurrentText(self.settings.value("resolution_type", "随机分辨率"))
            self.resolution_width.setValue(self.settings.value("resolution_width", 1920, type=int))
            self.resolution_height.setValue(self.settings.value("resolution_height", 1080, type=int))
            self.pixel_ratio.setCurrentText(self.settings.value("pixel_ratio", "随机"))
            self.browser_language.setCurrentText(self.settings.value("browser_language", "中文"))
            self.randomize_canvas.setChecked(self.settings.value("randomize_canvas", True, type=bool))
            self.disable_webrtc.setChecked(self.settings.value("disable_webrtc", True, type=bool))

        except Exception as e:
            print(f"加载设置时出错: {str(e)}")

    def save_current_settings(self):
        """保存当前设置"""
        try:
            # 时间设置
            self.settings.setValue("morning_start", self.morning_start.time().toString('HH:mm'))
            self.settings.setValue("morning_end", self.morning_end.time().toString('HH:mm'))
            self.settings.setValue("afternoon_start", self.afternoon_start.time().toString('HH:mm'))
            self.settings.setValue("afternoon_end", self.afternoon_end.time().toString('HH:mm'))
            self.settings.setValue("evening_start", self.evening_start.time().toString('HH:mm'))
            self.settings.setValue("evening_end", self.evening_end.time().toString('HH:mm'))
            self.settings.setValue("enable_morning", self.enable_morning.isChecked())
            self.settings.setValue("enable_afternoon", self.enable_afternoon.isChecked())
            self.settings.setValue("enable_evening", self.enable_evening.isChecked())
            self.settings.setValue("randomize_time", self.randomize_time.isChecked())

            # 行为设置
            self.settings.setValue("browse_count", self.browse_count.value())
            self.settings.setValue("browse_duration", self.browse_duration.value())
            self.settings.setValue("enable_scroll", self.enable_scroll.isChecked())
            self.settings.setValue("like_probability", self.like_probability.value())
            self.settings.setValue("like_count", self.like_count.value())
            self.settings.setValue("comment_probability", self.comment_probability.value())
            self.settings.setValue("comment_count", self.comment_count.value())
            self.settings.setValue("comment_pool", self.comment_pool.toPlainText())
            self.settings.setValue("follow_probability", self.follow_probability.value())
            self.settings.setValue("follow_count", self.follow_count.value())
            self.settings.setValue("random_delay", self.random_delay.isChecked())
            self.settings.setValue("min_delay", self.min_delay.value())
            self.settings.setValue("max_delay", self.max_delay.value())
            self.settings.setValue("headless_mode", self.headless_mode.isChecked())
            self.settings.setValue("force_close_browser", self.force_close_browser.isChecked())
            self.settings.setValue("max_threads", self.max_threads.value())

            # 性能优化设置
            self.settings.setValue("smart_memory_management", self.smart_memory_management.isChecked())
            self.settings.setValue("clear_browser_cache", self.clear_browser_cache.isChecked())
            self.settings.setValue("thread_start_interval", self.thread_start_interval.value())
            self.settings.setValue("max_retries", self.max_retries.value())
            self.settings.setValue("natural_behavior", self.natural_behavior.isChecked())

            # 账号选择设置
            self.settings.setValue("select_all_accounts", self.select_all_accounts.isChecked())

            # 伪装设置
            self.settings.setValue("use_batch_proxy", self.use_batch_proxy.isChecked())
            self.settings.setValue("use_global_proxy", self.use_global_proxy.isChecked())
            self.settings.setValue("enable_proxy", self.enable_proxy.isChecked())
            self.settings.setValue("proxy_type", self.proxy_type.currentText())
            self.settings.setValue("proxy_address", self.proxy_address.toPlainText())
            self.settings.setValue("auto_switch_proxy", self.auto_switch_proxy.isChecked())
            self.settings.setValue("proxy_switch_interval", self.proxy_switch_interval.currentText())

            self.settings.setValue("enable_random_ua", self.enable_random_ua.isChecked())
            self.settings.setValue("device_type", self.device_type.currentText())
            self.settings.setValue("browser_type", self.browser_type.currentText())
            self.settings.setValue("custom_ua", self.custom_ua.toPlainText())

            self.settings.setValue("enable_fingerprint", self.enable_fingerprint.isChecked())
            self.settings.setValue("resolution_type", self.resolution_type.currentText())
            self.settings.setValue("resolution_width", self.resolution_width.value())
            self.settings.setValue("resolution_height", self.resolution_height.value())
            self.settings.setValue("pixel_ratio", self.pixel_ratio.currentText())
            self.settings.setValue("browser_language", self.browser_language.currentText())
            self.settings.setValue("randomize_canvas", self.randomize_canvas.isChecked())
            self.settings.setValue("disable_webrtc", self.disable_webrtc.isChecked())

        except Exception as e:
            print(f"保存设置时出错: {str(e)}")

    def get_settings(self):
        """获取养号设置

        Returns:
            dict: 养号设置字典
        """
        # 保存当前设置
        self.save_current_settings()

        # 获取评论池
        comment_text = self.comment_pool.toPlainText()
        comment_lines = [line.strip() for line in comment_text.split("\n") if line.strip()]

        # 获取代理地址
        proxy_text = self.proxy_address.toPlainText()
        proxy_lines = [line.strip() for line in proxy_text.split("\n") if line.strip()]

        # 获取自定义UA
        ua_text = self.custom_ua.toPlainText()
        ua_lines = [line.strip() for line in ua_text.split("\n") if line.strip()]

        # 构建设置字典
        settings = {
            # 时间设置
            'morning_start': self.morning_start.time().toString('HH:mm'),
            'morning_end': self.morning_end.time().toString('HH:mm'),
            'afternoon_start': self.afternoon_start.time().toString('HH:mm'),
            'afternoon_end': self.afternoon_end.time().toString('HH:mm'),
            'evening_start': self.evening_start.time().toString('HH:mm'),
            'evening_end': self.evening_end.time().toString('HH:mm'),
            'enable_morning': self.enable_morning.isChecked(),
            'enable_afternoon': self.enable_afternoon.isChecked(),
            'enable_evening': self.enable_evening.isChecked(),
            'randomize_time': self.randomize_time.isChecked(),

            # 行为设置
            'browse_count': self.browse_count.value(),
            'browse_duration': self.browse_duration.value(),
            'enable_scroll': self.enable_scroll.isChecked(),
            'like_probability': self.like_probability.value(),
            'like_count': self.like_count.value(),
            'comment_probability': self.comment_probability.value(),
            'comment_count': self.comment_count.value(),
            'follow_probability': self.follow_probability.value(),
            'follow_count': self.follow_count.value(),
            'random_delay': self.random_delay.isChecked(),
            'min_delay': self.min_delay.value(),
            'max_delay': self.max_delay.value(),
            'headless_mode': self.headless_mode.isChecked(),
            'force_close_browser': self.force_close_browser.isChecked(),
            'max_threads': self.max_threads.value(),

            # 性能优化设置
            'smart_memory_management': self.smart_memory_management.isChecked(),
            'clear_browser_cache': self.clear_browser_cache.isChecked(),
            'thread_start_interval': self.thread_start_interval.value(),
            'max_retries': self.max_retries.value(),
            'natural_behavior': self.natural_behavior.isChecked(),

            # 评论设置
            'comment_pool': comment_lines,

            # 账号选择设置
            'select_all_accounts': self.select_all_accounts.isChecked(),

            # 伪装设置
            'use_global_proxy': self.use_global_proxy.isChecked(),
            'enable_proxy': self.enable_proxy.isChecked(),
            'proxy_type': self.proxy_type.currentText(),
            'proxy_list': proxy_lines,
            'auto_switch_proxy': self.auto_switch_proxy.isChecked(),
            'proxy_switch_interval': self.proxy_switch_interval.currentText(),

            'enable_random_ua': self.enable_random_ua.isChecked(),
            'device_type': self.device_type.currentText(),
            'browser_type': self.browser_type.currentText(),
            'custom_ua_list': ua_lines,

            'enable_fingerprint': self.enable_fingerprint.isChecked(),
            'resolution_type': self.resolution_type.currentText(),
            'resolution_width': self.resolution_width.value(),
            'resolution_height': self.resolution_height.value(),
            'pixel_ratio': self.pixel_ratio.currentText(),
            'browser_language': self.browser_language.currentText(),
            'randomize_canvas': self.randomize_canvas.isChecked(),
            'disable_webrtc': self.disable_webrtc.isChecked(),
        }

        return settings