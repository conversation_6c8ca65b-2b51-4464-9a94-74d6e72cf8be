#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
视频处理功能模块
实现视频筛选和封面生成功能
"""

# pyright: reportAttributeAccessIssue=false
# pylint: disable=no-member

import os
import sys
import time
import random
import shutil
import threading
import json
from typing import List, Optional, Dict, Any, Union

# 尝试导入numpy
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QMessageBox, QGroupBox, QTextEdit, QLineEdit, QFileDialog,
    QCheckBox, QProgressBar, QApplication, QComboBox,
    QTabWidget, QWidget, QGridLayout, QSpinBox,
    QScrollArea, QDesktopWidget, QProgressD<PERSON>og, Q<PERSON>rame,
    QSlider, QColorDialog, QFontComboBox, QSpacerItem, QSizePolicy,
    QListWidget, QListWidgetItem, QInputDialog, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView, QMenu, QAction, QToolButton, QButtonGroup,
    QFormLayout, QDoubleSpinBox
)
from app.widgets.sidebar_navigation import SidebarNavigation, ContentArea
from PyQt5.QtCore import (
    Qt, QTimer, pyqtSignal, QObject,
    QThreadPool, QRunnable, QSize, QRect, QPoint
)
from PyQt5.QtGui import (
    QFont, QColor, QPalette, QPixmap, QIcon, QPainter, QBrush, QLinearGradient,
    QPaintEvent, QMouseEvent, QPen, QFontMetrics, QImage, QPainterPath
)

# 类型注解修复 - 解决PyQt5信号类型检查问题
try:
    from PyQt5.QtCore import pyqtBoundSignal
except ImportError:
    # 如果导入失败，使用Any类型
    pyqtBoundSignal = Any

# 导入项目的日志系统
try:
    from app.utils.logger import info, warning, error, debug
except ImportError:
    # 如果导入失败，使用标准logging
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    def info(message):
        logger.info(message)

    def warning(message):
        logger.warning(message)

    def error(message):
        logger.error(message)

    def debug(message):
        logger.debug(message)

# 尝试导入视频处理库
try:
    import cv2
    CV2_AVAILABLE = True
    info("OpenCV库已加载，支持视频处理功能")
except ImportError:
    CV2_AVAILABLE = False
    warning("OpenCV库未安装，视频处理功能将受限")

try:
    from PIL import Image, ImageEnhance, ImageDraw, ImageFont
    PIL_AVAILABLE = True
    info("PIL库已加载，支持图像处理功能")
except ImportError:
    PIL_AVAILABLE = False
    warning("PIL库未安装，图像处理功能将受限")

# 导入颜色选择对话框
try:
    from PyQt5.QtWidgets import QColorDialog
    from PyQt5.QtGui import QColor, QPixmap, QPainter, QFont as QFontClass
    COLOR_DIALOG_AVAILABLE = True
except ImportError:
    COLOR_DIALOG_AVAILABLE = False


class MultiLineWatermarkConfig:
    """多行水印单行配置类"""
    def __init__(self):
        self.enabled = True
        self.char_count = 3  # 该行显示的字符数
        self.font_family = "Microsoft YaHei"
        self.font_size = 24  # 适合预览的字体大小
        self.text_color = "#FFFFFF"
        self.shadow_enabled = True
        self.shadow_color = "#000000"
        self.shadow_offset_x = 2
        self.shadow_offset_y = 2
        self.stroke_enabled = False
        self.stroke_color = "#000000"
        self.stroke_width = 2
        self.position_x = 50  # 百分比位置
        self.position_y = 90  # 百分比位置
        self.opacity = 80  # 透明度 0-100

    def to_dict(self):
        """转换为字典"""
        return {
            'enabled': self.enabled,
            'char_count': self.char_count,
            'font_family': self.font_family,
            'font_size': self.font_size,
            'text_color': self.text_color,
            'shadow_enabled': self.shadow_enabled,
            'shadow_color': self.shadow_color,
            'shadow_offset_x': self.shadow_offset_x,
            'shadow_offset_y': self.shadow_offset_y,
            'stroke_enabled': self.stroke_enabled,
            'stroke_color': self.stroke_color,
            'stroke_width': self.stroke_width,
            'position_x': self.position_x,
            'position_y': self.position_y,
            'opacity': self.opacity
        }

    def from_dict(self, data):
        """从字典加载"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)


class WatermarkConfig:
    """水印配置类"""
    def __init__(self):
        self.enabled = True  # 默认启用水印
        self.font_family = "Microsoft YaHei"
        self.font_category = "现代字体"  # 字体分类
        self.font_size = 100  # 修改默认字体大小以适应实际显示需求
        self.text_color = "#FFFFFF"
        self.shadow_enabled = True
        self.shadow_color = "#000000"
        self.shadow_offset_x = 2
        self.shadow_offset_y = 2
        self.stroke_enabled = False  # 描边功能
        self.stroke_color = "#000000"
        self.stroke_width = 2
        self.position_x = 50  # 百分比位置
        self.position_y = 90  # 百分比位置
        self.opacity = 80  # 透明度 0-100

        # 单行水印字数设置
        self.single_char_count = 6  # 单行水印显示的字符数

        # 多行水印配置
        self.multi_line_enabled = False  # 是否启用多行模式
        self.multi_line_configs = []  # 多行配置列表
        self.init_default_multi_lines()

    def init_default_multi_lines(self):
        """初始化默认的多行配置"""
        # 创建3行默认配置，设置安全的位置避免超出边界
        line1 = MultiLineWatermarkConfig()
        line1.char_count = 3
        line1.position_x = 70  # 右上角，稍微左移
        line1.position_y = 25  # 增加上边距，避免超出边界

        line2 = MultiLineWatermarkConfig()
        line2.char_count = 2
        line2.position_x = 15  # 左中
        line2.position_y = 50

        line3 = MultiLineWatermarkConfig()
        line3.char_count = 2
        line3.position_x = 75  # 右下，稍微左移
        line3.position_y = 80  # 稍微上移，避免超出下边界

        self.multi_line_configs = [line1, line2, line3]
        print(f"初始化多行水印安全位置: 第1行({line1.position_x}%,{line1.position_y}%), 第2行({line2.position_x}%,{line2.position_y}%), 第3行({line3.position_x}%,{line3.position_y}%)")

    def validate_multiline_positions(self):
        """验证并修正多行水印位置，确保不超出边界"""
        for i, config in enumerate(self.multi_line_configs):
            # 确保位置在合理范围内
            original_x, original_y = config.position_x, config.position_y

            # X位置：5% - 90% 是安全范围
            config.position_x = max(5, min(90, config.position_x))

            # Y位置：10% - 90% 是安全范围
            config.position_y = max(10, min(90, config.position_y))

            if original_x != config.position_x or original_y != config.position_y:
                print(f"第{i+1}行位置修正: ({original_x}%,{original_y}%) -> ({config.position_x}%,{config.position_y}%)")

    def to_dict(self):
        """转换为字典"""
        return {
            'enabled': self.enabled,
            'font_family': self.font_family,
            'font_category': self.font_category,
            'font_size': self.font_size,
            'text_color': self.text_color,
            'shadow_enabled': self.shadow_enabled,
            'shadow_color': self.shadow_color,
            'shadow_offset_x': self.shadow_offset_x,
            'shadow_offset_y': self.shadow_offset_y,
            'stroke_enabled': self.stroke_enabled,
            'stroke_color': self.stroke_color,
            'stroke_width': self.stroke_width,
            'position_x': self.position_x,
            'position_y': self.position_y,
            'opacity': self.opacity,
            # 单行字数设置
            'single_char_count': self.single_char_count,
            # 多行配置
            'multi_line_enabled': self.multi_line_enabled,
            'multi_line_configs': [config.to_dict() for config in self.multi_line_configs]
        }

    def from_dict(self, data):
        """从字典加载"""
        self.enabled = data.get('enabled', False)
        self.font_family = data.get('font_family', "Microsoft YaHei")
        self.font_category = data.get('font_category', "现代字体")
        self.font_size = data.get('font_size', 100)  # 修改默认字体大小以适应实际显示需求
        self.text_color = data.get('text_color', "#FFFFFF")
        self.shadow_enabled = data.get('shadow_enabled', True)
        self.shadow_color = data.get('shadow_color', "#000000")
        self.shadow_offset_x = data.get('shadow_offset_x', 2)
        self.shadow_offset_y = data.get('shadow_offset_y', 2)
        self.stroke_enabled = data.get('stroke_enabled', False)
        self.stroke_color = data.get('stroke_color', "#000000")
        self.stroke_width = data.get('stroke_width', 2)
        self.position_x = data.get('position_x', 50)
        self.position_y = data.get('position_y', 90)
        self.opacity = data.get('opacity', 80)

        # 单行字数设置
        self.single_char_count = data.get('single_char_count', 6)

        # 多行配置
        self.multi_line_enabled = data.get('multi_line_enabled', False)
        multi_line_data = data.get('multi_line_configs', [])
        self.multi_line_configs = []
        for line_data in multi_line_data:
            line_config = MultiLineWatermarkConfig()
            line_config.from_dict(line_data)
            self.multi_line_configs.append(line_config)

        # 如果没有多行配置，初始化默认配置
        if not self.multi_line_configs:
            self.init_default_multi_lines()


class VideoProcessorSignals(QObject):
    """视频处理信号类"""
    progress_updated = pyqtSignal(int, str)  # 进度值, 状态消息
    log_message = pyqtSignal(str, str)  # 消息, 级别
    task_completed = pyqtSignal(dict)  # 完成统计信息
    error_occurred = pyqtSignal(str)  # 错误消息


class BatchRewriteSignals(QObject):
    """批量AI改写信号类"""
    progress_updated = pyqtSignal(int, int, str)  # 当前进度, 总数, 当前文件
    log_message = pyqtSignal(str, str)  # 日志消息, 日志级别
    rewrite_finished = pyqtSignal(dict)  # 改写完成，返回文件名映射
    rewrite_failed = pyqtSignal(str)  # 改写失败，返回错误信息









































# 所有水印相关类已删除

# 所有水印编辑器类已删除

# 所有水印设置区域已删除
# 所有水印设置方法已删除
# 所有水印编辑器方法已删除
# 所有水印设置对话框已删除
# 所有水印对话框初始化代码已删除


class WatermarkSettingsDialog(QDialog):
    """水印设置对话框 - 重构后的简化版本"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("水印设置")
        self.setModal(True)

    def force_update_preview(self):
        """强制更新预览 - 确保预览内容正确显示"""
        try:
            # 防止递归调用
            if hasattr(self, '_force_updating_preview') and self._force_updating_preview:
                return
            self._force_updating_preview = True

            # 初始化预览内容（如果需要）
            if not hasattr(self, '_current_preview_pixmap'):
                self._current_preview_pixmap = None

            # 只在没有其他更新进行时才更新预览
            if not (hasattr(self, '_updating_global_preview') and self._updating_global_preview):
                self.update_preview()

            print("预览已强制更新")  # 调试信息
        except Exception as e:
            print(f"强制更新预览失败: {e}")
        finally:
            # 重置递归标志
            self._force_updating_preview = False



    def create_global_preview_area(self):
        """创建右侧全局预览区域"""
        self.global_preview_widget = QWidget()
        self.global_preview_widget.setMinimumWidth(500)  # 最小宽度
        self.global_preview_widget.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-left: 2px solid #e9ecef;
            }
        """)

        layout = QVBoxLayout(self.global_preview_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题区域
        title_widget = QWidget()
        title_layout = QHBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(10)

        title_label = QLabel("🎬 实时预览")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #2c3e50;
                margin: 0;
            }
        """)
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # 预览状态指示器
        self.preview_status_label = QLabel("● 实时同步")
        self.preview_status_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #28a745;
                font-weight: bold;
            }
        """)
        title_layout.addWidget(self.preview_status_label)

        layout.addWidget(title_widget)

        # 主预览区域
        preview_container = QWidget()
        preview_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 3px solid #dee2e6;
                border-radius: 12px;
            }
        """)

        preview_layout = QVBoxLayout(preview_container)
        preview_layout.setContentsMargins(10, 10, 10, 10)

        # 创建大尺寸预览标签
        self.global_preview_label = QLabel()
        self.global_preview_label.setMinimumSize(480, 270)
        self.global_preview_label.setStyleSheet("""
            QLabel {
                border: 2px solid #e9ecef;
                border-radius: 8px;
                background-color: #f8f9fa;
            }
        """)
        self.global_preview_label.setAlignment(Qt.AlignCenter)
        self.global_preview_label.setScaledContents(True)
        preview_layout.addWidget(self.global_preview_label)

        layout.addWidget(preview_container)

        # 预览信息区域
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(8)



        # 预览尺寸信息
        self.preview_size_label = QLabel("预览尺寸: 480×270")
        self.preview_size_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #868e96;
            }
        """)
        info_layout.addWidget(self.preview_size_label)

        layout.addWidget(info_widget)

        # 添加弹性空间
        layout.addStretch()

        # 初始化全局预览
        self.update_global_preview()

    def create_top_menu_bar(self):
        """创建顶部菜单栏"""
        self.top_menu_bar = QWidget()
        self.top_menu_bar.setFixedHeight(80)
        self.top_menu_bar.setStyleSheet("""
            QWidget {
                background-color: #2c3e50;
                border-bottom: 3px solid #34495e;
            }
        """)

        layout = QHBoxLayout(self.top_menu_bar)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(20)

        # 左侧：应用图标和标题
        left_widget = QWidget()
        left_layout = QHBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(15)

        # 应用图标
        icon_label = QLabel("🎨")
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                color: #ecf0f1;
            }
        """)
        left_layout.addWidget(icon_label)

        # 标题和副标题
        title_widget = QWidget()
        title_layout = QVBoxLayout(title_widget)
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(2)



        left_layout.addWidget(title_widget)
        layout.addWidget(left_widget)

        # 中间：弹性空间
        layout.addStretch()

        # 右侧：状态和操作按钮
        right_widget = QWidget()
        right_layout = QHBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(15)

        # 预览状态指示器
        self.menu_status_label = QLabel("● 实时同步")
        self.menu_status_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #2ecc71;
                font-weight: bold;
                padding: 5px 10px;
                background-color: rgba(46, 204, 113, 0.2);
                border-radius: 12px;
            }
        """)
        right_layout.addWidget(self.menu_status_label)

        # 快捷操作按钮
        reset_btn = QPushButton("重置")
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        reset_btn.clicked.connect(self.reset_settings)
        right_layout.addWidget(reset_btn)

        save_btn = QPushButton("保存")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        save_btn.clicked.connect(self.save_settings)
        right_layout.addWidget(save_btn)

        layout.addWidget(right_widget)

    def reset_settings(self):
        """重置设置到默认值"""
        # 重置预览显示
        if hasattr(self, 'global_preview_label'):
            self.global_preview_label.clear()
            self.global_preview_label.setText("预览区域")



    def save_settings(self):
        """保存当前设置 - 使用视频处理窗口的成功方案"""
        try:
            print("开始保存水印设置...")

            # 方案1：直接同步当前UI状态到settings对象
            self.sync_ui_to_settings()

            # 保存基本设置
            pass

            # 更新菜单栏状态
            if hasattr(self, 'menu_status_label'):
                self.menu_status_label.setText("● 已保存")
                self.menu_status_label.setStyleSheet("""
                    QLabel {
                        font-size: 12px;
                        color: #27ae60;
                        font-weight: bold;
                        padding: 5px 10px;
                        background-color: rgba(39, 174, 96, 0.2);
                        border-radius: 12px;
                    }
                """)

        except Exception as e:
            print(f"保存设置失败: {e}")
            import traceback
            traceback.print_exc()

            # 显示错误状态
            if hasattr(self, 'menu_status_label'):
                self.menu_status_label.setText("● 保存失败")
                self.menu_status_label.setStyleSheet("""
                    QLabel {
                        font-size: 12px;
                        color: #e74c3c;
                        font-weight: bold;
                        padding: 5px 10px;
                        background-color: rgba(231, 76, 60, 0.2);
                        border-radius: 12px;
                    }
                """)

    # 水印UI同步方法已删除



    def connect_realtime_sync_signals(self):
        """连接实时同步信号"""
        try:
            print("🔗 连接实时同步信号...")

            # 基本控件的实时同步
            if hasattr(self, 'enable_checkbox'):
                self.enable_checkbox.stateChanged.connect(self.on_setting_changed)

            if hasattr(self, 'font_size_spin'):
                self.font_size_spin.valueChanged.connect(self.on_setting_changed)

            if hasattr(self, 'opacity_slider'):
                self.opacity_slider.valueChanged.connect(self.on_setting_changed)

            if hasattr(self, 'multiline_checkbox'):
                self.multiline_checkbox.stateChanged.connect(self.on_setting_changed)

            # 位置按钮组
            if hasattr(self, 'position_buttons'):
                self.position_buttons.buttonClicked.connect(self.on_setting_changed)

            # 字体按钮组
            if hasattr(self, 'font_buttons'):
                self.font_buttons.buttonClicked.connect(self.on_setting_changed)

            # 颜色按钮组
            if hasattr(self, 'color_buttons'):
                self.color_buttons.buttonClicked.connect(self.on_setting_changed)

            print("✅ 实时同步信号连接完成")

        except Exception as e:
            print(f"❌ 连接实时同步信号失败: {e}")

    def on_setting_changed(self):
        """设置改变时的处理"""
        try:
            # 延迟同步，避免频繁更新
            if hasattr(self, '_sync_timer'):
                self._sync_timer.stop()
            else:
                from PyQt5.QtCore import QTimer
                self._sync_timer = QTimer()
                self._sync_timer.setSingleShot(True)
                self._sync_timer.timeout.connect(self.delayed_sync_and_update)

            self._sync_timer.start(300)  # 300ms延迟

        except Exception as e:
            print(f"❌ 设置改变处理失败: {e}")

    def delayed_sync_and_update(self):
        """延迟同步和更新"""
        try:
            print("⏰ 执行延迟同步...")

            # 同步UI到设置
            self.sync_ui_to_settings()

            # 触发预览更新
            self.trigger_preview_update()

        except Exception as e:
            print(f"❌ 延迟同步失败: {e}")

    def auto_save_settings(self):
        """自动保存设置（延迟保存，避免频繁写入）"""
        if hasattr(self, '_save_timer'):
            self._save_timer.stop()

        from PyQt5.QtCore import QTimer
        self._save_timer = QTimer()
        self._save_timer.setSingleShot(True)
        self._save_timer.timeout.connect(self.save_settings)
        self._save_timer.start(1000)  # 1秒后保存

    def update_global_preview(self):
        """更新右侧全局预览 - 按照1920×1080分辨率显示水印大小"""
        try:
            # 防止递归调用
            if hasattr(self, '_updating_global_preview') and self._updating_global_preview:
                return
            self._updating_global_preview = True

            # 创建全局预览pixmap，按照1920×1080比例显示水印
            global_preview_pixmap = self.create_global_preview_with_correct_watermark()

            # 更新全局预览标签
            if hasattr(self, 'global_preview_label'):
                self.global_preview_label.setPixmap(global_preview_pixmap)

            # 更新状态信息
            self.update_preview_status()

        except Exception as e:
            print(f"更新全局预览失败: {e}")
        finally:
            # 重置递归标志
            self._updating_global_preview = False

    # 水印预览方法已删除

    def create_simple_default_preview(self):
        """创建简单的默认预览pixmap - 避免递归调用"""
        pixmap = QPixmap(480, 270)
        pixmap.fill(QColor(240, 240, 240))

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制简单的渐变背景
        gradient = QLinearGradient(0, 0, pixmap.width(), pixmap.height())
        gradient.setColorAt(0, QColor(200, 220, 255))
        gradient.setColorAt(1, QColor(150, 180, 220))
        painter.fillRect(pixmap.rect(), gradient)

        # 绘制简单的提示文字
        painter.setPen(QColor(100, 100, 100))
        painter.setFont(QFont("Arial", 16))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "预览区域\n水印将显示在这里")

        painter.end()
        return pixmap

    def update_preview_status(self):
        """更新预览状态信息"""
        if hasattr(self, 'preview_size_label'):
            self.preview_size_label.setText("预览尺寸: 480×270")



    def create_base_preview_pixmap(self):
        """创建基础预览pixmap - 简化版本避免递归"""
        # 创建与主预览相同尺寸的pixmap
        pixmap = QPixmap(500, 400)
        pixmap.fill(QColor(240, 240, 240))

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制与主预览相同的背景
        gradient = QLinearGradient(0, 0, pixmap.width(), pixmap.height())
        gradient.setColorAt(0, QColor(200, 220, 255))
        gradient.setColorAt(1, QColor(150, 180, 220))
        painter.fillRect(pixmap.rect(), gradient)

        # 水印功能已删除
        painter.setPen(QColor(100, 100, 100))
        painter.setFont(QFont("Arial", 16))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "预览区域")

        painter.end()
        return pixmap



    def init_ui(self):
        """初始化界面 - 顶部菜单栏 + 左右布局模式"""
        # 创建主布局（垂直布局）
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建顶部菜单栏
        self.create_top_menu_bar()
        main_layout.addWidget(self.top_menu_bar)

        # 创建下方内容区域（水平布局）
        content_widget = QWidget()
        content_layout = QHBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)

        # 左侧功能区域
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(0)

        # 创建侧边栏导航
        self.sidebar = SidebarNavigation()
        self.sidebar.navigationChanged.connect(self.on_navigation_changed)
        left_layout.addWidget(self.sidebar)

        # 创建分隔线
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.VLine)
        separator1.setFrameShadow(QFrame.Sunken)
        left_layout.addWidget(separator1)

        # 创建左侧内容区域（不包含预览）
        self.content_area = ContentArea()
        left_layout.addWidget(self.content_area)

        # 添加左侧区域到内容布局
        content_layout.addWidget(left_widget, 2)  # 占2/3空间

        # 创建分隔线
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.VLine)
        separator2.setFrameShadow(QFrame.Sunken)
        content_layout.addWidget(separator2)

        # 右侧全局预览区域
        self.create_global_preview_area()
        content_layout.addWidget(self.global_preview_widget, 1)  # 占1/3空间

        # 添加内容区域到主布局
        main_layout.addWidget(content_widget)

        # 创建侧边栏布局
        self.create_sidebar_layout()

        # 连接信号
        self.connect_signals()

        # 连接实时同步信号
        self.connect_realtime_sync_signals()

        # 加载导航状态
        self.sidebar.load_navigation_state()

    def create_sidebar_layout(self):
        """创建侧边栏布局"""
        # 设置导航项
        navigation_items = [
            {"name": "文字设置", "icon": "📝"},
            {"name": "字体样式", "icon": "🎨"},
            {"name": "颜色位置", "icon": "📍"},
            {"name": "多行模式", "icon": "📄"},
            {"name": "效果设置", "icon": "✨"},
            {"name": "模板选择", "icon": "🎭"},
            {"name": "预览调试", "icon": "👁"}
        ]

        self.sidebar.set_navigation_items(navigation_items)

        # 创建内容组件
        self.create_content_widgets()

        # 设置默认选中第一项
        self.sidebar.set_current_index(0)
        self.on_navigation_changed(0, "文字设置")

    def create_content_widgets(self):
        """创建内容组件"""
        # 文字设置内容
        text_content = self.create_text_content()
        self.content_area.add_content_widget("文字设置", text_content)

        # 字体样式内容
        font_content = self.create_font_content()
        self.content_area.add_content_widget("字体样式", font_content)

        # 颜色位置内容
        color_position_content = self.create_color_position_content()
        self.content_area.add_content_widget("颜色位置", color_position_content)

        # 多行模式内容
        multiline_content = self.create_multiline_content()
        self.content_area.add_content_widget("多行模式", multiline_content)

        # 水印功能已删除

        # 效果设置内容
        effects_content = self.create_effects_content()
        self.content_area.add_content_widget("效果设置", effects_content)

        # 模板选择内容
        template_content = self.create_template_content()
        self.content_area.add_content_widget("模板选择", template_content)

        # 预览调试内容
        preview_content = self.create_preview_content()
        self.content_area.add_content_widget("预览调试", preview_content)

    def on_navigation_changed(self, index, name):
        """导航选择改变事件"""
        _ = index  # 使用变量避免警告
        # 显示对应的内容
        self.content_area.show_content(name, name)

        # 强制更新预览
        self.force_update_preview()

        print(f"导航切换到: {name}，预览已更新")  # 调试信息









    def create_text_input_section(self):
        """创建文本输入区域 - 仅支持文件名水印"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        # 水印来源说明
        info_label = QLabel("💡 水印内容：自动使用视频文件名（已去除扩展名）")
        info_label.setStyleSheet("color: #666; font-size: 12px; padding: 5px;")
        layout.addWidget(info_label)

        # 启用水印复选框
        self.enable_checkbox = QCheckBox("启用水印")
        self.enable_checkbox.setChecked(True)
        layout.addWidget(self.enable_checkbox)

        # 文件名清理选项
        cleanup_label = QLabel("文件名清理选项：")
        cleanup_label.setStyleSheet("color: #333; font-weight: bold; margin-top: 10px;")
        layout.addWidget(cleanup_label)

        # 移除特殊字符复选框
        self.remove_special_chars = QCheckBox("移除特殊字符和标点符号")
        self.remove_special_chars.setChecked(True)
        self.remove_special_chars.setToolTip("移除文件名中的标点符号、特殊字符等")
        layout.addWidget(self.remove_special_chars)

        # 限制长度复选框
        self.limit_length = QCheckBox("限制水印长度")
        self.limit_length.setChecked(True)
        self.limit_length.setToolTip("过长的文件名将被截断并添加省略号")
        layout.addWidget(self.limit_length)
        # use_filename_checkbox已移除，强制使用文件名

        return widget

    def create_style_section(self):
        """创建外观样式区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)

        # 字体选择
        font_section = self.create_font_section()
        layout.addWidget(font_section)

        # 颜色选择
        color_section = self.create_color_section()
        layout.addWidget(color_section)

        return widget

    def create_position_layout_section(self):
        """创建位置布局区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)

        # 位置选择
        position_section = self.create_position_section()
        layout.addWidget(position_section)

        # 注意：多行设置已移至专门的多行模式页面的左中右布局中

        return widget

    def create_preview_section(self):
        """创建预览区域 - 现在只显示提示信息"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 预览提示信息
        info_label = QLabel("📺 实时预览")
        info_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border-left: 4px solid #3498db;
            }
        """)
        layout.addWidget(info_label)

        # 说明文字
        desc_label = QLabel("水印预览已移至右侧全局预览区域\n\n在左侧调整设置时，右侧会实时显示预览效果")
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #7f8c8d;
                line-height: 1.6;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 6px;
            }
        """)
        desc_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(desc_label)

        layout.addStretch()
        return widget

    def initialize_preview_content(self):
        """初始化预览内容"""
        # 创建初始预览图像
        from PyQt5.QtGui import QPixmap, QPainter, QColor, QLinearGradient, QFont, QPen
        from PyQt5.QtCore import Qt

        pixmap = QPixmap(600, 400)
        pixmap.fill(QColor(240, 240, 240))

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制背景渐变
        gradient = QLinearGradient(0, 0, pixmap.width(), pixmap.height())
        gradient.setColorAt(0, QColor(200, 220, 255))
        gradient.setColorAt(1, QColor(150, 180, 220))
        painter.fillRect(pixmap.rect(), gradient)

        # 绘制边框
        painter.setPen(QPen(QColor(100, 100, 100), 2))
        painter.drawRect(pixmap.rect().adjusted(1, 1, -1, -1))

        # 绘制示例文字
        painter.setPen(QColor(50, 50, 50))
        painter.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "水印预览区域\n\n请在左侧设置水印参数\n修改设置后将实时显示效果")

        # 绘制示例水印
        if hasattr(self, 'temp_settings') and self.temp_settings.text:
            painter.setPen(QColor(255, 0, 0, 180))  # 半透明红色
            painter.setFont(QFont("Microsoft YaHei", 24, QFont.Bold))
            painter.drawText(50, 350, f"示例水印: {self.temp_settings.text}")

        painter.end()

        # 设置到预览标签
        if hasattr(self, 'preview_label') and self.preview_label:
            self.preview_label.setPixmap(pixmap)
            print("预览内容已初始化")  # 调试信息

    def create_button_section(self):
        """创建按钮区域"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(20, 15, 20, 15)
        layout.setSpacing(15)

        # 添加弹簧
        layout.addStretch()

        # 重置按钮
        self.reset_button = QPushButton("重置")
        self.reset_button.setMinimumSize(80, 35)
        self.reset_button.clicked.connect(self.reset_settings)
        layout.addWidget(self.reset_button)

        # 确定按钮
        self.ok_button = QPushButton("确定")
        self.ok_button.setMinimumSize(80, 35)
        self.ok_button.clicked.connect(self.accept)
        layout.addWidget(self.ok_button)

        # 取消按钮
        cancel_button = QPushButton("取消")
        cancel_button.setMinimumSize(80, 35)
        cancel_button.clicked.connect(self.reject)
        layout.addWidget(cancel_button)

        return widget

    def create_text_content(self):
        """创建文字设置内容 - 左右布局"""
        widget = QWidget()
        main_layout = QHBoxLayout(widget)
        main_layout.setContentsMargins(0, 0, 0, 20)
        main_layout.setSpacing(20)

        # 左侧：文本输入区域
        left_section = QWidget()
        left_layout = QVBoxLayout(left_section)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(15)

        # 文本输入区域
        text_section = self.create_text_input_section()
        left_layout.addWidget(text_section)

        # 底部按钮
        button_section = self.create_button_section()
        left_layout.addWidget(button_section)

        # 添加弹簧推到底部
        left_layout.addStretch()

        left_section.setMinimumWidth(350)
        left_section.setMaximumWidth(400)
        main_layout.addWidget(left_section, 1)  # 权重1

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        main_layout.addWidget(separator)

        # 右侧：预览区域
        right_section = self.create_text_preview_section()
        main_layout.addWidget(right_section, 2)  # 权重2，占更多空间

        return widget

    def create_text_preview_section(self):
        """创建文字设置专用的预览区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        # 预览标题
        title_label = QLabel("实时预览")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                padding: 5px;
                border-bottom: 2px solid #007ACC;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # 说明文字
        desc_label = QLabel("文字效果预览已移至右侧全局预览区域\n\n输入文字内容并调整样式时，右侧会实时显示效果")
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #7f8c8d;
                line-height: 1.6;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 6px;
            }
        """)
        desc_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(desc_label)

        # 预览信息
        info_layout = QHBoxLayout()

        # 文字长度信息
        self.text_length_label = QLabel("字符数: 0")
        self.text_length_label.setStyleSheet("font-size: 12px; color: #666;")
        info_layout.addWidget(self.text_length_label)

        info_layout.addStretch()

        # 预览状态信息
        self.preview_status_label = QLabel("预览已更新")
        self.preview_status_label.setStyleSheet("font-size: 12px; color: #007ACC;")
        info_layout.addWidget(self.preview_status_label)

        layout.addLayout(info_layout)

        return widget

    def initialize_text_preview_content(self):
        """初始化文字预览内容"""
        from PyQt5.QtGui import QPixmap, QPainter, QColor, QLinearGradient, QFont, QPen
        from PyQt5.QtCore import Qt

        pixmap = QPixmap(500, 350)
        pixmap.fill(QColor(245, 245, 245))

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制背景渐变
        gradient = QLinearGradient(0, 0, pixmap.width(), pixmap.height())
        gradient.setColorAt(0, QColor(220, 235, 255))
        gradient.setColorAt(1, QColor(180, 210, 240))
        painter.fillRect(pixmap.rect(), gradient)

        # 绘制边框
        painter.setPen(QPen(QColor(120, 120, 120), 1))
        painter.drawRect(pixmap.rect().adjusted(0, 0, -1, -1))

        # 绘制提示文字
        painter.setPen(QColor(80, 80, 80))
        painter.setFont(QFont("Microsoft YaHei", 14))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "文字设置预览区域\n\n在左侧输入水印文字\n调整设置后将实时显示效果")

        painter.end()

        # 设置到预览标签
        if hasattr(self, 'text_preview_label') and self.text_preview_label:
            self.text_preview_label.setPixmap(pixmap)

    def create_font_content(self):
        """创建字体样式内容"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 20)
        layout.setSpacing(20)

        # 字体设置区域
        font_section = self.create_font_section()
        layout.addWidget(font_section)

        # 预览区域
        preview_section = self.create_preview_section()
        layout.addWidget(preview_section)

        return widget

    def create_color_position_content(self):
        """创建颜色位置内容"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 20)
        layout.setSpacing(20)

        # 颜色设置
        color_section = self.create_color_section()
        layout.addWidget(color_section)

        # 位置设置
        position_section = self.create_position_section()
        layout.addWidget(position_section)

        # 预览区域
        preview_section = self.create_preview_section()
        layout.addWidget(preview_section)

        return widget

    def create_multiline_content(self):
        """创建多行模式内容 - 完整的多行水印编辑器"""
        widget = QWidget()
        main_layout = QVBoxLayout(widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # 顶部：多行模式控制
        top_section = self.create_multiline_top_section()
        main_layout.addWidget(top_section)

        # 中间：多行编辑器（可滚动）
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setMinimumHeight(400)

        # 多行编辑器容器
        self.multiline_editor_widget = QWidget()
        self.multiline_editor_layout = QVBoxLayout(self.multiline_editor_widget)
        self.multiline_editor_layout.setContentsMargins(10, 10, 10, 10)
        self.multiline_editor_layout.setSpacing(15)

        # 初始化多行编辑器
        self.init_multiline_editor()

        scroll_area.setWidget(self.multiline_editor_widget)
        main_layout.addWidget(scroll_area)

        # 底部：操作按钮
        bottom_section = self.create_multiline_bottom_section()
        main_layout.addWidget(bottom_section)

        return widget

    def create_multiline_top_section(self):
        """创建多行模式顶部控制区域"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(20)

        # 左侧：基本控制
        left_group = QGroupBox("基本设置")
        left_layout = QVBoxLayout(left_group)
        left_layout.setSpacing(10)

        # 启用多行模式
        self.multiline_checkbox = QCheckBox("启用多行模式")
        self.multiline_checkbox.setChecked(self.temp_settings.multi_line_mode)
        self.multiline_checkbox.toggled.connect(self.on_multiline_mode_changed)
        left_layout.addWidget(self.multiline_checkbox)

        # 使用文件名
        self.filename_multiline_checkbox = QCheckBox("使用文件名作为水印源")
        self.filename_multiline_checkbox.setChecked(getattr(self.temp_settings, 'use_filename_multiline', False))
        self.filename_multiline_checkbox.toggled.connect(self.on_multiline_settings_changed)
        left_layout.addWidget(self.filename_multiline_checkbox)

        layout.addWidget(left_group)

        # 中间：位置设置
        center_group = QGroupBox("位置设置")
        center_layout = QGridLayout(center_group)
        center_layout.setSpacing(10)

        # 水印位置
        center_layout.addWidget(QLabel("水印位置:"), 0, 0)
        self.multiline_position_combo = QComboBox()
        self.multiline_position_combo.addItems([
            "左上角", "上中", "右上角",
            "左中", "居中", "右中",
            "左下角", "下中", "右下角"
        ])
        # 设置当前位置
        current_position = getattr(self.temp_settings, 'position', 'bottom_right')
        position_map = {
            'top_left': '左上角', 'top_center': '上中', 'top_right': '右上角',
            'middle_left': '左中', 'middle_center': '居中', 'middle_right': '右中',
            'bottom_left': '左下角', 'bottom_center': '下中', 'bottom_right': '右下角'
        }
        if current_position in position_map:
            self.multiline_position_combo.setCurrentText(position_map[current_position])
        self.multiline_position_combo.currentTextChanged.connect(self.on_multiline_position_changed)
        center_layout.addWidget(self.multiline_position_combo, 0, 1)

        # 水平偏移
        center_layout.addWidget(QLabel("水平偏移:"), 1, 0)
        self.multiline_offset_x_spin = QSpinBox()
        self.multiline_offset_x_spin.setRange(-500, 500)
        self.multiline_offset_x_spin.setValue(getattr(self.temp_settings, 'offset_x', 20))
        self.multiline_offset_x_spin.setSuffix("px")
        self.multiline_offset_x_spin.valueChanged.connect(self.on_multiline_offset_changed)
        center_layout.addWidget(self.multiline_offset_x_spin, 1, 1)

        # 垂直偏移
        center_layout.addWidget(QLabel("垂直偏移:"), 2, 0)
        self.multiline_offset_y_spin = QSpinBox()
        self.multiline_offset_y_spin.setRange(-500, 500)
        self.multiline_offset_y_spin.setValue(getattr(self.temp_settings, 'offset_y', 20))
        self.multiline_offset_y_spin.setSuffix("px")
        self.multiline_offset_y_spin.valueChanged.connect(self.on_multiline_offset_changed)
        center_layout.addWidget(self.multiline_offset_y_spin, 2, 1)

        layout.addWidget(center_group)

        # 右侧：全局设置
        right_group = QGroupBox("全局设置")
        right_layout = QGridLayout(right_group)
        right_layout.setSpacing(10)

        # 行间距
        right_layout.addWidget(QLabel("行间距:"), 0, 0)
        self.line_spacing_spin = QSpinBox()
        self.line_spacing_spin.setRange(0, 50)
        self.line_spacing_spin.setValue(getattr(self.temp_settings, 'line_spacing', 5))
        self.line_spacing_spin.setSuffix("px")
        self.line_spacing_spin.valueChanged.connect(self.on_line_spacing_changed)
        right_layout.addWidget(self.line_spacing_spin, 0, 1)

        # 最大显示行数
        right_layout.addWidget(QLabel("最大显示行数:"), 1, 0)
        self.max_display_lines_spin = QSpinBox()
        self.max_display_lines_spin.setRange(1, 10)
        self.max_display_lines_spin.setValue(getattr(self.temp_settings, 'display_lines', 3))
        self.max_display_lines_spin.valueChanged.connect(self.on_display_lines_changed)
        right_layout.addWidget(self.max_display_lines_spin, 1, 1)

        layout.addWidget(right_group)
        layout.addStretch()

        return widget

    def create_multiline_bottom_section(self):
        """创建多行模式底部操作区域"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)

        # 添加行按钮
        add_line_btn = QPushButton("➕ 添加行")
        add_line_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        add_line_btn.clicked.connect(self.add_watermark_line)
        layout.addWidget(add_line_btn)

        # 清空所有行按钮
        clear_all_btn = QPushButton("🗑️ 清空所有")
        clear_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        clear_all_btn.clicked.connect(self.clear_all_lines)
        layout.addWidget(clear_all_btn)

        layout.addStretch()

        # 状态信息
        self.multiline_status_label = QLabel("准备添加水印行")
        self.multiline_status_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 12px;
                padding: 5px 10px;
                background-color: #f8f9fa;
                border-radius: 4px;
            }
        """)
        layout.addWidget(self.multiline_status_label)

        return widget

    def init_multiline_editor(self):
        """初始化多行编辑器"""
        # 确保temp_settings有lines属性
        if not hasattr(self.temp_settings, 'lines'):
            self.temp_settings.lines = []

        # 如果没有行，添加默认行
        if not self.temp_settings.lines:
            self.add_default_lines()

        # 创建行编辑器列表
        self.line_editors = []

        # 为每个现有行创建编辑器
        for i, line_settings in enumerate(self.temp_settings.lines):
            self.create_line_editor(i, line_settings)

        # 更新状态
        self.update_multiline_status()

    def add_default_lines(self):
        """添加默认的水印行"""
        # 添加默认的多行配置
        if hasattr(self, 'watermark_config') and hasattr(self.watermark_config, 'multi_line_configs'):
            if not self.watermark_config.multi_line_configs:
                self.watermark_config.init_default_multi_lines()

    def create_line_editor(self, index, line_settings):
        """创建单行编辑器"""
        _ = index, line_settings  # 使用变量避免警告
        # 简化的行编辑器创建
        return QWidget()

    # 所有水印行管理方法已删除

    # 所有水印多行模式方法已删除



    def create_multiline_center_section(self):
        """创建多行模式中间预览区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # 标题
        title_label = QLabel("📺 多行水印实时预览")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #2c3e50;
                padding: 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 6px;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(title_label)

        # 预览容器 - 使用与单行水印完全一致的设置
        preview_container = QWidget()
        preview_container.setFixedSize(768, 432)  # 与单行水印保持一致
        preview_container.setStyleSheet("""
            QWidget {
                border: 3px solid #34495e;
                border-radius: 8px;
                background-color: #2c3e50;
            }
        """)
        preview_layout = QVBoxLayout(preview_container)
        preview_layout.setContentsMargins(0, 0, 0, 0)

        # 预览标签 - 直接使用单行水印的预览容器，设置为多行模式
        self.multiline_preview_label = DraggableWatermarkLabel(preview_container)
        # 使用与单行水印完全一致的设置：填充整个容器
        self.multiline_preview_label.setGeometry(0, 0, 768, 432)  # 与单行水印保持一致
        self.multiline_preview_label.setAlignment(Qt.AlignCenter)
        self.multiline_preview_label.setScaledContents(True)

        # 设置预览尺寸 - 使用与单行水印完全一致的尺寸
        self.multiline_preview_label.set_preview_dimensions(768, 432)
        self.multiline_preview_label.set_cover_dimensions(1920, 1080)

        # 设置为多行模式
        self.multiline_preview_label.set_multiline_mode(True)

        # 调试信息：验证尺寸设置
        print(f"多行水印预览设置完成:")
        print(f"  容器尺寸: {preview_container.size()}")
        print(f"  标签尺寸: {self.multiline_preview_label.size()}")
        print(f"  预览尺寸: 768x432")
        print(f"  封面尺寸: 1920x1080")

        # 连接多行水印信号
        self.multiline_preview_label.multi_line_position_changed.connect(self.on_multiline_position_changed)
        self.multiline_preview_label.line_selected.connect(self.on_multiline_line_selected)

        self.multiline_preview_label.setStyleSheet("""
            QLabel {
                background-color: transparent;
            }
        """)
        # 标签已通过setGeometry直接定位，不需要通过布局添加
        layout.addWidget(preview_container)

        # 预览信息
        info_layout = QHBoxLayout()

        # 当前行数显示
        self.current_lines_label = QLabel("当前行数: 1")
        self.current_lines_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 11px;
                padding: 4px 8px;
                background-color: #ecf0f1;
                border-radius: 3px;
            }
        """)
        info_layout.addWidget(self.current_lines_label)

        info_layout.addStretch()

        # 字符统计显示
        self.char_count_label = QLabel("字符数: 0")
        self.char_count_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 11px;
                padding: 4px 8px;
                background-color: #ecf0f1;
                border-radius: 3px;
            }
        """)
        info_layout.addWidget(self.char_count_label)

        layout.addLayout(info_layout)

        return widget

    def create_multiline_right_section(self):
        """创建多行模式右侧运行参数区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # 标题
        title_label = QLabel("运行参数")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)

        # 字符数限制设置
        chars_group = QWidget()
        chars_layout = QVBoxLayout(chars_group)
        chars_layout.setContentsMargins(0, 0, 0, 0)
        chars_layout.setSpacing(8)

        # 字符数标题和值
        chars_header = QHBoxLayout()
        chars_label = QLabel("单行字符数:")
        chars_header.addWidget(chars_label)
        chars_header.addStretch()
        self.chars_value_label = QLabel("20")
        self.chars_value_label.setStyleSheet("font-weight: bold; color: #007bff;")
        chars_header.addWidget(self.chars_value_label)
        chars_layout.addLayout(chars_header)

        # 字符数滑块
        self.chars_per_line_slider = QSlider(Qt.Horizontal)
        self.chars_per_line_slider.setRange(5, 50)
        self.chars_per_line_slider.setValue(20)
        chars_layout.addWidget(self.chars_per_line_slider)
        layout.addWidget(chars_group)

        # 行间距设置
        spacing_group = QWidget()
        spacing_layout = QVBoxLayout(spacing_group)
        spacing_layout.setContentsMargins(0, 0, 0, 0)
        spacing_layout.setSpacing(8)

        # 行间距标题和值
        spacing_header = QHBoxLayout()
        spacing_label = QLabel("行间距:")
        spacing_header.addWidget(spacing_label)
        spacing_header.addStretch()
        self.spacing_value_label = QLabel("5px")
        self.spacing_value_label.setStyleSheet("font-weight: bold; color: #007bff;")
        spacing_header.addWidget(self.spacing_value_label)
        spacing_layout.addLayout(spacing_header)

        # 行间距滑块
        self.line_spacing_slider = QSlider(Qt.Horizontal)
        self.line_spacing_slider.setRange(0, 20)
        self.line_spacing_slider.setValue(5)
        spacing_layout.addWidget(self.line_spacing_slider)
        layout.addWidget(spacing_group)

        # 最大行数设置
        max_lines_layout = QHBoxLayout()
        max_lines_label = QLabel("最大行数:")
        max_lines_layout.addWidget(max_lines_label)
        max_lines_layout.addStretch()

        self.max_lines_spin = QSpinBox()
        self.max_lines_spin.setRange(1, 10)
        self.max_lines_spin.setValue(3)
        self.max_lines_spin.setMinimumWidth(60)
        max_lines_layout.addWidget(self.max_lines_spin)
        layout.addLayout(max_lines_layout)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator)

        # 性能参数
        performance_label = QLabel("性能参数")
        performance_font = QFont()
        performance_font.setPointSize(10)
        performance_font.setBold(True)
        performance_label.setFont(performance_font)
        layout.addWidget(performance_label)

        # 处理速度
        speed_layout = QHBoxLayout()
        speed_label = QLabel("处理速度:")
        speed_layout.addWidget(speed_label)
        speed_layout.addStretch()

        self.processing_speed_combo = QComboBox()
        self.processing_speed_combo.addItems(["快速", "标准", "高质量"])
        self.processing_speed_combo.setCurrentText("标准")
        speed_layout.addWidget(self.processing_speed_combo)
        layout.addLayout(speed_layout)

        # 内存使用
        memory_layout = QHBoxLayout()
        memory_label = QLabel("内存使用:")
        memory_layout.addWidget(memory_label)
        memory_layout.addStretch()

        self.memory_usage_combo = QComboBox()
        self.memory_usage_combo.addItems(["低", "中", "高"])
        self.memory_usage_combo.setCurrentText("中")
        memory_layout.addWidget(self.memory_usage_combo)
        layout.addLayout(memory_layout)

        # 弹簧
        layout.addStretch()

        return widget

    def create_effects_content(self):
        """创建效果设置内容"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 20)
        layout.setSpacing(20)

        # 透明度设置
        opacity_section = self.create_opacity_section()
        layout.addWidget(opacity_section)

        # 效果设置
        effects_section = self.create_effects_section()
        layout.addWidget(effects_section)

        # 预览区域
        preview_section = self.create_preview_section()
        layout.addWidget(preview_section)

        return widget

    def create_template_content(self):
        """创建模板选择内容"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 20)
        layout.setSpacing(20)

        # 模板选择区域
        template_section = self.create_template_section()
        layout.addWidget(template_section)

        # 预览区域
        preview_section = self.create_preview_section()
        layout.addWidget(preview_section)

        return widget

    def create_preview_content(self):
        """创建预览调试内容"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 20)
        layout.setSpacing(20)

        # 大尺寸预览区域
        preview_section = self.create_large_preview_section()
        layout.addWidget(preview_section)

        # 调试信息区域
        debug_section = self.create_debug_section()
        layout.addWidget(debug_section)

        return widget

    def create_large_preview_section(self):
        """创建大尺寸预览区域 - 现在只显示提示"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 预览提示信息
        info_label = QLabel("🖼️ 大尺寸预览")
        info_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                border-left: 4px solid #9b59b6;
            }
        """)
        layout.addWidget(info_label)

        # 说明文字
        desc_label = QLabel("大尺寸预览已整合到右侧全局预览区域\n\n右侧预览区域提供了更好的预览体验")
        desc_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #7f8c8d;
                line-height: 1.6;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 6px;
            }
        """)
        desc_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(desc_label)

        layout.addStretch()
        return widget

    def create_debug_section(self):
        """创建调试信息区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # 调试信息标签
        debug_label = QLabel("调试信息")
        debug_font = QFont()
        debug_font.setBold(True)
        debug_label.setFont(debug_font)
        layout.addWidget(debug_label)

        # 调试信息文本
        self.debug_text = QLabel("水印设置正常")
        self.debug_text.setWordWrap(True)
        self.debug_text.setStyleSheet("color: #666; padding: 10px; background-color: #f8f9fa; border-radius: 4px;")
        layout.addWidget(self.debug_text)

        return widget







    def create_opacity_section(self):
        """创建透明度设置区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        # 透明度标签和值
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(QLabel("透明度:"))

        self.opacity_value_label = QLabel("100%")
        self.opacity_value_label.setStyleSheet("font-size: 11px; color: #666; min-width: 35px;")
        opacity_layout.addStretch()
        opacity_layout.addWidget(self.opacity_value_label)
        layout.addLayout(opacity_layout)

        # 透明度滑块
        self.opacity_slider = QSlider(Qt.Horizontal)
        self.opacity_slider.setRange(0, 100)
        self.opacity_slider.setValue(100)
        self.opacity_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #e1e8ed;
                height: 6px;
                background: #f1f5f9;
                border-radius: 3px;
            }
            QSlider::handle:horizontal {
                background: #3b82f6;
                border: 1px solid #3b82f6;
                width: 16px;
                height: 16px;
                border-radius: 8px;
                margin: -5px 0;
            }
            QSlider::sub-page:horizontal {
                background: #3b82f6;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.opacity_slider)

        return widget

    def resizeEvent(self, a0):
        """窗口大小变化事件"""
        super().resizeEvent(a0)
        self.adjust_layout_for_size()

    def adjust_layout_for_size(self):
        """根据窗口大小调整布局"""
        window_width = self.width()
        window_height = self.height()

        # 根据窗口宽度调整面板宽度
        if window_width > 1400:
            # 大屏幕：增加左右面板宽度
            if hasattr(self, 'left_panel'):
                self.left_panel.setFixedWidth(400)
            if hasattr(self, 'right_panel'):
                self.right_panel.setFixedWidth(350)
        elif window_width < 1000:
            # 小屏幕：减少左右面板宽度
            if hasattr(self, 'left_panel'):
                self.left_panel.setFixedWidth(300)
            if hasattr(self, 'right_panel'):
                self.right_panel.setFixedWidth(280)
        else:
            # 默认尺寸
            if hasattr(self, 'left_panel'):
                self.left_panel.setFixedWidth(350)
            if hasattr(self, 'right_panel'):
                self.right_panel.setFixedWidth(300)

        # 根据窗口高度调整预览图大小
        if window_height > 800:
            # 大屏幕：增加预览图尺寸
            if hasattr(self, 'preview_label'):
                self.preview_label.setMinimumSize(450, 360)
        elif window_height < 600:
            # 小屏幕：减少预览图尺寸
            if hasattr(self, 'preview_label'):
                self.preview_label.setMinimumSize(300, 240)
        else:
            # 默认尺寸
            if hasattr(self, 'preview_label'):
                self.preview_label.setMinimumSize(350, 280)



    def create_template_section(self):
        """创建模板选择区域 - 原生样式"""
        widget = QWidget()
        widget.setMinimumWidth(500)  # 增加最小宽度避免挤压
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        # 模板标题 - 原生样式
        template_label = QLabel("水印模板")
        # 移除自定义样式，使用系统默认外观
        layout.addWidget(template_label)

        # 模板网格容器 - 原生样式
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(140)
        # 移除自定义样式，使用系统默认外观

        # 模板网格内容 - 紧凑布局
        template_content = QWidget()
        template_layout = QGridLayout(template_content)
        template_layout.setSpacing(4)
        template_layout.setContentsMargins(6, 4, 6, 4)

        # 创建模板按钮
        self.template_buttons = []
        templates = []  # 水印模板功能已删除

        for i, template in enumerate(templates):
            btn = QPushButton()
            btn.setFixedSize(80, 40)
            btn.setCheckable(True)
            # 移除自定义样式，使用系统默认外观

            # 设置按钮文本（模板名称）
            btn.setText(template["name"])
            btn.setToolTip(template["description"])

            # 连接点击事件
            btn.clicked.connect(lambda checked, idx=i: self.apply_template(idx))

            # 添加到网格 - 5列2行布局
            row = i // 5
            col = i % 5
            template_layout.addWidget(btn, row, col)
            self.template_buttons.append(btn)

        scroll_area.setWidget(template_content)
        layout.addWidget(scroll_area)

        return widget

    def apply_template(self, template_index):
        """应用选中的模板 - 水印功能已删除"""
        # 水印功能已删除，此方法不再执行任何操作
        pass



    def create_font_section(self):
        """创建字体选择区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(5, 5, 5, 5)  # 添加内边距
        layout.setSpacing(18)  # 增加间距

        # 字体标题 - 原生样式
        font_label = QLabel("字体")
        # 移除自定义样式，使用系统默认外观
        layout.addWidget(font_label)

        # 字体选择网格
        font_grid = self.create_font_grid()
        layout.addWidget(font_grid)

        # 字体大小 - 原生样式
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("大小:"))
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 200)
        self.font_size_spin.setValue(36)
        # 移除自定义样式，使用系统默认外观
        size_layout.addWidget(self.font_size_spin)
        size_layout.addStretch()
        layout.addLayout(size_layout)

        return widget

    def create_font_grid(self):
        """创建字体选择网格"""
        widget = QWidget()
        grid = QGridLayout(widget)
        grid.setSpacing(12)  # 增加间距
        grid.setContentsMargins(5, 5, 5, 5)  # 添加内边距

        # 字体选项 - 使用系统常见字体
        fonts = [
            ("微软雅黑", "Microsoft YaHei"),
            ("宋体", "SimSun"),
            ("黑体", "SimHei"),
            ("楷体", "KaiTi"),
            ("仿宋", "FangSong"),
            ("Arial", "Arial"),
            ("Times New Roman", "Times New Roman"),
            ("Calibri", "Calibri"),
            ("Verdana", "Verdana")
        ]

        self.font_buttons = QButtonGroup()

        for i, (name, font_family) in enumerate(fonts):
            button = QPushButton(name)
            button.setCheckable(True)
            button.setProperty("font_family", font_family)
            button.setMinimumSize(80, 35)  # 设置最小尺寸确保可见
            # 移除自定义样式，使用系统默认外观
            self.font_buttons.addButton(button)
            grid.addWidget(button, i // 3, i % 3)

            # 设置默认选择（微软雅黑）
            if font_family == "Microsoft YaHei":
                button.setChecked(True)

        return widget

    def create_color_section(self):
        """创建颜色选择区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)

        # 颜色标题
        color_label = QLabel("颜色")
        color_label.setStyleSheet("font-weight: bold; color: #333; font-size: 14px;")
        layout.addWidget(color_label)

        # 颜色选择网格
        color_grid = self.create_color_grid()
        layout.addWidget(color_grid)

        return widget

    def create_color_grid(self):
        """创建颜色选择网格"""
        widget = QWidget()
        grid = QGridLayout(widget)
        grid.setSpacing(8)

        # 预设颜色
        colors = [
            ("#000000", "黑色"),
            ("#666666", "灰色"),
            ("#999999", "浅灰"),
            ("#FF0000", "红色"),
            ("#00FF00", "绿色"),
            ("#0000FF", "蓝色"),
            ("#FFFF00", "黄色"),
            ("#FF00FF", "紫色"),
            ("#00FFFF", "青色"),
            ("#FFFFFF", "白色"),
            ("#FFA500", "橙色"),
            ("#800080", "深紫")
        ]

        self.color_buttons = QButtonGroup()

        for i, (color_code, color_name) in enumerate(colors):
            button = QPushButton()
            button.setFixedSize(40, 30)
            button.setCheckable(True)
            button.setProperty("color_code", color_code)
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color_code};
                    border: 2px solid #ddd;
                    border-radius: 4px;
                }}
                QPushButton:checked {{
                    border-color: #007acc;
                    border-width: 3px;
                }}
            """)
            button.setToolTip(color_name)
            self.color_buttons.addButton(button)
            grid.addWidget(button, i // 4, i % 4)

        return widget

    def create_position_section(self):
        """创建位置设置区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)

        # 透明度设置
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(QLabel("透明度:"))
        self.opacity_slider = QSlider(Qt.Horizontal)
        self.opacity_slider.setRange(0, 100)
        self.opacity_slider.setValue(80)
        self.opacity_label = QLabel("80%")
        opacity_layout.addWidget(self.opacity_slider)
        opacity_layout.addWidget(self.opacity_label)
        layout.addLayout(opacity_layout)

        # 位置选择网格
        position_grid = self.create_position_grid()
        layout.addWidget(position_grid)

        return widget

    def create_position_grid(self):
        """创建位置选择网格"""
        widget = QWidget()
        grid = QGridLayout(widget)
        grid.setSpacing(5)

        # 9宫格位置 - 水印功能已删除
        positions = [
            ("左上", "top_left", 0, 0),
            ("中上", "top_center", 0, 1),
            ("右上", "top_right", 0, 2),
            ("左中", "middle_left", 1, 0),
            ("居中", "middle_center", 1, 1),
            ("右中", "middle_right", 1, 2),
            ("左下", "bottom_left", 2, 0),
            ("中下", "bottom_center", 2, 1),
            ("右下", "bottom_right", 2, 2),
        ]

        self.position_buttons = QButtonGroup()

        for text, position, row, col in positions:
            button = QPushButton(text)
            button.setCheckable(True)
            button.setProperty("position", position)
            button.setFixedSize(60, 40)
            button.setStyleSheet("""
                QPushButton {
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    background-color: white;
                    font-size: 11px;
                }
                QPushButton:checked {
                    background-color: #007acc;
                    color: white;
                    border-color: #007acc;
                }
            """)
            self.position_buttons.addButton(button)
            grid.addWidget(button, row, col)

        return widget

    def create_effects_section(self):
        """创建描边和阴影效果设置区域 - 原生样式"""
        widget = QWidget()
        widget.setMinimumWidth(450)  # 增加最小宽度避免挤压
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)

        # 效果标题 - 原生样式
        effects_label = QLabel("文字效果")
        # 移除自定义样式，使用系统默认外观
        layout.addWidget(effects_label)

        # 描边设置 - 紧凑布局
        stroke_section = self.create_compact_stroke_section()
        layout.addWidget(stroke_section)

        # 阴影设置 - 紧凑布局
        shadow_section = self.create_compact_shadow_section()
        layout.addWidget(shadow_section)

        return widget

    def create_compact_stroke_section(self):
        """创建紧凑版描边设置区域 - 原生样式"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(6)

        # 启用描边复选框 - 原生样式
        self.stroke_enabled_checkbox = QCheckBox("启用描边")
        # 移除自定义样式，使用系统默认外观
        layout.addWidget(self.stroke_enabled_checkbox)

        # 描边设置行
        stroke_row = QHBoxLayout()

        # 描边颜色
        stroke_row.addWidget(QLabel("颜色:"))
        self.stroke_color_buttons = QButtonGroup()
        stroke_colors = [("#000000", "黑"), ("#FFFFFF", "白"), ("#FF0000", "红"), ("#0000FF", "蓝")]

        for color_code, color_name in stroke_colors:
            button = QPushButton()
            button.setFixedSize(24, 24)
            button.setCheckable(True)
            button.setProperty("color_code", color_code)
            # 移除自定义样式，使用系统默认外观
            button.setToolTip(f"{color_name}色描边")
            self.stroke_color_buttons.addButton(button)
            stroke_row.addWidget(button)

        stroke_row.addWidget(QLabel("宽度:"))

        # 描边宽度
        self.stroke_width_slider = QSlider(Qt.Horizontal)
        self.stroke_width_slider.setRange(1, 10)
        self.stroke_width_slider.setValue(2)
        self.stroke_width_slider.setMaximumWidth(100)
        stroke_row.addWidget(self.stroke_width_slider)

        self.stroke_width_label = QLabel("2px")
        self.stroke_width_label.setStyleSheet("font-size: 11px; color: #666; min-width: 25px;")
        stroke_row.addWidget(self.stroke_width_label)

        stroke_row.addStretch()
        layout.addLayout(stroke_row)

        return widget

    def create_compact_shadow_section(self):
        """创建紧凑版阴影设置区域 - 原生样式"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(6)

        # 启用阴影复选框 - 原生样式
        self.shadow_enabled_checkbox = QCheckBox("启用阴影")
        # 移除自定义样式，使用系统默认外观
        layout.addWidget(self.shadow_enabled_checkbox)

        # 阴影设置行1：颜色和偏移
        shadow_row1 = QHBoxLayout()

        # 阴影颜色
        shadow_row1.addWidget(QLabel("颜色:"))
        self.shadow_color_buttons = QButtonGroup()
        shadow_colors = [("#000000", "黑"), ("#808080", "灰"), ("#FF0000", "红"), ("#0000FF", "蓝")]

        for color_code, color_name in shadow_colors:
            button = QPushButton()
            button.setFixedSize(24, 24)
            button.setCheckable(True)
            button.setProperty("color_code", color_code)
            # 移除自定义样式，使用系统默认外观
            button.setToolTip(f"{color_name}色阴影")
            self.shadow_color_buttons.addButton(button)
            shadow_row1.addWidget(button)

        shadow_row1.addStretch()
        layout.addLayout(shadow_row1)

        # 阴影设置行2：偏移和模糊
        shadow_row2 = QHBoxLayout()

        shadow_row2.addWidget(QLabel("X:"))
        self.shadow_x_slider = QSlider(Qt.Horizontal)
        self.shadow_x_slider.setRange(-10, 10)
        self.shadow_x_slider.setValue(3)
        self.shadow_x_slider.setMaximumWidth(70)
        shadow_row2.addWidget(self.shadow_x_slider)

        self.shadow_x_label = QLabel("3")
        self.shadow_x_label.setStyleSheet("font-size: 11px; color: #666; min-width: 20px;")
        shadow_row2.addWidget(self.shadow_x_label)

        shadow_row2.addWidget(QLabel("Y:"))
        self.shadow_y_slider = QSlider(Qt.Horizontal)
        self.shadow_y_slider.setRange(-10, 10)
        self.shadow_y_slider.setValue(3)
        self.shadow_y_slider.setMaximumWidth(70)
        shadow_row2.addWidget(self.shadow_y_slider)

        self.shadow_y_label = QLabel("3")
        self.shadow_y_label.setStyleSheet("font-size: 11px; color: #666; min-width: 20px;")
        shadow_row2.addWidget(self.shadow_y_label)

        shadow_row2.addWidget(QLabel("模糊:"))
        self.shadow_blur_slider = QSlider(Qt.Horizontal)
        self.shadow_blur_slider.setRange(0, 10)
        self.shadow_blur_slider.setValue(5)
        self.shadow_blur_slider.setMaximumWidth(70)
        shadow_row2.addWidget(self.shadow_blur_slider)

        self.shadow_blur_label = QLabel("5")
        self.shadow_blur_label.setStyleSheet("font-size: 11px; color: #666; min-width: 20px;")
        shadow_row2.addWidget(self.shadow_blur_label)

        shadow_row2.addStretch()
        layout.addLayout(shadow_row2)

        return widget

    def create_stroke_section(self):
        """创建描边设置区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # 启用描边复选框
        self.stroke_enabled_checkbox = QCheckBox("启用描边")
        self.stroke_enabled_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 13px;
                color: #333;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:checked {
                background-color: #007acc;
                border: 1px solid #007acc;
            }
        """)
        layout.addWidget(self.stroke_enabled_checkbox)

        # 描边颜色选择
        stroke_color_layout = QHBoxLayout()
        stroke_color_layout.addWidget(QLabel("描边颜色:"))

        self.stroke_color_buttons = QButtonGroup()
        stroke_colors = [
            ("#000000", "黑色"),
            ("#FFFFFF", "白色"),
            ("#FF0000", "红色"),
            ("#00FF00", "绿色"),
            ("#0000FF", "蓝色"),
            ("#FFFF00", "黄色")
        ]

        stroke_color_grid = QHBoxLayout()
        for color_code, color_name in stroke_colors:
            button = QPushButton()
            button.setFixedSize(25, 25)
            button.setCheckable(True)
            button.setProperty("color_code", color_code)
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color_code};
                    border: 2px solid #ddd;
                    border-radius: 3px;
                }}
                QPushButton:checked {{
                    border-color: #007acc;
                    border-width: 3px;
                }}
            """)
            button.setToolTip(color_name)
            self.stroke_color_buttons.addButton(button)
            stroke_color_grid.addWidget(button)

            # 默认选择黑色
            if color_code == "#000000":
                button.setChecked(True)

        stroke_color_layout.addLayout(stroke_color_grid)
        stroke_color_layout.addStretch()
        layout.addLayout(stroke_color_layout)

        # 描边宽度设置
        stroke_width_layout = QHBoxLayout()
        stroke_width_layout.addWidget(QLabel("描边宽度:"))

        self.stroke_width_slider = QSlider(Qt.Horizontal)
        self.stroke_width_slider.setRange(1, 5)
        self.stroke_width_slider.setValue(2)
        self.stroke_width_slider.setFixedWidth(120)

        self.stroke_width_label = QLabel("2px")
        self.stroke_width_label.setFixedWidth(30)

        stroke_width_layout.addWidget(self.stroke_width_slider)
        stroke_width_layout.addWidget(self.stroke_width_label)
        stroke_width_layout.addStretch()
        layout.addLayout(stroke_width_layout)

        return widget

    def create_shadow_section(self):
        """创建阴影设置区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # 启用阴影复选框
        self.shadow_enabled_checkbox = QCheckBox("启用阴影")
        self.shadow_enabled_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 13px;
                color: #333;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:checked {
                background-color: #007acc;
                border: 1px solid #007acc;
            }
        """)
        layout.addWidget(self.shadow_enabled_checkbox)

        # 阴影颜色选择
        shadow_color_layout = QHBoxLayout()
        shadow_color_layout.addWidget(QLabel("阴影颜色:"))

        self.shadow_color_buttons = QButtonGroup()
        shadow_colors = [
            ("#000000", "黑色"),
            ("#666666", "灰色"),
            ("#333333", "深灰"),
            ("#FFFFFF", "白色"),
            ("#FF0000", "红色"),
            ("#0000FF", "蓝色")
        ]

        shadow_color_grid = QHBoxLayout()
        for color_code, color_name in shadow_colors:
            button = QPushButton()
            button.setFixedSize(25, 25)
            button.setCheckable(True)
            button.setProperty("color_code", color_code)
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color_code};
                    border: 2px solid #ddd;
                    border-radius: 3px;
                }}
                QPushButton:checked {{
                    border-color: #007acc;
                    border-width: 3px;
                }}
            """)
            button.setToolTip(color_name)
            self.shadow_color_buttons.addButton(button)
            shadow_color_grid.addWidget(button)

            # 默认选择黑色
            if color_code == "#000000":
                button.setChecked(True)

        shadow_color_layout.addLayout(shadow_color_grid)
        shadow_color_layout.addStretch()
        layout.addLayout(shadow_color_layout)

        # 阴影偏移设置
        offset_layout = QHBoxLayout()

        # X偏移
        offset_layout.addWidget(QLabel("X偏移:"))
        self.shadow_x_slider = QSlider(Qt.Horizontal)
        self.shadow_x_slider.setRange(-10, 10)
        self.shadow_x_slider.setValue(2)
        self.shadow_x_slider.setFixedWidth(80)
        self.shadow_x_label = QLabel("2px")
        self.shadow_x_label.setFixedWidth(25)
        offset_layout.addWidget(self.shadow_x_slider)
        offset_layout.addWidget(self.shadow_x_label)

        # Y偏移
        offset_layout.addWidget(QLabel("Y偏移:"))
        self.shadow_y_slider = QSlider(Qt.Horizontal)
        self.shadow_y_slider.setRange(-10, 10)
        self.shadow_y_slider.setValue(2)
        self.shadow_y_slider.setFixedWidth(80)
        self.shadow_y_label = QLabel("2px")
        self.shadow_y_label.setFixedWidth(25)
        offset_layout.addWidget(self.shadow_y_slider)
        offset_layout.addWidget(self.shadow_y_label)

        layout.addLayout(offset_layout)

        # 阴影模糊半径设置
        blur_layout = QHBoxLayout()
        blur_layout.addWidget(QLabel("模糊半径:"))

        self.shadow_blur_slider = QSlider(Qt.Horizontal)
        self.shadow_blur_slider.setRange(0, 10)
        self.shadow_blur_slider.setValue(3)
        self.shadow_blur_slider.setFixedWidth(120)

        self.shadow_blur_label = QLabel("3px")
        self.shadow_blur_label.setFixedWidth(30)

        blur_layout.addWidget(self.shadow_blur_slider)
        blur_layout.addWidget(self.shadow_blur_label)
        blur_layout.addStretch()
        layout.addLayout(blur_layout)

        return widget









    def create_multiline_section(self):
        """创建多行水印设置面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(25, 25, 25, 25)  # 增加内边距
        layout.setSpacing(20)  # 增加间距

        # 标题 - 原生样式
        title_label = QLabel("多行水印设置")
        # 移除自定义样式，使用系统默认外观
        layout.addWidget(title_label)

        # 第一行：启用多行模式 - 原生样式
        self.multiline_checkbox = QCheckBox("启用多行模式")
        # 移除自定义样式，使用系统默认外观
        layout.addWidget(self.multiline_checkbox)

        # 第二行：使用文件名 - 原生样式
        self.filename_multiline_checkbox = QCheckBox("使用文件名作为水印源")
        # 移除自定义样式，使用系统默认外观
        layout.addWidget(self.filename_multiline_checkbox)

        # 字符数限制设置
        chars_container = QWidget()
        chars_layout = QVBoxLayout(chars_container)
        chars_layout.setContentsMargins(0, 0, 0, 0)
        chars_layout.setSpacing(5)

        chars_header = QHBoxLayout()
        chars_label = QLabel("单行字符数:")
        # 移除自定义样式，使用系统默认外观
        chars_header.addWidget(chars_label)

        self.chars_value_label = QLabel("20")
        # 移除自定义样式，使用系统默认外观
        chars_header.addStretch()
        chars_header.addWidget(self.chars_value_label)
        chars_layout.addLayout(chars_header)

        self.chars_per_line_slider = QSlider(Qt.Horizontal)
        self.chars_per_line_slider.setRange(5, 50)
        self.chars_per_line_slider.setValue(20)
        # 移除自定义样式，使用系统默认外观
        chars_layout.addWidget(self.chars_per_line_slider)
        layout.addWidget(chars_container)

        # 行间距设置
        spacing_container = QWidget()
        spacing_layout = QVBoxLayout(spacing_container)
        spacing_layout.setContentsMargins(0, 0, 0, 0)
        spacing_layout.setSpacing(5)

        spacing_header = QHBoxLayout()
        spacing_label = QLabel("行间距:")
        # 移除自定义样式，使用系统默认外观
        spacing_header.addWidget(spacing_label)

        self.spacing_value_label = QLabel("5px")
        self.spacing_value_label.setStyleSheet("font-size: 12px; color: #333; font-weight: bold;")
        spacing_header.addStretch()
        spacing_header.addWidget(self.spacing_value_label)
        spacing_layout.addLayout(spacing_header)

        self.line_spacing_slider = QSlider(Qt.Horizontal)
        self.line_spacing_slider.setRange(0, 20)
        self.line_spacing_slider.setValue(5)
        self.line_spacing_slider.setStyleSheet(self.chars_per_line_slider.styleSheet())
        spacing_layout.addWidget(self.line_spacing_slider)
        layout.addWidget(spacing_container)

        # 最大行数设置
        max_lines_container = QWidget()
        max_lines_layout = QHBoxLayout(max_lines_container)
        max_lines_layout.setContentsMargins(0, 0, 0, 0)

        max_lines_label = QLabel("最大行数:")
        max_lines_label.setStyleSheet("font-size: 12px; color: #666;")
        max_lines_layout.addWidget(max_lines_label)

        self.max_lines_spin = QSpinBox()
        self.max_lines_spin.setRange(1, 10)
        self.max_lines_spin.setValue(3)
        self.max_lines_spin.setStyleSheet("""
            QSpinBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
                min-width: 60px;
            }
            QSpinBox:focus {
                border-color: #007acc;
            }
        """)
        max_lines_layout.addStretch()
        max_lines_layout.addWidget(self.max_lines_spin)
        layout.addWidget(max_lines_container)

        # 标点符号过滤
        self.filter_punctuation_checkbox = QCheckBox("过滤标点符号")
        self.filter_punctuation_checkbox.setStyleSheet(self.multiline_checkbox.styleSheet())
        layout.addWidget(self.filter_punctuation_checkbox)

        # 标点符号类型选择
        punct_container = QWidget()
        punct_layout = QHBoxLayout(punct_container)
        punct_layout.setContentsMargins(0, 0, 0, 0)

        punct_label = QLabel("类型:")
        punct_label.setStyleSheet("font-size: 12px; color: #666;")
        punct_layout.addWidget(punct_label)

        self.punctuation_combo = QComboBox()
        self.punctuation_combo.addItems(["中文标点", "英文标点", "全部标点"])
        self.punctuation_combo.setCurrentText("中文标点")
        self.punctuation_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 12px;
                background-color: white;
                min-width: 100px;
            }
            QComboBox:focus {
                border-color: #007acc;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMgNUw2IDhMOSA1IiBzdHJva2U9IiM2NjYiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
            }
        """)
        punct_layout.addStretch()
        punct_layout.addWidget(self.punctuation_combo)
        layout.addWidget(punct_container)

        # 显示行数设置
        display_lines_container = QWidget()
        display_lines_layout = QHBoxLayout(display_lines_container)
        display_lines_layout.setContentsMargins(0, 0, 0, 0)

        display_lines_label = QLabel("显示行数:")
        display_lines_label.setStyleSheet("font-size: 12px; color: #666;")
        display_lines_layout.addWidget(display_lines_label)

        self.display_lines_spin = QSpinBox()
        self.display_lines_spin.setRange(1, 10)
        self.display_lines_spin.setValue(3)
        self.display_lines_spin.setStyleSheet("""
            QSpinBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px;
                font-size: 12px;
                background-color: white;
                min-width: 60px;
            }
            QSpinBox:focus {
                border-color: #007acc;
            }
        """)
        display_lines_layout.addStretch()
        display_lines_layout.addWidget(self.display_lines_spin)
        layout.addWidget(display_lines_container)

        # 文本预览区域
        preview_label = QLabel("分行预览:")
        preview_label.setStyleSheet("font-size: 12px; color: #666; margin-top: 5px;")
        layout.addWidget(preview_label)

        self.multiline_preview = QTextEdit()
        self.multiline_preview.setMaximumHeight(60)
        self.multiline_preview.setReadOnly(True)
        self.multiline_preview.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #f9f9f9;
                font-size: 11px;
                color: #333;
                padding: 6px;
            }
        """)
        self.multiline_preview.setPlaceholderText("这里将显示多行水印的分行预览...")
        layout.addWidget(self.multiline_preview)

        # 逐行参数设置区域
        line_params_section = self.create_line_params_section()
        layout.addWidget(line_params_section)

        # 连接信号
        self.connect_multiline_signals()

        return widget

    def create_line_params_section(self):
        """创建逐行参数设置区域"""
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border-radius: 6px;
                border: 1px solid #ddd;
            }
        """)

        layout = QVBoxLayout(widget)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(10)

        # 标题和行选择器
        header_layout = QHBoxLayout()

        params_label = QLabel("逐行参数设置")
        params_label.setStyleSheet("font-weight: bold; color: #333; font-size: 12px;")
        header_layout.addWidget(params_label)

        header_layout.addStretch()

        # 行选择器
        line_selector_label = QLabel("编辑行:")
        line_selector_label.setStyleSheet("font-size: 11px; color: #666;")
        header_layout.addWidget(line_selector_label)

        self.line_selector = QComboBox()
        self.line_selector.setStyleSheet("""
            QComboBox {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 3px 6px;
                font-size: 11px;
                background-color: white;
                min-width: 60px;
            }
            QComboBox:focus {
                border-color: #007acc;
            }
        """)
        self.line_selector.addItems(["第1行", "第2行", "第3行"])
        header_layout.addWidget(self.line_selector)

        layout.addLayout(header_layout)

        # 参数控制区域
        params_container = QWidget()
        params_layout = QVBoxLayout(params_container)
        params_layout.setContentsMargins(0, 0, 0, 0)
        params_layout.setSpacing(8)

        # 字体颜色和大小
        font_row = QHBoxLayout()

        # 字体颜色
        font_row.addWidget(QLabel("颜色:"))
        self.line_color_buttons = QButtonGroup()
        line_colors = [("#FFFFFF", "白"), ("#000000", "黑"), ("#FF0000", "红"), ("#00FF00", "绿"), ("#0000FF", "蓝"), ("#FFFF00", "黄")]

        for color_code, color_name in line_colors:
            button = QPushButton()
            button.setFixedSize(18, 18)
            button.setCheckable(True)
            button.setProperty("color_code", color_code)
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color_code};
                    border: 2px solid #ddd;
                    border-radius: 2px;
                }}
                QPushButton:checked {{
                    border-color: #007acc;
                    border-width: 3px;
                }}
            """)
            button.setToolTip(f"{color_name}色")
            self.line_color_buttons.addButton(button)
            font_row.addWidget(button)

        font_row.addWidget(QLabel("大小:"))

        # 字体大小
        self.line_font_size_spin = QSpinBox()
        self.line_font_size_spin.setRange(8, 72)
        self.line_font_size_spin.setValue(24)
        self.line_font_size_spin.setStyleSheet("""
            QSpinBox {
                border: 1px solid #ddd;
                border-radius: 3px;
                padding: 2px;
                font-size: 11px;
                background-color: white;
                min-width: 50px;
            }
            QSpinBox:focus {
                border-color: #007acc;
            }
        """)
        font_row.addWidget(self.line_font_size_spin)

        font_row.addStretch()
        params_layout.addLayout(font_row)

        # 描边设置
        stroke_row = QHBoxLayout()

        self.line_stroke_checkbox = QCheckBox("描边")
        self.line_stroke_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 11px;
                color: #333;
            }
            QCheckBox::indicator {
                width: 12px;
                height: 12px;
            }
        """)
        stroke_row.addWidget(self.line_stroke_checkbox)

        # 描边颜色
        self.line_stroke_color_buttons = QButtonGroup()
        stroke_colors = [("#000000", "黑"), ("#FFFFFF", "白"), ("#FF0000", "红")]

        for color_code, color_name in stroke_colors:
            button = QPushButton()
            button.setFixedSize(16, 16)
            button.setCheckable(True)
            button.setProperty("color_code", color_code)
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color_code};
                    border: 1px solid #ddd;
                    border-radius: 2px;
                }}
                QPushButton:checked {{
                    border-color: #007acc;
                    border-width: 2px;
                }}
            """)
            button.setToolTip(f"{color_name}色描边")
            self.line_stroke_color_buttons.addButton(button)
            stroke_row.addWidget(button)

        stroke_row.addWidget(QLabel("宽度:"))

        self.line_stroke_width_spin = QSpinBox()
        self.line_stroke_width_spin.setRange(1, 5)
        self.line_stroke_width_spin.setValue(2)
        self.line_stroke_width_spin.setStyleSheet(self.line_font_size_spin.styleSheet())
        self.line_stroke_width_spin.setMaximumWidth(40)
        stroke_row.addWidget(self.line_stroke_width_spin)

        stroke_row.addStretch()
        params_layout.addLayout(stroke_row)

        # 阴影设置
        shadow_row = QHBoxLayout()

        self.line_shadow_checkbox = QCheckBox("阴影")
        self.line_shadow_checkbox.setStyleSheet(self.line_stroke_checkbox.styleSheet())
        shadow_row.addWidget(self.line_shadow_checkbox)

        # 阴影颜色
        self.line_shadow_color_buttons = QButtonGroup()
        shadow_colors = [("#000000", "黑"), ("#808080", "灰")]

        for color_code, color_name in shadow_colors:
            button = QPushButton()
            button.setFixedSize(16, 16)
            button.setCheckable(True)
            button.setProperty("color_code", color_code)
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color_code};
                    border: 1px solid #ddd;
                    border-radius: 2px;
                }}
                QPushButton:checked {{
                    border-color: #007acc;
                    border-width: 2px;
                }}
            """)
            button.setToolTip(f"{color_name}色阴影")
            self.line_shadow_color_buttons.addButton(button)
            shadow_row.addWidget(button)

        shadow_row.addWidget(QLabel("X:"))

        self.line_shadow_x_spin = QSpinBox()
        self.line_shadow_x_spin.setRange(-10, 10)
        self.line_shadow_x_spin.setValue(3)
        self.line_shadow_x_spin.setStyleSheet(self.line_font_size_spin.styleSheet())
        self.line_shadow_x_spin.setMaximumWidth(40)
        shadow_row.addWidget(self.line_shadow_x_spin)

        shadow_row.addWidget(QLabel("Y:"))

        self.line_shadow_y_spin = QSpinBox()
        self.line_shadow_y_spin.setRange(-10, 10)
        self.line_shadow_y_spin.setValue(3)
        self.line_shadow_y_spin.setStyleSheet(self.line_font_size_spin.styleSheet())
        self.line_shadow_y_spin.setMaximumWidth(40)
        shadow_row.addWidget(self.line_shadow_y_spin)

        shadow_row.addWidget(QLabel("模糊:"))

        self.line_shadow_blur_spin = QSpinBox()
        self.line_shadow_blur_spin.setRange(0, 10)
        self.line_shadow_blur_spin.setValue(5)
        self.line_shadow_blur_spin.setStyleSheet(self.line_font_size_spin.styleSheet())
        self.line_shadow_blur_spin.setMaximumWidth(40)
        shadow_row.addWidget(self.line_shadow_blur_spin)

        shadow_row.addStretch()
        params_layout.addLayout(shadow_row)

        # 快捷操作按钮
        button_row = QHBoxLayout()

        apply_all_btn = QPushButton("应用到全部")
        apply_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
        """)
        apply_all_btn.clicked.connect(self.apply_current_params_to_all)
        button_row.addWidget(apply_all_btn)

        copy_btn = QPushButton("复制参数")
        copy_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #1e7e34;
            }
        """)
        copy_btn.clicked.connect(self.copy_current_line_params)
        button_row.addWidget(copy_btn)

        button_row.addStretch()
        params_layout.addLayout(button_row)

        layout.addWidget(params_container)

        # 连接信号
        self.connect_line_params_signals()

        return widget

    def connect_multiline_signals(self):
        """连接多行设置的信号"""
        # 基本多行设置信号
        if hasattr(self, 'multiline_checkbox'):
            self.multiline_checkbox.toggled.connect(self.on_multiline_settings_changed)
        if hasattr(self, 'filename_multiline_checkbox'):
            self.filename_multiline_checkbox.toggled.connect(self.on_multiline_settings_changed)
        if hasattr(self, 'filter_punctuation_checkbox'):
            self.filter_punctuation_checkbox.toggled.connect(self.on_multiline_settings_changed)
        if hasattr(self, 'punctuation_combo'):
            self.punctuation_combo.currentTextChanged.connect(self.on_multiline_settings_changed)

        # 参数调整信号
        if hasattr(self, 'chars_per_line_slider'):
            self.chars_per_line_slider.valueChanged.connect(self.on_chars_per_line_changed)
        if hasattr(self, 'line_spacing_slider'):
            self.line_spacing_slider.valueChanged.connect(self.on_line_spacing_changed)
        if hasattr(self, 'max_lines_spin'):
            self.max_lines_spin.valueChanged.connect(self.on_multiline_settings_changed)

        # 新增的运行参数信号连接
        if hasattr(self, 'processing_speed_combo'):
            self.processing_speed_combo.currentTextChanged.connect(self.on_processing_speed_changed)
        if hasattr(self, 'memory_usage_combo'):
            self.memory_usage_combo.currentTextChanged.connect(self.on_memory_usage_changed)
        if hasattr(self, 'display_lines_spin'):
            self.display_lines_spin.valueChanged.connect(self.on_display_lines_changed)

    def connect_line_params_signals(self):
        """连接逐行参数设置的信号"""
        if hasattr(self, 'line_selector'):
            self.line_selector.currentIndexChanged.connect(self.on_line_selector_changed)
        if hasattr(self, 'line_color_buttons'):
            self.line_color_buttons.buttonClicked.connect(self.on_line_color_changed)
        if hasattr(self, 'line_font_size_spin'):
            self.line_font_size_spin.valueChanged.connect(self.on_line_font_size_changed)
        if hasattr(self, 'line_stroke_checkbox'):
            self.line_stroke_checkbox.toggled.connect(self.on_line_stroke_enabled_changed)
        if hasattr(self, 'line_stroke_color_buttons'):
            self.line_stroke_color_buttons.buttonClicked.connect(self.on_line_stroke_color_changed)
        if hasattr(self, 'line_stroke_width_spin'):
            self.line_stroke_width_spin.valueChanged.connect(self.on_line_stroke_width_changed)
        if hasattr(self, 'line_shadow_checkbox'):
            self.line_shadow_checkbox.toggled.connect(self.on_line_shadow_enabled_changed)
        if hasattr(self, 'line_shadow_color_buttons'):
            self.line_shadow_color_buttons.buttonClicked.connect(self.on_line_shadow_color_changed)
        if hasattr(self, 'line_shadow_x_spin'):
            self.line_shadow_x_spin.valueChanged.connect(self.on_line_shadow_x_changed)
        if hasattr(self, 'line_shadow_y_spin'):
            self.line_shadow_y_spin.valueChanged.connect(self.on_line_shadow_y_changed)
        if hasattr(self, 'line_shadow_blur_spin'):
            self.line_shadow_blur_spin.valueChanged.connect(self.on_line_shadow_blur_changed)

    def on_chars_per_line_changed(self, value):
        """单行字符数改变"""
        # 安全检查标签是否存在
        if hasattr(self, 'chars_value_label'):
            self.chars_value_label.setText(str(value))

        # 更新设置
        if hasattr(self, 'temp_settings'):
            self.temp_settings.max_chars_per_line = value

        # 更新预览
        if hasattr(self, 'update_multiline_preview_info'):
            self.update_multiline_preview_info()
        if hasattr(self, 'on_multiline_settings_changed'):
            self.on_multiline_settings_changed()

    def on_line_spacing_changed_old(self, value):
        """行间距改变（旧版本，已废弃）"""
        # 安全检查标签是否存在
        if hasattr(self, 'spacing_value_label'):
            self.spacing_value_label.setText(f"{value}px")

        # 更新设置
        if hasattr(self, 'temp_settings'):
            self.temp_settings.line_spacing = value

        # 更新预览
        if hasattr(self, 'update_multiline_preview_info'):
            self.update_multiline_preview_info()
        if hasattr(self, 'on_multiline_settings_changed'):
            self.on_multiline_settings_changed()

    def on_display_lines_changed(self, value):
        """显示行数改变"""
        self.temp_settings.display_lines = value
        self.update_multiline_preview()
        self.update_preview()

    def on_multiline_settings_changed(self):
        """多行设置改变"""
        # 更新设置（添加安全检查）
        if hasattr(self, 'multiline_checkbox'):
            self.temp_settings.multi_line_mode = self.multiline_checkbox.isChecked()
        if hasattr(self, 'filename_multiline_checkbox'):
            self.temp_settings.use_filename_multiline = self.filename_multiline_checkbox.isChecked()

        old_max_lines = self.temp_settings.max_lines
        if hasattr(self, 'max_lines_spin'):
            self.temp_settings.max_lines = self.max_lines_spin.value()
        if hasattr(self, 'filter_punctuation_checkbox'):
            self.temp_settings.filter_punctuation = self.filter_punctuation_checkbox.isChecked()

        # 更新标点符号类型
        if hasattr(self, 'punctuation_combo'):
            punctuation_map = {
                "中文标点": "chinese",
                "英文标点": "english",
                "全部标点": "all"
            }
            self.temp_settings.punctuation_type = punctuation_map.get(
                self.punctuation_combo.currentText(), "chinese"
            )

        # 如果行数发生变化，更新行选择器和清理多余参数
        if old_max_lines != self.temp_settings.max_lines:
            self.temp_settings.cleanup_line_params(self.temp_settings.max_lines)
            if hasattr(self, 'line_selector'):
                self.update_line_selector_items()

        # 更新预览
        self.update_multiline_preview()
        self.update_preview()

    def on_processing_speed_changed(self, speed):
        """处理速度改变事件"""
        # 更新调试信息
        if hasattr(self, 'debug_text'):
            self.debug_text.setText(f"处理速度: {speed}")

    def on_memory_usage_changed(self, usage):
        """内存使用改变事件"""
        # 更新调试信息
        if hasattr(self, 'debug_text'):
            current_text = self.debug_text.text()
            self.debug_text.setText(f"{current_text} | 内存使用: {usage}")

    def update_multiline_preview_info(self):
        """更新多行预览信息"""
        if hasattr(self, 'current_lines_label') and hasattr(self, 'char_count_label'):
            # 获取当前文本
            text = "示例文件名"  # 强制使用示例文件名

            # 计算行数和字符数
            if hasattr(self, 'chars_per_line_slider'):
                chars_per_line = self.chars_per_line_slider.value()
                lines = max(1, len(text) // chars_per_line + (1 if len(text) % chars_per_line > 0 else 0))
            else:
                lines = 1

            # 更新显示
            self.current_lines_label.setText(f"当前行数: {lines}")
            self.char_count_label.setText(f"字符数: {len(text)}")

    def on_multiline_position_changed(self, line_index, x_percent, y_percent):
        """处理多行水印位置改变信号"""
        print(f"多行水印第{line_index+1}行位置改变: ({x_percent}%, {y_percent}%)")
        # 这里可以添加位置改变的处理逻辑
        # 例如更新界面控件、保存配置等

    def on_multiline_line_selected(self, line_index):
        """处理多行水印行选择信号"""
        print(f"选择了多行水印第{line_index+1}行")
        # 这里可以添加行选择的处理逻辑
        # 例如切换当前编辑的行、更新界面等

    def update_multiline_preview(self):
        """更新多行预览"""
        # 检查是否有多行预览组件
        if not hasattr(self, 'multiline_preview') and not hasattr(self, 'multiline_preview_label'):
            return

        if not self.temp_settings.multi_line_mode:
            # 如果有旧的文本预览组件
            if hasattr(self, 'multiline_preview'):
                self.multiline_preview.setPlainText("多行模式未启用")
            # 更新图像预览
            self.update_preview()
            return

        # 获取文本源
        if self.temp_settings.use_filename_multiline:
            text = "示例文件名_这是一个很长的文件名用于演示多行水印效果.mp4"
        else:
            text = "示例文件名用于演示多行效果"  # 强制使用文件名

        # 过滤标点符号
        if self.temp_settings.filter_punctuation:
            import re
            if self.temp_settings.punctuation_type == "chinese":
                text = re.sub(r'[，。！？；：""''（）【】《》、]', '', text)
            elif self.temp_settings.punctuation_type == "english":
                text = re.sub(r'[,.!?;:"\'()\[\]<>/\\]', '', text)
            elif self.temp_settings.punctuation_type == "all":
                chinese_punct = r'[，。！？；：""''（）【】《》、]'
                english_punct = r'[,.!?;:"\'()\[\]<>/\\]'
                text = re.sub(chinese_punct, '', text)
                text = re.sub(english_punct, '', text)

        # 分行处理
        lines = []
        chars_per_line = self.temp_settings.max_chars_per_line
        max_lines = self.temp_settings.max_lines

        for i in range(0, len(text), chars_per_line):
            if len(lines) >= max_lines:
                break
            line = text[i:i + chars_per_line]
            lines.append(f"第{len(lines)+1}行: {line} ({len(line)}字符)")

        if len(text) > chars_per_line * max_lines:
            lines.append("...")

        # 如果有旧的文本预览组件，更新它
        if hasattr(self, 'multiline_preview'):
            preview_text = "\n".join(lines)
            self.multiline_preview.setPlainText(preview_text)

        # 如果有新的多行预览标签，设置多行配置
        if hasattr(self, 'multiline_preview_label') and hasattr(self.multiline_preview_label, 'set_multi_line_configs'):
            # 创建多行配置列表
            multi_line_configs = []
            for i in range(len(lines)):
                # 这里需要根据实际的多行配置创建配置对象
                # 暂时使用默认配置
                pass

            # 设置文件名文本
            self.multiline_preview_label.set_filename_text(text)

        # 更新图像预览
        self.update_preview()

    def on_line_selector_changed(self, index):
        """行选择器改变"""
        self.temp_settings.current_editing_line = index
        self.load_current_line_params()
        self.update_preview()

    def on_line_color_changed(self, button):
        """行字体颜色改变"""
        color_code = button.property("color_code")
        if color_code:
            color = QColor(color_code)
            rgb = (color.red(), color.green(), color.blue())
            self.update_current_line_param("font_color", rgb)

    def on_line_font_size_changed(self, value):
        """行字体大小改变"""
        self.update_current_line_param("font_size", value)

    def on_line_stroke_enabled_changed(self, enabled):
        """行描边启用状态改变"""
        self.update_current_line_param("stroke_enabled", enabled)

    def on_line_stroke_color_changed(self, button):
        """行描边颜色改变"""
        color_code = button.property("color_code")
        if color_code:
            color = QColor(color_code)
            rgb = (color.red(), color.green(), color.blue())
            self.update_current_line_param("stroke_color", rgb)

    def on_line_stroke_width_changed(self, value):
        """行描边宽度改变"""
        self.update_current_line_param("stroke_width", value)

    def on_line_shadow_enabled_changed(self, enabled):
        """行阴影启用状态改变"""
        self.update_current_line_param("shadow_enabled", enabled)

    def on_line_shadow_color_changed(self, button):
        """行阴影颜色改变"""
        color_code = button.property("color_code")
        if color_code:
            color = QColor(color_code)
            rgb = (color.red(), color.green(), color.blue())
            self.update_current_line_param("shadow_color", rgb)

    def on_line_shadow_x_changed(self, value):
        """行阴影X偏移改变"""
        self.update_current_line_param("shadow_offset_x", value)

    def on_line_shadow_y_changed(self, value):
        """行阴影Y偏移改变"""
        self.update_current_line_param("shadow_offset_y", value)

    def on_line_shadow_blur_changed(self, value):
        """行阴影模糊改变"""
        self.update_current_line_param("shadow_blur", value)

    def update_current_line_param(self, param_name, value):
        """更新当前行的参数"""
        line_index = self.temp_settings.current_editing_line
        current_params = self.temp_settings.get_line_params(line_index)
        current_params[param_name] = value
        self.temp_settings.set_line_params(line_index, current_params)
        self.update_preview()

    def load_current_line_params(self):
        """加载当前行的参数到界面"""
        line_index = self.temp_settings.current_editing_line
        params = self.temp_settings.get_line_params(line_index)

        # 更新字体颜色按钮
        if hasattr(self, 'line_color_buttons'):
            color_hex = QColor(*params["font_color"]).name()
            for button in self.line_color_buttons.buttons():
                if button.property("color_code") == color_hex:
                    button.setChecked(True)
                    break

        # 更新字体大小
        if hasattr(self, 'line_font_size_spin'):
            self.line_font_size_spin.setValue(params["font_size"])

        # 更新描边设置
        self.line_stroke_checkbox.setChecked(params["stroke_enabled"])
        stroke_color_hex = QColor(*params["stroke_color"]).name()
        for button in self.line_stroke_color_buttons.buttons():
            if button.property("color_code") == stroke_color_hex:
                button.setChecked(True)
                break
        self.line_stroke_width_spin.setValue(params["stroke_width"])

        # 更新阴影设置
        self.line_shadow_checkbox.setChecked(params["shadow_enabled"])
        shadow_color_hex = QColor(*params["shadow_color"]).name()
        for button in self.line_shadow_color_buttons.buttons():
            if button.property("color_code") == shadow_color_hex:
                button.setChecked(True)
                break
        self.line_shadow_x_spin.setValue(params["shadow_offset_x"])
        self.line_shadow_y_spin.setValue(params["shadow_offset_y"])
        self.line_shadow_blur_spin.setValue(params["shadow_blur"])

    def apply_current_params_to_all(self):
        """将当前行参数应用到所有行"""
        current_line = self.temp_settings.current_editing_line
        current_params = self.temp_settings.get_line_params(current_line)
        max_lines = self.temp_settings.max_lines

        for i in range(max_lines):
            self.temp_settings.set_line_params(i, current_params.copy())

        self.update_preview()

    def copy_current_line_params(self):
        """复制当前行参数（存储到剪贴板变量）"""
        current_line = self.temp_settings.current_editing_line
        self.copied_line_params = self.temp_settings.get_line_params(current_line).copy()

        # 可以在这里添加视觉反馈，比如短暂改变按钮颜色
        sender = self.sender()
        if sender:
            original_style = sender.styleSheet()
            sender.setStyleSheet(original_style.replace("#28a745", "#17a2b8"))
            QTimer.singleShot(200, lambda: sender.setStyleSheet(original_style))

    def update_line_selector_items(self):
        """更新行选择器的选项"""
        if not hasattr(self, 'line_selector'):
            return

        max_lines = self.temp_settings.max_lines
        self.line_selector.clear()
        for i in range(max_lines):
            self.line_selector.addItem(f"第{i+1}行")

        # 确保当前编辑行不超出范围
        if self.temp_settings.current_editing_line >= max_lines:
            self.temp_settings.current_editing_line = max_lines - 1
            self.line_selector.setCurrentIndex(self.temp_settings.current_editing_line)

    def load_sample_image(self):
        """加载示例图片"""
        # 创建一个示例图片
        pixmap = QPixmap(500, 400)
        pixmap.fill(QColor(240, 240, 240))

        # 绘制示例内容
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制背景渐变
        gradient = QLinearGradient(0, 0, pixmap.width(), pixmap.height())
        gradient.setColorAt(0, QColor(200, 220, 255))
        gradient.setColorAt(1, QColor(150, 180, 220))
        painter.fillRect(pixmap.rect(), gradient)

        # 绘制示例文字
        painter.setPen(QColor(100, 100, 100))
        painter.setFont(QFont("Arial", 16))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "预览区域\n水印将显示在这里")

        painter.end()

        # 保存预览数据
        self._current_preview_pixmap = pixmap

        # 更新全局预览
        self.update_global_preview()

    def connect_signals(self):
        """连接信号"""
        # 文本变化
        # text_edit已移除，强制使用文件名

        # 字体变化
        if hasattr(self, 'font_buttons'):
            self.font_buttons.buttonClicked.connect(self.on_font_changed)

        # 颜色变化
        if hasattr(self, 'color_buttons'):
            self.color_buttons.buttonClicked.connect(self.on_color_changed)

        # 位置变化
        if hasattr(self, 'position_buttons'):
            self.position_buttons.buttonClicked.connect(self.on_position_changed)

        # 透明度变化
        self.opacity_slider.valueChanged.connect(self.on_opacity_changed)

        # 字体大小变化
        self.font_size_spin.valueChanged.connect(self.update_preview)

        # 复选框变化
        self.enable_checkbox.toggled.connect(self.update_preview)
        # use_filename_checkbox已移除，强制使用文件名

        # 描边效果信号连接
        if hasattr(self, 'stroke_enabled_checkbox'):
            self.stroke_enabled_checkbox.toggled.connect(self.on_stroke_enabled_changed)
        if hasattr(self, 'stroke_color_buttons'):
            self.stroke_color_buttons.buttonClicked.connect(self.on_stroke_color_changed)
        if hasattr(self, 'stroke_width_slider'):
            self.stroke_width_slider.valueChanged.connect(self.on_stroke_width_changed)

        # 阴影效果信号连接
        if hasattr(self, 'shadow_enabled_checkbox'):
            self.shadow_enabled_checkbox.toggled.connect(self.on_shadow_enabled_changed)
        if hasattr(self, 'shadow_color_buttons'):
            self.shadow_color_buttons.buttonClicked.connect(self.on_shadow_color_changed)
        if hasattr(self, 'shadow_x_slider'):
            self.shadow_x_slider.valueChanged.connect(self.on_shadow_x_changed)
        if hasattr(self, 'shadow_y_slider'):
            self.shadow_y_slider.valueChanged.connect(self.on_shadow_y_changed)
        if hasattr(self, 'shadow_blur_slider'):
            self.shadow_blur_slider.valueChanged.connect(self.on_shadow_blur_changed)

        # 按钮点击
        self.ok_button.clicked.connect(self.accept)
        self.reset_button.clicked.connect(self.reset_settings)

        # 预览标签信号
        if hasattr(self.preview_label, 'watermark_moved'):
            self.preview_label.watermark_moved.connect(self.on_watermark_moved)

        # 多行设置信号连接
        self.connect_multiline_signals()

        # 逐行参数设置信号连接
        self.connect_line_params_signals()

    def load_settings(self):
        """加载设置到界面"""
        # 基本设置（添加安全检查）
        if hasattr(self, 'enable_checkbox'):
            self.enable_checkbox.setChecked(self.temp_settings.enabled)
        # use_filename_checkbox已移除，强制使用文件名
        # self.temp_settings.use_filename 始终为 True
        # text_edit已移除，强制使用文件名

        # 位置设置
        if hasattr(self, 'position_buttons'):
            for button in self.position_buttons.buttons():
                if button.property("position") == self.temp_settings.position:
                    button.setChecked(True)
                    break

        # 字体设置（添加安全检查）
        if hasattr(self, 'font_size_spin'):
            self.font_size_spin.setValue(self.temp_settings.font_size)

        # 字体选择
        if hasattr(self, 'font_buttons'):
            for button in self.font_buttons.buttons():
                if button.property("font_family") == self.temp_settings.font_name:
                    button.setChecked(True)
                    break

        # 颜色设置
        if hasattr(self, 'color_buttons'):
            # 处理font_color属性（RGB元组格式）
            if hasattr(self.temp_settings, 'font_color'):
                if isinstance(self.temp_settings.font_color, tuple):
                    r, g, b = self.temp_settings.font_color
                    color_hex = QColor(r, g, b).name()
                    for button in self.color_buttons.buttons():
                        if button.property("color_code") == color_hex:
                            button.setChecked(True)
                            break

        # 视觉效果（添加安全检查）
        if hasattr(self, 'opacity_slider'):
            self.opacity_slider.setValue(self.temp_settings.opacity)
        if hasattr(self, 'opacity_label'):
            self.opacity_label.setText(f"{self.temp_settings.opacity}%")

        # 描边设置
        if hasattr(self, 'stroke_enabled_checkbox'):
            self.stroke_enabled_checkbox.setChecked(self.temp_settings.stroke_enabled)
        if hasattr(self, 'stroke_color_buttons'):
            if hasattr(self.temp_settings, 'stroke_color'):
                if isinstance(self.temp_settings.stroke_color, tuple):
                    r, g, b = self.temp_settings.stroke_color
                    color_hex = QColor(r, g, b).name()
                    for button in self.stroke_color_buttons.buttons():
                        if button.property("color_code") == color_hex:
                            button.setChecked(True)
                            break
        if hasattr(self, 'stroke_width_slider'):
            self.stroke_width_slider.setValue(self.temp_settings.stroke_width)
            self.stroke_width_label.setText(f"{self.temp_settings.stroke_width}px")

        # 阴影设置
        if hasattr(self, 'shadow_enabled_checkbox'):
            self.shadow_enabled_checkbox.setChecked(self.temp_settings.shadow_enabled)
        if hasattr(self, 'shadow_color_buttons'):
            if hasattr(self.temp_settings, 'shadow_color'):
                if isinstance(self.temp_settings.shadow_color, tuple):
                    r, g, b = self.temp_settings.shadow_color
                    color_hex = QColor(r, g, b).name()
                    for button in self.shadow_color_buttons.buttons():
                        if button.property("color_code") == color_hex:
                            button.setChecked(True)
                            break
        if hasattr(self, 'shadow_x_slider'):
            self.shadow_x_slider.setValue(self.temp_settings.shadow_offset_x)
            self.shadow_x_label.setText(f"{self.temp_settings.shadow_offset_x}px")
        if hasattr(self, 'shadow_y_slider'):
            self.shadow_y_slider.setValue(self.temp_settings.shadow_offset_y)
            self.shadow_y_label.setText(f"{self.temp_settings.shadow_offset_y}px")
        if hasattr(self, 'shadow_blur_slider'):
            self.shadow_blur_slider.setValue(self.temp_settings.shadow_blur)
            self.shadow_blur_label.setText(f"{self.temp_settings.shadow_blur}px")

        # 多行设置
        if hasattr(self, 'multiline_checkbox'):
            print(f"设置多行复选框状态: {self.temp_settings.multi_line_mode}")
            self.multiline_checkbox.setChecked(self.temp_settings.multi_line_mode)
            print(f"多行复选框当前状态: {self.multiline_checkbox.isChecked()}")
        else:
            print("多行复选框不存在")
        if hasattr(self, 'filename_multiline_checkbox'):
            self.filename_multiline_checkbox.setChecked(self.temp_settings.use_filename_multiline)
        if hasattr(self, 'chars_per_line_slider'):
            self.chars_per_line_slider.setValue(self.temp_settings.max_chars_per_line)
            if hasattr(self, 'chars_value_label'):
                self.chars_value_label.setText(str(self.temp_settings.max_chars_per_line))
        if hasattr(self, 'line_spacing_slider'):
            self.line_spacing_slider.setValue(self.temp_settings.line_spacing)
            if hasattr(self, 'spacing_value_label'):
                self.spacing_value_label.setText(f"{self.temp_settings.line_spacing}px")
        if hasattr(self, 'max_lines_spin'):
            self.max_lines_spin.setValue(self.temp_settings.max_lines)
        if hasattr(self, 'filter_punctuation_checkbox'):
            self.filter_punctuation_checkbox.setChecked(self.temp_settings.filter_punctuation)
        if hasattr(self, 'punctuation_combo'):
            punctuation_map = {
                "chinese": "中文标点",
                "english": "英文标点",
                "all": "全部标点"
            }
            combo_text = punctuation_map.get(self.temp_settings.punctuation_type, "中文标点")
            self.punctuation_combo.setCurrentText(combo_text)
        if hasattr(self, 'display_lines_spin'):
            self.display_lines_spin.setValue(self.temp_settings.display_lines)

        # 加载多行数据（仅在多行编辑器存在时）
        if hasattr(self, 'multiline_editor') and self.multiline_editor:
            self.load_multiline_data()
        else:
            print("多行编辑器不存在，跳过多行数据加载")

        # 更新多行预览
        if hasattr(self, 'update_multiline_preview'):
            self.update_multiline_preview()

        # 更新逐行参数设置
        if hasattr(self, 'line_selector'):
            self.update_line_selector_items()
            self.load_current_line_params()

        # 更新预览 - 强制刷新
        self.update_preview()

        # 确保预览内容正确显示
        if hasattr(self, 'preview_label') and self.preview_label:
            # 如果预览标签没有内容，重新初始化
            pixmap = self.preview_label.pixmap()
            if not pixmap or pixmap.isNull():
                self.initialize_preview_content()
                self.update_preview()

    def load_multiline_data(self):
        """加载多行数据到多行编辑器 - 水印功能已删除"""
        pass



    # on_text_changed方法已移除，因为不再有文本输入框

    def on_color_changed(self, button):
        """颜色改变"""
        color_code = button.property("color_code")
        if color_code:
            color = QColor(color_code)
            # 设置为RGB元组格式以兼容原有代码
            self.temp_settings.font_color = (color.red(), color.green(), color.blue())
            self.update_preview()

    def on_font_changed(self, button):
        """字体改变"""
        font_family = button.property("font_family")
        if font_family:
            self.temp_settings.font_name = font_family
            print(f"字体已更改为: {font_family}")  # 调试信息
            self.update_preview()

    def on_position_changed(self, button):
        """位置改变"""
        self.temp_settings.position = button.property("position")
        self.temp_settings.use_custom_position = False
        self.update_preview()

    def on_opacity_changed(self, value):
        """透明度改变"""
        self.temp_settings.opacity = value
        self.opacity_label.setText(f"{value}%")
        self.update_preview()

    def on_watermark_moved(self, x, y):
        """水印被拖拽移动"""
        self.temp_settings.custom_x = x
        self.temp_settings.custom_y = y
        self.temp_settings.use_custom_position = True

        # 取消位置按钮选择

    def on_stroke_enabled_changed(self, enabled):
        """描边启用状态改变"""
        self.temp_settings.stroke_enabled = enabled
        self.update_preview()

    def on_stroke_color_changed(self, button):
        """描边颜色改变"""
        color_code = button.property("color_code")
        if color_code:
            color = QColor(color_code)
            self.temp_settings.stroke_color = (color.red(), color.green(), color.blue())
            self.update_preview()

    def on_stroke_width_changed(self, value):
        """描边宽度改变"""
        self.temp_settings.stroke_width = value
        self.stroke_width_label.setText(f"{value}px")
        self.update_preview()

    def on_shadow_enabled_changed(self, enabled):
        """阴影启用状态改变"""
        self.temp_settings.shadow_enabled = enabled
        self.update_preview()

    def on_shadow_color_changed(self, button):
        """阴影颜色改变"""
        color_code = button.property("color_code")
        if color_code:
            color = QColor(color_code)
            self.temp_settings.shadow_color = (color.red(), color.green(), color.blue())
            self.update_preview()

    def on_shadow_x_changed(self, value):
        """阴影X偏移改变"""
        self.temp_settings.shadow_offset_x = value
        self.shadow_x_label.setText(f"{value}px")
        self.update_preview()

    def on_shadow_y_changed(self, value):
        """阴影Y偏移改变"""
        self.temp_settings.shadow_offset_y = value
        self.shadow_y_label.setText(f"{value}px")
        self.update_preview()

    def on_shadow_blur_changed(self, value):
        """阴影模糊半径改变"""
        self.temp_settings.shadow_blur = value
        self.shadow_blur_label.setText(f"{value}px")
        self.update_preview()
        for button in self.position_buttons.buttons():
            button.setChecked(False)

    def update_preview(self):
        """更新预览"""
        # 防止重复更新
        if hasattr(self, '_updating_preview') and self._updating_preview:
            return

        self._updating_preview = True

        try:
            # 如果预览标签不存在，先初始化
            if not hasattr(self, 'preview_label') or self.preview_label is None:
                # 尝试创建预览标签
                from PyQt5.QtWidgets import QSizePolicy
                self.preview_label = QLabel("水印预览功能已删除")
                self.preview_label.setMinimumSize(480, 270)  # 16:9比例
                self.preview_label.setMaximumSize(960, 540)  # 最大2倍尺寸，保持16:9
                self.preview_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
                self.initialize_preview_content()

            # 获取当前设置
            text = "示例文件名"  # 强制使用示例文件名

            # 检查是否启用水印（添加安全检查）
            watermark_enabled = True
            if hasattr(self, 'enable_checkbox'):
                watermark_enabled = self.enable_checkbox.isChecked()

            if not watermark_enabled:
                # 如果未启用水印，只显示背景
                self.load_sample_image()
                return

            # 重新绘制预览 - 使用与封面一致的1920x1080内部尺寸
            # 创建1920x1080的内部图像，确保水印大小逻辑与封面完全一致
            full_size_img = Image.new('RGB', (1920, 1080), color='#2c3e50')

            # 创建渐变背景
            draw = ImageDraw.Draw(full_size_img)
            for y in range(1080):
                color_value = int(44 + (y / 1080) * 40)
                color = (color_value, color_value + 20, color_value + 40)
                draw.line([(0, y), (1920, y)], fill=color)

            # 添加水印 - 使用与封面相同的逻辑
            if hasattr(self, 'add_watermark_to_image'):
                watermarked_img = self.add_watermark_to_image(full_size_img, text)
            else:
                watermarked_img = full_size_img

            # 缩放到预览显示尺寸 (保持与封面的相对比例一致)
            preview_img = watermarked_img.resize((self.preview_width, self.preview_height), Image.LANCZOS)

            # 转换为QPixmap
            width, height = preview_img.size
            qimg = QImage(preview_img.tobytes(), width, height, QImage.Format_RGB888)
            base_pixmap = QPixmap.fromImage(qimg)

            # 只保存预览数据，不更新多个标签
            self._current_preview_pixmap = base_pixmap

            # 更新多行预览信息
            self.update_multiline_preview_info()

            # 更新全局预览
            self.update_global_preview()

        except Exception as e:
            print(f"更新预览失败: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self._updating_preview = False

    def draw_watermark_preview(self, painter, text, size):
        """绘制水印预览"""
        # 首先检查是否启用多行模式
        multi_line_mode = False
        if hasattr(self, 'temp_settings') and hasattr(self.temp_settings, 'multi_line_mode'):
            multi_line_mode = self.temp_settings.multi_line_mode

        print(f"绘制预览 - 多行模式: {multi_line_mode}")  # 调试信息

        if multi_line_mode:
            # 多行模式 - 使用多行绘制逻辑
            self.draw_multiline_watermark_preview(painter, text, size)
        else:
            # 单行模式 - 使用原有的单行绘制逻辑
            self.draw_single_watermark_preview(painter, text, size)

    def draw_single_watermark_preview(self, painter, text, size):
        """绘制单行水印预览"""
        # 绘制简单的示例水印
        painter.setPen(QColor(255, 255, 255, 180))
        painter.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        painter.drawText(size.width() - 200, size.height() - 30, "示例水印")



    def draw_multiline_watermark_preview(self, painter, text, size):
        """绘制多行水印预览"""
        print("开始绘制多行水印预览")  # 调试信息

        # 检查是否有多行设置
        if not hasattr(self.temp_settings, 'lines') or not self.temp_settings.lines:
            print("没有多行设置，使用默认单行")
            self.draw_single_watermark_preview(painter, text, size)
            return

        # 获取启用的行
        enabled_lines = [line for line in self.temp_settings.lines
                        if getattr(line, 'enabled', False) and getattr(line, 'text', '').strip()]

        if not enabled_lines:
            print("没有启用的行，使用默认单行")
            self.draw_single_watermark_preview(painter, text, size)
            return

        print(f"找到{len(enabled_lines)}个启用的行")

        # 计算总高度
        total_height = 0
        line_spacing = getattr(self.temp_settings, 'line_spacing', 5)
        line_heights = []

        for line_settings in enabled_lines:
            font = QFont(getattr(line_settings, 'font_name', 'Microsoft YaHei'),
                        getattr(line_settings, 'font_size', 24))
            font.setBold(getattr(line_settings, 'bold', False))
            font.setItalic(getattr(line_settings, 'italic', False))

            metrics = QFontMetrics(font)
            line_height = metrics.height()
            line_heights.append(line_height)
            total_height += line_height

        # 添加行间距
        total_height += line_spacing * (len(enabled_lines) - 1)

        # 计算起始位置（根据位置设置）
        start_x, start_y = self.calculate_multiline_position_for_preview(size, total_height, line_heights[0])

        # 绘制每一行
        current_y = start_y
        for i, line_settings in enumerate(enabled_lines):
            print(f"绘制第{i+1}行: {line_settings.text}")

            # 设置字体
            font = QFont(getattr(line_settings, 'font_name', 'Microsoft YaHei'),
                        getattr(line_settings, 'font_size', 24))
            font.setBold(getattr(line_settings, 'bold', False))
            font.setItalic(getattr(line_settings, 'italic', False))
            painter.setFont(font)

            # 设置颜色和透明度
            color_str = getattr(line_settings, 'color', '#FFFFFF')
            color = QColor(color_str)
            opacity = getattr(line_settings, 'opacity', 80)
            color.setAlpha(int(255 * opacity / 100))

            # 绘制阴影（如果启用）
            if getattr(line_settings, 'shadow_enabled', False):
                shadow_color = QColor(getattr(line_settings, 'shadow_color', '#000000'))
                shadow_color.setAlpha(int(255 * opacity / 100 / 2))  # 阴影透明度减半
                painter.setPen(shadow_color)

                shadow_offset_x = getattr(line_settings, 'shadow_offset_x', 2)
                shadow_offset_y = getattr(line_settings, 'shadow_offset_y', 2)
                painter.drawText(start_x + shadow_offset_x, current_y + shadow_offset_y, line_settings.text)

            # 绘制描边（如果启用）
            if getattr(line_settings, 'stroke_enabled', False):
                stroke_color = QColor(getattr(line_settings, 'stroke_color', '#000000'))
                stroke_color.setAlpha(int(255 * opacity / 100))
                stroke_width = getattr(line_settings, 'stroke_width', 2)

                # 简化的描边效果
                for dx in [-1, 0, 1]:
                    for dy in [-1, 0, 1]:
                        if dx != 0 or dy != 0:
                            painter.setPen(stroke_color)
                            painter.drawText(start_x + dx, current_y + dy, line_settings.text)

            # 绘制主文字
            painter.setPen(color)
            painter.drawText(start_x, current_y, line_settings.text)

            # 移动到下一行位置
            current_y += line_heights[i] + line_spacing

        print("多行水印绘制完成")

    def calculate_multiline_position_for_preview(self, size, total_height, first_line_height):
        """计算多行水印在预览中的位置"""
        try:
            # 获取位置设置
            position = getattr(self.temp_settings, 'position', 'bottom_right')
            offset_x = getattr(self.temp_settings, 'offset_x', 20)
            offset_y = getattr(self.temp_settings, 'offset_y', 20)

            print(f"计算预览位置: {position}, 偏移: ({offset_x}, {offset_y})")

            # 预估水印宽度（用于居中计算）
            estimated_width = 200

            # 计算基础位置
            if position == 'top_left':
                start_x = offset_x
                start_y = offset_y + first_line_height
            elif position == 'top_center':
                start_x = (size.width() - estimated_width) // 2
                start_y = offset_y + first_line_height
            elif position == 'top_right':
                start_x = size.width() - estimated_width - offset_x
                start_y = offset_y + first_line_height
            elif position == 'middle_left':
                start_x = offset_x
                start_y = (size.height() - total_height) // 2 + first_line_height
            elif position == 'middle_center':
                start_x = (size.width() - estimated_width) // 2
                start_y = (size.height() - total_height) // 2 + first_line_height
            elif position == 'middle_right':
                start_x = size.width() - estimated_width - offset_x
                start_y = (size.height() - total_height) // 2 + first_line_height
            elif position == 'bottom_left':
                start_x = offset_x
                start_y = size.height() - total_height - offset_y + first_line_height
            elif position == 'bottom_center':
                start_x = (size.width() - estimated_width) // 2
                start_y = size.height() - total_height - offset_y + first_line_height
            else:  # bottom_right
                start_x = size.width() - estimated_width - offset_x
                start_y = size.height() - total_height - offset_y + first_line_height

            print(f"计算得到位置: ({start_x}, {start_y})")
            return start_x, start_y

        except Exception as e:
            print(f"计算预览位置失败: {e}")
            # 返回默认位置
            return 50, (size.height() - total_height) // 2 + first_line_height

    def calculate_watermark_position(self, position, canvas_size, text_size):
        """计算水印位置"""
        # 简化的位置计算
        if position == 'bottom_right':
            return canvas_size[0] - text_size[0] - 20, canvas_size[1] - text_size[1] - 20
        elif position == 'bottom_left':
            return 20, canvas_size[1] - text_size[1] - 20
        elif position == 'top_right':
            return canvas_size[0] - text_size[0] - 20, 20
        elif position == 'top_left':
            return 20, 20
        else:
            return canvas_size[0] - text_size[0] - 20, canvas_size[1] - text_size[1] - 20

    # 重复的 reset_settings 方法已删除 - 使用第555行的版本

    def accept(self):
        """确定 - 应用设置并关闭对话框"""
        try:
            print("🔄 应用水印设置...")

            # 保存导航状态
            if hasattr(self, 'sidebar'):
                self.sidebar.save_navigation_state()

            # 先同步UI到临时设置
            if hasattr(self, 'sync_ui_to_settings'):
                # 注意：这里同步到temp_settings，因为对话框使用temp_settings
                old_settings = self.settings
                self.settings = self.temp_settings  # 临时切换
                self.sync_ui_to_settings()
                temp_dict = self.settings.to_dict()
                self.settings = old_settings  # 恢复

                # 应用到实际设置
                self.settings.from_dict(temp_dict)
                print("✅ 设置已从UI同步并应用")
            else:
                # 应用设置（备用方案）
                self.settings.from_dict(self.temp_settings.to_dict())
                print("✅ 设置已应用（备用方案）")

            # 保存设置到文件
            if hasattr(self, 'save_settings_to_file'):
                self.save_settings_to_file()
                print("✅ 设置已保存到文件")

            # 调试输出最终设置
            print(f"📋 最终设置:")
            print(f"  启用: {self.settings.enabled}")
            print(f"  字体大小: {self.settings.font_size}")
            print(f"  位置: {self.settings.position}")
            print(f"  多行模式: {getattr(self.settings, 'multi_line_mode', False)}")
            if hasattr(self.settings, 'lines') and self.settings.lines:
                print(f"  多行数量: {len(self.settings.lines)}")

            super().accept()

        except Exception as e:
            print(f"❌ 应用设置失败: {e}")
            import traceback
            traceback.print_exc()
            super().accept()  # 即使失败也要关闭对话框

    def reject(self):
        """取消"""
        # 保存导航状态
        if hasattr(self, 'sidebar'):
            self.sidebar.save_navigation_state()
        super().reject()












class BatchRewriteWorker(QRunnable):
    """批量AI改写工作线程"""

    def __init__(self, video_files, ai_rewriter):
        super().__init__()
        self.video_files = video_files
        self.ai_rewriter = ai_rewriter
        self.signals = BatchRewriteSignals()
        self.is_cancelled = False

    def run(self):
        """执行批量改写"""
        try:
            filename_mapping = {}
            total_files = len(self.video_files)

            self.signals.log_message.emit(f"开始批量AI改写 {total_files} 个文件名...", "INFO")

            # 提取文件名（不包含路径）
            filenames = [os.path.basename(video_file) for video_file in self.video_files]

            # 定义进度回调函数
            def progress_callback(current, total, filename):
                if self.is_cancelled:
                    raise Exception("用户取消了批量改写操作")

                self.signals.progress_updated.emit(current, total, filename)

                # 更新主界面日志
                if current % 10 == 0 or current == total:  # 每10个文件或最后一个文件记录一次
                    self.signals.log_message.emit(f"AI改写进度: {current}/{total} ({current/total*100:.1f}%)", "INFO")

            # 调用AI改写器的批量改写方法
            results = self.ai_rewriter.batch_rewrite(filenames, progress_callback)

            # 处理结果
            success_count = 0
            for original_filename, (success, new_name, error) in results.items():
                if success and new_name:
                    # 找到对应的完整路径
                    for video_file in self.video_files:
                        if os.path.basename(video_file) == original_filename:
                            filename_mapping[video_file] = new_name
                            success_count += 1
                            break
                else:
                    # 改写失败，记录错误但继续处理
                    if error:
                        self.signals.log_message.emit(f"改写失败 {original_filename}: {error}", "WARNING")

            self.signals.log_message.emit(f"批量AI改写完成: 成功 {success_count}/{total_files} 个文件", "SUCCESS")

            # 显示改写结果示例
            if filename_mapping:
                self.signals.log_message.emit("改写示例:", "INFO")
                count = 0
                for original_path, new_name in filename_mapping.items():
                    if count < 5:  # 只显示前5个示例
                        original_name = os.path.basename(original_path)
                        self.signals.log_message.emit(f"  {original_name} -> {new_name}", "INFO")
                        count += 1
                    else:
                        break
                if len(filename_mapping) > 5:
                    self.signals.log_message.emit(f"  ... 还有 {len(filename_mapping) - 5} 个文件", "INFO")

            # 发送完成信号
            self.signals.rewrite_finished.emit(filename_mapping)

        except Exception as e:
            error_msg = f"批量AI改写过程中出错: {str(e)}"
            self.signals.log_message.emit(error_msg, "ERROR")
            self.signals.rewrite_failed.emit(error_msg)

    def cancel(self):
        """取消改写操作"""
        self.is_cancelled = True


class VideoProcessorWorker(QRunnable):
    """视频处理工作线程"""

    def __init__(self, input_dir: str, output_dir: str, cover_dir: str,
                 thread_count: int, worker_id: int, shared_data: Dict[str, Any],
                 duration_settings: Optional[Dict[str, Any]] = None, ai_rewriter=None,
                 watermark_config=None):
        super().__init__()
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.cover_dir = cover_dir
        self.thread_count = thread_count
        self.worker_id = worker_id
        self.shared_data = shared_data
        self.signals = VideoProcessorSignals()
        self.is_running = True
        self.ai_rewriter = ai_rewriter
        self.watermark_config = watermark_config or WatermarkConfig()

        # 时长筛选设置
        self.duration_settings = duration_settings or {
            'enabled': False,
            'min_duration': 0,
            'max_duration': 0
        }

        # 支持的视频格式
        self.video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}

    def run(self):
        """执行视频处理任务"""
        try:
            # 安全地发送日志消息
            try:
                self.signals.log_message.emit(f"工作线程 {self.worker_id} 开始处理", "INFO")
            except:
                pass  # 忽略信号发送错误

            while self.is_running:
                # 从共享队列获取任务
                video_file = self.get_next_video()
                if video_file is None:
                    break

                try:
                    # 处理单个视频文件
                    self.process_single_video(video_file)

                    # 更新统计信息
                    with self.shared_data['lock']:
                        self.shared_data['processed_count'] += 1
                        processed = self.shared_data['processed_count']
                        total = self.shared_data['total_count']

                    # 安全地发送进度更新
                    try:
                        progress = int((processed / total) * 100) if total > 0 else 0
                        self.signals.progress_updated.emit(
                            progress,
                            f"已处理 {processed}/{total} 个文件"
                        )
                    except:
                        pass  # 忽略信号发送错误

                except Exception as e:
                    error_msg = f"处理视频文件 {video_file} 时出错: {str(e)}"
                    try:
                        self.signals.log_message.emit(error_msg, "ERROR")
                    except:
                        pass  # 忽略信号发送错误

                    # 更新失败统计
                    with self.shared_data['lock']:
                        self.shared_data['failed_count'] += 1

        except Exception as e:
            try:
                self.signals.error_occurred.emit(f"工作线程 {self.worker_id} 出错: {str(e)}")
            except:
                pass  # 忽略信号发送错误
        finally:
            try:
                self.signals.log_message.emit(f"工作线程 {self.worker_id} 结束", "INFO")
            except:
                pass  # 忽略信号发送错误

    def get_next_video(self) -> Optional[str]:
        """从共享队列获取下一个视频文件"""
        with self.shared_data['lock']:
            if self.shared_data['video_queue']:
                return self.shared_data['video_queue'].pop(0)
            return None

    def process_single_video(self, video_path: str):
        """处理单个视频文件"""
        try:
            video_name = os.path.basename(video_path)
            try:
                self.signals.log_message.emit(f"开始处理: {video_name}", "INFO")
            except:
                pass

            # 检查是否为横屏视频
            if not self.is_landscape_video(video_path):
                try:
                    self.signals.log_message.emit(f"跳过竖屏视频: {video_name}", "INFO")
                except:
                    pass
                # 更新竖屏跳过统计
                with self.shared_data['lock']:
                    self.shared_data['skipped_portrait_count'] += 1
                return

            # 检查视频时长筛选
            if not self.check_duration_filter(video_path):
                try:
                    duration_minutes = self.get_video_duration_minutes(video_path)
                    self.signals.log_message.emit(f"跳过时长不符视频: {video_name} (时长: {duration_minutes:.1f}分钟)", "INFO")
                except:
                    pass
                # 更新时长筛选跳过统计
                with self.shared_data['lock']:
                    self.shared_data['skipped_duration_count'] += 1
                return

            # 处理文件名（使用批量AI改写结果）
            processed_name = self.process_filename(video_name, video_path)

            # 复制视频文件到输出目录
            output_path = os.path.join(self.output_dir, processed_name)
            if not os.path.exists(output_path):
                shutil.copy2(video_path, output_path)
                try:
                    self.signals.log_message.emit(f"已复制视频: {processed_name}", "SUCCESS")
                except:
                    pass

                # 更新成功统计
                with self.shared_data['lock']:
                    self.shared_data['success_count'] += 1
            else:
                try:
                    self.signals.log_message.emit(f"视频已存在，跳过: {processed_name}", "INFO")
                except:
                    pass

            # 生成封面
            self.generate_cover(video_path, processed_name)

        except Exception as e:
            raise Exception(f"处理视频文件失败: {str(e)}")

    def get_video_duration_minutes(self, video_path: str) -> float:
        """获取视频时长（分钟）"""
        if not CV2_AVAILABLE:
            return 0.0

        cap = None
        try:
            cap = cv2.VideoCapture(video_path)  # type: ignore
            if not cap.isOpened():
                return 0.0

            # 获取视频帧数和帧率
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))  # type: ignore
            fps = cap.get(cv2.CAP_PROP_FPS)  # type: ignore

            if fps <= 0:
                return 0.0

            # 计算时长（秒）然后转换为分钟
            duration_seconds = frame_count / fps
            duration_minutes = duration_seconds / 60.0
            return duration_minutes

        except Exception:
            return 0.0
        finally:
            # 确保释放资源
            if cap is not None:
                try:
                    cap.release()
                except:
                    pass

    def check_duration_filter(self, video_path: str) -> bool:
        """检查视频是否符合时长筛选条件"""
        # 如果未启用时长筛选，直接返回True
        if not self.duration_settings['enabled']:
            return True

        duration_minutes = self.get_video_duration_minutes(video_path)
        if duration_minutes <= 0:
            # 如果无法获取时长，默认通过筛选
            return True

        min_duration = self.duration_settings['min_duration']
        max_duration = self.duration_settings['max_duration']

        # 检查最小时长
        if duration_minutes < min_duration:
            return False

        # 检查最大时长（0表示无限制）
        if max_duration > 0 and duration_minutes > max_duration:
            return False

        return True

    def is_landscape_video(self, video_path: str) -> bool:
        """检查视频是否为横屏"""
        if not CV2_AVAILABLE:
            # 如果没有OpenCV，默认认为是横屏
            warning("OpenCV不可用，无法检测视频方向，默认处理所有视频")
            return True

        cap = None
        try:
            cap = cv2.VideoCapture(video_path)  # type: ignore
            if not cap.isOpened():
                warning(f"无法打开视频文件: {video_path}")
                return False

            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))  # type: ignore
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))  # type: ignore

            # 横屏视频：宽度 > 高度
            is_landscape = width > height
            debug(f"视频尺寸 {width}x{height}, 横屏: {is_landscape}")
            return is_landscape

        except Exception as e:
            warning(f"检测视频方向时出错: {str(e)}")
            return False
        finally:
            # 确保释放资源
            if cap is not None:
                try:
                    cap.release()
                except:
                    pass



    def generate_cover(self, video_path: str, video_name: str):
        """生成视频封面"""
        if not CV2_AVAILABLE:
            warning("OpenCV不可用，无法生成封面")
            return

        cap = None
        try:
            cap = cv2.VideoCapture(video_path)  # type: ignore
            if not cap.isOpened():
                warning(f"无法打开视频文件生成封面: {video_path}")
                return

            # 获取视频总帧数和帧率
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))  # type: ignore
            fps = cap.get(cv2.CAP_PROP_FPS)  # type: ignore

            if total_frames <= 0 or fps <= 0:
                warning(f"无法获取视频信息: {video_path}")
                return

            # 计算前10%时长对应的帧数范围
            ten_percent_frames = int(total_frames * 0.1)
            if ten_percent_frames < 1:
                ten_percent_frames = min(total_frames, 30)  # 至少30帧

            # 尝试多次读取帧，确保获取有效数据
            frame = None
            max_attempts = 5

            for attempt in range(max_attempts):
                try:
                    # 随机选择一帧
                    random_frame = random.randint(0, ten_percent_frames - 1)
                    cap.set(cv2.CAP_PROP_POS_FRAMES, random_frame)  # type: ignore

                    ret, temp_frame = cap.read()

                    if ret and temp_frame is not None and temp_frame.size > 0:
                        # 验证帧数据的有效性
                        if len(temp_frame.shape) == 3 and temp_frame.shape[2] == 3:
                            frame = temp_frame
                            debug(f"成功读取视频帧 (尝试 {attempt + 1}): {video_path}")
                            break
                        else:
                            warning(f"读取到无效帧格式 (尝试 {attempt + 1}): {temp_frame.shape}")
                    else:
                        warning(f"读取帧失败 (尝试 {attempt + 1}): ret={ret}, frame={temp_frame is not None}")

                    # 如果不是最后一次尝试，尝试不同的帧位置
                    if attempt < max_attempts - 1:
                        ten_percent_frames = min(ten_percent_frames + 10, total_frames - 1)

                except Exception as e:
                    warning(f"读取帧时出错 (尝试 {attempt + 1}): {str(e)}")

            if frame is None:
                warning(f"无法读取有效视频帧: {video_path}")
                return

            # 调整封面尺寸为1920x1080
            try:
                resized_frame = cv2.resize(frame, (1920, 1080), interpolation=cv2.INTER_LANCZOS4)  # type: ignore
                debug(f"帧尺寸调整成功: {frame.shape} -> {resized_frame.shape}")
            except Exception as e:
                warning(f"调整帧尺寸失败: {str(e)}")
                resized_frame = frame  # 使用原始帧

            # 修复问题1：生成封面文件名（去掉_cover后缀）
            cover_name = os.path.splitext(video_name)[0] + '.jpg'
            cover_path = os.path.join(self.cover_dir, cover_name)

            # 添加水印功能
            if hasattr(self, 'watermark_config') and self.watermark_config.enabled:
                resized_frame = self.add_watermark_to_frame(resized_frame, video_name)

            # 修复问题2：增强封面保存的可靠性
            success = self.save_cover_with_fallback(cover_path, resized_frame, cover_name)

            if success:
                try:
                    self.signals.log_message.emit(f"已生成封面: {cover_name}", "SUCCESS")
                except:
                    pass

                # 如果有PIL，进一步优化图像质量（不再重复添加水印）
                if PIL_AVAILABLE:
                    self.enhance_cover_quality_only(cover_path)
                    print(f"Worker {self.worker_id}: 封面质量优化完成: {cover_name}")
            else:
                try:
                    self.signals.log_message.emit(f"保存封面失败: {cover_name}", "ERROR")
                except:
                    pass

        except Exception as e:
            error_msg = f"生成封面时出错: {str(e)}"
            warning(error_msg)
            try:
                self.signals.log_message.emit(error_msg, "ERROR")
            except:
                pass
        finally:
            # 确保释放资源
            if cap is not None:
                try:
                    cap.release()
                except:
                    pass












    def save_cover_with_fallback(self, cover_path: str, frame, cover_name: str) -> bool:
        """增强的封面保存方法，包含多种故障恢复机制"""
        try:
            # 清理文件路径，移除特殊字符
            cover_path = self.sanitize_file_path(cover_path)
            cover_dir = os.path.dirname(cover_path)

            # 检查目标目录是否存在，不存在则创建
            if not os.path.exists(cover_dir):
                try:
                    os.makedirs(cover_dir, exist_ok=True)
                    debug(f"创建封面目录: {cover_dir}")
                except Exception as e:
                    error_msg = f"创建封面目录失败: {cover_dir}, 错误: {str(e)}"
                    warning(error_msg)
                    try:
                        self.signals.log_message.emit(error_msg, "ERROR")
                    except:
                        pass
                    return False

            # 检查目录权限
            if not os.access(cover_dir, os.W_OK):
                error_msg = f"封面目录没有写入权限: {cover_dir}"
                warning(error_msg)
                try:
                    self.signals.log_message.emit(error_msg, "ERROR")
                except:
                    pass
                return False

            # 检查磁盘空间（至少需要10MB）
            try:
                import shutil
                free_space = shutil.disk_usage(cover_dir).free
                if free_space < 10 * 1024 * 1024:  # 10MB
                    error_msg = f"磁盘空间不足: {free_space / 1024 / 1024:.1f}MB"
                    warning(error_msg)
                    try:
                        self.signals.log_message.emit(error_msg, "ERROR")
                    except:
                        pass
                    return False
            except Exception as e:
                warning(f"检查磁盘空间时出错: {str(e)}")

            # 验证frame数据
            if frame is None or frame.size == 0:
                error_msg = f"无效的图像数据: {cover_name}"
                warning(error_msg)
                try:
                    self.signals.log_message.emit(error_msg, "ERROR")
                except:
                    pass
                return False

            # 检查磁盘空间（简单检查）
            try:
                import shutil
                free_space = shutil.disk_usage(cover_dir).free
                if free_space < 10 * 1024 * 1024:  # 少于10MB
                    error_msg = f"磁盘空间不足: {free_space / 1024 / 1024:.1f}MB"
                    warning(error_msg)
                    try:
                        self.signals.log_message.emit(error_msg, "ERROR")
                    except:
                        pass
                    return False
            except:
                pass  # 忽略磁盘空间检查错误

            # 处理文件名中的特殊字符
            safe_cover_path = self.sanitize_file_path(cover_path)
            if safe_cover_path != cover_path:
                debug(f"文件路径已清理: {cover_path} -> {safe_cover_path}")
                cover_path = safe_cover_path

            # 方法1：使用OpenCV保存（主要方法）
            try:
                # 添加重试机制
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        # 确保frame是有效的numpy数组
                        if not hasattr(frame, 'shape') or len(frame.shape) != 3:
                            raise ValueError(f"无效的图像数据格式: {type(frame)}")

                        # 使用更保守的JPEG质量设置
                        encode_params = [cv2.IMWRITE_JPEG_QUALITY, 90]  # type: ignore
                        success = cv2.imwrite(cover_path, frame, encode_params)  # type: ignore

                        # 验证文件是否真的被创建且有内容
                        if success and os.path.exists(cover_path):
                            file_size = os.path.getsize(cover_path)
                            if file_size > 1024:  # 至少1KB
                                debug(f"OpenCV保存封面成功: {cover_name} (大小: {file_size} 字节)")
                                return True
                            else:
                                warning(f"OpenCV保存的文件过小: {file_size} 字节")
                                if os.path.exists(cover_path):
                                    os.remove(cover_path)
                        else:
                            warning(f"OpenCV保存失败，文件不存在或创建失败: {cover_path}")

                        # 如果不是最后一次尝试，等待一下再重试
                        if attempt < max_retries - 1:
                            import time
                            time.sleep(0.1)
                            debug(f"OpenCV保存重试 {attempt + 2}/{max_retries}: {cover_name}")

                    except Exception as retry_e:
                        warning(f"OpenCV保存尝试 {attempt + 1} 失败: {str(retry_e)}")
                        if attempt == max_retries - 1:
                            raise retry_e

            except Exception as e:
                warning(f"OpenCV保存封面失败: {str(e)}")

            # 方法2：使用PIL作为备用方法
            if PIL_AVAILABLE:
                try:
                    from PIL import Image
                    # 将OpenCV的BGR格式转换为PIL的RGB格式
                    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)  # type: ignore
                    pil_image = Image.fromarray(rgb_frame)

                    # 添加重试机制
                    max_retries = 3
                    for attempt in range(max_retries):
                        try:
                            pil_image.save(cover_path, 'JPEG', quality=90, optimize=True)

                            # 验证文件是否真的被创建且有内容
                            if os.path.exists(cover_path):
                                file_size = os.path.getsize(cover_path)
                                if file_size > 1024:  # 至少1KB
                                    debug(f"PIL保存封面成功: {cover_name} (大小: {file_size} 字节)")
                                    return True
                                else:
                                    warning(f"PIL保存的文件过小: {file_size} 字节")
                                    if os.path.exists(cover_path):
                                        os.remove(cover_path)
                            else:
                                warning(f"PIL保存失败，文件不存在: {cover_path}")

                            # 如果不是最后一次尝试，等待一下再重试
                            if attempt < max_retries - 1:
                                import time
                                time.sleep(0.1)
                                debug(f"PIL保存重试 {attempt + 2}/{max_retries}: {cover_name}")

                        except Exception as retry_e:
                            warning(f"PIL保存尝试 {attempt + 1} 失败: {str(retry_e)}")
                            if attempt == max_retries - 1:
                                raise retry_e

                except Exception as e:
                    warning(f"PIL保存封面失败: {str(e)}")

            # 方法3：尝试使用临时文件名
            try:
                import uuid
                temp_name = f"temp_{uuid.uuid4().hex[:8]}_{cover_name}"
                temp_path = os.path.join(cover_dir, temp_name)

                # 确保临时文件名不冲突
                while os.path.exists(temp_path):
                    temp_name = f"temp_{uuid.uuid4().hex[:8]}_{cover_name}"
                    temp_path = os.path.join(cover_dir, temp_name)

                encode_params = [cv2.IMWRITE_JPEG_QUALITY, 90]  # type: ignore
                success = cv2.imwrite(temp_path, frame, encode_params)  # type: ignore

                if success and os.path.exists(temp_path):
                    file_size = os.path.getsize(temp_path)
                    if file_size > 1024:  # 至少1KB
                        # 重命名临时文件
                        if os.path.exists(cover_path):
                            try:
                                os.remove(cover_path)
                            except Exception as remove_e:
                                warning(f"删除已存在文件失败: {str(remove_e)}")

                        try:
                            os.rename(temp_path, cover_path)
                            debug(f"临时文件保存封面成功: {cover_name} (大小: {file_size} 字节)")
                            return True
                        except Exception as rename_e:
                            warning(f"重命名临时文件失败: {str(rename_e)}")
                            # 清理临时文件
                            if os.path.exists(temp_path):
                                try:
                                    os.remove(temp_path)
                                except:
                                    pass
                    else:
                        warning(f"临时文件过小: {file_size} 字节")
                        if os.path.exists(temp_path):
                            try:
                                os.remove(temp_path)
                            except:
                                pass
                else:
                    warning(f"临时文件创建失败: {temp_path}")

            except Exception as e:
                warning(f"临时文件保存封面失败: {str(e)}")

            # 所有方法都失败
            error_msg = f"所有保存方法都失败: {cover_name}"
            warning(error_msg)
            try:
                self.signals.log_message.emit(error_msg, "ERROR")
            except:
                pass
            return False

        except Exception as e:
            error_msg = f"保存封面时发生未知错误: {str(e)}"
            warning(error_msg)
            try:
                self.signals.log_message.emit(error_msg, "ERROR")
            except:
                pass
            return False

    def sanitize_file_path(self, file_path: str) -> str:
        """清理文件路径中的特殊字符"""
        try:
            # 分离目录和文件名
            dir_path, filename = os.path.split(file_path)

            # 清理文件名中的非法字符
            import re
            # Windows文件名非法字符 + 一些可能导致问题的字符
            illegal_chars = r'[<>:"/\\|?*\x00-\x1f\x7f-\x9f]'
            clean_filename = re.sub(illegal_chars, '_', filename)

            # 处理以点开头的隐藏文件
            if clean_filename.startswith('.') and len(clean_filename) > 1:
                clean_filename = '_' + clean_filename[1:]

            # 移除文件名开头和结尾的空格和点
            clean_filename = clean_filename.strip(' .')

            # 确保文件名不为空
            if not clean_filename or clean_filename == '_':
                import time
                clean_filename = f"cover_{int(time.time())}.jpg"

            # 限制文件名长度（Windows路径限制）
            name, ext = os.path.splitext(clean_filename)
            if len(name) > 200:  # 限制文件名长度
                name = name[:200]
                clean_filename = name + ext

            # 确保扩展名正确
            if not ext.lower() in ['.jpg', '.jpeg']:
                clean_filename = os.path.splitext(clean_filename)[0] + '.jpg'

            # 处理目录路径
            clean_dir_path = os.path.normpath(dir_path)

            # 组合最终路径
            final_path = os.path.join(clean_dir_path, clean_filename)

            # 确保路径长度不超过Windows限制（260字符）
            if len(final_path) > 250:
                # 缩短文件名
                name, ext = os.path.splitext(clean_filename)
                max_name_len = 250 - len(clean_dir_path) - len(ext) - 1
                if max_name_len > 10:
                    name = name[:max_name_len]
                    clean_filename = name + ext
                    final_path = os.path.join(clean_dir_path, clean_filename)
                else:
                    # 路径太长，使用简短的文件名
                    import time
                    clean_filename = f"c_{int(time.time())}.jpg"
                    final_path = os.path.join(clean_dir_path, clean_filename)

            debug(f"文件路径清理: {filename} -> {clean_filename}")
            return final_path

        except Exception as e:
            warning(f"清理文件路径失败: {str(e)}")
            # 返回一个安全的默认路径
            try:
                dir_path = os.path.dirname(file_path)
                import time
                safe_filename = f"cover_{int(time.time())}.jpg"
                return os.path.join(dir_path, safe_filename)
            except:
                return file_path

    def enhance_cover_quality(self, cover_path: str, video_name: Optional[str] = None):
        """使用PIL增强封面质量（水印已在生成时添加）"""
        try:

            with Image.open(cover_path) as img:
                # 轻微增强对比度和锐度
                enhancer = ImageEnhance.Contrast(img)
                img = enhancer.enhance(1.1)

                enhancer = ImageEnhance.Sharpness(img)
                img = enhancer.enhance(1.1)

                # 水印已在封面生成时添加，这里不再重复添加

                # 保存优化后的图像
                img.save(cover_path, 'JPEG', quality=95, optimize=True)

        except Exception as e:
            debug(f"增强封面质量时出错: {str(e)}")

    def enhance_cover_quality_only(self, cover_path: str):
        """仅增强封面质量，不添加水印（避免重复添加）"""
        try:
            from PIL import Image, ImageEnhance

            with Image.open(cover_path) as img:
                # 轻微增强对比度和锐度
                enhancer = ImageEnhance.Contrast(img)
                img = enhancer.enhance(1.1)

                enhancer = ImageEnhance.Sharpness(img)
                img = enhancer.enhance(1.1)

                # 保存优化后的图像（不添加水印）
                img.save(cover_path, 'JPEG', quality=95, optimize=True)
                print(f"封面质量增强完成: {os.path.basename(cover_path)}")

        except Exception as e:
            debug(f"增强封面质量时出错: {str(e)}")

    def process_filename(self, filename: str, video_path: Optional[str] = None) -> str:
        """处理文件名，使用预先批量改写的文件名映射"""
        name, ext = os.path.splitext(filename)
        original_name = name

        # 检查是否有预先批量改写的文件名映射
        filename_mapping = self.shared_data.get('filename_mapping', {})

        if video_path and video_path in filename_mapping:
            # 使用预先批量改写的文件名
            name = filename_mapping[video_path]
            debug(f"使用批量AI改写结果: {original_name} -> {name}")

            try:
                self.signals.log_message.emit(f"使用AI改写: {original_name} -> {name}", "SUCCESS")
            except:
                pass
        else:
            # 没有预先改写的结果，使用原始文件名
            debug(f"使用原始文件名: {original_name}")

        # 限制文件名长度为30个字符
        if len(name) > 30:
            name = name[:30]
            debug(f"文件名已截断: {filename} -> {name + ext}")

        return name + ext

    def add_watermark_to_frame(self, frame, video_name):
        """为视频帧添加水印"""
        if not PIL_AVAILABLE or not self.watermark_config.enabled:
            return frame

        try:
            # 将OpenCV帧转换为PIL图像
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(frame_rgb)

            # 获取文件名（去除扩展名）作为水印文字
            watermark_text = os.path.splitext(video_name)[0]

            # 添加水印
            watermarked_image = self.add_watermark_to_pil_image(pil_image, watermark_text)

            # 转换回OpenCV格式
            if watermarked_image:
                watermarked_array = np.array(watermarked_image)
                watermarked_frame = cv2.cvtColor(watermarked_array, cv2.COLOR_RGB2BGR)
                return watermarked_frame

        except Exception as e:
            warning(f"添加水印失败: {e}")

        return frame

    def add_watermark_to_pil_image(self, img, text):
        """为PIL图像添加水印（封面参数放大10倍）"""
        try:
            print(f"封面生成水印: {text}")
            print(f"多行模式状态: {self.watermark_config.multi_line_enabled}")

            # 检查是否启用多行模式
            if self.watermark_config.multi_line_enabled:
                print("使用多行水印模式")
                return self.add_multiline_watermark_to_pil_image(img, text)
            else:
                print("使用单行水印模式")
                return self.add_single_watermark_to_pil_image(img, text, self.watermark_config)

        except Exception as e:
            print(f"添加水印失败: {e}")
            return img



    # 重复的 get_preview_font 方法已删除 - 使用第10585行的完整版本
    # 重复的 hex_to_rgba 方法已删除 - 使用第5175行的版本

    def _safe_font_operation(self, draw, operation, *args, font=None, **kwargs):
        """安全的字体操作，处理DummyFont类型问题"""
        try:
            if font is not None and hasattr(font, 'getbbox'):
                # 使用有效的字体
                return getattr(draw, operation)(*args, font=font, **kwargs)
            else:
                # 不传递字体参数，使用默认字体
                return getattr(draw, operation)(*args, **kwargs)
        except Exception as e:
            print(f"字体操作失败: {e}")
            # 使用默认字体重试
            return getattr(draw, operation)(*args, **kwargs)

    def add_single_watermark_to_pil_image(self, watermarked, text, config):
        """为PIL图像添加单行水印"""
        try:
            # 获取当前界面的字体设置，封面字体放大5倍
            if hasattr(self, 'font_combo') and hasattr(self, 'font_size_spinbox'):
                current_font_family = self.font_combo.currentText()
                original_font_size = self.font_size_spinbox.value()
                current_font_size = int(original_font_size * 5)  # 封面字体放大5倍
                print(f"封面生成字体: {current_font_family}, 预览{original_font_size}px → 封面{current_font_size}px (5倍)")
            else:
                current_font_family = config.font_family
                original_font_size = config.font_size
                current_font_size = int(original_font_size * 5)  # 封面字体放大5倍
                print(f"封面生成字体: {current_font_family}, 预览{original_font_size}px → 封面{current_font_size}px (5倍)")

            # 尝试加载字体 - 使用与预览相同的字体加载方法
            try:
                font = self.get_preview_font(current_font_family, current_font_size)
                print(f"封面字体加载成功: {current_font_size}px")
            except Exception as e:
                print(f"封面字体加载失败: {e}")
                try:
                    # 直接尝试加载微软雅黑
                    font_path = "C:/Windows/Fonts/msyh.ttc"
                    font = ImageFont.truetype(font_path, current_font_size)
                    print(f"封面使用微软雅黑: {current_font_size}px")
                except Exception as e2:
                    print(f"微软雅黑加载也失败: {e2}")
                    try:
                        font = ImageFont.load_default()
                        print("封面生成使用默认字体")
                    except Exception as e3:
                        print(f"默认字体加载失败: {e3}")
                        return watermarked

            # 计算文字位置
            draw = ImageDraw.Draw(watermarked)
            # 使用安全的字体操作
            bbox = self._safe_font_operation(draw, 'textbbox', (0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            # 获取当前界面的位置设置，确保与预览一致
            if hasattr(self, 'position_x_spinbox') and hasattr(self, 'position_y_spinbox'):
                current_position_x = self.position_x_spinbox.value()
                current_position_y = self.position_y_spinbox.value()
                print(f"封面生成使用界面当前位置: ({current_position_x}%, {current_position_y}%)")
            else:
                current_position_x = config.position_x
                current_position_y = config.position_y
                print(f"封面生成使用配置位置: ({current_position_x}%, {current_position_y}%)")

            img_width, img_height = watermarked.size
            # 修复：使用正确的百分比位置计算，与预览保持一致
            x = int(img_width * current_position_x / 100)
            y = int(img_height * current_position_y / 100)

            print(f"封面水印位置: {current_position_x}%,{current_position_y}% -> ({x},{y}) 基于尺寸({img_width}x{img_height})")

            # 获取当前界面的样式设置，确保与预览一致
            if hasattr(self, 'text_color_label'):
                current_text_color = self.text_color_label.text()
            else:
                current_text_color = config.text_color

            if hasattr(self, 'opacity_slider'):
                current_opacity = self.opacity_slider.value()
            else:
                current_opacity = config.opacity

            if hasattr(self, 'shadow_checkbox'):
                current_shadow_enabled = self.shadow_checkbox.isChecked()
                current_shadow_color = self.shadow_color_label.text() if hasattr(self, 'shadow_color_label') else config.shadow_color
                current_shadow_offset_x = self.shadow_offset_x_spinbox.value() if hasattr(self, 'shadow_offset_x_spinbox') else config.shadow_offset_x
                current_shadow_offset_y = self.shadow_offset_y_spinbox.value() if hasattr(self, 'shadow_offset_y_spinbox') else config.shadow_offset_y
            else:
                current_shadow_enabled = config.shadow_enabled
                current_shadow_color = config.shadow_color
                current_shadow_offset_x = config.shadow_offset_x
                current_shadow_offset_y = config.shadow_offset_y

            if hasattr(self, 'stroke_checkbox'):
                current_stroke_enabled = self.stroke_checkbox.isChecked()
                current_stroke_color = self.stroke_color_label.text() if hasattr(self, 'stroke_color_label') else getattr(config, 'stroke_color', '#000000')
                current_stroke_width = self.stroke_width_spinbox.value() if hasattr(self, 'stroke_width_spinbox') else getattr(config, 'stroke_width', 2)
            else:
                current_stroke_enabled = getattr(config, 'stroke_enabled', False)
                current_stroke_color = getattr(config, 'stroke_color', '#000000')
                current_stroke_width = getattr(config, 'stroke_width', 2)

            print(f"封面样式: 颜色={current_text_color}, 透明度={current_opacity}%, 阴影={current_shadow_enabled}, 描边={current_stroke_enabled}")

            # 创建高质量渲染图层
            overlay = Image.new('RGBA', watermarked.size, (0, 0, 0, 0))
            overlay_draw = ImageDraw.Draw(overlay)

            # 绘制阴影
            if current_shadow_enabled:
                # 简单阴影
                shadow_x = x + current_shadow_offset_x
                shadow_y = y + current_shadow_offset_y
                shadow_rgba = self.hex_to_rgba(current_shadow_color, current_opacity)
                self._safe_font_operation(overlay_draw, 'text', (shadow_x, shadow_y), text, font=font, fill=shadow_rgba)

            # 绘制描边
            if current_stroke_enabled:
                stroke_rgba = self.hex_to_rgba(current_stroke_color, current_opacity)
                # 绘制多层描边以获得更好的效果
                for dx in range(-current_stroke_width, current_stroke_width + 1):
                    for dy in range(-current_stroke_width, current_stroke_width + 1):
                        if dx*dx + dy*dy <= current_stroke_width*current_stroke_width:
                            self._safe_font_operation(overlay_draw, 'text', (x + dx, y + dy), text, font=font, fill=stroke_rgba)

            # 绘制主文字
            text_rgba = self.hex_to_rgba(current_text_color, current_opacity)
            self._safe_font_operation(overlay_draw, 'text', (x, y), text, font=font, fill=text_rgba)

            # 合成图像
            watermarked = watermarked.convert('RGBA')
            watermarked = Image.alpha_composite(watermarked, overlay)
            watermarked = watermarked.convert('RGB')

            return watermarked

        except Exception as e:
            print(f"添加单行水印失败: {e}")
            return watermarked

    def add_multiline_watermark_to_pil_image(self, watermarked, text):
        """为PIL图像添加多行水印"""
        try:
            # 创建高质量渲染图层
            overlay = Image.new('RGBA', watermarked.size, (0, 0, 0, 0))

            # 遍历每一行配置
            for i, line_config in enumerate(self.watermark_config.multi_line_configs):
                if not line_config.enabled:
                    continue

                # 获取该行的文字
                line_text = self.get_line_text_for_video(text, i)
                if not line_text:
                    continue

                print(f"绘制第{i+1}行水印: '{line_text}'")

                # 为该行添加水印
                overlay = self.add_line_watermark_to_overlay(overlay, line_text, line_config)

            # 合成图像
            watermarked = watermarked.convert('RGBA')
            watermarked = Image.alpha_composite(watermarked, overlay)
            watermarked = watermarked.convert('RGB')

            return watermarked

        except Exception as e:
            print(f"添加多行水印失败: {e}")
            return watermarked

    def get_line_text_for_video(self, full_text, line_index):
        """获取视频处理时指定行的文字"""
        try:
            if line_index >= len(self.watermark_config.multi_line_configs):
                return ""

            line_config = self.watermark_config.multi_line_configs[line_index]
            char_count = line_config.char_count

            # 计算该行的起始位置
            start_pos = 0
            for i in range(line_index):
                if i < len(self.watermark_config.multi_line_configs):
                    start_pos += self.watermark_config.multi_line_configs[i].char_count

            # 提取该行的文字
            end_pos = start_pos + char_count
            line_text = full_text[start_pos:end_pos] if start_pos < len(full_text) else ""

            return line_text

        except Exception as e:
            print(f"获取第{line_index+1}行文字失败: {e}")
            return ""

    def add_line_watermark_to_overlay(self, overlay, text, line_config):
        """在overlay上添加单行水印"""
        try:
            overlay_draw = ImageDraw.Draw(overlay)

            # 简化字体配置获取逻辑，直接使用line_config中的设置
            # 这样确保每行使用自己配置的字体，而不是界面当前值
            current_font_family = line_config.font_family
            original_font_size = line_config.font_size
            current_font_size = int(original_font_size * 5)  # 封面字体放大5倍

            # 验证字体配置
            print(f"多行水印字体配置验证:")
            print(f"  字体名称: {current_font_family}")
            print(f"  原始大小: {original_font_size}px")
            print(f"  视频大小: {current_font_size}px")
            print(f"  文字内容: '{text}'")

            # 尝试加载字体 - 使用与预览相同的字体加载方法
            try:
                font = self.get_preview_font(current_font_family, current_font_size)
                print(f"封面多行字体加载成功: {current_font_size}px")
            except Exception as e:
                print(f"封面多行字体加载失败: {e}")
                try:
                    # 直接尝试加载微软雅黑
                    font_path = "C:/Windows/Fonts/msyh.ttc"
                    font = ImageFont.truetype(font_path, current_font_size)
                    print(f"封面多行使用微软雅黑: {current_font_size}px")
                except Exception as e2:
                    print(f"微软雅黑加载也失败: {e2}")
                    try:
                        font = ImageFont.load_default()
                        print("封面生成使用默认字体")
                    except Exception as e3:
                        print(f"默认字体加载失败: {e3}")
                        return overlay

            # 计算文字位置 - 修复位置计算公式
            bbox = self._safe_font_operation(overlay_draw, 'textbbox', (0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            img_width, img_height = overlay.size
            # 修复：使用正确的百分比位置计算，与预览保持一致
            x = int(img_width * line_config.position_x / 100)
            y = int(img_height * line_config.position_y / 100)

            print(f"视频水印位置计算: {line_config.position_x}%,{line_config.position_y}% -> ({x},{y}) 基于尺寸({img_width}x{img_height})")

            # 绘制描边
            if line_config.stroke_enabled:
                stroke_rgba = self.hex_to_rgba(line_config.stroke_color, line_config.opacity)
                stroke_width = line_config.stroke_width
                # 绘制多层描边以获得更好的效果
                for dx in range(-stroke_width, stroke_width + 1):
                    for dy in range(-stroke_width, stroke_width + 1):
                        if dx*dx + dy*dy <= stroke_width*stroke_width:
                            self._safe_font_operation(overlay_draw, 'text', (x + dx, y + dy), text, font=font, fill=stroke_rgba)

            # 绘制阴影
            if line_config.shadow_enabled:
                shadow_x = x + line_config.shadow_offset_x
                shadow_y = y + line_config.shadow_offset_y
                shadow_rgba = self.hex_to_rgba(line_config.shadow_color, line_config.opacity)
                self._safe_font_operation(overlay_draw, 'text', (shadow_x, shadow_y), text, font=font, fill=shadow_rgba)

            # 绘制主文字
            text_rgba = self.hex_to_rgba(line_config.text_color, line_config.opacity)
            self._safe_font_operation(overlay_draw, 'text', (x, y), text, font=font, fill=text_rgba)

            return overlay

        except Exception as e:
            print(f"添加行水印到overlay失败: {e}")
            return overlay

    def get_font_path(self, font_family):
        """获取字体文件路径"""
        font_map = {
            "Microsoft YaHei": "msyh.ttc",
            "SimSun": "simsun.ttc",
            "SimHei": "simhei.ttf",
            "KaiTi": "simkai.ttf",
            "Arial": "arial.ttf",
            "Times New Roman": "times.ttf",
            "Helvetica": "arial.ttf"
        }

        font_file = font_map.get(font_family, "msyh.ttc")
        return f"C:/Windows/Fonts/{font_file}"

    def hex_to_rgba(self, hex_color, opacity):
        """将十六进制颜色转换为RGBA"""
        hex_color = hex_color.lstrip('#')
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)
        a = int(255 * opacity / 100)
        return (r, g, b, a)


class VideoProcessorDialog(QDialog):
    """视频高质量去重工具对话框

    主要功能：
    - 自动筛选横屏视频（去除竖屏和方形视频）
    - 生成高质量1920x1080封面图片
    - 智能清空输出目录，避免文件冲突
    - 多线程并行处理，提升处理效率
    """

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🎬 视频高质量去重工具")
        self.setModal(True)

        # 初始化变量
        self.thread_pool = QThreadPool()
        self.is_processing = False
        self.worker_threads = []
        self.shared_data = {
            'video_queue': [],
            'total_count': 0,
            'processed_count': 0,
            'success_count': 0,
            'failed_count': 0,
            'skipped_portrait_count': 0,
            'skipped_duration_count': 0,
            'lock': threading.Lock()
        }

        # 初始化水印配置
        self.watermark_config = WatermarkConfig()

        # 预览相关变量
        self.preview_image = None
        self.preview_label = None

        # 批量改写相关变量
        self.batch_rewrite_worker = None
        self.batch_progress_dialog = None

        # 水印功能已删除

        # 初始化自动保存标志
        self._auto_save_enabled = True

        # 初始化AI文件名改写器
        try:
            from app.utils.ai_filename_rewriter import AIFilenameRewriter
            self.ai_rewriter = AIFilenameRewriter()
            info("AI文件名改写器初始化成功")
        except ImportError as e:
            error_msg = f"AI文件名改写器导入失败: {e}"
            warning(error_msg)
            self.ai_rewriter = None
        except Exception as e:
            error_msg = f"AI文件名改写器初始化失败: {e}"
            warning(error_msg)
            self.ai_rewriter = None

        # 创建UI
        self.init_ui()

        # 自适应屏幕尺寸设置（在UI初始化后调用）
        self.setup_adaptive_window_size()

        # 加载设置
        self.load_settings()

        # 水印设置加载已删除

        # 连接信号
        self.connect_signals()

        # 连接保存设置的信号
        self.save_settings_checkbox.stateChanged.connect(self.on_save_settings_changed)

        # 连接路径改变信号，自动保存设置
        self.input_dir_edit.textChanged.connect(self.auto_save_settings)
        self.output_dir_edit.textChanged.connect(self.auto_save_settings)
        self.cover_dir_edit.textChanged.connect(self.auto_save_settings)
        self.thread_count_combo.currentTextChanged.connect(self.auto_save_settings)
        self.overwrite_checkbox.stateChanged.connect(self.auto_save_settings)
        self.generate_cover_checkbox.stateChanged.connect(self.auto_save_settings)
        # 封面水印功能已删除
        self.ai_rewrite_checkbox.stateChanged.connect(self.auto_save_settings)

        # 连接智能体配置信号
        if hasattr(self, 'agent_id_edit'):
            self.agent_id_edit.textChanged.connect(self.on_agent_config_changed)
        if hasattr(self, 'token_edit'):
            self.token_edit.textChanged.connect(self.on_agent_config_changed)



        # 更新AI服务状态
        self.update_ai_service_status()

        # 同步水印配置与复选框状态
        self.watermark_config.enabled = self.watermark_checkbox.isChecked()
        print(f"初始化水印配置: enabled={self.watermark_config.enabled}")

        info("视频处理对话框已初始化")

    def setup_adaptive_window_size(self):
        """设置自适应窗口尺寸"""
        try:
            # 获取屏幕信息
            desktop = QApplication.desktop()
            if desktop:
                screen_rect = desktop.availableGeometry()
            else:
                # 使用默认屏幕尺寸
                screen_rect = None

            # 计算窗口尺寸
            if screen_rect is not None:
                screen_width = screen_rect.width()
                screen_height = screen_rect.height()

                # 设置窗口宽度（最小900，最大1400，屏幕宽度的80%）
                window_width = max(900, min(1400, int(screen_width * 0.8)))

                # 设置窗口高度（屏幕可用高度的90%，但不超过1100）
                window_height = min(1100, int(screen_height * 0.9))

                # 设置最小尺寸
                min_width = 900
                min_height = min(800, int(screen_height * 0.7))
            else:
                # 使用默认尺寸
                window_width = 1200
                window_height = 800
                min_width = 900
                min_height = 700

            self.setMinimumSize(min_width, min_height)
            self.resize(window_width, window_height)

            # 允许最大化 - 跳过设置，避免兼容性问题
            # 注释：WindowMaximizeButtonHint在某些PyQt5版本中可能不可用
            pass

            # 将窗口居中显示
            self.center_window(screen_rect, window_width, window_height)

            # 强制刷新布局，确保所有控件正确显示
            QApplication.processEvents()
            self.updateGeometry()
            self.update()

        except Exception as e:
            print(f"设置视频处理窗口自适应尺寸时出错: {e}")
            self.setMinimumSize(900, 800)
            self.resize(1000, 900)

    def center_window(self, screen_rect, window_width, window_height):
        """将窗口居中显示在屏幕上"""
        try:
            x = screen_rect.x() + (screen_rect.width() - window_width) // 2
            y = screen_rect.y() + (screen_rect.height() - window_height) // 2
            x = max(screen_rect.x(), min(x, screen_rect.x() + screen_rect.width() - window_width))
            y = max(screen_rect.y(), min(y, screen_rect.y() + screen_rect.height() - window_height))
            self.move(x, y)
        except Exception as e:
            print(f"居中视频处理窗口时出错: {e}")

    def showEvent(self, a0):
        """窗口显示事件，确保布局正确"""
        super().showEvent(a0)
        # 延迟刷新布局，确保所有控件正确显示
        QTimer.singleShot(50, self.refresh_layout)

    def refresh_layout(self):
        """刷新布局"""
        try:
            self.updateGeometry()
            self.update()
            QApplication.processEvents()
        except Exception as e:
            print(f"刷新视频处理对话框布局时出错: {e}")

        # 添加功能介绍
        self.add_log("=" * 60, "INFO")
        self.add_log("视频高质量去重工具 - 专业视频处理解决方案", "SUCCESS")
        self.add_log("=" * 60, "INFO")
        self.add_log("核心功能:", "INFO")
        self.add_log("🎯 自动筛选横屏视频（去除竖屏和方形视频）", "SUCCESS")
        self.add_log("🖼️ 生成高质量1920x1080封面图片", "SUCCESS")
        self.add_log("🧹 智能清空输出目录，避免文件冲突", "SUCCESS")
        self.add_log("⚡ 多线程并行处理，提升处理效率", "SUCCESS")
        self.add_log("🛡️ 完善的错误处理和恢复机制", "SUCCESS")
        self.add_log("", "INFO")
        self.add_log("去重原理:", "INFO")
        self.add_log("• 通过视频尺寸比例筛选，只保留横屏视频", "INFO")
        self.add_log("• 生成统一规格封面，便于后续处理", "INFO")
        self.add_log("• 自动重命名和整理，避免重复文件", "INFO")
        self.add_log("", "INFO")
        self.add_log("技术特性:", "INFO")
        self.add_log("• 智能线程数管理，根据系统配置自动调整", "INFO")
        self.add_log("• OpenCV视频分析，精确检测视频方向", "INFO")
        self.add_log("• 多重封面保存机制，确保生成成功", "INFO")
        self.add_log("• 详细处理日志，全程可追踪", "INFO")
        self.add_log("• 智能设置保存，自动记忆用户配置", "INFO")
        self.add_log("", "INFO")
        self.add_log("设置保存功能:", "INFO")
        self.add_log("• 自动保存路径、线程数等所有设置", "INFO")
        self.add_log("• 下次启动时自动恢复上次配置", "INFO")
        self.add_log("• 支持手动开启/关闭设置保存", "INFO")
        self.add_log("• 设置文件保存在用户目录，安全可靠", "INFO")
        self.add_log("", "INFO")
        self.add_log("使用建议:", "INFO")
        self.add_log("• 推荐使用8线程设置，平衡性能与稳定性", "INFO")
        self.add_log("• 大量文件建议分批处理，避免系统负载过高", "INFO")
        self.add_log("• 处理过程中避免关闭程序，确保数据完整性", "INFO")
        self.add_log("• 定期清理输出目录，保持文件组织有序", "INFO")
        self.add_log("• 启用设置保存功能，提升使用体验", "INFO")
        self.add_log("=" * 60, "INFO")

    def init_ui(self):
        """初始化用户界面 - 简洁白色极简设计"""
        # 设置对话框样式
        self.setStyleSheet("""
            QDialog {
                background-color: white;
                color: #333;
            }
            QGroupBox {
                font-weight: 500;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                margin-top: 8px;
                padding-top: 8px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #333;
                background-color: white;
            }
            QLineEdit {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 6px 10px;
                background-color: white;
                font-size: 13px;
                color: #333;
            }
            QLineEdit:focus {
                border: 1px solid #007acc;
                outline: none;
            }
            QPushButton {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 13px;
                color: #333;
                min-height: 24px;
            }
            QPushButton:hover {
                background-color: #f8f8f8;
                border-color: #007acc;
            }
            QPushButton:pressed {
                background-color: #f0f0f0;
            }
            QComboBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 6px 10px;
                background-color: white;
                font-size: 13px;
                color: #333;
                min-height: 24px;
            }
            QComboBox:focus {
                border: 1px solid #007acc;
            }
            QCheckBox {
                color: #333;
                font-size: 13px;
            }
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #f8f8f8;
                border: 1px solid #ddd;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }
        """)

        # 创建主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建滚动区域
        scroll_area = QScrollArea(self)
        scroll_area.setWidgetResizable(True)
        # 设置滚动条策略 - 使用默认策略避免兼容性问题
        # 注释：ScrollBarAsNeeded在某些PyQt5版本中可能不可用
        pass
        scroll_area.setFrameShape(QScrollArea.NoFrame)

        # 创建内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(20, 20, 20, 10)
        content_layout.setSpacing(15)

        # 创建标题
        title_label = QLabel("🎬 视频处理工具")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(18)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #333; margin-bottom: 5px;")
        content_layout.addWidget(title_label)

        # 创建说明标签
        desc_label = QLabel("自动筛选横屏视频并生成高质量封面")
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setStyleSheet("color: #666; font-size: 13px; margin-bottom: 10px;")
        content_layout.addWidget(desc_label)

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 创建设置标签页
        self.settings_tab = QWidget()
        self.init_settings_tab()
        self.tab_widget.addTab(self.settings_tab, "⚙️ 设置")

        # 创建日志标签页
        self.log_tab = QWidget()
        self.init_log_tab()
        self.tab_widget.addTab(self.log_tab, "📋 处理日志")

        content_layout.addWidget(self.tab_widget)

        # 设置滚动区域内容
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area, 1)

        # 创建底部控制区域（固定在底部，不滚动）
        self.init_control_area(main_layout)

    def init_settings_tab(self):
        """初始化设置标签页 - 简洁白色极简设计"""
        layout = QVBoxLayout(self.settings_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)

        # 路径设置组
        path_group = QGroupBox("路径设置")
        path_layout = QGridLayout(path_group)
        path_layout.setSpacing(8)
        path_layout.setContentsMargins(15, 20, 15, 15)

        # 输入目录
        input_label = QLabel("输入目录:")
        input_label.setStyleSheet("font-weight: 500; color: #333; font-size: 13px;")
        path_layout.addWidget(input_label, 0, 0)

        self.input_dir_edit = QLineEdit()
        self.input_dir_edit.setPlaceholderText("选择包含视频文件的输入目录")
        self.input_dir_edit.setMinimumHeight(32)
        path_layout.addWidget(self.input_dir_edit, 0, 1)

        input_browse_btn = QPushButton("浏览")
        input_browse_btn.setFixedWidth(70)
        input_browse_btn.clicked.connect(self.browse_input_dir)
        path_layout.addWidget(input_browse_btn, 0, 2)

        # 视频输出目录
        output_label = QLabel("视频输出目录:")
        output_label.setStyleSheet("font-weight: 500; color: #333; font-size: 13px;")
        path_layout.addWidget(output_label, 1, 0)

        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setPlaceholderText("筛选后的横屏视频保存目录")
        self.output_dir_edit.setMinimumHeight(32)
        path_layout.addWidget(self.output_dir_edit, 1, 1)

        output_browse_btn = QPushButton("浏览")
        output_browse_btn.setFixedWidth(70)
        output_browse_btn.clicked.connect(self.browse_output_dir)
        path_layout.addWidget(output_browse_btn, 1, 2)

        # 封面输出目录
        cover_label = QLabel("封面输出目录:")
        cover_label.setStyleSheet("font-weight: 500; color: #333; font-size: 13px;")
        path_layout.addWidget(cover_label, 2, 0)

        self.cover_dir_edit = QLineEdit()
        self.cover_dir_edit.setPlaceholderText("生成的封面图片保存目录")
        self.cover_dir_edit.setMinimumHeight(32)
        path_layout.addWidget(self.cover_dir_edit, 2, 1)

        cover_browse_btn = QPushButton("浏览")
        cover_browse_btn.setFixedWidth(70)
        cover_browse_btn.clicked.connect(self.browse_cover_dir)
        path_layout.addWidget(cover_browse_btn, 2, 2)

        layout.addWidget(path_group)

        # 处理设置组
        process_group = QGroupBox("处理设置")
        process_layout = QGridLayout(process_group)
        process_layout.setSpacing(8)
        process_layout.setContentsMargins(15, 20, 15, 15)

        # 线程数设置
        thread_label = QLabel("线程数:")
        thread_label.setStyleSheet("font-weight: 500; color: #333; font-size: 13px;")
        process_layout.addWidget(thread_label, 0, 0)

        self.thread_count_combo = QComboBox()
        self.thread_count_combo.addItems(["4", "8", "12", "16"])
        self.thread_count_combo.setCurrentText("8")  # 默认8线程，更稳定
        self.thread_count_combo.setFixedWidth(80)
        self.thread_count_combo.setMinimumHeight(32)
        process_layout.addWidget(self.thread_count_combo, 0, 1)

        # 添加说明标签
        thread_desc = QLabel("(推荐8线程)")
        thread_desc.setStyleSheet("color: #666; font-size: 12px;")
        process_layout.addWidget(thread_desc, 0, 2)

        # 覆盖设置
        self.overwrite_checkbox = QCheckBox("覆盖已存在的文件")
        self.overwrite_checkbox.setChecked(False)
        self.overwrite_checkbox.setStyleSheet("margin-top: 5px;")
        process_layout.addWidget(self.overwrite_checkbox, 1, 0, 1, 3)

        # 生成封面设置
        self.generate_cover_checkbox = QCheckBox("生成视频封面")
        self.generate_cover_checkbox.setChecked(True)
        process_layout.addWidget(self.generate_cover_checkbox, 2, 0, 1, 3)

        # 封面水印设置
        self.watermark_checkbox = QCheckBox("启用封面水印")
        self.watermark_checkbox.setChecked(True)  # 默认启用水印
        self.watermark_checkbox.toggled.connect(self.on_watermark_toggled)
        process_layout.addWidget(self.watermark_checkbox, 3, 0, 1, 3)

        # 视频时长筛选设置
        duration_label = QLabel("视频时长筛选（分钟）:")
        duration_label.setStyleSheet("font-weight: 500; color: #333; font-size: 13px; margin-top: 10px;")
        process_layout.addWidget(duration_label, 4, 0, 1, 3)

        # 时长筛选启用复选框
        self.duration_filter_checkbox = QCheckBox("启用时长筛选")
        self.duration_filter_checkbox.setChecked(False)
        self.duration_filter_checkbox.setStyleSheet("margin-top: 5px;")
        self.duration_filter_checkbox.stateChanged.connect(self.on_duration_filter_changed)
        process_layout.addWidget(self.duration_filter_checkbox, 5, 0, 1, 3)

        # 时长范围设置
        duration_range_layout = QHBoxLayout()

        # 最小时长
        min_duration_label = QLabel("最小:")
        min_duration_label.setStyleSheet("color: #666; font-size: 12px;")
        duration_range_layout.addWidget(min_duration_label)

        self.min_duration_spinbox = QSpinBox()
        self.min_duration_spinbox.setRange(0, 999)
        self.min_duration_spinbox.setValue(0)
        self.min_duration_spinbox.setSuffix(" 分钟")
        self.min_duration_spinbox.setFixedWidth(100)
        self.min_duration_spinbox.setEnabled(False)
        self.min_duration_spinbox.valueChanged.connect(self.auto_save_settings)
        duration_range_layout.addWidget(self.min_duration_spinbox)

        duration_range_layout.addSpacing(20)

        # 最大时长
        max_duration_label = QLabel("最大:")
        max_duration_label.setStyleSheet("color: #666; font-size: 12px;")
        duration_range_layout.addWidget(max_duration_label)

        self.max_duration_spinbox = QSpinBox()
        self.max_duration_spinbox.setRange(0, 999)
        self.max_duration_spinbox.setValue(0)
        self.max_duration_spinbox.setSuffix(" 分钟")
        self.max_duration_spinbox.setSpecialValueText("无限制")
        self.max_duration_spinbox.setFixedWidth(100)
        self.max_duration_spinbox.setEnabled(False)
        self.max_duration_spinbox.valueChanged.connect(self.auto_save_settings)
        duration_range_layout.addWidget(self.max_duration_spinbox)

        duration_range_layout.addStretch()

        process_layout.addLayout(duration_range_layout, 6, 0, 1, 3)

        # 时长筛选帮助提示
        duration_help_label = QLabel("设置视频时长范围，只处理符合条件的视频")
        duration_help_label.setStyleSheet("color: #888; font-size: 11px; font-style: italic; margin-top: 2px;")
        process_layout.addWidget(duration_help_label, 7, 0, 1, 3)

        # 水印配置按钮
        self.watermark_config_button = QPushButton("水印配置")
        self.watermark_config_button.setEnabled(False)
        self.watermark_config_button.clicked.connect(self.open_watermark_config)
        self.watermark_config_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px 15px;
                font-size: 12px;
            }
            QPushButton:enabled {
                background-color: #e3f2fd;
                border-color: #2196f3;
                color: #1976d2;
            }
            QPushButton:enabled:hover {
                background-color: #bbdefb;
            }
        """)
        process_layout.addWidget(self.watermark_config_button, 3, 3, 1, 1)

        # 新手建议提示
        newbie_tip_label = QLabel("💡 新号建议：选择60秒以内的短视频进行养号，有助于提高账号活跃度和推荐权重")
        newbie_tip_label.setStyleSheet("""
            QLabel {
                background-color: #e3f2fd;
                color: #1976d2;
                font-size: 12px;
                padding: 8px 12px;
                border-radius: 4px;
                border-left: 3px solid #2196f3;
                margin-top: 8px;
            }
        """)
        newbie_tip_label.setWordWrap(True)
        process_layout.addWidget(newbie_tip_label, 7, 0, 1, 3)

        # AI文件名改写设置
        ai_rewrite_label = QLabel("AI文件名改写:")
        ai_rewrite_label.setStyleSheet("font-weight: 500; color: #333; font-size: 13px; margin-top: 15px;")
        process_layout.addWidget(ai_rewrite_label, 8, 0, 1, 3)

        # AI改写启用复选框
        self.ai_rewrite_checkbox = QCheckBox("启用AI智能改写文件名")
        self.ai_rewrite_checkbox.setChecked(False)
        self.ai_rewrite_checkbox.setStyleSheet("margin-top: 5px;")
        self.ai_rewrite_checkbox.stateChanged.connect(self.on_ai_rewrite_changed)
        process_layout.addWidget(self.ai_rewrite_checkbox, 9, 0, 1, 3)

        # 智能体配置
        agent_config_layout = QGridLayout()

        # 智能体ID输入
        agent_id_label = QLabel("智能体ID:")
        agent_id_label.setStyleSheet("color: #666; font-size: 12px;")
        agent_config_layout.addWidget(agent_id_label, 0, 0)

        self.agent_id_edit = QLineEdit()
        self.agent_id_edit.setPlaceholderText("请输入腾讯元器智能体ID")
        self.agent_id_edit.setFixedHeight(28)
        self.agent_id_edit.textChanged.connect(self.auto_save_settings)
        agent_config_layout.addWidget(self.agent_id_edit, 0, 1)

        # TOKEN输入
        token_label = QLabel("TOKEN:")
        token_label.setStyleSheet("color: #666; font-size: 12px;")
        agent_config_layout.addWidget(token_label, 1, 0)

        self.token_edit = QLineEdit()
        self.token_edit.setPlaceholderText("请输入API TOKEN")
        self.token_edit.setEchoMode(QLineEdit.Password)
        self.token_edit.setFixedHeight(28)
        self.token_edit.textChanged.connect(self.auto_save_settings)
        agent_config_layout.addWidget(self.token_edit, 1, 1)

        process_layout.addLayout(agent_config_layout, 10, 0, 1, 3)

        # AI服务配置按钮
        ai_config_layout = QHBoxLayout()

        self.ai_service_label = QLabel("服务: 未配置")
        self.ai_service_label.setStyleSheet("color: #666; font-size: 12px;")
        ai_config_layout.addWidget(self.ai_service_label)

        self.ai_config_btn = QPushButton("高级配置")
        self.ai_config_btn.setFixedWidth(80)
        self.ai_config_btn.setFixedHeight(28)
        self.ai_config_btn.clicked.connect(self.open_ai_config)
        ai_config_layout.addWidget(self.ai_config_btn)

        self.ai_tutorial_btn = QPushButton("📖 AI配置教程")
        self.ai_tutorial_btn.setFixedWidth(100)
        self.ai_tutorial_btn.setFixedHeight(28)
        self.ai_tutorial_btn.clicked.connect(self.open_ai_tutorial)
        self.ai_tutorial_btn.setToolTip("打开AI配置详细教程，了解如何配置腾讯元器智能体")
        ai_config_layout.addWidget(self.ai_tutorial_btn)

        self.ai_test_btn = QPushButton("测试连接")
        self.ai_test_btn.setFixedWidth(80)
        self.ai_test_btn.setFixedHeight(28)
        self.ai_test_btn.clicked.connect(self.test_ai_service)
        self.ai_test_btn.setEnabled(False)
        ai_config_layout.addWidget(self.ai_test_btn)

        ai_config_layout.addStretch()

        process_layout.addLayout(ai_config_layout, 11, 0, 1, 3)

        # AI改写帮助提示
        ai_help_label = QLabel("使用腾讯元器智能体改写视频文件名，让文件名更具描述性和吸引力")
        ai_help_label.setStyleSheet("color: #888; font-size: 11px; font-style: italic; margin-top: 2px;")
        process_layout.addWidget(ai_help_label, 12, 0, 1, 3)

        # 封面设置组
        cover_settings_group = QGroupBox("封面设置")
        cover_settings_group.setStyleSheet("QGroupBox { font-weight: bold; margin-top: 15px; }")
        cover_settings_layout = QVBoxLayout(cover_settings_group)
        cover_settings_layout.setSpacing(8)
        cover_settings_layout.setContentsMargins(15, 20, 15, 15)

        # 封面设置说明
        cover_settings_info = QLabel("配置封面生成和水印设置")
        cover_settings_info.setStyleSheet("color: #666; font-size: 13px;")
        cover_settings_layout.addWidget(cover_settings_info)

        # 封面水印功能已删除

        # 封面设置按钮 - 水印功能已删除
        self.cover_settings_button = QPushButton("⚙️ 封面设置")
        self.cover_settings_button.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: 1px solid #007acc;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: 500;
                min-height: 36px;
            }
            QPushButton:hover {
                background-color: #005a9e;
                border-color: #005a9e;
            }
            QPushButton:pressed {
                background-color: #004578;
            }
        """)
        self.cover_settings_button.clicked.connect(self.open_cover_settings)
        cover_settings_layout.addWidget(self.cover_settings_button)

        # 添加封面设置组到处理布局
        process_layout.addWidget(cover_settings_group, 13, 0, 1, 3)

        # 保存设置复选框
        self.save_settings_checkbox = QCheckBox("保存设置（下次启动时自动加载）")
        self.save_settings_checkbox.setChecked(True)
        self.save_settings_checkbox.setStyleSheet("color: #007acc; font-weight: 500; margin-top: 10px;")
        process_layout.addWidget(self.save_settings_checkbox, 14, 0, 1, 3)

        layout.addWidget(process_group)

        # 统计信息组
        stats_group = QGroupBox("统计信息")
        stats_layout = QGridLayout(stats_group)
        stats_layout.setSpacing(8)
        stats_layout.setContentsMargins(15, 20, 15, 15)

        self.total_files_label = QLabel("总文件数: 0")
        self.total_files_label.setStyleSheet("color: #333; font-size: 13px;")

        self.processed_files_label = QLabel("已处理: 0")
        self.processed_files_label.setStyleSheet("color: #333; font-size: 13px;")

        self.success_files_label = QLabel("成功: 0")
        self.success_files_label.setStyleSheet("color: #28a745; font-size: 13px; font-weight: 500;")

        self.failed_files_label = QLabel("失败: 0")
        self.failed_files_label.setStyleSheet("color: #dc3545; font-size: 13px; font-weight: 500;")

        self.skipped_portrait_label = QLabel("跳过竖屏: 0")
        self.skipped_portrait_label.setStyleSheet("color: #ffc107; font-size: 13px; font-weight: 500;")

        self.skipped_duration_label = QLabel("跳过时长: 0")
        self.skipped_duration_label.setStyleSheet("color: #17a2b8; font-size: 13px; font-weight: 500;")

        stats_layout.addWidget(self.total_files_label, 0, 0)
        stats_layout.addWidget(self.processed_files_label, 0, 1)
        stats_layout.addWidget(self.success_files_label, 1, 0)
        stats_layout.addWidget(self.failed_files_label, 1, 1)
        stats_layout.addWidget(self.skipped_portrait_label, 2, 0)
        stats_layout.addWidget(self.skipped_duration_label, 2, 1)

        layout.addWidget(stats_group)

        layout.addStretch()

    def init_log_tab(self):
        """初始化日志标签页"""
        layout = QVBoxLayout(self.log_tab)
        layout.setContentsMargins(10, 10, 10, 10)

        # 创建日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setLineWrapMode(QTextEdit.WidgetWidth)
        self.log_text.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 12px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
            }
        """)
        layout.addWidget(self.log_text)

        # 创建日志控制按钮
        log_control_layout = QHBoxLayout()

        clear_log_btn = QPushButton("清空日志")
        clear_log_btn.clicked.connect(self.clear_log)
        log_control_layout.addWidget(clear_log_btn)

        save_log_btn = QPushButton("保存日志")
        save_log_btn.clicked.connect(self.save_log)
        log_control_layout.addWidget(save_log_btn)

        log_control_layout.addStretch()

        # 自动滚动复选框
        self.auto_scroll_checkbox = QCheckBox("自动滚动")
        self.auto_scroll_checkbox.setChecked(True)
        log_control_layout.addWidget(self.auto_scroll_checkbox)

        layout.addLayout(log_control_layout)

    def open_cover_settings(self):
        """封面设置功能已删除"""
        QMessageBox.information(self, "提示", "封面水印功能已删除")

    # 水印设置保存和加载方法已删除


    def init_control_area(self, main_layout):
        """初始化底部控制区域（固定在底部，不滚动）"""
        # 创建底部控制容器
        control_widget = QWidget()
        control_widget.setFixedHeight(120)
        control_widget.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
            }
        """)

        control_layout = QVBoxLayout(control_widget)
        control_layout.setContentsMargins(20, 10, 20, 10)
        control_layout.setSpacing(8)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ddd;
                border-radius: 4px;
                text-align: center;
                font-size: 12px;
                background-color: white;
            }
            QProgressBar::chunk {
                background-color: #007acc;
                border-radius: 3px;
            }
        """)
        control_layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("🟢 就绪")
        self.status_label.setStyleSheet("color: #666; font-size: 12px; font-weight: 500;")
        control_layout.addWidget(self.status_label)

        # 按钮布局
        button_layout = QHBoxLayout()

        # 从设置加载路径按钮
        load_paths_btn = QPushButton("从设置加载路径")
        load_paths_btn.clicked.connect(self.load_paths_from_settings)
        button_layout.addWidget(load_paths_btn)

        button_layout.addStretch()

        # 开始处理按钮
        self.start_btn = QPushButton("开始处理")
        self.start_btn.setMinimumHeight(36)
        self.start_btn.setFixedWidth(100)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: 500;
                font-size: 13px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #ccc;
                color: #666;
            }
        """)
        self.start_btn.clicked.connect(self.start_processing)
        button_layout.addWidget(self.start_btn)

        # 停止处理按钮
        self.stop_btn = QPushButton("停止处理")
        self.stop_btn.setMinimumHeight(36)
        self.stop_btn.setFixedWidth(100)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: 500;
                font-size: 13px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
        """)
        self.stop_btn.clicked.connect(self.stop_processing)
        self.stop_btn.setVisible(False)
        button_layout.addWidget(self.stop_btn)

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.setMinimumHeight(36)
        close_btn.setFixedWidth(80)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: white;
                color: #333;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 13px;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #f8f8f8;
                border-color: #007acc;
            }
        """)
        close_btn.clicked.connect(self.close_dialog)
        button_layout.addWidget(close_btn)

        # 将按钮布局添加到控制布局
        control_layout.addLayout(button_layout)

        # 将控制容器添加到主布局
        main_layout.addWidget(control_widget)

    def connect_signals(self):
        """连接信号槽"""
        pass  # 信号连接在工作线程中处理

    def get_settings_file_path(self) -> str:
        """获取设置文件路径 - 保存在软件目录中"""
        try:
            import sys

            # 确定软件目录
            if getattr(sys, 'frozen', False):
                # 打包后的环境 - 保存在应用程序目录
                app_dir = os.path.dirname(sys.executable)
            else:
                # 开发环境 - 保存在项目根目录
                app_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))

            # 确保目录存在
            os.makedirs(app_dir, exist_ok=True)

            return os.path.join(app_dir, "video_processor_settings.json")
        except Exception:
            # 如果失败，使用当前目录
            return "video_processor_settings.json"

    def save_current_settings(self):
        """保存当前设置到文件"""
        if not self.save_settings_checkbox.isChecked():
            return

        try:
            settings = {
                'input_dir': self.input_dir_edit.text().strip(),
                'output_dir': self.output_dir_edit.text().strip(),
                'cover_dir': self.cover_dir_edit.text().strip(),
                'thread_count': self.thread_count_combo.currentText(),
                'overwrite_files': self.overwrite_checkbox.isChecked(),
                'generate_cover': self.generate_cover_checkbox.isChecked(),
                'watermark_enabled': self.watermark_checkbox.isChecked(),
                'watermark_config': self.watermark_config.to_dict(),
                'duration_filter_enabled': self.duration_filter_checkbox.isChecked(),
                'min_duration': self.min_duration_spinbox.value(),
                'max_duration': self.max_duration_spinbox.value(),
                'ai_rewrite_enabled': self.ai_rewrite_checkbox.isChecked(),
                'agent_id': self.agent_id_edit.text().strip(),
                'token': self.token_edit.text().strip(),
                'save_settings': self.save_settings_checkbox.isChecked(),
                'auto_scroll': self.auto_scroll_checkbox.isChecked(),
                'last_saved': time.strftime("%Y-%m-%d %H:%M:%S")
            }



            settings_file = self.get_settings_file_path()
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            debug(f"设置已保存到: {settings_file}")

        except Exception as e:
            warning(f"保存设置失败: {str(e)}")

    def load_saved_settings(self):
        """从文件加载保存的设置"""
        try:
            settings_file = self.get_settings_file_path()
            if not os.path.exists(settings_file):
                debug("设置文件不存在，使用默认设置")
                return

            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

            # 应用设置
            if 'input_dir' in settings and settings['input_dir']:
                self.input_dir_edit.setText(settings['input_dir'])

            if 'output_dir' in settings and settings['output_dir']:
                self.output_dir_edit.setText(settings['output_dir'])

            if 'cover_dir' in settings and settings['cover_dir']:
                self.cover_dir_edit.setText(settings['cover_dir'])

            if 'thread_count' in settings:
                thread_count = str(settings['thread_count'])
                index = self.thread_count_combo.findText(thread_count)
                if index >= 0:
                    self.thread_count_combo.setCurrentIndex(index)

            if 'overwrite_files' in settings:
                self.overwrite_checkbox.setChecked(settings['overwrite_files'])

            if 'generate_cover' in settings:
                self.generate_cover_checkbox.setChecked(settings['generate_cover'])

            # 加载水印配置
            if 'watermark_enabled' in settings:
                self.watermark_checkbox.setChecked(settings['watermark_enabled'])

            if 'watermark_config' in settings:
                self.watermark_config.from_dict(settings['watermark_config'])

            if 'duration_filter_enabled' in settings:
                self.duration_filter_checkbox.setChecked(settings['duration_filter_enabled'])

            if 'min_duration' in settings:
                self.min_duration_spinbox.setValue(settings['min_duration'])

            if 'max_duration' in settings:
                self.max_duration_spinbox.setValue(settings['max_duration'])

            if 'save_settings' in settings:
                self.save_settings_checkbox.setChecked(settings['save_settings'])

            if 'auto_scroll' in settings:
                self.auto_scroll_checkbox.setChecked(settings['auto_scroll'])

            if 'ai_rewrite_enabled' in settings:
                self.ai_rewrite_checkbox.setChecked(settings['ai_rewrite_enabled'])

            if 'agent_id' in settings:
                self.agent_id_edit.setText(settings['agent_id'])

            if 'token' in settings:
                self.token_edit.setText(settings['token'])

            # 水印设置已删除

            info(f"已从文件加载设置: {settings_file}")
            if 'last_saved' in settings:
                self.add_log(f"已加载保存的设置（上次保存: {settings['last_saved']}）", "SUCCESS")

        except Exception as e:
            warning(f"加载设置失败: {str(e)}")

    def on_duration_filter_changed(self, state):
        """时长筛选复选框状态改变"""
        enabled = state == 2  # 选中状态
        self.min_duration_spinbox.setEnabled(enabled)
        self.max_duration_spinbox.setEnabled(enabled)

        if enabled:
            self.add_log("已启用视频时长筛选功能", "INFO")
        else:
            self.add_log("已禁用视频时长筛选功能", "INFO")

        # 自动保存设置
        self.auto_save_settings()

    def on_watermark_toggled(self, checked):
        """水印开关状态改变"""
        self.watermark_config.enabled = checked
        self.watermark_config_button.setEnabled(checked)

        if checked:
            self.add_log("已启用封面水印功能", "INFO")
        else:
            self.add_log("已禁用封面水印功能", "INFO")

        self.auto_save_settings()

    def open_watermark_config(self):
        """打开水印配置对话框"""
        dialog = WatermarkConfigDialog(self.watermark_config, self)
        result = dialog.exec_()

        # 无论用户点击确定还是取消，都强制保存当前配置
        # 这样确保预览中看到的效果与最终生成的封面一致
        updated_config = dialog.get_config()
        self.watermark_config = updated_config

        if result == QDialog.Accepted:
            self.add_log("水印配置已保存", "SUCCESS")
        else:
            self.add_log("水印配置已同步（预览效果已保存）", "INFO")

        print(f"水印配置同步完成: 字体大小={self.watermark_config.font_size}px")
        self.auto_save_settings()

    def on_save_settings_changed(self, state):
        """保存设置复选框状态改变"""
        if state == 2:  # 选中状态
            self.add_log("已启用设置保存功能", "INFO")
            # 立即保存当前设置
            self.save_current_settings()
        else:
            self.add_log("已禁用设置保存功能", "INFO")

    def auto_save_settings(self):
        """自动保存设置（延迟保存，避免频繁写入）"""
        if hasattr(self, '_save_timer'):
            self._save_timer.stop()

        self._save_timer = QTimer()
        self._save_timer.setSingleShot(True)
        self._save_timer.timeout.connect(self.save_current_settings)
        self._save_timer.start(1000)  # 1秒后保存

    def load_settings(self):
        """加载设置"""
        try:
            # 首先尝试加载保存的设置
            self.load_saved_settings()

            # 如果没有保存的设置，尝试从主窗口获取设置路径
            if not self.input_dir_edit.text().strip():
                if self.parent():
                    main_window = self.parent()
                    while main_window and not hasattr(main_window, 'setting_tab'):
                        main_window = main_window.parent()

                    if main_window and hasattr(main_window, 'setting_tab'):
                        setting_tab = getattr(main_window, 'setting_tab', None)
                        if setting_tab is not None:
                            # 设置默认路径
                            video_path = setting_tab.video_path.text()
                            cover_path = setting_tab.cover_path.text()

                            if video_path and not self.output_dir_edit.text().strip():
                                self.output_dir_edit.setText(video_path)
                            if cover_path and not self.cover_dir_edit.text().strip():
                                self.cover_dir_edit.setText(cover_path)

                            info("已从主窗口设置加载默认路径")

            # 同步AI配置
            if self.ai_rewriter:
                self.sync_config_from_ai_rewriter()

        except Exception as e:
            warning(f"加载设置时出错: {str(e)}")

    def load_paths_from_settings(self):
        """从设置重新加载路径"""
        self.load_settings()
        self.add_log("已从设置重新加载路径", "INFO")

    def browse_input_dir(self):
        """浏览选择输入目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择输入目录", self.input_dir_edit.text()
        )
        if dir_path:
            self.input_dir_edit.setText(dir_path)
            self.add_log(f"已选择输入目录: {dir_path}", "INFO")

    def browse_output_dir(self):
        """浏览选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择视频输出目录", self.output_dir_edit.text()
        )
        if dir_path:
            self.output_dir_edit.setText(dir_path)
            self.add_log(f"已选择视频输出目录: {dir_path}", "INFO")

    def browse_cover_dir(self):
        """浏览选择封面目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择封面输出目录", self.cover_dir_edit.text()
        )
        if dir_path:
            self.cover_dir_edit.setText(dir_path)
            self.add_log(f"已选择封面输出目录: {dir_path}", "INFO")

    def add_log(self, message: str, level: str = "INFO"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")

        # 根据级别设置颜色
        color_map = {
            "INFO": "#333333",
            "SUCCESS": "#28a745",
            "WARNING": "#ffc107",
            "ERROR": "#dc3545",
            "DEBUG": "#6c757d"
        }

        color = color_map.get(level, "#333333")
        formatted_message = f'<span style="color: {color};">[{timestamp}] [{level}] {message}</span>'

        self.log_text.append(formatted_message)

        # 自动滚动到底部
        if self.auto_scroll_checkbox.isChecked():
            scrollbar = self.log_text.verticalScrollBar()
            if scrollbar:
                scrollbar.setValue(scrollbar.maximum())

        # 处理界面事件
        QApplication.processEvents()

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.add_log("日志已清空", "INFO")

    def save_log(self):
        """保存日志到文件"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存日志", f"video_processor_log_{time.strftime('%Y%m%d_%H%M%S')}.txt",
                "文本文件 (*.txt);;所有文件 (*.*)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    # 移除HTML标签，只保存纯文本
                    import re
                    plain_text = re.sub(r'<[^>]+>', '', self.log_text.toPlainText())
                    f.write(plain_text)

                self.add_log(f"日志已保存到: {file_path}", "SUCCESS")
                QMessageBox.information(self, "保存成功", f"日志已保存到:\n{file_path}")
        except Exception as e:
            self.add_log(f"保存日志失败: {str(e)}", "ERROR")
            QMessageBox.critical(self, "保存失败", f"保存日志时出错:\n{str(e)}")

    def validate_settings(self) -> bool:
        """验证设置"""
        # 检查输入目录
        input_dir = self.input_dir_edit.text().strip()
        if not input_dir:
            QMessageBox.warning(self, "设置错误", "请选择输入目录")
            return False

        if not os.path.exists(input_dir):
            QMessageBox.warning(self, "设置错误", "输入目录不存在")
            return False

        # 检查输出目录
        output_dir = self.output_dir_edit.text().strip()
        if not output_dir:
            QMessageBox.warning(self, "设置错误", "请选择视频输出目录")
            return False

        # 检查封面目录（如果启用封面生成）
        if self.generate_cover_checkbox.isChecked():
            cover_dir = self.cover_dir_edit.text().strip()
            if not cover_dir:
                QMessageBox.warning(self, "设置错误", "请选择封面输出目录")
                return False

        # 检查依赖库
        if not CV2_AVAILABLE:
            reply = QMessageBox.question(
                self, "依赖库缺失",
                "OpenCV库未安装，无法检测视频方向和生成封面。\n\n"
                "是否继续处理？（将处理所有视频文件，但无法生成封面）",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply != QMessageBox.Yes:
                return False

        return True

    def scan_video_files(self, input_dir: str) -> List[str]:
        """扫描输入目录中的视频文件"""
        video_files = []
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}

        try:
            for root, _, files in os.walk(input_dir):
                for file in files:
                    if os.path.splitext(file.lower())[1] in video_extensions:
                        video_files.append(os.path.join(root, file))

            self.add_log(f"扫描完成，找到 {len(video_files)} 个视频文件", "SUCCESS")
            return video_files
        except Exception as e:
            self.add_log(f"扫描视频文件时出错: {str(e)}", "ERROR")
            return []

    def clear_output_directories(self, output_dir: str, cover_dir: str):
        """清空输出目录中的视频和封面文件"""
        try:
            # 支持的视频格式
            video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm', '.m4v'}
            # 支持的图片格式
            image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif', '.tiff'}

            # 清空视频输出目录
            video_count = 0
            if os.path.exists(output_dir):
                self.add_log(f"清空视频输出目录: {output_dir}", "INFO")
                for filename in os.listdir(output_dir):
                    file_path = os.path.join(output_dir, filename)
                    if os.path.isfile(file_path):
                        file_ext = os.path.splitext(filename)[1].lower()
                        if file_ext in video_extensions:
                            try:
                                os.remove(file_path)
                                video_count += 1
                                debug(f"删除视频文件: {filename}")
                            except Exception as e:
                                warning(f"删除视频文件失败: {filename}, 错误: {str(e)}")
                                self.add_log(f"删除视频文件失败: {filename}", "WARNING")

                if video_count > 0:
                    self.add_log(f"已清空 {video_count} 个视频文件", "SUCCESS")
                else:
                    self.add_log("视频输出目录中没有视频文件", "INFO")

            # 清空封面输出目录
            cover_count = 0
            if self.generate_cover_checkbox.isChecked() and os.path.exists(cover_dir):
                self.add_log(f"清空封面输出目录: {cover_dir}", "INFO")
                for filename in os.listdir(cover_dir):
                    file_path = os.path.join(cover_dir, filename)
                    if os.path.isfile(file_path):
                        file_ext = os.path.splitext(filename)[1].lower()
                        if file_ext in image_extensions:
                            try:
                                os.remove(file_path)
                                cover_count += 1
                                debug(f"删除封面文件: {filename}")
                            except Exception as e:
                                warning(f"删除封面文件失败: {filename}, 错误: {str(e)}")
                                self.add_log(f"删除封面文件失败: {filename}", "WARNING")

                if cover_count > 0:
                    self.add_log(f"已清空 {cover_count} 个封面文件", "SUCCESS")
                else:
                    self.add_log("封面输出目录中没有封面文件", "INFO")

            # 总结清空结果
            total_cleared = video_count + cover_count
            if total_cleared > 0:
                self.add_log(f"清空完成: 共删除 {total_cleared} 个文件（视频: {video_count}, 封面: {cover_count}）", "SUCCESS")
            else:
                self.add_log("输出目录已经是空的，无需清空", "INFO")

        except Exception as e:
            error_msg = f"清空输出目录时出错: {str(e)}"
            warning(error_msg)
            self.add_log(error_msg, "ERROR")
            raise

    def start_processing(self):
        """开始处理视频"""
        if self.is_processing:
            return

        try:
            self.add_log("开始验证设置...", "INFO")

            # 验证设置
            if not self.validate_settings():
                self.add_log("设置验证失败", "ERROR")
                return

            self.add_log("设置验证通过", "SUCCESS")

        except Exception as e:
            self.add_log(f"验证设置时出错: {str(e)}", "ERROR")
            return

        try:
            # 获取设置
            input_dir = self.input_dir_edit.text().strip()
            output_dir = self.output_dir_edit.text().strip()
            cover_dir = self.cover_dir_edit.text().strip()
            requested_threads = int(self.thread_count_combo.currentText())

            # 智能线程数管理 - 确保稳定性
            try:
                import psutil
                cpu_count = psutil.cpu_count() or 4
                memory_gb = psutil.virtual_memory().total / (1024**3)
            except:
                cpu_count = 4
                memory_gb = 8  # 默认8GB

            # 根据系统配置智能调整线程数
            if memory_gb < 8:
                max_safe_threads = min(requested_threads, 4)
            elif memory_gb < 16:
                max_safe_threads = min(requested_threads, 8)
            else:
                max_safe_threads = min(requested_threads, 12)

            # 最终限制：不超过CPU核心数的1.5倍，且不超过12个
            final_threads = min(max_safe_threads, int(cpu_count * 1.5), 12)

            if final_threads != requested_threads:
                self.add_log(f"线程数已从 {requested_threads} 优化为 {final_threads}（系统自适应）", "WARNING")
                self.add_log(f"系统配置: CPU {cpu_count}核, 内存 {memory_gb:.1f}GB", "INFO")

            self.add_log(f"输入目录: {input_dir}", "INFO")
            self.add_log(f"输出目录: {output_dir}", "INFO")
            self.add_log(f"封面目录: {cover_dir}", "INFO")
            self.add_log(f"优化后线程数: {final_threads}", "SUCCESS")

            thread_count = final_threads

            # 创建输出目录
            self.add_log("创建输出目录...", "INFO")
            try:
                os.makedirs(output_dir, exist_ok=True)
                self.add_log(f"视频输出目录已创建: {output_dir}", "SUCCESS")

                if self.generate_cover_checkbox.isChecked():
                    os.makedirs(cover_dir, exist_ok=True)
                    self.add_log(f"封面输出目录已创建: {cover_dir}", "SUCCESS")
            except Exception as e:
                self.add_log(f"创建输出目录失败: {str(e)}", "ERROR")
                return

            # 清空输出目录
            self.add_log("清空输出目录...", "INFO")
            try:
                self.clear_output_directories(output_dir, cover_dir)
            except Exception as e:
                self.add_log(f"清空输出目录失败: {str(e)}", "ERROR")
                return

            # 扫描视频文件
            self.add_log("开始扫描视频文件...", "INFO")
            try:
                video_files = self.scan_video_files(input_dir)
            except Exception as e:
                self.add_log(f"扫描视频文件时出错: {str(e)}", "ERROR")
                return

            if not video_files:
                self.add_log("在输入目录中没有找到任何视频文件", "WARNING")
                QMessageBox.warning(self, "没有找到视频", "在输入目录中没有找到任何视频文件")
                return

            # 初始化共享数据
            self.add_log("初始化共享数据...", "INFO")
            try:
                self.shared_data = {
                    'video_queue': video_files.copy(),
                    'total_count': len(video_files),
                    'processed_count': 0,
                    'success_count': 0,
                    'failed_count': 0,
                    'skipped_portrait_count': 0,
                    'skipped_duration_count': 0,
                    'lock': threading.Lock()
                }
                self.add_log("共享数据初始化成功", "SUCCESS")
            except Exception as e:
                self.add_log(f"初始化共享数据失败: {str(e)}", "ERROR")
                return

            # 更新UI状态
            self.is_processing = True
            self.start_btn.setVisible(False)
            self.stop_btn.setVisible(True)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_label.setText("正在处理...")

            # 更新统计信息
            self.update_stats()

            # 切换到日志标签页
            self.tab_widget.setCurrentIndex(1)

            # 准备时长筛选设置
            duration_settings = {
                'enabled': self.duration_filter_checkbox.isChecked(),
                'min_duration': self.min_duration_spinbox.value(),
                'max_duration': self.max_duration_spinbox.value()
            }

            # 记录时长筛选设置
            if duration_settings['enabled']:
                min_dur = duration_settings['min_duration']
                max_dur = duration_settings['max_duration']
                max_text = f"{max_dur}分钟" if max_dur > 0 else "无限制"
                self.add_log(f"时长筛选已启用: {min_dur}-{max_text}", "INFO")
            else:
                self.add_log("时长筛选已禁用", "INFO")

            # 第一阶段：预筛选视频文件
            self.add_log("开始预筛选视频文件...", "INFO")
            try:
                filtered_video_files = self.pre_filter_videos(video_files, duration_settings)
                skipped_count = len(video_files) - len(filtered_video_files)

                self.add_log(f"预筛选完成: 符合条件 {len(filtered_video_files)} 个，跳过 {skipped_count} 个", "SUCCESS")

                if not filtered_video_files:
                    self.add_log("没有视频文件通过筛选条件", "WARNING")
                    QMessageBox.warning(self, "没有符合条件的视频", "没有视频文件通过筛选条件，请检查筛选设置")
                    return

            except Exception as e:
                self.add_log(f"预筛选视频文件失败: {str(e)}", "ERROR")
                return

            # 第二阶段：批量AI改写文件名（仅针对筛选后的文件）
            ai_rewriter = None

            if self.ai_rewrite_checkbox.isChecked():
                if self.ai_rewriter is not None:
                    ai_rewriter = self.ai_rewriter
                    self.add_log(f"AI文件名改写已启用，开始批量改写 {len(filtered_video_files)} 个筛选后的文件名...", "INFO")

                    # 使用后台线程进行批量AI改写
                    self.start_batch_rewrite(filtered_video_files, thread_count)
                    return  # 等待批量改写完成后再继续
                else:
                    self.add_log("AI改写器未初始化，将跳过AI改写功能", "WARNING")

            # 如果不需要AI改写，直接开始视频处理
            self.start_video_processing(filtered_video_files, {}, thread_count)

            # 设置线程池最大线程数
            self.thread_pool.setMaxThreadCount(thread_count)

            # 创建并启动工作线程
            self.add_log("创建工作线程...", "INFO")
            self.worker_threads = []

            try:
                # 优化的分批创建机制
                batch_size = 2  # 减少每批线程数，提高稳定性
                created_count = 0

                self.add_log(f"开始分批创建 {thread_count} 个工作线程...", "INFO")

                for batch_start in range(0, thread_count, batch_size):
                    batch_end = min(batch_start + batch_size, thread_count)

                    self.add_log(f"创建第 {batch_start//batch_size + 1} 批线程...", "INFO")

                    for i in range(batch_start, batch_end):
                        try:
                            worker = VideoProcessorWorker(
                                input_dir, output_dir, cover_dir,
                                thread_count, i, self.shared_data, duration_settings, ai_rewriter,
                                self.watermark_config
                            )

                            # 安全连接信号
                            try:
                                worker.signals.progress_updated.connect(self.on_progress_updated)
                                worker.signals.log_message.connect(self.add_log)
                                worker.signals.task_completed.connect(self.on_task_completed)
                                worker.signals.error_occurred.connect(self.on_error_occurred)
                            except Exception as signal_error:
                                self.add_log(f"连接信号时出错: {str(signal_error)}", "WARNING")

                            self.worker_threads.append(worker)
                            self.thread_pool.start(worker)
                            created_count += 1

                            self.add_log(f"线程 {i+1}/{thread_count} 创建成功", "SUCCESS")

                        except Exception as worker_error:
                            self.add_log(f"创建线程 {i+1} 失败: {str(worker_error)}", "ERROR")
                            continue

                        # 处理事件，保持UI响应
                        QApplication.processEvents()

                    # 批次间延迟，让系统稳定
                    if batch_end < thread_count:
                        import time
                        time.sleep(0.2)  # 增加延迟时间
                        QApplication.processEvents()

                self.add_log(f"线程创建完成: {created_count}/{thread_count} 个线程启动成功", "SUCCESS")

                if created_count == 0:
                    self.add_log("没有成功创建任何线程，无法继续处理", "ERROR")
                    self.reset_ui_state()
                    return

            except Exception as e:
                self.add_log(f"创建工作线程时出错: {str(e)}", "ERROR")
                import traceback
                self.add_log(f"详细错误: {traceback.format_exc()}", "ERROR")
                self.reset_ui_state()
                return

            # 启动监控定时器
            self.monitor_timer = QTimer()
            self.monitor_timer.timeout.connect(self.check_completion)
            self.monitor_timer.start(1000)  # 每秒检查一次

        except Exception as e:
            self.add_log(f"启动处理时出错: {str(e)}", "ERROR")
            QMessageBox.critical(self, "启动失败", f"启动处理时出错:\n{str(e)}")
            self.reset_ui_state()

    def stop_processing(self):
        """停止处理"""
        if not self.is_processing:
            return

        reply = QMessageBox.question(
            self, "确认停止",
            "确定要停止视频处理吗？\n\n已处理的文件不会被删除。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.add_log("用户请求停止处理", "WARNING")
            self.is_processing = False

            # 停止批量改写（如果正在进行）
            if hasattr(self, 'batch_rewrite_worker') and self.batch_rewrite_worker:
                self.batch_rewrite_worker.cancel()

            if hasattr(self, 'batch_progress_dialog') and self.batch_progress_dialog:
                self.batch_progress_dialog.close()
                self.batch_progress_dialog = None

            # 停止所有工作线程
            for worker in self.worker_threads:
                worker.is_running = False

            # 停止监控定时器
            if hasattr(self, 'monitor_timer'):
                self.monitor_timer.stop()

            # 等待线程池完成
            self.thread_pool.waitForDone(3000)  # 等待3秒

            self.reset_ui_state()
            self.add_log("处理已停止", "WARNING")

    def check_completion(self):
        """检查处理是否完成"""
        if not self.is_processing:
            return

        with self.shared_data['lock']:
            total = self.shared_data['total_count']
            processed = self.shared_data['processed_count']

        # 检查是否所有文件都已处理
        if processed >= total:
            self.on_processing_completed()

    def on_processing_completed(self):
        """处理完成"""
        if hasattr(self, 'monitor_timer'):
            self.monitor_timer.stop()

        self.is_processing = False
        self.reset_ui_state()

        # 显示完成统计
        with self.shared_data['lock']:
            stats = {
                'total': self.shared_data['total_count'],
                'processed': self.shared_data['processed_count'],
                'success': self.shared_data['success_count'],
                'failed': self.shared_data['failed_count'],
                'skipped_portrait': self.shared_data.get('skipped_portrait_count', 0),
                'skipped_duration': self.shared_data.get('skipped_duration_count', 0)
            }

        self.add_log("=" * 50, "INFO")
        self.add_log("处理完成！", "SUCCESS")
        self.add_log(f"总文件数: {stats['total']}", "INFO")
        self.add_log(f"已处理: {stats['processed']}", "INFO")
        self.add_log(f"成功复制: {stats['success']}", "SUCCESS")
        self.add_log(f"失败: {stats['failed']}", "ERROR" if stats['failed'] > 0 else "INFO")
        self.add_log(f"跳过竖屏视频: {stats['skipped_portrait']}", "WARNING" if stats['skipped_portrait'] > 0 else "INFO")
        self.add_log(f"跳过时长不符: {stats['skipped_duration']}", "WARNING" if stats['skipped_duration'] > 0 else "INFO")
        self.add_log("=" * 50, "INFO")

        # 显示完成对话框
        message_parts = [
            "视频处理已完成！",
            "",
            f"总文件数: {stats['total']}",
            f"成功处理: {stats['success']}",
            f"失败: {stats['failed']}"
        ]

        if stats['skipped_portrait'] > 0:
            message_parts.append(f"跳过竖屏: {stats['skipped_portrait']}")

        if stats['skipped_duration'] > 0:
            message_parts.append(f"跳过时长不符: {stats['skipped_duration']}")

        QMessageBox.information(
            self, "处理完成",
            "\n".join(message_parts)
        )

    def on_progress_updated(self, progress: int, message: str):
        """进度更新"""
        self.progress_bar.setValue(progress)
        self.status_label.setText(message)
        self.update_stats()

    def on_task_completed(self, stats: dict):
        """任务完成"""
        # 统计信息通过progress_updated更新
        # stats参数保留用于接口兼容性
        pass

    def on_error_occurred(self, error_msg: str):
        """错误发生"""
        self.add_log(error_msg, "ERROR")

    def update_stats(self):
        """更新统计信息"""
        with self.shared_data['lock']:
            total = self.shared_data['total_count']
            processed = self.shared_data['processed_count']
            success = self.shared_data['success_count']
            failed = self.shared_data['failed_count']
            skipped_portrait = self.shared_data.get('skipped_portrait_count', 0)
            skipped_duration = self.shared_data.get('skipped_duration_count', 0)

        self.total_files_label.setText(f"总文件数: {total}")
        self.processed_files_label.setText(f"已处理: {processed}")
        self.success_files_label.setText(f"成功: {success}")
        self.failed_files_label.setText(f"失败: {failed}")
        self.skipped_portrait_label.setText(f"跳过竖屏: {skipped_portrait}")
        self.skipped_duration_label.setText(f"跳过时长: {skipped_duration}")

    def reset_ui_state(self):
        """重置UI状态"""
        self.start_btn.setVisible(True)
        self.stop_btn.setVisible(False)
        self.progress_bar.setVisible(False)
        self.status_label.setText("就绪")
        self.is_processing = False

    def close_dialog(self):
        """关闭对话框"""
        if self.is_processing:
            reply = QMessageBox.question(
                self, "确认关闭",
                "视频处理正在进行中，确定要关闭吗？\n\n这将停止所有处理任务。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.stop_processing()
                # 关闭前保存设置
                self.save_current_settings()
                self.accept()
        else:
            # 关闭前保存设置
            self.save_current_settings()
            self.accept()

    def on_ai_rewrite_changed(self):
        """AI改写选项改变时的处理"""
        enabled = self.ai_rewrite_checkbox.isChecked()
        self.ai_test_btn.setEnabled(enabled and self.ai_rewriter is not None)

        if enabled and self.ai_rewriter is None:
            QMessageBox.warning(self, "警告", "AI文件名改写器未正确初始化，请检查相关模块是否正确安装。")
            self.ai_rewrite_checkbox.setChecked(False)

        # 更新AI配置
        if enabled and self.ai_rewriter is not None:
            self.update_ai_config_from_ui()

    def on_agent_config_changed(self):
        """智能体配置改变时的处理"""
        if self.ai_rewrite_checkbox.isChecked() and self.ai_rewriter is not None:
            self.update_ai_config_from_ui()



    def update_ai_service_status(self):
        """更新AI服务状态显示"""
        if self.ai_rewriter is None:
            self.ai_service_label.setText("服务: 不可用")
            self.ai_service_label.setStyleSheet("color: #dc3545; font-size: 12px;")
            if hasattr(self, 'add_log'):
                self.add_log("AI改写器未初始化", "WARNING")
            return

        try:
            status = self.ai_rewriter.get_service_status()

            # 检查是否有错误信息
            if "error" in status:
                error_msg = f"AI服务配置错误: {status['error']}"
                warning(error_msg)
                self.ai_service_label.setText("服务: 配置错误")
                self.ai_service_label.setStyleSheet("color: #dc3545; font-size: 12px;")
                if hasattr(self, 'add_log'):
                    self.add_log(error_msg, "ERROR")
                return

            service_name = status.get("service_name", "未知")
            has_api_key = status.get("has_api_key", False)

            # 构建状态文本
            if has_api_key:
                status_text = f"服务: {service_name}"
                color = "#28a745"  # 绿色表示正常
                self.ai_service_label.setText(status_text)
                self.ai_service_label.setStyleSheet(f"color: {color}; font-size: 12px;")
                if hasattr(self, 'add_log'):
                    self.add_log(f"AI服务状态正常: {service_name}", "INFO")
            else:
                self.ai_service_label.setText(f"服务: {service_name} (未配置)")
                self.ai_service_label.setStyleSheet("color: #ffc107; font-size: 12px;")
                if hasattr(self, 'add_log'):
                    self.add_log(f"AI服务未配置: {service_name}", "WARNING")

        except Exception as e:
            error_msg = f"获取AI服务状态时出错: {str(e)}"
            warning(error_msg)
            self.ai_service_label.setText("服务: 错误")
            self.ai_service_label.setStyleSheet("color: #dc3545; font-size: 12px;")
            # 在日志中记录详细错误信息
            if hasattr(self, 'add_log'):
                self.add_log(error_msg, "ERROR")
                # 添加更多调试信息
                import traceback
                self.add_log(f"错误堆栈: {traceback.format_exc()}", "DEBUG")

    def open_ai_config(self):
        """打开AI配置对话框"""
        try:
            from app.dialogs.ai_config_dialog import AIConfigDialog
            dialog = AIConfigDialog(self.ai_rewriter, self)
            result = dialog.exec_()

            # 无论用户是否点击保存，都同步配置（因为高级配置有自动保存）
            self.sync_config_from_ai_rewriter()

            if result == QDialog.Accepted:
                # 配置已更新，刷新状态
                self.update_ai_service_status()
                self.add_log("AI配置已更新", "SUCCESS")
        except ImportError:
            # 如果AI配置对话框不存在，显示简单的配置提示
            self.show_simple_ai_config()
        except Exception as e:
            self.add_log(f"打开AI配置对话框时出错: {str(e)}", "ERROR")

    def open_ai_tutorial(self):
        """打开AI配置教程"""
        try:
            # 使用内置浏览器打开AI配置教程
            tutorial_url = "https://x0a8ja29q2i.feishu.cn/docx/Vt5wdQ4PYodqDJxCYrHcJs1Un2e?from=from_copylink"

            # 导入内置浏览器对话框
            from app.dialogs.web_browser_dialog import WebBrowserDialog

            # 创建并显示内置浏览器
            self.ai_tutorial_browser = WebBrowserDialog(
                parent=self,
                url=tutorial_url,
                title="AI配置教程 - 内置浏览器",
                content_type="ai_tutorial"
            )
            self.ai_tutorial_browser.show()

            self.add_log("已在内置浏览器中打开AI配置教程", "INFO")
        except Exception as e:
            self.add_log(f"打开AI配置教程时出错: {str(e)}", "ERROR")

            # 如果内置浏览器失败，回退到外部浏览器
            try:
                import webbrowser
                tutorial_url = "https://x0a8ja29q2i.feishu.cn/docx/Vt5wdQ4PYodqDJxCYrHcJs1Un2e?from=from_copylink"
                webbrowser.open(tutorial_url)
                self.add_log("已使用外部浏览器打开AI配置教程", "INFO")
            except Exception as fallback_error:
                error_msg = f"内置浏览器打开失败: {str(e)}\n外部浏览器也打开失败: {str(fallback_error)}"
                self.add_log(error_msg, "ERROR")
                QMessageBox.warning(self, "打开失败", error_msg)

    def show_simple_ai_config(self):
        """显示简单的AI配置提示"""
        msg = QMessageBox(self)
        msg.setWindowTitle("AI服务配置")
        msg.setIcon(QMessageBox.Information)
        msg.setText("请配置AI服务以启用文件名改写功能")
        msg.setDetailedText("""
配置文件位置: ai_rewriter_config.json

支持的AI服务:
1. 腾讯混元 (hunyuan)
   - URL: https://open.hunyuan.tencent.com/openapi/v1/agent/chat/completions
   - 需要API密钥

2. OpenAI (openai)
   - URL: https://api.openai.com/v1/chat/completions
   - 需要API密钥

3. 本地模型 (local)
   - URL: http://localhost:11434/api/chat
   - 无需API密钥，需要本地运行Ollama等服务

请手动编辑配置文件或联系开发者获取详细配置说明。
        """)
        msg.exec_()

    def test_ai_service(self):
        """测试AI服务连接"""
        if self.ai_rewriter is None:
            QMessageBox.warning(self, "错误", "AI改写器未初始化")
            return

        # 不显示进度对话框，改为在主界面日志中显示状态
        self.add_log("正在测试AI服务连接...", "INFO")

        try:
            # 在后台线程中测试
            import threading
            result = {"success": False, "message": ""}

            def test_thread():
                try:
                    if self.ai_rewriter and hasattr(self.ai_rewriter, 'test_ai_service'):
                        result["success"], result["message"] = self.ai_rewriter.test_ai_service()
                    else:
                        result["success"] = False
                        result["message"] = "AI改写器未初始化或不支持测试功能"
                except Exception as e:
                    result["success"] = False
                    result["message"] = f"测试异常: {str(e)}"

            thread = threading.Thread(target=test_thread)
            thread.daemon = True
            thread.start()

            # 等待测试完成（最多10秒）
            for _ in range(100):
                if not thread.is_alive():
                    break
                QApplication.processEvents()
                time.sleep(0.1)

            if result["success"]:
                QMessageBox.information(self, "测试成功", result["message"])
            else:
                QMessageBox.warning(self, "测试失败", result["message"])

        except Exception as e:
            self.add_log(f"AI服务测试错误: {str(e)}", "ERROR")
            QMessageBox.critical(self, "测试错误", f"测试过程中发生错误: {str(e)}")

    def update_ai_config_from_ui(self):
        """从UI更新AI配置"""
        if self.ai_rewriter is None:
            return

        try:
            # 获取UI中的配置
            agent_id = self.agent_id_edit.text().strip()
            token = self.token_edit.text().strip()

            # 如果UI中的配置为空，不进行更新
            if not agent_id and not token:
                debug("UI中的智能体配置为空，跳过更新")
                return

            # 更新AI改写器配置
            config = self.ai_rewriter.config

            # 设置默认使用元器智能体
            config["ai_service"] = "yuanqi"

            # 更新智能体配置（如果有的话）
            agents_config = config.get("agents", [])
            if agent_id and token and agents_config:
                # 更新第一个智能体配置
                agents_config[0]["assistant_id"] = agent_id
                agents_config[0]["api_key"] = token
                debug(f"更新智能体配置: agent_id={agent_id[:10]}...")

            # 同时更新传统服务配置（保持兼容性）
            if "services" not in config:
                config["services"] = {}

            if "yuanqi" not in config["services"]:
                config["services"]["yuanqi"] = {
                    "name": "腾讯元器智能体",
                    "url": "https://yuanqi.tencent.com/openapi/v1/agent/chat/completions",
                    "api_key": "",
                    "assistant_id": "",
                    "user_id": "user_001",
                    "timeout": 30,
                    "max_retries": 3
                }

            # 更新传统服务配置值
            config["services"]["yuanqi"]["api_key"] = token
            config["services"]["yuanqi"]["assistant_id"] = agent_id

            # 保存配置
            self.ai_rewriter.save_config()

            # 重新加载并发配置
            self.ai_rewriter.load_concurrent_configs()

            # 更新状态显示
            self.update_ai_service_status()

        except Exception as e:
            warning(f"更新AI配置失败: {str(e)}")



    def sync_config_from_ai_rewriter(self):
        """从AI改写器同步配置到UI"""
        if self.ai_rewriter is None:
            return

        try:
            config = self.ai_rewriter.config

            # 从智能体配置或传统服务配置中获取
            agents_config = config.get("agents", [])
            agent_id = ""
            token = ""

            # 优先使用智能体配置
            if agents_config:
                first_agent = agents_config[0]
                agent_id = first_agent.get("assistant_id", "")
                token = first_agent.get("api_key", "")
                debug(f"从智能体配置同步")
            else:
                # 回退到传统服务配置
                services = config.get("services", {})
                yuanqi_config = services.get("yuanqi", {})
                agent_id = yuanqi_config.get("assistant_id", "")
                token = yuanqi_config.get("api_key", "")
                debug(f"从传统服务配置同步")

            # 临时禁用自动保存，避免循环更新
            old_auto_save = hasattr(self, '_auto_save_enabled') and self._auto_save_enabled
            self._auto_save_enabled = False

            # 更新UI控件
            if agent_id != self.agent_id_edit.text():
                self.agent_id_edit.setText(agent_id)

            if token != self.token_edit.text():
                self.token_edit.setText(token)



            # 恢复自动保存
            self._auto_save_enabled = old_auto_save

            # 保存到视频处理对话框的设置文件
            self.auto_save_settings()

            debug(f"已从AI改写器同步配置: agent_id={agent_id[:10] if agent_id else '(空)'}..., token={'***' if token else '(空)'}")

        except Exception as e:
            warning(f"同步AI配置时出错: {str(e)}")

    def start_batch_rewrite(self, filtered_video_files, thread_count):
        """启动批量AI改写（后台线程）"""
        try:
            # 创建批量改写Worker
            self.batch_rewrite_worker = BatchRewriteWorker(filtered_video_files, self.ai_rewriter)

            # 连接信号
            self.batch_rewrite_worker.signals.progress_updated.connect(self.on_batch_rewrite_progress)
            self.batch_rewrite_worker.signals.log_message.connect(self.add_log)
            self.batch_rewrite_worker.signals.rewrite_finished.connect(
                lambda mapping: self.on_batch_rewrite_finished(mapping, filtered_video_files, thread_count)
            )
            self.batch_rewrite_worker.signals.rewrite_failed.connect(
                lambda error: self.on_batch_rewrite_failed(error, filtered_video_files, thread_count)
            )

            # 不显示进度对话框，改为在主界面日志中显示进度
            self.batch_progress_dialog = None
            self.add_log(f"开始批量AI改写 {len(filtered_video_files)} 个文件名...", "INFO")

            # 启动批量改写线程
            self.thread_pool.start(self.batch_rewrite_worker)

        except Exception as e:
            self.add_log(f"启动批量改写失败: {str(e)}", "ERROR")
            # 直接开始视频处理，不使用AI改写
            self.start_video_processing(filtered_video_files, {}, thread_count)

    def on_batch_rewrite_progress(self, current, total, filename):
        """批量改写进度更新"""
        # 在日志中显示进度，每10个文件或最后一个文件记录一次
        if current % 10 == 0 or current == total:
            progress_percent = (current / total * 100) if total > 0 else 0
            self.add_log(f"AI改写进度: {current}/{total} ({progress_percent:.1f}%) - {filename}", "INFO")

    def on_batch_rewrite_finished(self, filename_mapping, filtered_video_files, thread_count):
        """批量改写完成"""
        try:
            # 记录完成信息
            self.add_log(f"批量AI改写完成，共改写 {len(filename_mapping)} 个文件名", "INFO")

            # 开始视频处理
            self.start_video_processing(filtered_video_files, filename_mapping, thread_count)

        except Exception as e:
            self.add_log(f"处理批量改写结果时出错: {str(e)}", "ERROR")
            # 即使出错也继续视频处理
            self.start_video_processing(filtered_video_files, {}, thread_count)

    def on_batch_rewrite_failed(self, error_msg, filtered_video_files, thread_count):
        """批量改写失败"""
        try:
            self.add_log(f"批量AI改写失败: {error_msg}，将使用原始文件名", "ERROR")

            # 继续视频处理，不使用AI改写
            self.start_video_processing(filtered_video_files, {}, thread_count)

        except Exception as e:
            self.add_log(f"处理批量改写失败时出错: {str(e)}", "ERROR")

    def cancel_batch_rewrite(self):
        """取消批量改写"""
        try:
            if hasattr(self, 'batch_rewrite_worker') and self.batch_rewrite_worker:
                self.batch_rewrite_worker.cancel()

            self.add_log("用户取消了批量改写操作", "WARNING")

            # 重置处理状态
            self.is_processing = False
            self.start_btn.setVisible(True)
            self.stop_btn.setVisible(False)
            self.progress_bar.setVisible(False)
            self.status_label.setText("就绪")

        except Exception as e:
            self.add_log(f"取消批量改写时出错: {str(e)}", "ERROR")

    def start_video_processing(self, filtered_video_files, filename_mapping, thread_count):
        """开始视频处理阶段"""
        try:
            # 更新共享数据，使用筛选后的文件列表
            self.shared_data['video_queue'] = filtered_video_files.copy()
            self.shared_data['total_count'] = len(filtered_video_files)
            self.shared_data['filename_mapping'] = filename_mapping

            self.add_log(f"第三阶段：开始处理 {len(filtered_video_files)} 个筛选后的视频文件，使用 {thread_count} 个线程", "INFO")

            # 设置线程池最大线程数
            self.thread_pool.setMaxThreadCount(thread_count)

            # 创建并启动工作线程
            self.add_log("创建工作线程...", "INFO")
            self.worker_threads = []

            try:
                # 优化的分批创建机制
                batch_size = 2  # 减少每批线程数，提高稳定性
                created_count = 0

                self.add_log(f"开始分批创建 {thread_count} 个工作线程...", "INFO")

                for batch_start in range(0, thread_count, batch_size):
                    batch_end = min(batch_start + batch_size, thread_count)

                    self.add_log(f"创建第 {batch_start//batch_size + 1} 批线程...", "INFO")

                    for i in range(batch_start, batch_end):
                        try:
                            worker = VideoProcessorWorker(
                                self.input_dir_edit.text().strip(),
                                self.output_dir_edit.text().strip(),
                                self.cover_dir_edit.text().strip(),
                                thread_count, i, self.shared_data,
                                {
                                    'enabled': self.duration_filter_checkbox.isChecked(),
                                    'min_duration': self.min_duration_spinbox.value(),
                                    'max_duration': self.max_duration_spinbox.value()
                                },
                                None,  # AI改写器不传递给Worker，因为已经预先改写了
                                self.watermark_config
                            )

                            # 安全连接信号
                            try:
                                worker.signals.progress_updated.connect(self.on_progress_updated)
                                worker.signals.log_message.connect(self.add_log)
                                worker.signals.task_completed.connect(self.on_task_completed)
                                worker.signals.error_occurred.connect(self.on_error_occurred)
                            except Exception as signal_error:
                                self.add_log(f"连接信号时出错: {str(signal_error)}", "WARNING")

                            self.worker_threads.append(worker)
                            self.thread_pool.start(worker)
                            created_count += 1

                            self.add_log(f"线程 {i+1}/{thread_count} 创建成功", "SUCCESS")

                        except Exception as worker_error:
                            self.add_log(f"创建线程 {i+1} 失败: {str(worker_error)}", "ERROR")
                            continue

                        # 处理事件，保持UI响应
                        QApplication.processEvents()

                    # 批次间延迟，让系统稳定
                    if batch_end < thread_count:
                        import time
                        time.sleep(0.2)  # 增加延迟时间
                        QApplication.processEvents()

                self.add_log(f"线程创建完成: {created_count}/{thread_count} 个线程启动成功", "SUCCESS")

                if created_count == 0:
                    self.add_log("没有成功创建任何工作线程", "ERROR")
                    self.stop_processing()
                    return

            except Exception as e:
                self.add_log(f"创建工作线程时出错: {str(e)}", "ERROR")
                self.stop_processing()
                return

        except Exception as e:
            self.add_log(f"启动视频处理时出错: {str(e)}", "ERROR")
            self.stop_processing()

    def pre_filter_videos(self, video_files, duration_settings):
        """预筛选视频文件，只保留符合条件的文件"""
        filtered_files = []
        skipped_portrait = 0
        skipped_duration = 0

        self.add_log(f"开始预筛选 {len(video_files)} 个视频文件...", "INFO")

        # 显示预筛选进度
        progress_dialog = QProgressDialog("正在预筛选视频文件...", "取消", 0, len(video_files), self)
        # 设置窗口模态 - 跳过设置避免兼容性问题
        # 注释：WindowModal在某些PyQt5版本中可能不可用
        pass
        progress_dialog.setAutoClose(False)
        progress_dialog.setAutoReset(False)
        progress_dialog.show()

        try:
            for i, video_path in enumerate(video_files):
                if progress_dialog.wasCanceled():
                    raise Exception("用户取消了预筛选操作")

                progress_dialog.setValue(i)
                progress_dialog.setLabelText(f"正在检查: {os.path.basename(video_path)} ({i+1}/{len(video_files)})")
                QApplication.processEvents()

                try:
                    # 检查是否为横屏视频
                    if not self.is_landscape_video(video_path):
                        skipped_portrait += 1
                        debug(f"跳过竖屏/方形视频: {os.path.basename(video_path)}")
                        continue

                    # 检查视频时长筛选
                    if not self.check_video_duration(video_path, duration_settings):
                        skipped_duration += 1
                        debug(f"跳过时长不符视频: {os.path.basename(video_path)}")
                        continue

                    # 通过所有筛选条件
                    filtered_files.append(video_path)

                except Exception as e:
                    warning(f"检查视频文件时出错 {video_path}: {str(e)}")
                    # 出错的文件跳过，不影响其他文件
                    continue

            progress_dialog.close()

            # 记录筛选结果
            self.add_log(f"预筛选统计:", "INFO")
            self.add_log(f"  总文件数: {len(video_files)}", "INFO")
            self.add_log(f"  通过筛选: {len(filtered_files)}", "SUCCESS")
            self.add_log(f"  跳过竖屏: {skipped_portrait}", "INFO")
            self.add_log(f"  跳过时长: {skipped_duration}", "INFO")

            return filtered_files

        except Exception as e:
            progress_dialog.close()
            raise Exception(f"预筛选过程中出错: {str(e)}")

    def is_landscape_video(self, video_path):
        """检查视频是否为横屏"""
        try:
            import cv2
            cap = cv2.VideoCapture(video_path)

            if not cap.isOpened():
                warning(f"无法打开视频文件: {video_path}")
                return False

            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            cap.release()

            if width <= 0 or height <= 0:
                warning(f"无法获取视频尺寸: {video_path}")
                return False

            # 横屏视频：宽度 > 高度
            return width > height

        except Exception as e:
            warning(f"检查视频方向时出错 {video_path}: {str(e)}")
            return False

    def check_video_duration(self, video_path, duration_settings):
        """检查视频时长是否符合筛选条件"""
        if not duration_settings.get('enabled', False):
            return True

        try:
            import cv2
            cap = cv2.VideoCapture(video_path)

            if not cap.isOpened():
                warning(f"无法打开视频文件: {video_path}")
                return True  # 无法检查时默认通过

            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            cap.release()

            if fps <= 0 or frame_count <= 0:
                warning(f"无法获取视频时长信息: {video_path}")
                return True  # 无法检查时默认通过

            duration_seconds = frame_count / fps
            duration_minutes = duration_seconds / 60

            min_duration = duration_settings.get('min_duration', 0)
            max_duration = duration_settings.get('max_duration', 0)

            # 检查最小时长
            if min_duration > 0 and duration_minutes < min_duration:
                return False

            # 检查最大时长
            if max_duration > 0 and duration_minutes > max_duration:
                return False

            return True

        except Exception as e:
            warning(f"检查视频时长时出错 {video_path}: {str(e)}")
            return True  # 出错时默认通过



    def closeEvent(self, a0):
        """关闭事件"""
        if self.is_processing:
            reply = QMessageBox.question(
                self, "确认关闭",
                "视频处理正在进行中，确定要关闭吗？\n\n这将停止所有处理任务。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.stop_processing()
                # 关闭前保存设置
                self.save_current_settings()
                a0.accept()  # type: ignore
            else:
                a0.ignore()  # type: ignore
        else:
            # 关闭前保存设置
            self.save_current_settings()
            a0.accept()  # type: ignore

    # 功能说明方法已删除


class MultiLineDraggableWatermarkLabel(QLabel):
    """多行可拖拽的水印预览标签"""
    position_changed = pyqtSignal(int, int, int)  # 行索引, x百分比, y百分比
    line_selected = pyqtSignal(int)  # 行选择信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.dragging = False
        self.dragging_line_index = -1  # 当前拖拽的行索引
        self.drag_start_position = QPoint()
        self.setMouseTracking(True)
        self._mouse_over_line = -1  # 鼠标悬停的行索引
        self.background_pixmap = None  # 背景图片
        self.selected_line_index = 0  # 当前选中的行索引

        # 预览尺寸信息 - 与单行水印保持一致
        self.preview_width = 768  # 默认值，会被覆盖
        self.preview_height = 432  # 默认值，会被覆盖
        self.cover_width = 1920
        self.cover_height = 1080

        # 多行水印配置
        self.multi_line_configs = []
        self.filename_text = "示例视频文件名.mp4"

        # 设置样式，确保可以接收鼠标事件
        self.setStyleSheet("""
            QLabel {
                background-color: transparent;
            }
        """)

    def set_preview_dimensions(self, preview_width, preview_height):
        """设置预览尺寸信息"""
        self.preview_width = preview_width
        self.preview_height = preview_height
        print(f"多行水印标签设置预览尺寸: {preview_width}x{preview_height}")

    def set_cover_dimensions(self, cover_width, cover_height):
        """设置封面尺寸信息"""
        self.cover_width = cover_width
        self.cover_height = cover_height
        print(f"多行水印标签设置封面尺寸: {cover_width}x{cover_height}")

    def set_multi_line_configs(self, configs):
        """设置多行配置"""
        self.multi_line_configs = configs
        self.update()

    def set_filename_text(self, text):
        """设置文件名文本"""
        self.filename_text = text
        self.update()

    def get_line_text(self, line_index):
        """获取指定行的文本"""
        if line_index >= len(self.multi_line_configs):
            return ""

        # 计算该行应该显示的文本
        start_pos = 0
        for i in range(line_index):
            if i < len(self.multi_line_configs):
                start_pos += self.multi_line_configs[i].char_count

        char_count = self.multi_line_configs[line_index].char_count
        end_pos = start_pos + char_count

        # 从文件名中截取对应的字符
        filename_without_ext = self.filename_text.replace('.mp4', '').replace('.avi', '').replace('.mkv', '')
        return filename_without_ext[start_pos:end_pos]

    # 重复的 get_line_rect 方法已删除 - 使用第7978行的完整版本

    def mousePressEvent(self, ev):
        """鼠标按下事件"""
        if ev and hasattr(ev, 'button') and ev.button() == Qt.LeftButton:
            # 检查点击了哪一行
            for i, config in enumerate(self.multi_line_configs):
                if not config.enabled:
                    continue
                line_rect = self.get_line_rect(i)
                if line_rect.contains(ev.pos()):
                    print(f"开始拖拽第{i+1}行水印")
                    self.dragging = True
                    self.dragging_line_index = i
                    self.selected_line_index = i

                    # 计算拖拽起始偏移
                    line_center = line_rect.center()
                    self.drag_start_position = ev.pos() - line_center

                    self.setCursor(Qt.ClosedHandCursor)
                    self.line_selected.emit(i)  # 发送行选择信号
                    self.update()
                    break
        super().mousePressEvent(ev)

    def mouseMoveEvent(self, ev):
        """鼠标移动事件"""
        if self.dragging and ev and hasattr(ev, 'buttons') and ev.buttons() == Qt.LeftButton and self.dragging_line_index >= 0:
            # 更新拖拽行的位置
            new_pos = ev.pos() - self.drag_start_position

            # 获取当前拖拽行的配置和文字
            if self.dragging_line_index < len(self.multi_line_configs):
                config = self.multi_line_configs[self.dragging_line_index]
                text = self.get_line_text(self.dragging_line_index)

                # 获取容器尺寸 - 使用预览尺寸而不是控件尺寸
                container_width = getattr(self, 'preview_width', self.width())
                container_height = getattr(self, 'preview_height', self.height())

                # 简化边界限制 - 只确保不超出容器边界
                margin = 5  # 最小边距
                new_pos.setX(max(margin, min(new_pos.x(), container_width - margin)))
                new_pos.setY(max(margin, min(new_pos.y(), container_height - margin)))

                print(f"第{self.dragging_line_index+1}行拖拽: 像素位置({new_pos.x()},{new_pos.y()}) 容器({container_width}x{container_height})")
            else:
                # 如果配置不存在，使用默认边界 - 使用预览尺寸
                preview_w = getattr(self, 'preview_width', self.width())
                preview_h = getattr(self, 'preview_height', self.height())
                new_pos.setX(max(30, min(new_pos.x(), preview_w - 30)))
                new_pos.setY(max(20, min(new_pos.y(), preview_h - 20)))

            # 计算百分比位置 - 使用正确的预览尺寸
            container_width = getattr(self, 'preview_width', self.width())
            container_height = getattr(self, 'preview_height', self.height())
            x_percent = max(0, min(100, int((new_pos.x() / container_width) * 100)))
            y_percent = max(0, min(100, int((new_pos.y() / container_height) * 100)))

            # 更新配置
            if self.dragging_line_index < len(self.multi_line_configs):
                self.multi_line_configs[self.dragging_line_index].position_x = x_percent
                self.multi_line_configs[self.dragging_line_index].position_y = y_percent

            # 发送位置改变信号
            self.position_changed.emit(self.dragging_line_index, x_percent, y_percent)

            # 重绘
            self.update()
        else:
            # 检查鼠标悬停在哪一行
            hover_line = -1
            for i, config in enumerate(self.multi_line_configs):
                if not config.enabled:
                    continue
                line_rect = self.get_line_rect(i)
                if ev and hasattr(ev, 'pos') and line_rect.contains(ev.pos()):
                    hover_line = i
                    break

            if hover_line != self._mouse_over_line:
                self._mouse_over_line = hover_line
                self.update()

            if hover_line >= 0:
                self.setCursor(Qt.OpenHandCursor)
                self.setToolTip(f"拖拽第{hover_line+1}行水印")
            else:
                self.setCursor(Qt.ArrowCursor)
                self.setToolTip("")

        super().mouseMoveEvent(ev)

    def mouseReleaseEvent(self, ev):
        """鼠标释放事件"""
        if ev and hasattr(ev, 'button') and ev.button() == Qt.LeftButton and self.dragging:
            print(f"结束拖拽第{self.dragging_line_index+1}行水印")
            self.dragging = False
            self.dragging_line_index = -1

            # 检查鼠标是否仍在某行区域
            hover_line = -1
            for i, config in enumerate(self.multi_line_configs):
                if not config.enabled:
                    continue
                line_rect = self.get_line_rect(i)
                if ev and hasattr(ev, 'pos') and line_rect.contains(ev.pos()):
                    hover_line = i
                    break

            if hover_line >= 0:
                self.setCursor(Qt.OpenHandCursor)
            else:
                self.setCursor(Qt.ArrowCursor)

            self.update()
        super().mouseReleaseEvent(ev)

    def paintEvent(self, a0):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制背景图片
        if self.background_pixmap:
            painter.drawPixmap(self.rect(), self.background_pixmap)
        else:
            # 如果没有背景图片，绘制默认背景
            painter.fillRect(self.rect(), QColor(44, 62, 80))
            painter.setPen(QPen(QColor(255, 255, 255, 100), 1))
            painter.drawText(self.rect(), Qt.AlignCenter, "多行水印预览区域")

        # 绘制每一行水印
        for i, config in enumerate(self.multi_line_configs):
            if not config.enabled:
                continue

            text = self.get_line_text(i)
            if not text:
                continue

            # 计算位置 - 使用预览尺寸而不是控件尺寸
            preview_w = getattr(self, 'preview_width', self.width())
            preview_h = getattr(self, 'preview_height', self.height())
            x = int(preview_w * config.position_x / 100)
            y = int(preview_h * config.position_y / 100)

            # 设置字体
            font = QFont(config.font_family, config.font_size, QFont.Bold)
            painter.setFont(font)

            # 绘制描边（先绘制描边，再绘制阴影和主文字）
            if config.stroke_enabled:
                stroke_color = QColor(config.stroke_color)
                stroke_color.setAlpha(int(255 * config.opacity / 100))

                # 使用多次偏移绘制模拟描边效果
                stroke_width = config.stroke_width
                for dx in range(-stroke_width, stroke_width + 1):
                    for dy in range(-stroke_width, stroke_width + 1):
                        if dx == 0 and dy == 0:
                            continue
                        painter.setPen(QPen(stroke_color, 1))
                        painter.drawText(x + dx, y + dy, text)

            # 绘制阴影
            if config.shadow_enabled:
                shadow_pos = QPoint(x + config.shadow_offset_x, y + config.shadow_offset_y)
                shadow_color = QColor(config.shadow_color)
                shadow_color.setAlpha(int(255 * config.opacity / 100))

                painter.setPen(QPen(shadow_color, 1))
                painter.drawText(shadow_pos, text)

            # 绘制主文字
            text_color = QColor(config.text_color)
            text_color.setAlpha(int(255 * config.opacity / 100))

            painter.setPen(QPen(text_color, 1))
            painter.drawText(x, y, text)

            # 绘制边界框和状态指示
            line_rect = self.get_line_rect(i)

            # 选中状态
            if i == self.selected_line_index:
                painter.setPen(QPen(QColor(0, 123, 255, 150), 2, Qt.SolidLine))
                painter.drawRect(line_rect)
                # 绘制选中标识
                painter.setPen(QPen(QColor(255, 255, 255), 1))
                painter.drawText(line_rect.x(), line_rect.y() - 5, f"第{i+1}行 (选中)")

            # 拖拽状态
            elif self.dragging and i == self.dragging_line_index:
                painter.setPen(QPen(QColor(255, 0, 0, 150), 2, Qt.DashLine))
                painter.drawRect(line_rect)
                painter.setPen(QPen(QColor(255, 255, 255), 1))
                painter.drawText(line_rect.x(), line_rect.y() - 5, f"第{i+1}行 (拖拽中...)")

            # 悬停状态
            elif i == self._mouse_over_line:
                painter.setPen(QPen(QColor(0, 255, 0, 100), 1, Qt.DashLine))
                painter.drawRect(line_rect)
                painter.setPen(QPen(QColor(255, 255, 255), 1))
                painter.drawText(line_rect.x(), line_rect.y() - 5, f"第{i+1}行")

        painter.end()


class DraggableWatermarkLabel(QLabel):
    """可拖拽的水印预览标签 - 支持单行和多行模式"""
    position_changed = pyqtSignal(int, int)  # 单行模式：位置改变信号 (x%, y%)
    line_selected = pyqtSignal(int)  # 多行模式：行选择信号
    multi_line_position_changed = pyqtSignal(int, int, int)  # 多行模式：位置改变信号 (line_index, x%, y%)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setAcceptDrops(True)
        self.dragging = False
        self.drag_start_position = QPoint()
        # 默认位置将在set_preview_dimensions中设置
        self.watermark_position = QPoint(384, 389)  # 临时默认位置 (50%, 90%) - 适应768x432尺寸
        self.watermark_text = ""  # 默认为空，避免在多行模式下显示
        self.setMouseTracking(True)
        self._mouse_over_watermark = False
        self.background_pixmap = None  # 背景图片
        self.full_size_image = None  # 完整尺寸的图像 (1920x1080)

        # 封面尺寸信息
        self.cover_width = 1920
        self.cover_height = 1080
        # 预览尺寸将通过set_preview_dimensions方法设置
        self.preview_width = 768  # 默认值，会被覆盖
        self.preview_height = 432  # 默认值，会被覆盖

        # 多行模式支持
        self.multiline_mode = False
        self.multi_line_configs = []
        self.filename_text = "示例视频文件名.mp4"
        self.dragging_line_index = -1  # 当前拖拽的行索引
        self.selected_line_index = 0  # 当前选中的行索引
        self._mouse_over_line = -1  # 鼠标悬停的行索引

        # 设置样式，确保可以接收鼠标事件
        self.setStyleSheet("""
            QLabel {
                background-color: transparent;
            }
        """)

    def set_cover_dimensions(self, cover_width, cover_height):
        """设置封面尺寸信息"""
        self.cover_width = cover_width
        self.cover_height = cover_height
        print(f"设置封面尺寸: {cover_width}x{cover_height}, 预览尺寸: {self.preview_width}x{self.preview_height}")

    def set_preview_dimensions(self, preview_width, preview_height):
        """设置预览尺寸信息并更新默认水印位置"""
        self.preview_width = preview_width
        self.preview_height = preview_height
        # 更新默认水印位置 (50%, 90%)
        self.watermark_position = QPoint(
            int(preview_width * 0.5),
            int(preview_height * 0.9)
        )
        print(f"设置预览尺寸: {preview_width}x{preview_height}, 默认水印位置: {self.watermark_position}")

    def set_multiline_mode(self, enabled):
        """设置多行模式"""
        self.multiline_mode = enabled
        print(f"预览标签设置多行模式: {enabled}")
        self.update()

    def set_multi_line_configs(self, configs):
        """设置多行配置"""
        self.multi_line_configs = configs
        self.update()

    def set_filename_text(self, text):
        """设置文件名文本"""
        self.filename_text = text
        self.update()

    def get_line_text(self, line_index):
        """获取指定行的文本"""
        if line_index >= len(self.multi_line_configs):
            return ""

        # 计算该行应该显示的文本
        start_pos = 0
        for i in range(line_index):
            if i < len(self.multi_line_configs):
                start_pos += self.multi_line_configs[i].char_count

        char_count = self.multi_line_configs[line_index].char_count
        end_pos = start_pos + char_count

        # 从文件名中截取对应的字符
        filename_without_ext = self.filename_text.replace('.mp4', '').replace('.avi', '').replace('.mkv', '').replace('.mov', '').replace('.wmv', '')

        # 确保有足够的字符
        if len(filename_without_ext) < end_pos:
            # 如果文件名不够长，用默认文字补充
            filename_without_ext = filename_without_ext + "水印文字" * 10

        result_text = filename_without_ext[start_pos:end_pos]
        print(f"第{line_index+1}行文字: '{result_text}' (从位置{start_pos}到{end_pos})")
        return result_text

    def get_line_rect(self, line_index):
        """获取指定行的矩形区域"""
        if line_index >= len(self.multi_line_configs):
            return QRect()

        config = self.multi_line_configs[line_index]
        text = self.get_line_text(line_index)

        # 计算位置 - 使用与绘制时相同的逻辑，包括边界调整
        preview_w = getattr(self, 'preview_width', self.width())
        preview_h = getattr(self, 'preview_height', self.height())

        # 获取实际的字体大小（与绘制时保持一致）
        current_line_index = getattr(self, 'selected_line_index', 0)
        if line_index == current_line_index and hasattr(self, 'parent') and hasattr(self.parent(), 'font_size_spinbox'):
            actual_font_size = self.parent().font_size_spinbox.value()
        else:
            actual_font_size = config.font_size

        # 估算文字尺寸（使用实际字体大小）
        text_width = len(text) * actual_font_size // 2
        text_height = actual_font_size

        # 计算基础位置
        base_x = int(preview_w * config.position_x / 100)
        base_y = int(preview_h * config.position_y / 100)

        # 应用与绘制时相同的边界调整逻辑
        x = max(10, min(base_x, preview_w - text_width - 10))
        y = max(text_height + 10, min(base_y, preview_h - 10))

        print(f"选中框位置计算: {config.position_x}%,{config.position_y}% -> 基础({base_x},{base_y}) -> 调整后({x},{y}) [文字{text_width}x{text_height}]")

        # 扩大选中区域，增加内边距使其更容易点击
        padding_x = 20  # 水平内边距
        padding_y = 10  # 垂直内边距

        expanded_width = text_width + padding_x * 2
        expanded_height = text_height + padding_y * 2

        return QRect(x - expanded_width//2, y - expanded_height//2, expanded_width, expanded_height)

    def get_actual_render_position(self, line_index):
        """获取水印实际绘制位置（与绘制逻辑完全一致）"""
        if line_index >= len(self.multi_line_configs):
            return None, None

        config = self.multi_line_configs[line_index]
        text = self.get_line_text(line_index)

        # 使用与绘制时完全相同的逻辑
        preview_w = getattr(self, 'preview_width', self.width())
        preview_h = getattr(self, 'preview_height', self.height())

        # 获取实际的字体大小（与绘制时保持一致）
        current_line_index = getattr(self, 'selected_line_index', 0)
        if line_index == current_line_index and hasattr(self, 'parent') and hasattr(self.parent(), 'font_size_spinbox'):
            actual_font_size = self.parent().font_size_spinbox.value()
            actual_font_family = self.parent().font_combo.currentText() if hasattr(self.parent(), 'font_combo') else config.font_family
        else:
            actual_font_size = config.font_size
            actual_font_family = config.font_family

        # 使用QFont计算精确的文字尺寸（与绘制时一致）
        font = QFont(actual_font_family, actual_font_size, QFont.Bold)
        font_metrics = QFontMetrics(font)
        text_width = font_metrics.horizontalAdvance(text)
        text_height = font_metrics.height()

        # 获取更精确的字体度量信息
        ascent = font_metrics.ascent()      # 基线到字体顶部的距离
        descent = font_metrics.descent()    # 基线到字体底部的距离

        # 计算基础位置
        base_x = int(preview_w * config.position_x / 100)
        base_y = int(preview_h * config.position_y / 100)

        # 应用与绘制时相同的边界调整逻辑
        baseline_x = max(10, min(base_x, preview_w - text_width - 10))
        baseline_y = max(text_height + 10, min(base_y, preview_h - 10))

        # 转换为文字的视觉中心位置（用于拖动框定位）
        # 水平中心：文字左边缘 + 一半宽度
        center_x = baseline_x + text_width // 2

        # 垂直中心：基线位置 - (ascent - descent) / 2
        # 这样计算得到的是文字实际显示区域的垂直中心
        center_y = baseline_y - (ascent - descent) // 2

        return center_x, center_y

    def get_text_baseline_position(self, line_index):
        """获取文字基线位置（用于实际绘制）"""
        if line_index >= len(self.multi_line_configs):
            return None, None

        config = self.multi_line_configs[line_index]
        text = self.get_line_text(line_index)

        # 使用与绘制时完全相同的逻辑
        preview_w = getattr(self, 'preview_width', self.width())
        preview_h = getattr(self, 'preview_height', self.height())

        # 获取实际的字体大小（与绘制时保持一致）
        current_line_index = getattr(self, 'selected_line_index', 0)
        if line_index == current_line_index and hasattr(self, 'parent') and hasattr(self.parent(), 'font_size_spinbox'):
            actual_font_size = self.parent().font_size_spinbox.value()
            actual_font_family = self.parent().font_combo.currentText() if hasattr(self.parent(), 'font_combo') else config.font_family
        else:
            actual_font_size = config.font_size
            actual_font_family = config.font_family

        # 使用QFont计算精确的文字尺寸（与绘制时一致）
        font = QFont(actual_font_family, actual_font_size, QFont.Bold)
        font_metrics = QFontMetrics(font)
        text_width = font_metrics.horizontalAdvance(text)
        text_height = font_metrics.height()

        # 获取字体度量信息（与get_actual_render_position保持一致）
        ascent = font_metrics.ascent()
        descent = font_metrics.descent()

        # 计算基础位置
        base_x = int(preview_w * config.position_x / 100)
        base_y = int(preview_h * config.position_y / 100)

        # 应用与绘制时相同的边界调整逻辑
        baseline_x = max(10, min(base_x, preview_w - text_width - 10))
        baseline_y = max(text_height + 10, min(base_y, preview_h - 10))

        return baseline_x, baseline_y

    def mousePressEvent(self, ev):
        """鼠标按下事件"""
        if ev and hasattr(ev, 'button') and ev.button() == Qt.LeftButton:
            if self.multiline_mode:
                # 多行模式：检查点击了哪一行 - 使用文字实际显示区域
                for i, config in enumerate(self.multi_line_configs):
                    if not config.enabled:
                        continue

                    # 获取文字基线位置
                    baseline_x, baseline_y = self.get_text_baseline_position(i)
                    if baseline_x is not None and baseline_y is not None:
                        # 创建基于文字实际显示区域的点击区域
                        text = self.get_line_text(i)
                        current_line_index = getattr(self, 'selected_line_index', 0)
                        if i == current_line_index and hasattr(self, 'parent') and hasattr(self.parent(), 'font_size_spinbox'):
                            actual_font_size = self.parent().font_size_spinbox.value()
                            actual_font_family = self.parent().font_combo.currentText() if hasattr(self.parent(), 'font_combo') else config.font_family
                        else:
                            actual_font_size = config.font_size
                            actual_font_family = config.font_family

                        font = QFont(actual_font_family, actual_font_size, QFont.Bold)
                        font_metrics = QFontMetrics(font)
                        text_width = font_metrics.horizontalAdvance(text)
                        text_height = font_metrics.height()
                        ascent = font_metrics.ascent()
                        descent = font_metrics.descent()

                        # 计算文字的实际显示区域
                        text_left = baseline_x
                        text_right = baseline_x + text_width
                        text_top = baseline_y - ascent
                        text_bottom = baseline_y + descent

                        # 创建点击区域（与拖动框相同）
                        padding_x = 20
                        padding_y = 10
                        click_rect = QRect(text_left - padding_x, text_top - padding_y,
                                         text_width + padding_x * 2,
                                         (text_bottom - text_top) + padding_y * 2)
                    else:
                        # 回退到原来的方法
                        click_rect = self.get_line_rect(i)

                    if ev and hasattr(ev, 'pos') and click_rect.contains(ev.pos()):
                        print(f"开始拖拽第{i+1}行水印")
                        self.dragging = True
                        self.dragging_line_index = i
                        self.selected_line_index = i

                        # 计算拖拽起始偏移 - 使用文字视觉中心位置
                        if baseline_x is not None and baseline_y is not None:
                            # 计算文字的视觉中心作为拖拽参考点
                            text_center_x = baseline_x + text_width // 2
                            text_center_y = baseline_y - (ascent - descent) // 2
                            watermark_pos = QPoint(text_center_x, text_center_y)
                            self.drag_start_position = ev.pos() - watermark_pos
                            print(f"开始拖拽第{i+1}行: 鼠标({ev.pos().x()},{ev.pos().y()}), 文字中心({text_center_x},{text_center_y}), 偏移({self.drag_start_position.x()},{self.drag_start_position.y()})")
                        else:
                            # 回退到原来的计算方式
                            config = self.multi_line_configs[i]
                            preview_w = getattr(self, 'preview_width', self.width())
                            preview_h = getattr(self, 'preview_height', self.height())
                            watermark_x = int(preview_w * config.position_x / 100)
                            watermark_y = int(preview_h * config.position_y / 100)
                            watermark_pos = QPoint(watermark_x, watermark_y)
                            self.drag_start_position = ev.pos() - watermark_pos
                            print(f"开始拖拽第{i+1}行(回退): 鼠标({ev.pos().x()},{ev.pos().y()}), 水印({watermark_x},{watermark_y}), 偏移({self.drag_start_position.x()},{self.drag_start_position.y()})")

                        self.setCursor(Qt.ClosedHandCursor)
                        # 发送行选择信号
                        self.line_selected.emit(i)
                        self.update()
                        break
            else:
                # 单行模式：检查是否点击在水印区域
                if ev and hasattr(ev, 'pos') and self.is_point_in_watermark(ev.pos()):
                    print(f"开始拖拽水印，点击位置: {ev.pos()}, 水印位置: {self.watermark_position}")
                    self.dragging = True
                    self.drag_start_position = ev.pos() - self.watermark_position
                    self.setCursor(Qt.ClosedHandCursor)
                    self.update()  # 重绘以显示拖拽状态
                else:
                    print(f"点击位置不在水印区域内: {ev.pos() if ev and hasattr(ev, 'pos') else 'Unknown'}")
        super().mousePressEvent(ev)

    def mouseMoveEvent(self, ev):
        """鼠标移动事件"""
        if self.dragging and ev and hasattr(ev, 'buttons') and ev.buttons() == Qt.LeftButton:
            if self.multiline_mode and self.dragging_line_index >= 0:
                # 多行模式：更新拖拽行的位置
                new_pos = ev.pos() - self.drag_start_position

                # 获取当前拖拽行的配置和文字
                if self.dragging_line_index < len(self.multi_line_configs):
                    config = self.multi_line_configs[self.dragging_line_index]
                    text = self.get_line_text(self.dragging_line_index)

                    # 使用正确的预览尺寸进行位置计算
                    preview_w = getattr(self, 'preview_width', self.width())
                    preview_h = getattr(self, 'preview_height', self.height())

                    # 简化边界限制 - 只确保不超出容器边界，给用户更多自由度
                    margin = 5  # 最小边距
                    new_pos.setX(max(margin, min(new_pos.x(), preview_w - margin)))
                    new_pos.setY(max(margin, min(new_pos.y(), preview_h - margin)))

                    # 计算百分比位置 - 基于正确的预览尺寸，确保范围在0-100之间
                    x_percent = max(0, min(100, int((new_pos.x() / preview_w) * 100)))
                    y_percent = max(0, min(100, int((new_pos.y() / preview_h) * 100)))

                    # 更新配置
                    config.position_x = x_percent
                    config.position_y = y_percent

                    print(f"拖拽第{self.dragging_line_index+1}行: 像素位置({new_pos.x()},{new_pos.y()}) -> 百分比({x_percent}%, {y_percent}%) 基于尺寸({preview_w}x{preview_h})")

                    # 发送多行位置改变信号（不影响单行水印）
                    self.multi_line_position_changed.emit(self.dragging_line_index, x_percent, y_percent)

                    # 重绘
                    self.update()
            else:
                # 单行模式：更新水印位置
                new_pos = ev.pos() - self.drag_start_position

                # 限制在预览区域内 - 使用预览尺寸与多行水印保持一致
                preview_w = getattr(self, 'preview_width', self.width())
                preview_h = getattr(self, 'preview_height', self.height())

                text_width = len(self.watermark_text) * 12
                max_x = preview_w - text_width - 10
                max_y = preview_h - 30

                new_pos.setX(max(5, min(new_pos.x(), max_x)))
                new_pos.setY(max(20, min(new_pos.y(), max_y)))

                self.watermark_position = new_pos

                # 计算百分比位置 - 使用与多行水印相同的预览尺寸基准
                preview_w = getattr(self, 'preview_width', self.width())
                preview_h = getattr(self, 'preview_height', self.height())
                x_percent = max(0, min(100, int((new_pos.x() / preview_w) * 100)))
                y_percent = max(0, min(100, int((new_pos.y() / preview_h) * 100)))

                print(f"单行水印拖拽: 像素位置({new_pos.x()},{new_pos.y()}) -> 百分比({x_percent}%, {y_percent}%) 基于尺寸({preview_w}x{preview_h})")

                print(f"拖拽位置: 预览({new_pos.x()}, {new_pos.y()}) -> 百分比({x_percent}%, {y_percent}%)")

                # 发送位置改变信号
                self.position_changed.emit(x_percent, y_percent)

                # 重绘
                self.update()
        else:
            # 检查鼠标是否在水印区域，改变光标和悬停状态
            is_over_watermark = ev and hasattr(ev, 'pos') and self.is_point_in_watermark(ev.pos())

            if is_over_watermark != self._mouse_over_watermark:
                self._mouse_over_watermark = is_over_watermark
                self.update()  # 重绘以显示/隐藏悬停效果

            if is_over_watermark:
                self.setCursor(Qt.OpenHandCursor)
                self.setToolTip("拖拽水印到新位置")
            else:
                self.setCursor(Qt.ArrowCursor)
                self.setToolTip("")

        super().mouseMoveEvent(ev)

    def mouseReleaseEvent(self, ev):
        """鼠标释放事件"""
        if ev and hasattr(ev, 'button') and ev.button() == Qt.LeftButton and self.dragging:
            print(f"结束拖拽水印，最终位置: {self.watermark_position}")
            self.dragging = False
            # 检查鼠标是否仍在水印区域
            if ev and hasattr(ev, 'pos') and self.is_point_in_watermark(ev.pos()):
                self.setCursor(Qt.OpenHandCursor)
            else:
                self.setCursor(Qt.ArrowCursor)
            self.update()  # 重绘以隐藏拖拽状态
        super().mouseReleaseEvent(ev)

    def is_point_in_watermark(self, point):
        """检查点是否在水印区域内"""
        # 根据文字长度动态计算矩形区域
        text_width = len(self.watermark_text) * 12  # 估算文字宽度
        text_height = 30  # 文字高度

        watermark_rect = QRect(
            self.watermark_position.x() - 5,
            self.watermark_position.y() - 20,
            text_width + 10,
            text_height
        )
        return watermark_rect.contains(point)

    def set_watermark_position_percent(self, x_percent, y_percent):
        """设置水印位置（百分比）- 使用与多行水印相同的预览尺寸基准"""
        # 使用预览尺寸而不是控件尺寸，与多行水印保持一致
        preview_w = getattr(self, 'preview_width', self.width())
        preview_h = getattr(self, 'preview_height', self.height())

        preview_x = int((preview_w * x_percent) / 100)
        preview_y = int((preview_h * y_percent) / 100)

        self.watermark_position = QPoint(preview_x, preview_y)
        print(f"设置单行水印位置: {x_percent}%, {y_percent}% -> ({preview_x}, {preview_y}) 基于尺寸({preview_w}x{preview_h})")

        # 验证坐标系统一致性
        self.verify_coordinate_consistency(x_percent, y_percent, preview_x, preview_y)
        self.update()

    def verify_coordinate_consistency(self, x_percent, y_percent, pixel_x, pixel_y):
        """验证单行和多行水印坐标系统的一致性"""
        preview_w = getattr(self, 'preview_width', self.width())
        preview_h = getattr(self, 'preview_height', self.height())

        # 验证反向计算是否一致
        calc_x_percent = int((pixel_x / preview_w) * 100)
        calc_y_percent = int((pixel_y / preview_h) * 100)

        if abs(calc_x_percent - x_percent) > 1 or abs(calc_y_percent - y_percent) > 1:
            print(f"⚠️ 坐标系统不一致: 输入({x_percent}%, {y_percent}%) -> 像素({pixel_x}, {pixel_y}) -> 反算({calc_x_percent}%, {calc_y_percent}%)")
        else:
            print(f"✅ 坐标系统一致: {x_percent}%, {y_percent}% ↔ ({pixel_x}, {pixel_y}) 基于({preview_w}x{preview_h})")

    def paintEvent(self, a0):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制背景图片 - 确保正确缩放到标签尺寸
        if self.background_pixmap:
            # 获取标签的实际尺寸
            label_rect = self.rect()
            # 将背景图片缩放到标签尺寸，保持宽高比
            scaled_pixmap = self.background_pixmap.scaled(
                label_rect.size(),
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            # 居中绘制
            x = (label_rect.width() - scaled_pixmap.width()) // 2
            y = (label_rect.height() - scaled_pixmap.height()) // 2
            painter.drawPixmap(x, y, scaled_pixmap)

            # 如果缩放后的图片小于标签，填充剩余区域
            if scaled_pixmap.size() != label_rect.size():
                painter.fillRect(label_rect, QColor(44, 62, 80))
                painter.drawPixmap(x, y, scaled_pixmap)
        else:
            # 如果没有背景图片，绘制默认背景（纯色，不显示文字）
            painter.fillRect(self.rect(), QColor(44, 62, 80))

        # 绘制水印文字（在背景图片之上）
        print(f"绘制水印 - 多行模式: {self.multiline_mode}, 配置数量: {len(self.multi_line_configs)}")
        if self.multiline_mode:
            # 多行模式：绘制每一行水印
            self.draw_multiline_watermarks(painter)
        else:
            # 单行模式：绘制单个水印
            self.draw_single_watermark(painter)

    def draw_single_watermark(self, painter):
        """绘制单行水印"""
        # 如果没有水印文字或处于多行模式，不绘制单行水印
        if not self.watermark_text or self.multiline_mode:
            print(f"跳过单行水印绘制 - 文字: '{self.watermark_text}', 多行模式: {self.multiline_mode}")
            return

        font = QFont("Microsoft YaHei", 16, QFont.Bold)
        painter.setFont(font)

        # 计算文字尺寸，确保不超出边界
        font_metrics = painter.fontMetrics()
        text_rect = font_metrics.boundingRect(self.watermark_text)
        text_width = text_rect.width()
        text_height = text_rect.height()

        # 调整水印位置，确保完全在预览区域内
        adjusted_x = self.watermark_position.x()
        adjusted_y = self.watermark_position.y()

        # 确保水印不超出边界 - 使用预览尺寸与多行水印保持一致
        preview_w = getattr(self, 'preview_width', self.width())
        preview_h = getattr(self, 'preview_height', self.height())

        if adjusted_x + text_width > preview_w - 10:
            adjusted_x = preview_w - text_width - 10
        if adjusted_y + text_height > preview_h - 10:
            adjusted_y = preview_h - text_height - 10
        if adjusted_x < 10:
            adjusted_x = 10
        if adjusted_y < text_height + 10:
            adjusted_y = text_height + 10

        print(f"单行水印边界调整: 原始({self.watermark_position.x()},{self.watermark_position.y()}) -> 调整后({adjusted_x},{adjusted_y}) 基于尺寸({preview_w}x{preview_h})")

        adjusted_position = QPoint(adjusted_x, adjusted_y)

        # 绘制阴影
        shadow_pos = QPoint(adjusted_position.x() + 2, adjusted_position.y() + 2)
        painter.setPen(QPen(QColor(0, 0, 0, 180), 2))
        painter.drawText(shadow_pos, self.watermark_text)

        # 绘制主文字
        painter.setPen(QPen(QColor(255, 255, 255, 255), 2))
        painter.drawText(adjusted_position, self.watermark_text)

        # 绘制字数标签
        char_count = len(self.watermark_text)
        label_text = f"单行({char_count}字)"
        label_color = QColor(255, 255, 255, 180)
        painter.setPen(QPen(label_color, 1))
        label_font = QFont("Microsoft YaHei", 10)
        painter.setFont(label_font)
        # 在水印文字上方显示标签
        painter.drawText(adjusted_position.x(), adjusted_position.y() - text_height - 5, label_text)

        # 绘制边界框
        watermark_rect = QRect(adjusted_position.x() - 5, adjusted_position.y() - text_height - 5,
                              text_width + 10, text_height + 10)

        if self.dragging:
            painter.setPen(QPen(QColor(255, 0, 0, 150), 2, Qt.DashLine))
            painter.drawRect(watermark_rect)
            painter.setPen(QPen(QColor(255, 255, 255, 200), 1))
            painter.drawText(watermark_rect.x(), watermark_rect.y() - 5, "拖拽中...")
        elif hasattr(self, '_mouse_over_watermark') and self._mouse_over_watermark:
            painter.setPen(QPen(QColor(0, 255, 0, 100), 1, Qt.DashLine))
            painter.drawRect(watermark_rect)

    def draw_multiline_watermarks(self, painter):
        """绘制多行水印"""
        # 如果不是多行模式或没有配置，不绘制多行水印
        if not self.multiline_mode or not self.multi_line_configs:
            return
        # 绘制每一行水印
        for i, config in enumerate(self.multi_line_configs):
            if not config.enabled:
                continue

            text = self.get_line_text(i)
            if not text:
                continue

            # 计算位置 - 使用正确的预览尺寸
            preview_w = getattr(self, 'preview_width', self.width())
            preview_h = getattr(self, 'preview_height', self.height())

            # 设置字体以获取文字尺寸 - 使用与单行模式一致的字体大小获取逻辑
            # 如果是当前选中的行，使用界面当前设置的字体大小，否则使用配置中的字体大小
            current_line_index = getattr(self, 'selected_line_index', 0)
            if i == current_line_index and hasattr(self, 'parent') and hasattr(self.parent(), 'font_size_spinbox'):
                # 使用界面当前设置的字体大小，与单行模式保持一致
                current_font_size = self.parent().font_size_spinbox.value()
                current_font_family = self.parent().font_combo.currentText() if hasattr(self.parent(), 'font_combo') else config.font_family
                print(f"多行第{i+1}行使用界面字体设置: {current_font_family} {current_font_size}px")
            else:
                # 使用配置中的字体设置
                current_font_size = config.font_size
                current_font_family = config.font_family
                print(f"多行第{i+1}行使用配置字体设置: {current_font_family} {current_font_size}px")

            font = QFont(current_font_family, current_font_size, QFont.Bold)
            painter.setFont(font)

            # 获取文字尺寸
            font_metrics = painter.fontMetrics()
            text_width = font_metrics.horizontalAdvance(text)
            text_height = font_metrics.height()

            # 计算基础位置
            base_x = int(preview_w * config.position_x / 100)
            base_y = int(preview_h * config.position_y / 100)

            # 边界检查和位置限制 - 与单行模式保持一致，添加10px边距
            # 确保文字不会超出左边界和右边界，添加10px边距
            x = max(10, min(base_x, preview_w - text_width - 10))
            # 确保文字不会超出上边界和下边界，添加10px边距
            # y坐标是文字基线位置，所以需要确保基线位置 >= text_height + 10（避免超出上边界）
            # 同时确保基线位置 <= preview_h - 10（避免超出下边界）
            y = max(text_height + 10, min(base_y, preview_h - 10))

            # 输出调试信息，与选中框位置计算保持一致的格式
            print(f"绘制位置计算: {config.position_x}%,{config.position_y}% -> 基础({base_x},{base_y}) -> 调整后({x},{y}) [文字{text_width}x{text_height}]")

            # 如果位置被调整了，额外输出调整信息
            if x != base_x or y != base_y:
                print(f"  ↳ 第{i+1}行位置已调整: 原({base_x},{base_y}) -> 新({x},{y})")

            # 减少调试输出，只在必要时打印
            if i == 0:  # 只打印第一行的信息，避免刷屏
                print(f"多行水印绘制: 第{i+1}行 '{text}' ({len(text)}字) 位置({config.position_x}%,{config.position_y}%) -> ({x},{y})")

            # 字体已在位置计算时设置，无需重复设置

            # 绘制阴影
            if config.shadow_enabled:
                shadow_pos = QPoint(x + config.shadow_offset_x, y + config.shadow_offset_y)
                shadow_color = QColor(config.shadow_color)
                shadow_color.setAlpha(int(255 * config.opacity / 100))
                painter.setPen(QPen(shadow_color, 1))
                painter.drawText(shadow_pos, text)

            # 绘制主文字
            text_color = QColor(config.text_color)
            text_color.setAlpha(int(255 * config.opacity / 100))
            painter.setPen(QPen(text_color, 1))
            painter.drawText(x, y, text)

            # 只在选中、拖拽或悬停时显示标签，减少视觉混乱
            show_label = (i == self.selected_line_index or
                         (self.dragging and i == self.dragging_line_index) or
                         i == self._mouse_over_line)

            if show_label:
                # 绘制行标签（显示第几行和字数）
                label_text = f"第{i+1}行({len(text)}字)"
                label_color = QColor(255, 255, 255, 220)
                painter.setPen(QPen(label_color, 1))
                label_font = QFont("Microsoft YaHei", 8)  # 更小的字体
                painter.setFont(label_font)

                # 智能计算标签位置，避免重叠
                label_width = 70
                label_height = 14

                # 尝试在水印左上方显示标签
                label_x = max(5, x - label_width - 5)
                label_y = max(15, y - 25)

                # 如果左上方空间不够，尝试右上方
                if label_x < 5:
                    label_x = min(preview_w - label_width - 5, x + 20)

                # 如果上方空间不够，显示在下方
                if label_y < 15:
                    label_y = y + config.font_size + 5

                # 确保标签不超出边界
                label_x = max(5, min(label_x, preview_w - label_width - 5))
                label_y = max(15, min(label_y, preview_h - 5))

                # 绘制标签背景（半透明黑色）
                label_rect = QRect(label_x - 2, label_y - 10, label_width, label_height)
                painter.fillRect(label_rect, QColor(0, 0, 0, 150))

                # 绘制标签文字
                painter.drawText(label_x, label_y, label_text)

            # 绘制边界框和状态指示 - 使用与水印绘制完全相同的位置计算
            # 重新计算位置，确保与上面的绘制逻辑完全一致
            baseline_x = x  # 使用与绘制时相同的x坐标
            baseline_y = y  # 使用与绘制时相同的y坐标

            # 获取文字尺寸来创建拖动框（重用已计算的值）
            # config, text, font, text_width, text_height 都已在上面计算过

            # 重新获取字体度量信息（确保与绘制时一致）
            font_metrics = painter.fontMetrics()  # 使用painter当前设置的字体
            ascent = font_metrics.ascent()
            descent = font_metrics.descent()

            # 正确理解drawText的坐标系统：
            # painter.drawText(baseline_x, baseline_y, text) 中：
            # - baseline_x 是文字的左边缘
            # - baseline_y 是文字的基线位置
            #
            # 文字的实际显示区域：
            text_left = baseline_x                    # 左边缘
            text_right = baseline_x + text_width      # 右边缘
            text_top = baseline_y - ascent           # 顶部（基线向上ascent距离）
            text_bottom = baseline_y + descent       # 底部（基线向下descent距离）

            # 计算文字的实际显示区域中心
            text_actual_center_x = text_left + text_width // 2
            text_actual_center_y = text_top + (text_bottom - text_top) // 2

            # 创建拖动框：以文字中心为基准，创建一个更大的框
            padding = 20  # 增加内边距，让拖动框更明显
            drag_width = text_width + padding * 2
            drag_height = (text_bottom - text_top) + padding * 2

            # 拖动框以文字中心为中心
            drag_rect = QRect(
                text_actual_center_x - drag_width // 2,   # 拖动框左边缘
                text_actual_center_y - drag_height // 2,  # 拖动框顶部
                drag_width,                               # 拖动框宽度
                drag_height                               # 拖动框高度
            )

            # 只在拖拽或调试模式下显示状态指示
            debug_mode = False  # 可以通过配置控制是否显示调试信息
            show_borders = True  # 控制是否显示边框

            # 选中状态 - 显示蓝色边框
            if i == self.selected_line_index and show_borders:
                painter.setPen(QPen(QColor(0, 123, 255, 150), 2, Qt.SolidLine))
                painter.drawRect(drag_rect)

                # 使用已计算的文字实际中心（用于绘制红点验证）
                text_center_x = text_actual_center_x
                text_center_y = text_actual_center_y

                # 在文字视觉中心绘制一个小红点，验证位置
                painter.setPen(QPen(QColor(255, 0, 0, 200), 1))
                painter.setBrush(QBrush(QColor(255, 0, 0, 200)))
                painter.drawEllipse(text_center_x - 2, text_center_y - 2, 4, 4)

                # 在拖动框中心绘制一个小绿点，验证拖动框中心
                drag_center_x = drag_rect.center().x()
                drag_center_y = drag_rect.center().y()
                painter.setPen(QPen(QColor(0, 255, 0, 200), 1))
                painter.setBrush(QBrush(QColor(0, 255, 0, 200)))
                painter.drawEllipse(drag_center_x - 2, drag_center_y - 2, 4, 4)

                # 添加可视化调试：绘制文字的实际边界
                painter.setPen(QPen(QColor(255, 255, 0, 150), 1, Qt.DashLine))
                text_bounds = QRect(text_left, text_top, text_width, text_bottom - text_top)
                painter.drawRect(text_bounds)

                # 在基线位置绘制一个小蓝点，验证基线位置
                painter.setPen(QPen(QColor(0, 0, 255, 200), 1))
                painter.setBrush(QBrush(QColor(0, 0, 255, 200)))
                painter.drawEllipse(baseline_x - 2, baseline_y - 2, 4, 4)

                print(f"绘制选中框: 第{i+1}行 '{text}'")
                print(f"  ↳ 绘制位置: ({baseline_x},{baseline_y}) [drawText的x,y参数]")
                print(f"  ↳ 字体度量: 宽{text_width} 高{text_height} 上升{ascent} 下降{descent}")
                print(f"  ↳ 显示区域: 左{text_left} 右{text_right} 顶{text_top} 底{text_bottom}")
                print(f"  ↳ 文字中心: ({text_center_x},{text_center_y}) [红点]")
                print(f"  ↳ 拖动框: {drag_rect}")
                print(f"  ↳ 框中心: ({drag_center_x},{drag_center_y}) [绿点]")
                print(f"  ↳ 中心偏差: X{text_center_x - drag_center_x:+d}, Y{text_center_y - drag_center_y:+d}")

                # 验证拖动框是否正确围绕文字
                if abs(text_center_x - drag_center_x) <= 1 and abs(text_center_y - drag_center_y) <= 1:
                    print(f"  ✅ 拖动框居中正确！")
                else:
                    print(f"  ❌ 拖动框居中错误！")

            # 拖拽状态 - 显示红色虚线边框
            if self.dragging and i == self.dragging_line_index and show_borders:
                painter.setPen(QPen(QColor(255, 0, 0, 150), 2, Qt.DashLine))
                painter.drawRect(drag_rect)

            # 悬停状态 - 显示绿色淡边框
            elif i == self._mouse_over_line and show_borders:
                painter.setPen(QPen(QColor(0, 255, 0, 100), 1, Qt.DashLine))
                painter.drawRect(drag_rect)

        painter.end()


class WatermarkConfigDialog(QDialog):
    """水印配置对话框"""

    def _safe_font_operation(self, draw, operation, *args, font=None, **kwargs):
        """安全的字体操作，处理DummyFont类型问题"""
        try:
            if font is not None and hasattr(font, 'getbbox'):
                # 使用有效的字体
                return getattr(draw, operation)(*args, font=font, **kwargs)
            else:
                # 不传递字体参数，使用默认字体
                return getattr(draw, operation)(*args, **kwargs)
        except Exception as e:
            print(f"字体操作失败: {e}")
            # 使用默认字体重试
            return getattr(draw, operation)(*args, **kwargs)

    def __init__(self, watermark_config, parent=None):
        super().__init__(parent)
        self.watermark_config = watermark_config.copy() if hasattr(watermark_config, 'copy') else WatermarkConfig()
        self.watermark_config.from_dict(watermark_config.to_dict())



        # 初始化颜色变量
        self._current_text_color = self.watermark_config.text_color
        self._current_shadow_color = self.watermark_config.shadow_color
        self._current_stroke_color = self.watermark_config.stroke_color

        # 初始化加载状态标志
        self._loading_line_config = False

        # 字体分类数据
        self.font_categories = {
            "现代字体": {
                "fonts": ["Microsoft YaHei", "PingFang SC", "Noto Sans CJK SC", "Source Han Sans SC"],
                "icon": "🔤",
                "description": "现代简洁的无衬线字体"
            },
            "创意字体": {
                "fonts": ["造字工房悦黑", "造字工房朗倩", "造字工房版黑", "造字工房尚雅"],
                "icon": "🎨",
                "description": "富有创意和设计感的字体"
            },
            "有趣字体": {
                "fonts": ["华文彩云", "华文琥珀", "华文新魏", "方正舒体"],
                "icon": "😊",
                "description": "活泼有趣的装饰性字体"
            },
            "书法字体": {
                "fonts": ["楷体", "行书", "隶书", "华文行楷", "华文新魏"],
                "icon": "✍️",
                "description": "传统书法风格字体"
            },
            "力量字体": {
                "fonts": ["黑体", "Microsoft YaHei Bold", "方正粗黑宋简体", "华文中宋"],
                "icon": "💪",
                "description": "粗壮有力的字体"
            },
            "卡通字体": {
                "fonts": ["华文彩云", "KaiTi", "华文行楷", "SimSun"],
                "icon": "🎪",
                "description": "可爱的卡通风格字体（使用系统可用字体）"
            }
        }

        # 预设颜色
        self.preset_colors = [
            "#FFFFFF", "#000000", "#FF0000", "#00FF00", "#0000FF",
            "#FFFF00", "#FF00FF", "#00FFFF", "#FFA500", "#800080",
            "#FFC0CB", "#A52A2A", "#808080", "#FFD700", "#32CD32",
            "#FF69B4", "#4169E1", "#DC143C", "#00CED1", "#FF1493"
        ]

        # 自定义字体存储
        self.custom_fonts = {}
        self.load_custom_fonts_from_config()

        self.init_ui()
        self.load_config()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("🎨 封面水印配置")
        self.setFixedSize(1600, 850)  # 增加窗口宽度和高度以适应更大的预览容器
        self.setModal(True)

        # 主布局
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(20)  # 增加间距

        # 左侧配置区域 - 进一步增加宽度确保控件可见
        config_widget = QWidget()
        config_widget.setFixedWidth(650)  # 从550增加到650，确保所有控件可见
        config_layout = QVBoxLayout(config_widget)

        # 标题
        title_label = QLabel("🎨 水印样式配置")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3498db, stop:1 #2980b9);
                color: white;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        config_layout.addWidget(title_label)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setMinimumHeight(600)  # 确保有足够高度显示所有控件

        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)

        # 添加各个配置组
        scroll_layout.addWidget(self.create_multiline_group())  # 多行模式组
        scroll_layout.addWidget(self.create_font_group())
        scroll_layout.addWidget(self.create_text_style_group())
        scroll_layout.addWidget(self.create_position_group())
        scroll_layout.addWidget(self.create_effects_group())

        scroll_area.setWidget(scroll_widget)
        config_layout.addWidget(scroll_area)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.reset_button = QPushButton("🔄 重置默认")
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #e67e22; }
        """)
        self.reset_button.clicked.connect(self.reset_to_default)
        button_layout.addWidget(self.reset_button)

        button_layout.addStretch()

        self.cancel_button = QPushButton("❌ 取消")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #c0392b; }
        """)
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)

        self.ok_button = QPushButton("✅ 确定")
        self.ok_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #229954; }
        """)
        self.ok_button.clicked.connect(self.accept)
        self.ok_button.setDefault(True)
        button_layout.addWidget(self.ok_button)

        config_layout.addLayout(button_layout)
        main_layout.addWidget(config_widget)

        # 右侧预览区域
        preview_widget = self.create_preview_widget()
        main_layout.addWidget(preview_widget)

    def create_multiline_group(self):
        """创建多行模式配置组"""
        group = QGroupBox("📝 多行水印模式")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #3498db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #f8f9fa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                background-color: #f8f9fa;
            }
        """)
        layout = QVBoxLayout(group)

        # 多行模式开关
        multiline_layout = QHBoxLayout()
        self.multiline_checkbox = QCheckBox("启用多行水印模式")
        self.multiline_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 14px;
                color: #2c3e50;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #bdc3c7;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #3498db;
                background-color: #3498db;
                border-radius: 3px;
            }
        """)
        self.multiline_checkbox.toggled.connect(self.on_multiline_mode_changed)
        multiline_layout.addWidget(self.multiline_checkbox)
        multiline_layout.addStretch()
        layout.addLayout(multiline_layout)

        # 行选择器
        line_selector_layout = QHBoxLayout()
        line_selector_layout.addWidget(QLabel("当前编辑行:"))
        self.line_selector = QComboBox()
        self.line_selector.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
                min-width: 100px;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
        """)
        self.line_selector.currentIndexChanged.connect(self.on_line_selected)
        line_selector_layout.addWidget(self.line_selector)

        # 添加/删除行按钮
        self.add_line_btn = QPushButton("➕ 添加行")
        self.add_line_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        self.add_line_btn.clicked.connect(self.add_watermark_line)
        line_selector_layout.addWidget(self.add_line_btn)

        self.remove_line_btn = QPushButton("➖ 删除行")
        self.remove_line_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.remove_line_btn.clicked.connect(self.remove_watermark_line)
        line_selector_layout.addWidget(self.remove_line_btn)

        line_selector_layout.addStretch()
        layout.addLayout(line_selector_layout)

        # 字符数设置
        char_count_layout = QHBoxLayout()
        char_count_layout.addWidget(QLabel("该行字符数:"))
        self.char_count_spinbox = QSpinBox()
        self.char_count_spinbox.setRange(1, 20)
        self.char_count_spinbox.setValue(3)
        self.char_count_spinbox.setStyleSheet("""
            QSpinBox {
                padding: 5px;
                border: 2px solid #bdc3c7;
                border-radius: 4px;
                background-color: white;
                min-width: 60px;
            }
            QSpinBox:focus {
                border-color: #3498db;
            }
        """)
        self.char_count_spinbox.valueChanged.connect(self.on_char_count_changed)
        char_count_layout.addWidget(self.char_count_spinbox)

        # 显示当前行文本
        self.current_line_text_label = QLabel("当前行文本: 示例视")
        self.current_line_text_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-style: italic;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 4px;
            }
        """)
        char_count_layout.addWidget(self.current_line_text_label)
        char_count_layout.addStretch()
        layout.addLayout(char_count_layout)

        # 文件名预览
        filename_layout = QVBoxLayout()
        filename_layout.addWidget(QLabel("文件名拆分预览:"))
        self.filename_preview_label = QLabel()
        self.filename_preview_label.setStyleSheet("""
            QLabel {
                background-color: #2c3e50;
                color: #ecf0f1;
                padding: 10px;
                border-radius: 4px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)
        self.update_filename_preview()
        filename_layout.addWidget(self.filename_preview_label)
        layout.addLayout(filename_layout)

        return group

    def on_multiline_mode_changed(self, enabled):
        """多行模式开关改变"""
        self.watermark_config.multi_line_enabled = enabled
        print(f"多行模式: {'启用' if enabled else '禁用'}")

        # 设置预览标签的模式
        if hasattr(self.preview_label, 'set_multiline_mode'):
            self.preview_label.set_multiline_mode(enabled)
            if enabled:
                self.preview_label.set_multi_line_configs(self.watermark_config.multi_line_configs)
                print(f"设置多行配置，共{len(self.watermark_config.multi_line_configs)}行")
            else:
                # 单行模式时清空多行配置
                self.preview_label.set_multi_line_configs([])
                print("清空多行配置，切换到单行模式")

        # 更新界面状态
        self.update_multiline_ui_state(enabled)

        # 如果启用多行模式，加载第一行的配置到界面
        if enabled and len(self.watermark_config.multi_line_configs) > 0:
            self.load_line_config_to_ui(0)
            # 设置行选择器为第一行
            if hasattr(self, 'line_selector'):
                self.line_selector.setCurrentIndex(0)
            print("多行模式启用，加载第1行配置到界面")

        # 统一使用预览更新
        self.update_preview()


        """创建多行预览标签"""
        # 多行预览标签已统一到单一预览标签中，无需单独创建
        print("多行预览功能已集成到统一预览标签中")

    def update_multiline_ui_state(self, enabled):
        """更新多行模式界面状态"""
        # 启用/禁用多行相关控件
        self.line_selector.setEnabled(enabled)
        self.add_line_btn.setEnabled(enabled)
        self.remove_line_btn.setEnabled(enabled and len(self.watermark_config.multi_line_configs) > 1)
        self.char_count_spinbox.setEnabled(enabled)

        # 启用/禁用基本水印参数控件
        # 字体设置控件
        if hasattr(self, 'font_combo'):
            self.font_combo.setEnabled(True)  # 多行模式下也要能调整字体
        if hasattr(self, 'font_size_spinbox'):
            self.font_size_spinbox.setEnabled(True)
        if hasattr(self, 'font_size_slider'):
            self.font_size_slider.setEnabled(True)

        # 颜色设置控件
        if hasattr(self, 'text_color_button'):
            self.text_color_button.setEnabled(True)  # 多行模式下要能调整颜色
        if hasattr(self, 'text_color_label'):
            self.text_color_label.setEnabled(True)

        # 位置设置控件
        if hasattr(self, 'position_x_spinbox'):
            self.position_x_spinbox.setEnabled(True)
        if hasattr(self, 'position_y_spinbox'):
            self.position_y_spinbox.setEnabled(True)
        if hasattr(self, 'position_x_slider'):
            self.position_x_slider.setEnabled(True)
        if hasattr(self, 'position_y_slider'):
            self.position_y_slider.setEnabled(True)

        # 透明度控件
        if hasattr(self, 'opacity_slider'):
            self.opacity_slider.setEnabled(True)

        # 阴影设置控件
        if hasattr(self, 'shadow_checkbox'):
            self.shadow_checkbox.setEnabled(True)
        if hasattr(self, 'shadow_color_button'):
            self.shadow_color_button.setEnabled(True)
        if hasattr(self, 'shadow_offset_x_spinbox'):
            self.shadow_offset_x_spinbox.setEnabled(True)
        if hasattr(self, 'shadow_offset_y_spinbox'):
            self.shadow_offset_y_spinbox.setEnabled(True)

        # 描边设置控件
        if hasattr(self, 'stroke_checkbox'):
            self.stroke_checkbox.setEnabled(True)
        if hasattr(self, 'stroke_color_button'):
            self.stroke_color_button.setEnabled(True)
        if hasattr(self, 'stroke_width_spinbox'):
            self.stroke_width_spinbox.setEnabled(True)
        if hasattr(self, 'stroke_width_slider'):
            self.stroke_width_slider.setEnabled(True)

        if enabled:
            self.update_line_selector()
            self.update_current_line_display()

    def on_line_selected(self, index):
        """行选择改变"""
        if 0 <= index < len(self.watermark_config.multi_line_configs):
            print(f"选择第{index+1}行进行编辑")
            self.load_line_config_to_ui(index)
            # 更新统一预览标签的选中行
            if hasattr(self.preview_label, 'selected_line_index'):
                self.preview_label.selected_line_index = index
                self.preview_label.update()

    def on_char_count_changed(self, count):
        """字符数改变"""
        current_index = self.line_selector.currentIndex()
        if 0 <= current_index < len(self.watermark_config.multi_line_configs):
            self.watermark_config.multi_line_configs[current_index].char_count = count
            self.update_current_line_display()
            self.update_filename_preview()
            # 更新统一预览标签
            if self.watermark_config.multi_line_enabled:
                self.preview_label.update()

    def add_watermark_line(self):
        """添加水印行"""
        new_line = MultiLineWatermarkConfig()
        new_line.char_count = 2
        new_line.position_y = 90 + len(self.watermark_config.multi_line_configs) * 5
        self.watermark_config.multi_line_configs.append(new_line)

        self.update_line_selector()
        self.update_filename_preview()
        self.remove_line_btn.setEnabled(len(self.watermark_config.multi_line_configs) > 1)

        # 更新统一预览标签的多行配置
        if hasattr(self.preview_label, 'set_multi_line_configs'):
            self.preview_label.set_multi_line_configs(self.watermark_config.multi_line_configs)

        print(f"添加新行，当前共{len(self.watermark_config.multi_line_configs)}行")

    def remove_watermark_line(self):
        """删除水印行"""
        if len(self.watermark_config.multi_line_configs) <= 1:
            return

        current_index = self.line_selector.currentIndex()
        if 0 <= current_index < len(self.watermark_config.multi_line_configs):
            self.watermark_config.multi_line_configs.pop(current_index)

            # 调整选择索引
            if current_index >= len(self.watermark_config.multi_line_configs):
                current_index = len(self.watermark_config.multi_line_configs) - 1

            self.update_line_selector()
            self.line_selector.setCurrentIndex(current_index)
            self.update_filename_preview()
            self.remove_line_btn.setEnabled(len(self.watermark_config.multi_line_configs) > 1)

            # 更新统一预览标签的多行配置
            if hasattr(self.preview_label, 'set_multi_line_configs'):
                self.preview_label.set_multi_line_configs(self.watermark_config.multi_line_configs)

            print(f"删除行，当前共{len(self.watermark_config.multi_line_configs)}行")

    def update_line_selector(self):
        """更新行选择器"""
        self.line_selector.clear()
        for i in range(len(self.watermark_config.multi_line_configs)):
            self.line_selector.addItem(f"第{i+1}行")

    def update_current_line_display(self):
        """更新当前行显示"""
        current_index = self.line_selector.currentIndex()
        if 0 <= current_index < len(self.watermark_config.multi_line_configs):
            # 计算当前行文本
            filename = "示例视频文件名.mp4"
            filename_without_ext = filename.replace('.mp4', '').replace('.avi', '').replace('.mkv', '')

            start_pos = 0
            for i in range(current_index):
                if i < len(self.watermark_config.multi_line_configs):
                    start_pos += self.watermark_config.multi_line_configs[i].char_count

            char_count = self.watermark_config.multi_line_configs[current_index].char_count
            end_pos = start_pos + char_count
            line_text = filename_without_ext[start_pos:end_pos]

            self.current_line_text_label.setText(f"当前行文本: {line_text}")

    def update_filename_preview(self):
        """更新文件名拆分预览"""
        if not hasattr(self, 'filename_preview_label'):
            return

        filename = "示例视频文件名.mp4"
        filename_without_ext = filename.replace('.mp4', '').replace('.avi', '').replace('.mkv', '')

        preview_lines = []
        start_pos = 0

        for i, config in enumerate(self.watermark_config.multi_line_configs):
            char_count = config.char_count
            end_pos = start_pos + char_count
            line_text = filename_without_ext[start_pos:end_pos]

            if line_text:
                preview_lines.append(f"第{i+1}行 ({char_count}字符): {line_text}")
            else:
                preview_lines.append(f"第{i+1}行 ({char_count}字符): (无内容)")

            start_pos = end_pos

        # 显示剩余字符
        if start_pos < len(filename_without_ext):
            remaining = filename_without_ext[start_pos:]
            preview_lines.append(f"剩余字符: {remaining}")

        self.filename_preview_label.setText("\n".join(preview_lines))

    def load_line_config_to_ui(self, line_index):
        """加载指定行配置到界面"""
        if 0 <= line_index < len(self.watermark_config.multi_line_configs):
            config = self.watermark_config.multi_line_configs[line_index]

            print(f"加载第{line_index+1}行配置到界面")

            # 临时禁用信号，避免循环更新
            self._loading_line_config = True

            try:
                # 1. 更新字符数
                self.char_count_spinbox.setValue(config.char_count)

                # 2. 更新字体设置
                if hasattr(self, 'font_combo'):
                    # 设置字体
                    font_index = self.font_combo.findText(config.font_family)
                    if font_index >= 0:
                        self.font_combo.setCurrentIndex(font_index)

                    # 设置字体大小
                    if hasattr(self, 'font_size_spinbox'):
                        self.font_size_spinbox.setValue(config.font_size)
                        # 同步滑块
                        if hasattr(self, 'font_size_slider'):
                            self.font_size_slider.setValue(config.font_size)

                # 设置字体分类（根据字体自动选择分类）
                if hasattr(self, 'font_category_combo'):
                    for i, (category, info) in enumerate(self.font_categories.items()):
                        _ = category  # 使用变量避免警告
                        if config.font_family in info["fonts"]:
                            self.font_category_combo.setCurrentIndex(i)
                            break

                # 3. 更新文字颜色
                if hasattr(self, 'text_color_button'):
                    self.text_color_button.setStyleSheet(f"background-color: {config.text_color}; border: 2px solid #333;")
                    self._current_text_color = config.text_color
                if hasattr(self, 'text_color_label'):
                    self.text_color_label.setText(config.text_color)

                # 4. 更新透明度
                if hasattr(self, 'opacity_slider'):
                    self.opacity_slider.setValue(config.opacity)
                if hasattr(self, 'opacity_label'):
                    self.opacity_label.setText(f"{config.opacity}%")

                # 5. 更新位置设置
                if hasattr(self, 'position_x_spinbox'):
                    self.position_x_spinbox.setValue(config.position_x)
                    # 同步滑块
                    if hasattr(self, 'position_x_slider'):
                        self.position_x_slider.setValue(config.position_x)
                if hasattr(self, 'position_y_spinbox'):
                    self.position_y_spinbox.setValue(config.position_y)
                    # 同步滑块
                    if hasattr(self, 'position_y_slider'):
                        self.position_y_slider.setValue(config.position_y)

                # 6. 更新阴影设置
                if hasattr(self, 'shadow_checkbox'):
                    self.shadow_checkbox.setChecked(config.shadow_enabled)
                if hasattr(self, 'shadow_color_button'):
                    self.shadow_color_button.setStyleSheet(f"background-color: {config.shadow_color}; border: 2px solid #333;")
                    self._current_shadow_color = config.shadow_color
                if hasattr(self, 'shadow_color_label'):
                    self.shadow_color_label.setText(config.shadow_color)
                if hasattr(self, 'shadow_offset_x_spinbox'):
                    self.shadow_offset_x_spinbox.setValue(config.shadow_offset_x)
                if hasattr(self, 'shadow_offset_y_spinbox'):
                    self.shadow_offset_y_spinbox.setValue(config.shadow_offset_y)

                # 7. 更新描边设置
                if hasattr(self, 'stroke_checkbox'):
                    self.stroke_checkbox.setChecked(config.stroke_enabled)
                if hasattr(self, 'stroke_color_button'):
                    self.stroke_color_button.setStyleSheet(f"background-color: {config.stroke_color}; border: 2px solid #333;")
                    self._current_stroke_color = config.stroke_color
                if hasattr(self, 'stroke_color_label'):
                    self.stroke_color_label.setText(config.stroke_color)
                if hasattr(self, 'stroke_width_spinbox'):
                    self.stroke_width_spinbox.setValue(config.stroke_width)
                    # 同步滑块
                    if hasattr(self, 'stroke_width_slider'):
                        self.stroke_width_slider.setValue(config.stroke_width)

                print(f"第{line_index+1}行配置加载完成")

            finally:
                # 重新启用信号
                self._loading_line_config = False

            self.update_current_line_display()

    def save_position_to_line(self, line_index):
        """专门保存位置信息到指定行"""
        if not hasattr(self, 'watermark_config') or not self.watermark_config.multi_line_enabled:
            return

        if 0 <= line_index < len(self.watermark_config.multi_line_configs):
            config = self.watermark_config.multi_line_configs[line_index]

            # 只保存位置信息
            if hasattr(self, 'position_x_spinbox'):
                config.position_x = self.position_x_spinbox.value()
            if hasattr(self, 'position_y_spinbox'):
                config.position_y = self.position_y_spinbox.value()

            print(f"专门保存第{line_index+1}行位置: ({config.position_x}%, {config.position_y}%)")

    def load_ui_config_from_line(self, line_index):
        """从指定行加载配置到界面控件"""
        if not hasattr(self, 'watermark_config') or not self.watermark_config.multi_line_enabled:
            return

        if 0 <= line_index < len(self.watermark_config.multi_line_configs):
            config = self.watermark_config.multi_line_configs[line_index]

            # 设置标志，避免在加载过程中触发保存
            self._loading_config = True

            try:
                # 1. 加载字数设置
                if hasattr(self, 'char_count_spinbox'):
                    self.char_count_spinbox.setValue(config.char_count)

                # 2. 加载位置设置
                if hasattr(self, 'position_x_spinbox'):
                    self.position_x_spinbox.setValue(config.position_x)
                if hasattr(self, 'position_y_spinbox'):
                    self.position_y_spinbox.setValue(config.position_y)

                # 3. 加载字体设置
                if hasattr(self, 'font_size_spinbox'):
                    self.font_size_spinbox.setValue(config.font_size)

                print(f"从第{line_index+1}行加载配置: 字数({config.char_count}) 位置({config.position_x}%,{config.position_y}%) 字体({config.font_size}px)")

            finally:
                self._loading_config = False

    def save_ui_config_to_line(self, line_index, save_position=True):
        """将界面配置保存到指定行

        Args:
            line_index: 行索引
            save_position: 是否保存位置信息，默认True
        """
        if 0 <= line_index < len(self.watermark_config.multi_line_configs):
            config = self.watermark_config.multi_line_configs[line_index]

            # 检查是否正在加载配置（避免循环更新）
            if hasattr(self, '_loading_line_config') and self._loading_line_config:
                return

            print(f"保存界面配置到第{line_index+1}行")

            # 1. 保存字符数
            if hasattr(self, 'char_count_spinbox'):
                config.char_count = self.char_count_spinbox.value()

            # 2. 保存字体设置
            if hasattr(self, 'font_combo'):
                config.font_family = self.font_combo.currentText()
            if hasattr(self, 'font_size_spinbox'):
                config.font_size = self.font_size_spinbox.value()
            # 字体分类会根据字体自动确定，不需要单独保存

            # 3. 保存文字颜色
            if hasattr(self, 'text_color_button') and hasattr(self, '_current_text_color'):
                config.text_color = self._current_text_color
            elif hasattr(self, 'text_color_label'):
                config.text_color = self.text_color_label.text()

            # 4. 保存透明度
            if hasattr(self, 'opacity_slider'):
                config.opacity = self.opacity_slider.value()

            # 5. 保存位置设置（只在允许且非拖拽状态下从控件读取）
            if save_position and (not hasattr(self, '_updating_position_controls') or not self._updating_position_controls):
                # 在多行模式下，只有当前选中行的位置才从控件读取
                # 其他行的位置应该保持拖拽设置的值
                current_line_index = self.line_selector.currentIndex() if hasattr(self, 'line_selector') else 0
                if line_index == current_line_index:
                    if hasattr(self, 'position_x_spinbox'):
                        config.position_x = self.position_x_spinbox.value()
                    if hasattr(self, 'position_y_spinbox'):
                        config.position_y = self.position_y_spinbox.value()
                    print(f"从控件读取第{line_index+1}行位置: ({config.position_x}%, {config.position_y}%)")
                else:
                    print(f"保持第{line_index+1}行拖拽位置: ({config.position_x}%, {config.position_y}%)")
            elif not save_position:
                print(f"跳过第{line_index+1}行位置保存，保持原位置: ({config.position_x}%, {config.position_y}%)")

            # 6. 保存阴影设置
            if hasattr(self, 'shadow_checkbox'):
                config.shadow_enabled = self.shadow_checkbox.isChecked()
            if hasattr(self, 'shadow_color_button') and hasattr(self, '_current_shadow_color'):
                config.shadow_color = self._current_shadow_color
            elif hasattr(self, 'shadow_color_label'):
                config.shadow_color = self.shadow_color_label.text()
            if hasattr(self, 'shadow_offset_x_spinbox'):
                config.shadow_offset_x = self.shadow_offset_x_spinbox.value()
            if hasattr(self, 'shadow_offset_y_spinbox'):
                config.shadow_offset_y = self.shadow_offset_y_spinbox.value()

            # 7. 保存描边设置
            if hasattr(self, 'stroke_checkbox'):
                config.stroke_enabled = self.stroke_checkbox.isChecked()
            if hasattr(self, 'stroke_color_button') and hasattr(self, '_current_stroke_color'):
                config.stroke_color = self._current_stroke_color
            elif hasattr(self, 'stroke_color_label'):
                config.stroke_color = self.stroke_color_label.text()
            if hasattr(self, 'stroke_width_spinbox'):
                config.stroke_width = self.stroke_width_spinbox.value()

            print(f"第{line_index+1}行配置保存完成")

            # 更新统一预览标签
            if self.watermark_config.multi_line_enabled:
                self.preview_label.update()

    def on_position_setting_changed(self):
        """位置设置改变时的专门处理"""
        # 如果正在加载配置，不要保存
        if hasattr(self, '_loading_line_config') and self._loading_line_config:
            return

        print("位置设置改变")

        # 如果是多行模式，只保存位置到当前选中的行
        if hasattr(self, 'watermark_config') and self.watermark_config.multi_line_enabled:
            if hasattr(self, 'line_selector'):
                current_index = self.line_selector.currentIndex()
                # 位置改变时只保存位置信息
                self.save_position_to_line(current_index)

        # 更新预览
        self.update_preview()

    def on_any_setting_changed(self):
        """任何设置改变时的通用处理"""
        # 如果正在加载配置，不要保存
        if hasattr(self, '_loading_line_config') and self._loading_line_config:
            return

        # 更新字数显示
        self.update_char_count()

        # 如果是多行模式，保存到当前选中的行（但不保存位置，避免位置跳跃）
        if hasattr(self, 'watermark_config') and self.watermark_config.multi_line_enabled:
            if hasattr(self, 'line_selector'):
                current_index = self.line_selector.currentIndex()
                # 调试信息：显示字体设置同步
                if hasattr(self, 'font_size_spinbox'):
                    current_font_size = self.font_size_spinbox.value()
                    print(f"多行模式字体设置同步: 界面{current_font_size}px -> 第{current_index+1}行配置")
                # 字体、颜色等设置改变时不保存位置，避免位置跳跃
                self.save_ui_config_to_line(current_index, save_position=False)

            # 多行模式也使用统一的预览更新
            self.update_preview()
        else:
            # 单行模式使用原来的预览更新
            self.update_preview()

    def on_single_char_count_changed(self):
        """单行水印字数改变处理"""
        try:
            char_count = self.single_char_count_spinbox.value()
            print(f"单行水印字数设置为: {char_count}字")

            # 保存到配置
            if hasattr(self, 'watermark_config'):
                self.watermark_config.single_char_count = char_count

            # 更新预览
            self.update_preview()

        except Exception as e:
            print(f"处理字数改变失败: {e}")

    def set_char_count_preset(self, count):
        """设置字数预设值"""
        try:
            self.single_char_count_spinbox.setValue(count)
            print(f"设置字数预设: {count}字")
        except Exception as e:
            print(f"设置字数预设失败: {e}")

    def get_single_watermark_text(self):
        """根据字数设置生成单行水印文本"""
        try:
            # 获取字数设置
            char_count = self.watermark_config.single_char_count if hasattr(self, 'watermark_config') else 6

            # 基础示例文本
            base_text = "示例视频文件名"

            # 根据字数截取或扩展文本
            if len(base_text) >= char_count:
                return base_text[:char_count]
            else:
                # 如果基础文本不够长，重复使用
                repeated_text = base_text * ((char_count // len(base_text)) + 1)
                return repeated_text[:char_count]

        except Exception as e:
            print(f"生成单行水印文本失败: {e}")
            return "示例视频文件"

    def update_char_count(self):
        """更新字数显示（保持兼容性）"""
        try:
            # 这个方法现在主要用于初始化
            if hasattr(self, 'single_char_count_spinbox'):
                char_count = self.single_char_count_spinbox.value()
                print(f"当前字数设置: {char_count}字")

        except Exception as e:
            print(f"更新字数显示失败: {e}")

    def on_multi_line_position_changed(self, line_index, x_percent, y_percent):
        """多行水印位置改变"""
        if 0 <= line_index < len(self.watermark_config.multi_line_configs):
            config = self.watermark_config.multi_line_configs[line_index]
            config.position_x = x_percent
            config.position_y = y_percent
            print(f"第{line_index+1}行位置更新: ({x_percent}%, {y_percent}%)")

            # 如果拖动的是当前选中的行，需要更新界面控件
            current_line_index = self.line_selector.currentIndex()
            if line_index == current_line_index:
                print(f"更新当前行({line_index+1})的位置控件")

                # 临时禁用信号，避免循环更新
                self._updating_position_controls = True

                try:
                    # 更新位置数值框
                    if hasattr(self, 'position_x_spinbox'):
                        self.position_x_spinbox.setValue(int(x_percent))
                    if hasattr(self, 'position_y_spinbox'):
                        self.position_y_spinbox.setValue(int(y_percent))

                    # 更新位置滑块
                    if hasattr(self, 'position_x_slider'):
                        self.position_x_slider.setValue(int(x_percent))
                    if hasattr(self, 'position_y_slider'):
                        self.position_y_slider.setValue(int(y_percent))

                    print(f"位置控件已更新: X={int(x_percent)}%, Y={int(y_percent)}%")

                finally:
                    # 重新启用信号
                    self._updating_position_controls = False

    def on_multi_line_selected(self, line_index):
        """多行水印行选择"""
        if 0 <= line_index < len(self.watermark_config.multi_line_configs):
            self.line_selector.setCurrentIndex(line_index)

    def create_font_group(self):
        """创建字体选择组"""
        group = QGroupBox("🔤 字体设置")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)
        layout = QVBoxLayout(group)

        # 字体分类选择
        category_layout = QHBoxLayout()
        category_layout.addWidget(QLabel("字体分类:"))

        self.font_category_combo = QComboBox()
        for category, info in self.font_categories.items():
            self.font_category_combo.addItem(f"{info['icon']} {category}")
        self.font_category_combo.currentTextChanged.connect(self.on_font_category_changed)
        category_layout.addWidget(self.font_category_combo)
        layout.addLayout(category_layout)

        # 字体选择
        font_layout = QHBoxLayout()
        font_layout.addWidget(QLabel("字体:"))

        self.font_combo = QComboBox()
        # 初始化时填充默认字体列表
        default_fonts = ["Microsoft YaHei", "SimSun", "SimHei", "KaiTi", "Arial", "Times New Roman"]
        self.font_combo.addItems(default_fonts)
        self.font_combo.currentTextChanged.connect(self.on_font_combo_changed)
        font_layout.addWidget(self.font_combo)

        # 字体预览按钮
        self.font_preview_btn = QPushButton("👁️ 预览")
        self.font_preview_btn.setFixedWidth(60)
        self.font_preview_btn.clicked.connect(self.show_font_preview)
        font_layout.addWidget(self.font_preview_btn)

        # 自定义字体加载按钮
        self.load_font_btn = QPushButton("📁 加载字体")
        self.load_font_btn.setFixedWidth(80)
        self.load_font_btn.clicked.connect(self.load_custom_font)
        font_layout.addWidget(self.load_font_btn)
        layout.addLayout(font_layout)

        # 自定义字体列表
        if hasattr(self, 'custom_fonts') and self.custom_fonts:
            custom_layout = QHBoxLayout()
            custom_layout.addWidget(QLabel("自定义字体:"))

            self.custom_font_combo = QComboBox()
            self.custom_font_combo.addItems(list(self.custom_fonts.keys()))
            self.custom_font_combo.currentTextChanged.connect(self.on_custom_font_selected)
            custom_layout.addWidget(self.custom_font_combo)
            layout.addLayout(custom_layout)

        # 字体大小
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("字体大小:"))

        self.font_size_spinbox = QSpinBox()
        self.font_size_spinbox.setRange(12, 200)  # 扩大范围以支持更大字体
        self.font_size_spinbox.setValue(100)  # 修改默认值为100px
        self.font_size_spinbox.setSuffix(" px")
        self.font_size_spinbox.valueChanged.connect(self.on_any_setting_changed)
        size_layout.addWidget(self.font_size_spinbox)

        # 字体大小滑块
        self.font_size_slider = QSlider(Qt.Horizontal)
        self.font_size_slider.setRange(12, 200)  # 扩大范围以支持更大字体
        self.font_size_slider.setValue(100)  # 修改默认值为100px
        self.font_size_slider.valueChanged.connect(self.font_size_spinbox.setValue)
        self.font_size_spinbox.valueChanged.connect(self.font_size_slider.setValue)
        size_layout.addWidget(self.font_size_slider)
        layout.addLayout(size_layout)

        # 添加字数设置控件
        char_count_layout = QHBoxLayout()
        char_count_layout.addWidget(QLabel("水印字数:"))

        self.single_char_count_spinbox = QSpinBox()
        self.single_char_count_spinbox.setRange(1, 50)  # 单行水印字数范围1-50
        self.single_char_count_spinbox.setValue(6)  # 默认6个字符
        self.single_char_count_spinbox.setSuffix(" 字")
        self.single_char_count_spinbox.setToolTip("设置单行水印显示的字符数量")
        self.single_char_count_spinbox.valueChanged.connect(self.on_single_char_count_changed)
        char_count_layout.addWidget(self.single_char_count_spinbox)

        # 添加字数预设按钮
        preset_char_layout = QHBoxLayout()
        preset_chars = [
            ("短", 4),
            ("中", 6),
            ("长", 10),
            ("超长", 15)
        ]

        for name, count in preset_chars:
            btn = QPushButton(f"{name}({count}字)")
            btn.setMaximumWidth(70)
            btn.clicked.connect(lambda _=None, c=count: self.set_char_count_preset(c))
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #e8f4fd;
                    border: 1px solid #3498db;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background-color: #d4edda;
                }
                QPushButton:pressed {
                    background-color: #c3e6cb;
                }
            """)
            preset_char_layout.addWidget(btn)

        char_count_layout.addLayout(preset_char_layout)
        char_count_layout.addStretch()
        layout.addLayout(char_count_layout)

        # 添加字体大小预设按钮
        preset_layout = QHBoxLayout()
        preset_sizes = [
            ("小", 80),
            ("中", 100),
            ("大", 120),
            ("超大", 150)
        ]

        for name, size in preset_sizes:
            btn = QPushButton(f"{name}({size}px)")
            btn.setMaximumWidth(80)
            btn.clicked.connect(lambda _=None, s=size: self.set_font_size_preset(s))
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #f0f0f0;
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-size: 11px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
                QPushButton:pressed {
                    background-color: #d0d0d0;
                }
            """)
            preset_layout.addWidget(btn)

        preset_layout.addStretch()
        layout.addLayout(preset_layout)
        layout.addLayout(size_layout)

        return group

    def set_font_size_preset(self, size):
        """设置字体大小预设"""
        self.font_size_spinbox.setValue(size)
        print(f"设置字体大小预设: {size}px")

    def create_text_style_group(self):
        """创建文字样式组"""
        group = QGroupBox("🎨 文字样式")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)
        layout = QVBoxLayout(group)

        # 文字颜色
        color_layout = QHBoxLayout()
        color_layout.addWidget(QLabel("文字颜色:"))

        self.text_color_button = QPushButton()
        self.text_color_button.setFixedSize(40, 30)
        self.text_color_button.setStyleSheet("background-color: #FFFFFF; border: 2px solid #333;")
        self.text_color_button.clicked.connect(self.choose_text_color)
        color_layout.addWidget(self.text_color_button)

        self.text_color_label = QLabel("#FFFFFF")
        color_layout.addWidget(self.text_color_label)

        # 预设颜色
        preset_layout = QHBoxLayout()
        for i, color in enumerate(self.preset_colors[:10]):
            _ = i  # 使用变量避免警告
            btn = QPushButton()
            btn.setFixedSize(25, 25)
            btn.setStyleSheet(f"background-color: {color}; border: 1px solid #333; border-radius: 3px;")
            btn.clicked.connect(lambda _=None, c=color: self.set_text_color(c))
            preset_layout.addWidget(btn)
        color_layout.addLayout(preset_layout)
        layout.addLayout(color_layout)

        # 透明度
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(QLabel("透明度:"))

        self.opacity_slider = QSlider(Qt.Horizontal)
        self.opacity_slider.setRange(0, 100)
        self.opacity_slider.setValue(80)
        self.opacity_slider.valueChanged.connect(self.update_opacity_label)
        self.opacity_slider.valueChanged.connect(self.on_any_setting_changed)
        opacity_layout.addWidget(self.opacity_slider)

        self.opacity_label = QLabel("80%")
        self.opacity_label.setFixedWidth(40)
        opacity_layout.addWidget(self.opacity_label)
        layout.addLayout(opacity_layout)

        return group

    def create_position_group(self):
        """创建位置控制组"""
        group = QGroupBox("📍 位置设置")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)
        layout = QVBoxLayout(group)

        # 水平位置
        h_layout = QHBoxLayout()
        h_layout.addWidget(QLabel("水平位置:"))

        self.position_x_spinbox = QSpinBox()
        self.position_x_spinbox.setRange(0, 100)
        self.position_x_spinbox.setValue(50)
        self.position_x_spinbox.setSuffix(" %")
        self.position_x_spinbox.valueChanged.connect(self.on_position_setting_changed)
        h_layout.addWidget(self.position_x_spinbox)

        self.position_x_slider = QSlider(Qt.Horizontal)
        self.position_x_slider.setRange(0, 100)
        self.position_x_slider.setValue(50)
        self.position_x_slider.valueChanged.connect(self.position_x_spinbox.setValue)
        self.position_x_spinbox.valueChanged.connect(self.position_x_slider.setValue)
        h_layout.addWidget(self.position_x_slider)
        layout.addLayout(h_layout)

        # 垂直位置
        v_layout = QHBoxLayout()
        v_layout.addWidget(QLabel("垂直位置:"))

        self.position_y_spinbox = QSpinBox()
        self.position_y_spinbox.setRange(0, 100)
        self.position_y_spinbox.setValue(90)
        self.position_y_spinbox.setSuffix(" %")
        self.position_y_spinbox.valueChanged.connect(self.on_position_setting_changed)
        v_layout.addWidget(self.position_y_spinbox)

        self.position_y_slider = QSlider(Qt.Horizontal)
        self.position_y_slider.setRange(0, 100)
        self.position_y_slider.setValue(90)
        self.position_y_slider.valueChanged.connect(self.position_y_spinbox.setValue)
        self.position_y_spinbox.valueChanged.connect(self.position_y_slider.setValue)
        v_layout.addWidget(self.position_y_slider)
        layout.addLayout(v_layout)

        # 位置预设
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel("快速定位:"))

        positions = [
            ("左上", 10, 10), ("中上", 50, 10), ("右上", 90, 10),
            ("左中", 10, 50), ("居中", 50, 50), ("右中", 90, 50),
            ("左下", 10, 90), ("中下", 50, 90), ("右下", 90, 90)
        ]

        for name, x, y in positions:
            btn = QPushButton(name)
            btn.setFixedSize(40, 25)
            btn.clicked.connect(lambda _=None, px=x, py=y: self.set_position(px, py))
            preset_layout.addWidget(btn)

        layout.addLayout(preset_layout)
        return group

    def create_effects_group(self):
        """创建特效组"""
        group = QGroupBox("✨ 文字特效")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
        """)
        layout = QVBoxLayout(group)

        # 阴影设置
        shadow_group = QGroupBox("🌫️ 阴影效果")
        shadow_layout = QVBoxLayout(shadow_group)

        # 阴影开关
        self.shadow_checkbox = QCheckBox("启用阴影")
        self.shadow_checkbox.setChecked(True)
        self.shadow_checkbox.toggled.connect(self.on_shadow_toggled)
        self.shadow_checkbox.toggled.connect(self.on_any_setting_changed)
        shadow_layout.addWidget(self.shadow_checkbox)

        # 阴影颜色
        shadow_color_layout = QHBoxLayout()
        shadow_color_layout.addWidget(QLabel("阴影颜色:"))

        self.shadow_color_button = QPushButton()
        self.shadow_color_button.setFixedSize(40, 30)
        self.shadow_color_button.setStyleSheet("background-color: #000000; border: 2px solid #333;")
        self.shadow_color_button.clicked.connect(self.choose_shadow_color)
        shadow_color_layout.addWidget(self.shadow_color_button)

        self.shadow_color_label = QLabel("#000000")
        shadow_color_layout.addWidget(self.shadow_color_label)
        shadow_layout.addLayout(shadow_color_layout)

        # 阴影偏移
        offset_layout = QHBoxLayout()
        offset_layout.addWidget(QLabel("水平偏移:"))

        self.shadow_offset_x_spinbox = QSpinBox()
        self.shadow_offset_x_spinbox.setRange(-20, 20)
        self.shadow_offset_x_spinbox.setValue(2)
        self.shadow_offset_x_spinbox.setSuffix(" px")
        self.shadow_offset_x_spinbox.valueChanged.connect(self.on_any_setting_changed)
        offset_layout.addWidget(self.shadow_offset_x_spinbox)

        offset_layout.addWidget(QLabel("垂直偏移:"))

        self.shadow_offset_y_spinbox = QSpinBox()
        self.shadow_offset_y_spinbox.setRange(-20, 20)
        self.shadow_offset_y_spinbox.setValue(2)
        self.shadow_offset_y_spinbox.setSuffix(" px")
        self.shadow_offset_y_spinbox.valueChanged.connect(self.on_any_setting_changed)
        offset_layout.addWidget(self.shadow_offset_y_spinbox)
        shadow_layout.addLayout(offset_layout)



        layout.addWidget(shadow_group)

        # 描边设置
        stroke_group = QGroupBox("🖊️ 描边效果")
        stroke_layout = QVBoxLayout(stroke_group)

        # 描边开关
        self.stroke_checkbox = QCheckBox("启用描边")
        self.stroke_checkbox.setChecked(False)
        self.stroke_checkbox.toggled.connect(self.on_stroke_toggled)
        self.stroke_checkbox.toggled.connect(self.on_any_setting_changed)
        stroke_layout.addWidget(self.stroke_checkbox)

        # 描边颜色
        stroke_color_layout = QHBoxLayout()
        stroke_color_layout.addWidget(QLabel("描边颜色:"))

        self.stroke_color_button = QPushButton()
        self.stroke_color_button.setFixedSize(40, 30)
        self.stroke_color_button.setStyleSheet("background-color: #000000; border: 2px solid #333;")
        self.stroke_color_button.clicked.connect(self.choose_stroke_color)
        stroke_color_layout.addWidget(self.stroke_color_button)

        self.stroke_color_label = QLabel("#000000")
        stroke_color_layout.addWidget(self.stroke_color_label)
        stroke_layout.addLayout(stroke_color_layout)

        # 描边宽度
        width_layout = QHBoxLayout()
        width_layout.addWidget(QLabel("描边宽度:"))

        self.stroke_width_spinbox = QSpinBox()
        self.stroke_width_spinbox.setRange(1, 10)
        self.stroke_width_spinbox.setValue(2)
        self.stroke_width_spinbox.setSuffix(" px")
        self.stroke_width_spinbox.valueChanged.connect(self.on_any_setting_changed)
        width_layout.addWidget(self.stroke_width_spinbox)

        self.stroke_width_slider = QSlider(Qt.Horizontal)
        self.stroke_width_slider.setRange(1, 10)
        self.stroke_width_slider.setValue(2)
        self.stroke_width_slider.valueChanged.connect(self.stroke_width_spinbox.setValue)
        self.stroke_width_spinbox.valueChanged.connect(self.stroke_width_slider.setValue)
        width_layout.addWidget(self.stroke_width_slider)
        stroke_layout.addLayout(width_layout)

        layout.addWidget(stroke_group)
        return group

    def create_preview_widget(self):
        """创建预览区域"""
        widget = QWidget()
        widget.setFixedWidth(800)  # 增大预览区域宽度以适应更大的预览容器
        layout = QVBoxLayout(widget)

        # 预览标题
        title_label = QLabel("👁️ 实时预览")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e74c3c, stop:1 #c0392b);
                color: white;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)

        # 预览说明
        info_label = QLabel("💡 提示：可以直接拖拽水印到任意位置")
        info_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 12px;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 4px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(info_label)

        # 预览区域容器 - 按1920x1080比例缩放到适合界面的尺寸
        self.preview_container = QWidget()  # 保存为实例变量
        # 计算预览尺寸：1920x1080按比例缩放到适合界面的尺寸
        # 使用1920x1080的1/2.5比例 = 768x432，增大预览容器
        self.preview_width = 768
        self.preview_height = 432
        self.cover_width = 1920  # 封面实际宽度
        self.cover_height = 1080  # 封面实际高度
        self.preview_scale = self.preview_width / self.cover_width  # 缩放比例

        self.preview_container.setFixedSize(self.preview_width, self.preview_height)
        self.preview_container.setStyleSheet("""
            QWidget {
                border: 3px solid #34495e;
                border-radius: 8px;
                background-color: #2c3e50;
            }
        """)

        # 创建自定义预览标签 - 暂时使用完整尺寸，先解决显示问题
        self.preview_label = DraggableWatermarkLabel(self.preview_container)
        # 使用完整尺寸，边框通过容器样式显示
        self.preview_label.setGeometry(0, 0, self.preview_width, self.preview_height)
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setScaledContents(True)

        # 设置预览标签的封面尺寸信息，用于准确计算水印位置
        self.preview_label.set_cover_dimensions(self.cover_width, self.cover_height)

        # 设置预览标签的预览尺寸信息
        self.preview_label.set_preview_dimensions(self.preview_width, self.preview_height)

        # 连接拖拽信号
        self.preview_label.position_changed.connect(self.on_watermark_position_changed)
        self.preview_label.line_selected.connect(self.on_line_selected)
        self.preview_label.multi_line_position_changed.connect(self.on_multi_line_position_changed)

        layout.addWidget(self.preview_container)

        # 预览控制按钮
        control_layout = QHBoxLayout()

        self.refresh_btn = QPushButton("🔄 刷新预览")
        self.refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #2980b9; }
        """)
        self.refresh_btn.clicked.connect(self.update_preview)
        control_layout.addWidget(self.refresh_btn)

        self.save_preview_btn = QPushButton("💾 保存预览")
        self.save_preview_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover { background-color: #8e44ad; }
        """)
        self.save_preview_btn.clicked.connect(self.save_preview_image)
        control_layout.addWidget(self.save_preview_btn)

        layout.addLayout(control_layout)

        # 位置信息显示
        self.position_info_label = QLabel("位置: (50%, 90%)")
        self.position_info_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 11px;
                padding: 5px;
                background-color: #ecf0f1;
                border-radius: 4px;
                margin-top: 5px;
            }
        """)
        layout.addWidget(self.position_info_label)

        layout.addStretch()
        return widget

    def on_font_category_changed(self, category_text):
        """字体分类改变"""
        category = category_text.split(' ', 1)[1]  # 移除图标
        if category in self.font_categories:
            fonts = self.font_categories[category]["fonts"]
            self.font_combo.clear()
            self.font_combo.addItems(fonts)
            # 设置默认选择第一个字体
            if fonts:
                self.font_combo.setCurrentIndex(0)
            self.update_preview()

    def on_font_combo_changed(self, font_name):
        """字体下拉框改变"""
        print(f"字体切换为: {font_name}")  # 调试信息

        # 如果是多行模式，保存到当前选中的行（字体切换不保存位置）
        if hasattr(self, 'watermark_config') and self.watermark_config.multi_line_enabled:
            current_index = self.line_selector.currentIndex() if hasattr(self, 'line_selector') else 0
            self.save_ui_config_to_line(current_index, save_position=False)

        self.update_preview()

        # 测试字体配置保存
        self.test_font_config_save()

    def test_font_config_save(self):
        """测试字体配置保存（调试用）"""
        if hasattr(self, 'watermark_config') and self.watermark_config.multi_line_enabled:
            current_index = self.line_selector.currentIndex() if hasattr(self, 'line_selector') else 0
            if current_index < len(self.watermark_config.multi_line_configs):
                config = self.watermark_config.multi_line_configs[current_index]
                print(f"字体配置测试 - 第{current_index+1}行:")
                print(f"  配置中的字体: {config.font_family}")
                print(f"  配置中的大小: {config.font_size}px")
                if hasattr(self, 'font_combo'):
                    print(f"  界面当前字体: {self.font_combo.currentText()}")
                if hasattr(self, 'font_size_spinbox'):
                    print(f"  界面当前大小: {self.font_size_spinbox.value()}px")

    def show_font_preview(self):
        """显示字体预览"""
        current_font = self.font_combo.currentText()
        compatibility = self.get_font_compatibility_info(current_font)

        # 创建预览对话框
        dialog = QDialog(self)
        dialog.setWindowTitle(f"字体预览 - {current_font}")
        dialog.setFixedSize(400, 300)

        layout = QVBoxLayout(dialog)

        # 兼容性信息
        compat_label = QLabel(f"兼容性状态: {compatibility}")
        compat_label.setStyleSheet("font-weight: bold; padding: 5px;")
        layout.addWidget(compat_label)

        # 预览文本
        preview_texts = [
            f"字体名称: {current_font}",
            "示例视频文件",
            "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
            "abcdefghijklmnopqrstuvwxyz",
            "0123456789",
            "中文字体预览效果",
            "特殊符号: !@#$%^&*()"
        ]

        for text in preview_texts:
            label = QLabel(text)
            try:
                font_path = self.get_font_path_for_preview(current_font)
                if font_path:
                    label.setStyleSheet(f"font-family: '{current_font}'; font-size: 14px; padding: 2px;")
                else:
                    label.setStyleSheet("font-size: 14px; padding: 2px; color: #999;")
                    label.setText(f"{text} (字体不可用)")
            except:
                label.setStyleSheet("font-size: 14px; padding: 2px; color: #999;")
                label.setText(f"{text} (字体加载失败)")

            layout.addWidget(label)

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(dialog.accept)
        layout.addWidget(close_btn)

        dialog.exec_()

    def set_text_color(self, color):
        """设置文字颜色"""
        self.text_color_label.setText(color)
        self.text_color_button.setStyleSheet(f"background-color: {color}; border: 2px solid #333;")
        self._current_text_color = color  # 保存当前颜色

        # 如果是多行模式，需要保存到当前选中的行并更新多行预览（颜色改变不保存位置）
        if hasattr(self, 'watermark_config') and self.watermark_config.multi_line_enabled:
            if hasattr(self, 'line_selector'):
                current_index = self.line_selector.currentIndex()
                self.save_ui_config_to_line(current_index, save_position=False)
            # 多行模式也使用统一的预览更新
            self.update_preview()
        else:
            # 单行模式使用原来的预览更新
            self.update_preview()

    def set_position(self, x, y):
        """设置位置"""
        self.position_x_spinbox.setValue(x)
        self.position_y_spinbox.setValue(y)
        self.preview_label.set_watermark_position_percent(x, y)
        self.update_position_info()

    def on_watermark_position_changed(self, x_percent, y_percent):
        """水印位置改变（来自拖拽）"""
        self.position_x_spinbox.setValue(x_percent)
        self.position_y_spinbox.setValue(y_percent)
        self.update_position_info()

    def update_position_info(self):
        """更新位置信息显示"""
        x = self.position_x_spinbox.value()
        y = self.position_y_spinbox.value()
        self.position_info_label.setText(f"位置: ({x}%, {y}%)")

    def update_opacity_label(self, value):
        """更新透明度标签"""
        self.opacity_label.setText(f"{value}%")

    def on_shadow_toggled(self, checked):
        """阴影开关状态改变"""
        self.shadow_color_button.setEnabled(checked)
        self.shadow_color_label.setEnabled(checked)
        self.shadow_offset_x_spinbox.setEnabled(checked)
        self.shadow_offset_y_spinbox.setEnabled(checked)

    def on_stroke_toggled(self, checked):
        """描边开关状态改变"""
        self.stroke_color_button.setEnabled(checked)
        self.stroke_color_label.setEnabled(checked)
        self.stroke_width_spinbox.setEnabled(checked)
        self.stroke_width_slider.setEnabled(checked)

    def choose_text_color(self):
        """选择文字颜色"""
        if not COLOR_DIALOG_AVAILABLE:
            return

        current_color = QColor(self.text_color_label.text())
        color = QColorDialog.getColor(current_color, self, "选择文字颜色")
        if color.isValid():
            color_hex = color.name()
            self.set_text_color(color_hex)
            self.on_any_setting_changed()  # 触发设置保存

    def choose_shadow_color(self):
        """选择阴影颜色"""
        if not COLOR_DIALOG_AVAILABLE:
            return

        current_color = QColor(self.shadow_color_label.text())
        color = QColorDialog.getColor(current_color, self, "选择阴影颜色")
        if color.isValid():
            color_hex = color.name()
            self.shadow_color_label.setText(color_hex)
            self.shadow_color_button.setStyleSheet(f"background-color: {color_hex}; border: 2px solid #333;")
            self._current_shadow_color = color_hex  # 保存当前颜色
            self.on_any_setting_changed()  # 触发设置保存

    def choose_stroke_color(self):
        """选择描边颜色"""
        if not COLOR_DIALOG_AVAILABLE:
            return

        current_color = QColor(self.stroke_color_label.text())
        color = QColorDialog.getColor(current_color, self, "选择描边颜色")
        if color.isValid():
            color_hex = color.name()
            self.stroke_color_label.setText(color_hex)
            self.stroke_color_button.setStyleSheet(f"background-color: {color_hex}; border: 2px solid #333;")
            self._current_stroke_color = color_hex  # 保存当前颜色
            self.on_any_setting_changed()  # 触发设置保存

    def save_preview_image(self):
        """保存预览图片"""
        if not self.preview_label.pixmap():
            QMessageBox.information(self, "提示", "没有可保存的预览图片")
            return

        from PyQt5.QtWidgets import QFileDialog
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存预览图片", "watermark_preview.png",
            "PNG图片 (*.png);;JPEG图片 (*.jpg);;所有文件 (*)"
        )

        if filename:
            pixmap = self.preview_label.pixmap()
            if pixmap and pixmap.save(filename):
                QMessageBox.information(self, "成功", f"预览图片已保存到:\n{filename}")
            else:
                QMessageBox.warning(self, "错误", "保存预览图片失败")

    def load_custom_font(self):
        """加载自定义字体文件"""
        from PyQt5.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择字体文件", "",
            "字体文件 (*.ttf *.otf);;TrueType字体 (*.ttf);;OpenType字体 (*.otf);;所有文件 (*)"
        )

        if file_path:
            try:
                # 测试字体是否可用
                test_font = ImageFont.truetype(file_path, 20)
                print(f"字体测试成功: {test_font}")  # 使用变量避免警告

                # 获取字体名称
                import os
                font_name = os.path.splitext(os.path.basename(file_path))[0]

                # 检查是否已存在
                if font_name in self.custom_fonts:
                    reply = QMessageBox.question(
                        self, "字体已存在",
                        f"字体 '{font_name}' 已存在，是否替换？",
                        QMessageBox.Yes | QMessageBox.No
                    )
                    if reply != QMessageBox.Yes:
                        return

                # 保存字体路径
                self.custom_fonts[font_name] = file_path

                # 添加到字体列表
                if not hasattr(self, 'custom_font_combo'):
                    # 如果还没有自定义字体下拉框，创建一个
                    self.create_custom_font_combo()
                else:
                    self.custom_font_combo.addItem(font_name)

                # 保存到配置
                self.save_custom_fonts_to_config()

                QMessageBox.information(self, "成功", f"字体 '{font_name}' 加载成功！")

            except Exception as e:
                QMessageBox.warning(self, "错误", f"加载字体失败：{str(e)}")

    def create_custom_font_combo(self):
        """创建自定义字体下拉框"""
        # 这个方法在需要时动态创建自定义字体选择器
        # 实际实现会根据UI布局调整
        pass

    def on_custom_font_selected(self, font_name):
        """选择自定义字体"""
        if font_name in self.custom_fonts:
            # 将自定义字体路径设置为当前字体
            self.current_custom_font = self.custom_fonts[font_name]
            self.update_preview()

    def load_custom_fonts_from_config(self):
        """从配置文件加载自定义字体"""
        try:
            import json
            import os

            config_file = "custom_fonts.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.custom_fonts = json.load(f)
        except Exception as e:
            print(f"加载自定义字体配置失败: {e}")
            self.custom_fonts = {}

    def save_custom_fonts_to_config(self):
        """保存自定义字体到配置文件"""
        try:
            import json

            config_file = "custom_fonts.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.custom_fonts, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存自定义字体配置失败: {e}")

    def get_font_compatibility_info(self, font_family):
        """获取字体兼容性信息"""
        try:
            font_path = self.get_font_path_for_preview(font_family)
            if font_path and os.path.exists(font_path):
                return "✅ 可用"
            else:
                return "❌ 不可用"
        except:
            return "❓ 未知"

    def get_font_path_for_preview(self, font_family):
        """获取预览用的字体路径"""
        # 首先检查是否是自定义字体
        if font_family in self.custom_fonts:
            return self.custom_fonts[font_family]

        # 然后检查系统字体
        font_files = {
            "Microsoft YaHei": "msyh.ttc",
            "SimSun": "simsun.ttc",
            "SimHei": "simhei.ttf",
            "KaiTi": "simkai.ttf",
            "楷体": "simkai.ttf",
            "黑体": "simhei.ttf",
            "Arial": "arial.ttf",
            "Times New Roman": "times.ttf"
        }

        font_file = font_files.get(font_family, "msyh.ttc")
        return f"C:/Windows/Fonts/{font_file}"









    def update_preview(self):
        """更新预览效果"""
        if not PIL_AVAILABLE or not hasattr(self, 'preview_label'):
            return

        # 验证多行水印位置，确保不超出边界
        if hasattr(self, 'watermark_config') and self.watermark_config.multi_line_enabled:
            self.watermark_config.validate_multiline_positions()

        # 调试信息：显示当前字体
        current_font = self.font_combo.currentText() if hasattr(self, 'font_combo') else "未知"
        print(f"更新预览，当前字体: {current_font}")

        try:
            # 创建预览图片 - 使用完整的预览尺寸，简化处理
            img = Image.new('RGB', (self.preview_width, self.preview_height), color='#2c3e50')

            # 创建渐变背景 - 适应完整预览尺寸
            draw = ImageDraw.Draw(img)
            for y in range(self.preview_height):
                color_value = int(44 + (y / self.preview_height) * 40)  # 从深蓝到稍亮的蓝
                color = (color_value, color_value + 20, color_value + 40)
                draw.line([(0, y), (self.preview_width, y)], fill=color)

            # 添加背景装饰 - 适应完整预览尺寸
            draw.rectangle([20, 20, self.preview_width-20, self.preview_height-20], outline=(255, 255, 255, 50), width=2)
            # 居中显示背景文字
            bg_text_x = self.preview_width // 2 - 40  # 大约居中
            bg_text_y = self.preview_height // 2 - 10  # 大约居中
            # 使用安全字体操作
            font = self.get_preview_font("Microsoft YaHei", 24)
            self._safe_font_operation(draw, 'text', (bg_text_x, bg_text_y), "预览背景",
                                    font=font, fill=(255, 255, 255, 100))

            # 根据模式添加对应的水印
            if self.watermark_config.multi_line_enabled:
                # 多行模式：不在这里添加水印，由预览标签的paintEvent处理
                watermarked_img = img
                print("多行模式：跳过单行水印添加")
            else:
                # 单行模式：添加单行水印
                watermark_text = self.get_single_watermark_text()
                watermarked_img = self.add_watermark_to_image_for_preview(img, watermark_text)
                print(f"单行模式：添加水印文字 '{watermark_text}'")

            # 预览图片使用内部尺寸（减去边框），无需缩放
            if watermarked_img:
                watermarked_img = watermarked_img.convert('RGB')
                preview_img = watermarked_img  # 直接使用，无需缩放

                width, height = preview_img.size
                qimg = QImage(preview_img.tobytes(), width, height, QImage.Format_RGB888)
                pixmap = QPixmap.fromImage(qimg)

                # 保存背景图片到拖拽标签，但不直接设置pixmap
                # 这样可以保持我们自定义的paintEvent功能
                self.preview_label.background_pixmap = pixmap

                # 同时保存原始1920x1080尺寸的图像，用于准确的水印计算
                self.preview_label.full_size_image = watermarked_img

                # 更新拖拽标签的水印位置和文本
                x_percent = self.position_x_spinbox.value()
                y_percent = self.position_y_spinbox.value()
                self.preview_label.set_watermark_position_percent(x_percent, y_percent)

                # 根据模式设置预览标签的显示内容
                if self.watermark_config.multi_line_enabled:
                    # 多行模式：清空单行水印文本，启用多行模式
                    self.preview_label.watermark_text = ""
                    self.preview_label.multiline_mode = True
                    self.preview_label.multi_line_configs = self.watermark_config.multi_line_configs
                    print("设置预览标签为多行模式")
                else:
                    # 单行模式：设置单行水印文本，禁用多行模式
                    watermark_text = self.get_single_watermark_text()
                    self.preview_label.watermark_text = watermark_text
                    self.preview_label.multiline_mode = False
                    self.preview_label.multi_line_configs = []
                    print(f"设置预览标签为单行模式，文字: '{watermark_text}'")

                # 触发重绘
                self.preview_label.update()

                # 添加动画效果
                self.animate_preview_update()

        except Exception as e:
            print(f"预览更新失败: {e}")
            # 创建错误提示图片 - 适应预览尺寸
            # 使用完整预览尺寸创建错误图片
            error_img = Image.new('RGB', (self.preview_width, self.preview_height), color='#e74c3c')
            draw = ImageDraw.Draw(error_img)
            error_text_x = self.preview_width // 2 - 40
            error_text_y = self.preview_height // 2 - 10
            # 使用安全字体操作
            error_font = self.get_preview_font("Microsoft YaHei", 20)
            self._safe_font_operation(draw, 'text', (error_text_x, error_text_y), "预览失败",
                                    font=error_font, fill=(255, 255, 255))

            error_img = error_img.convert('RGB')
            qimg = QImage(error_img.tobytes(), self.preview_width, self.preview_height, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(qimg)
            self.preview_label.setPixmap(pixmap)

    def add_watermark_to_image_for_preview(self, img, text):
        """为预览图片添加水印（参数缩小一倍但视觉大小保持）"""
        if not PIL_AVAILABLE:
            return img

        try:
            # 创建副本
            watermarked = img.copy()

            # 获取当前配置
            font_family = self.font_combo.currentText()
            original_font_size = self.font_size_spinbox.value()
            # 预览中使用原始参数，保持正常大小
            preview_font_size = original_font_size  # 预览使用原始参数

            text_color = self.text_color_label.text()
            shadow_enabled = self.shadow_checkbox.isChecked()
            shadow_color = self.shadow_color_label.text()
            shadow_offset_x = self.shadow_offset_x_spinbox.value()  # 预览使用原始偏移
            shadow_offset_y = self.shadow_offset_y_spinbox.value()
            stroke_enabled = self.stroke_checkbox.isChecked()
            stroke_color = self.stroke_color_label.text()
            stroke_width = self.stroke_width_spinbox.value()  # 预览使用原始描边宽度
            position_x = self.position_x_spinbox.value()
            position_y = self.position_y_spinbox.value()
            opacity = self.opacity_slider.value()

            print(f"预览水印: 使用原始参数{original_font_size}px")

            # 加载缩小参数的字体
            font = self.get_preview_font(font_family, preview_font_size)

            # 计算文字位置
            draw = ImageDraw.Draw(watermarked)
            bbox = self._safe_font_operation(draw, 'textbbox', (0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            img_width, img_height = watermarked.size
            # 修复：使用正确的百分比位置计算，与预览保持一致
            x = int(img_width * position_x / 100)
            y = int(img_height * position_y / 100)

            print(f"封面单行水印位置: {position_x}%,{position_y}% -> ({x},{y}) 基于尺寸({img_width}x{img_height})")

            # 创建高质量渲染图层
            overlay = Image.new('RGBA', watermarked.size, (0, 0, 0, 0))
            overlay_draw = ImageDraw.Draw(overlay)

            # 绘制阴影
            if shadow_enabled:
                shadow_x = x + shadow_offset_x
                shadow_y = y + shadow_offset_y
                shadow_rgba = self.hex_to_rgba(shadow_color, opacity)
                self._safe_font_operation(overlay_draw, 'text', (shadow_x, shadow_y), text, font=font, fill=shadow_rgba)

            # 绘制描边
            if stroke_enabled:
                stroke_rgba = self.hex_to_rgba(stroke_color, opacity)
                for dx in range(-stroke_width, stroke_width + 1):
                    for dy in range(-stroke_width, stroke_width + 1):
                        if dx*dx + dy*dy <= stroke_width*stroke_width:
                            self._safe_font_operation(overlay_draw, 'text', (x + dx, y + dy), text, font=font, fill=stroke_rgba)

            # 绘制主文字
            text_rgba = self.hex_to_rgba(text_color, opacity)
            self._safe_font_operation(overlay_draw, 'text', (x, y), text, font=font, fill=text_rgba)

            # 绘制字数标签
            char_count = len(text)
            label_text = f"({char_count}字)"
            label_font = self.get_preview_font("Microsoft YaHei", max(12, preview_font_size // 2))
            label_rgba = (255, 255, 255, 180)
            # 在水印文字上方显示标签
            label_y = max(0, y - 20)
            self._safe_font_operation(overlay_draw, 'text', (x, label_y), label_text, font=label_font, fill=label_rgba)

            # 合成图像
            watermarked = watermarked.convert('RGBA')
            watermarked = Image.alpha_composite(watermarked, overlay)
            watermarked = watermarked.convert('RGB')

            return watermarked

        except Exception as e:
            print(f"预览水印添加失败: {e}")
            return img

    def add_single_watermark_to_pil_image_for_cover(self, img, text, config):
        """为封面添加单行水印（参数放大10倍）"""
        try:
            # 创建副本
            watermarked = img.copy()

            # 获取配置，参数放大10倍
            font_family = config.font_family
            original_font_size = config.font_size
            cover_font_size = int(original_font_size * 10)  # 封面参数放大10倍

            text_color = config.text_color
            shadow_enabled = True  # 假设启用阴影
            shadow_color = "#000000"  # 默认阴影颜色
            shadow_offset_x = int(2 * 10)  # 阴影偏移放大10倍
            shadow_offset_y = int(2 * 10)
            stroke_enabled = False  # 假设不启用描边
            stroke_color = "#000000"
            stroke_width = max(1, int(1 * 10))  # 描边宽度放大10倍
            position_x = config.position_x
            position_y = config.position_y
            opacity = config.opacity

            print(f"封面单行水印: 原始参数{original_font_size}px → 封面参数{cover_font_size}px (放大10倍)")

            # 加载放大的字体
            font = self.get_preview_font(font_family, cover_font_size)

            # 计算文字位置
            from PIL import ImageDraw
            draw = ImageDraw.Draw(watermarked)
            bbox = self._safe_font_operation(draw, 'textbbox', (0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            img_width, img_height = watermarked.size
            # 修复：使用正确的百分比位置计算，与预览保持一致
            x = int(img_width * position_x / 100)
            y = int(img_height * position_y / 100)

            print(f"视频水印位置: {position_x}%,{position_y}% -> ({x},{y}) 基于尺寸({img_width}x{img_height})")

            # 创建高质量渲染图层
            overlay = Image.new('RGBA', watermarked.size, (0, 0, 0, 0))
            overlay_draw = ImageDraw.Draw(overlay)

            # 绘制阴影
            if shadow_enabled:
                shadow_x = x + shadow_offset_x
                shadow_y = y + shadow_offset_y
                shadow_rgba = self.hex_to_rgba(shadow_color, opacity)
                self._safe_font_operation(overlay_draw, 'text', (shadow_x, shadow_y), text, font=font, fill=shadow_rgba)

            # 绘制描边
            if stroke_enabled:
                stroke_rgba = self.hex_to_rgba(stroke_color, opacity)
                for dx in range(-stroke_width, stroke_width + 1):
                    for dy in range(-stroke_width, stroke_width + 1):
                        if dx*dx + dy*dy <= stroke_width*stroke_width:
                            self._safe_font_operation(overlay_draw, 'text', (x + dx, y + dy), text, font=font, fill=stroke_rgba)

            # 绘制主文字
            text_rgba = self.hex_to_rgba(text_color, opacity)
            self._safe_font_operation(overlay_draw, 'text', (x, y), text, font=font, fill=text_rgba)

            # 合成图像
            watermarked = watermarked.convert('RGBA')
            watermarked = Image.alpha_composite(watermarked, overlay)
            watermarked = watermarked.convert('RGB')

            return watermarked

        except Exception as e:
            print(f"封面单行水印添加失败: {e}")
            return img

    def add_multiline_watermark_to_pil_image_for_cover(self, img, text):
        """为封面添加多行水印（参数放大10倍）"""
        try:
            # 创建副本
            watermarked = img.copy()

            print(f"封面多行水印开始: {text}")

            # 遍历多行配置
            for i, line_config in enumerate(self.watermark_config.multi_line_configs):
                if not line_config.enabled:
                    continue

                # 获取该行的文字
                line_text = self.get_line_text_for_video(text, i)
                if not line_text:
                    continue

                print(f"绘制第{i+1}行水印: '{line_text}'")

                # 获取该行配置，参数放大10倍
                font_family = line_config.font_family
                original_font_size = line_config.font_size
                cover_font_size = int(original_font_size * 10)  # 封面参数放大10倍

                text_color = line_config.text_color
                position_x = self.watermark_config.position_x
                position_y = line_config.position_y
                opacity = line_config.opacity

                print(f"封面多行第{i+1}行: 原始{original_font_size}px → 封面{cover_font_size}px")

                # 加载放大的字体
                font = self.get_preview_font(font_family, cover_font_size)

                # 计算文字位置
                from PIL import ImageDraw
                draw = ImageDraw.Draw(watermarked)
                bbox = self._safe_font_operation(draw, 'textbbox', (0, 0), line_text, font=font)
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]

                img_width, img_height = watermarked.size
                # 修复：使用正确的百分比位置计算，与预览保持一致
                x = int(img_width * position_x / 100)
                y = int(img_height * position_y / 100)

                print(f"封面多行第{i+1}行位置: {position_x}%,{position_y} -> ({x},{y}) 基于尺寸({img_width}x{img_height})")

                # 创建高质量渲染图层
                overlay = Image.new('RGBA', watermarked.size, (0, 0, 0, 0))
                overlay_draw = ImageDraw.Draw(overlay)

                # 绘制阴影（放大10倍）
                shadow_offset_x = int(2 * 10)
                shadow_offset_y = int(2 * 10)
                shadow_x = x + shadow_offset_x
                shadow_y = y + shadow_offset_y
                shadow_rgba = self.hex_to_rgba("#000000", opacity)
                self._safe_font_operation(overlay_draw, 'text', (shadow_x, shadow_y), line_text, font=font, fill=shadow_rgba)

                # 绘制主文字
                text_rgba = self.hex_to_rgba(text_color, opacity)
                self._safe_font_operation(overlay_draw, 'text', (x, y), line_text, font=font, fill=text_rgba)

                # 绘制行标签（第几行和字数）
                char_count = len(line_text)
                label_text = f"第{i+1}行({char_count}字)"
                label_font = self.get_preview_font("Microsoft YaHei", max(120, cover_font_size // 2))  # 封面标签字体也放大
                label_rgba = (255, 255, 255, 180)
                # 在水印文字上方显示标签
                label_y = max(0, y - 200)  # 封面标签偏移也放大
                self._safe_font_operation(overlay_draw, 'text', (x, label_y), label_text, font=label_font, fill=label_rgba)

                # 合成图像
                watermarked = watermarked.convert('RGBA')
                watermarked = Image.alpha_composite(watermarked, overlay)
                watermarked = watermarked.convert('RGB')

            print(f"封面多行水印完成")
            return watermarked

        except Exception as e:
            print(f"封面多行水印添加失败: {e}")
            return img

    # 重复的 add_watermark_to_image 方法已删除 - 功能已整合到其他方法中



    def get_preview_font(self, font_family, font_size):
        """获取预览字体（支持自定义字体）"""
        try:
            # 首先检查是否是自定义字体
            if hasattr(self, 'custom_fonts') and font_family in self.custom_fonts:
                font_path = self.custom_fonts[font_family]
                return ImageFont.truetype(font_path, font_size)

            # 然后检查系统字体
            font_files = {
                # 现代字体
                "Microsoft YaHei": "msyh.ttc",
                "PingFang SC": "msyh.ttc",  # 回退到微软雅黑
                "Noto Sans CJK SC": "msyh.ttc",  # 回退到微软雅黑
                "Source Han Sans SC": "msyh.ttc",  # 回退到微软雅黑

                # 创意字体 - 造字工房系列（回退到系统字体）
                "造字工房悦黑": "msyh.ttc",  # 回退到微软雅黑
                "造字工房朗倩": "simkai.ttf",  # 回退到楷体
                "造字工房版黑": "simhei.ttf",  # 回退到黑体
                "造字工房尚雅": "msyh.ttc",  # 回退到微软雅黑

                # 有趣字体
                "华文彩云": "STCAIYUN.TTF",
                "华文琥珀": "STHUPO.TTF",
                "华文新魏": "STXINWEI.TTF",
                "方正舒体": "simkai.ttf",  # 回退到楷体

                # 书法字体
                "楷体": "simkai.ttf",
                "行书": "STXINGKA.TTF",  # 使用华文行楷
                "隶书": "STLITI.TTF",   # 华文隶书
                "华文行楷": "STXINGKA.TTF",
                "华文新魏": "STXINWEI.TTF",

                # 力量字体
                "黑体": "simhei.ttf",
                "Microsoft YaHei Bold": "msyhbd.ttc",
                "方正粗黑宋简体": "simhei.ttf",  # 回退到黑体
                "华文中宋": "STZHONGS.TTF",

                # 卡通字体
                "华文彩云": "STCAIYUN.TTF",
                "KaiTi": "simkai.ttf",
                "华文行楷": "STXINGKA.TTF",
                "SimSun": "simsun.ttc",

                # 传统字体
                "SimSun": "simsun.ttc",
                "SimHei": "simhei.ttf",
                "KaiTi": "simkai.ttf",
                "宋体": "simsun.ttc",

                # 英文字体
                "Arial": "arial.ttf",
                "Times New Roman": "times.ttf",
                "Calibri": "calibri.ttf",
                "Verdana": "verdana.ttf",

                # 其他华文字体
                "华文仿宋": "STFANGSO.TTF",
                "华文宋体": "STSONG.TTF",
                "华文细黑": "STXIHEI.TTF",
                "华文楷体": "STKAITI.TTF"
            }

            font_file = font_files.get(font_family, "msyh.ttc")
            font_path = f"C:/Windows/Fonts/{font_file}"

            # 检查字体文件是否存在
            import os
            if not os.path.exists(font_path):
                print(f"字体文件不存在: {font_path}，使用微软雅黑替代")
                font_path = "C:/Windows/Fonts/msyh.ttc"

            print(f"加载字体: {font_family} -> {font_path}")  # 调试信息
            font = ImageFont.truetype(font_path, font_size)
            print(f"字体加载成功: {font_family} {font_size}px")
            return font
        except Exception as e:
            print(f"字体加载失败: {font_family} -> {e}")
            try:
                print("尝试加载默认字体...")
                default_font = ImageFont.load_default()
                print("默认字体加载成功")
                return default_font
            except Exception as e2:
                print(f"默认字体加载也失败: {e2}")
                # 如果都失败了，创建一个虚拟字体对象
                class DummyFont:
                    def getsize(self, text):
                        return (len(text) * font_size // 2, font_size)
                    def getbbox(self, text):
                        width = len(text) * font_size // 2
                        return (0, 0, width, font_size)
                print("使用虚拟字体对象")
                return DummyFont()

    # 重复的 add_watermark_to_image 方法已删除 - 使用 add_watermark_to_image_for_preview 替代

    def setup_animations(self):
        """设置界面动画效果"""
        try:
            from PyQt5.QtCore import QPropertyAnimation, QEasingCurve
            from PyQt5.QtWidgets import QGraphicsOpacityEffect

            # 为预览区域添加淡入效果
            if hasattr(self, 'preview_label'):
                self.opacity_effect = QGraphicsOpacityEffect()
                self.preview_label.setGraphicsEffect(self.opacity_effect)

                self.fade_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
                self.fade_animation.setDuration(300)
                self.fade_animation.setStartValue(0.0)
                self.fade_animation.setEndValue(1.0)
                self.fade_animation.setEasingCurve(QEasingCurve.InOutQuad)
        except ImportError:
            # 如果动画库不可用，跳过动画设置
            pass

    def animate_preview_update(self):
        """预览更新动画"""
        try:
            if hasattr(self, 'fade_animation'):
                self.fade_animation.start()
        except:
            pass

    def create_styled_button(self, text, color, icon=""):
        """创建样式化按钮"""
        button = QPushButton(f"{icon} {text}" if icon else text)
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background-color: {self.darken_color(color, 0.1)};
                transform: translateY(-1px);
            }}
            QPushButton:pressed {{
                background-color: {self.darken_color(color, 0.2)};
                transform: translateY(1px);
            }}
        """)
        return button

    def darken_color(self, color, factor):
        """使颜色变暗"""
        try:
            # 简单的颜色变暗算法
            if color.startswith('#'):
                color = color[1:]

            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)

            r = max(0, int(r * (1 - factor)))
            g = max(0, int(g * (1 - factor)))
            b = max(0, int(b * (1 - factor)))

            return f"#{r:02x}{g:02x}{b:02x}"
        except:
            return color

    def add_tooltip_with_icon(self, widget, text, icon="💡"):
        """为控件添加带图标的工具提示"""
        widget.setToolTip(f"{icon} {text}")
        widget.setStyleSheet(widget.styleSheet() + """
            QToolTip {
                background-color: #2c3e50;
                color: white;
                border: 1px solid #34495e;
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
            }
        """)

    def create_separator_line(self):
        """创建分隔线"""
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("""
            QFrame {
                color: #bdc3c7;
                background-color: #bdc3c7;
                height: 1px;
                margin: 10px 0;
            }
        """)
        return line

    def show_success_message(self, message):
        """显示成功消息"""
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Information)
        msg.setWindowTitle("✅ 成功")
        msg.setText(message)
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #d4edda;
                color: #155724;
            }
            QMessageBox QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QMessageBox QPushButton:hover {
                background-color: #218838;
            }
        """)
        msg.exec_()

    def show_error_message(self, message):
        """显示错误消息"""
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle("❌ 错误")
        msg.setText(message)
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #f8d7da;
                color: #721c24;
            }
            QMessageBox QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QMessageBox QPushButton:hover {
                background-color: #c82333;
            }
        """)
        msg.exec_()

    def hex_to_rgba(self, hex_color, opacity):
        """将十六进制颜色转换为RGBA"""
        hex_color = hex_color.lstrip('#')
        r = int(hex_color[0:2], 16)
        g = int(hex_color[2:4], 16)
        b = int(hex_color[4:6], 16)
        a = int(255 * opacity / 100)
        return (r, g, b, a)

    def load_config(self):
        """加载配置到界面"""
        # 设置字体分类
        font_found = False
        for i, (category, info) in enumerate(self.font_categories.items()):
            _ = category  # 使用变量避免警告
            if self.watermark_config.font_family in info["fonts"]:
                self.font_category_combo.setCurrentIndex(i)
                font_found = True
                break

        # 如果没找到字体，使用默认分类
        if not font_found:
            self.font_category_combo.setCurrentIndex(0)

        # 更新字体列表并设置当前字体
        self.on_font_category_changed(self.font_category_combo.currentText())

        # 设置当前字体，如果不存在则使用第一个
        if self.watermark_config.font_family in [self.font_combo.itemText(i) for i in range(self.font_combo.count())]:
            self.font_combo.setCurrentText(self.watermark_config.font_family)
        else:
            # 如果配置的字体不在列表中，使用Microsoft YaHei作为默认
            if self.font_combo.findText("Microsoft YaHei") >= 0:
                self.font_combo.setCurrentText("Microsoft YaHei")
            elif self.font_combo.count() > 0:
                self.font_combo.setCurrentIndex(0)

        # 字体大小
        self.font_size_spinbox.setValue(self.watermark_config.font_size)

        # 单行字数设置
        if hasattr(self, 'single_char_count_spinbox'):
            self.single_char_count_spinbox.setValue(self.watermark_config.single_char_count)

        # 位置
        self.position_x_spinbox.setValue(self.watermark_config.position_x)
        self.position_y_spinbox.setValue(self.watermark_config.position_y)

        # 透明度
        self.opacity_slider.setValue(self.watermark_config.opacity)

        # 多行模式设置
        if hasattr(self, 'multiline_checkbox'):
            self.multiline_checkbox.setChecked(self.watermark_config.multi_line_enabled)
            self.update_multiline_ui_state(self.watermark_config.multi_line_enabled)

            if self.watermark_config.multi_line_enabled:
                self.update_line_selector()
                self.update_filename_preview()
                if len(self.watermark_config.multi_line_configs) > 0:
                    self.line_selector.setCurrentIndex(0)
                    self.load_line_config_to_ui(0)

        # 文字颜色
        self.text_color_label.setText(self.watermark_config.text_color)
        self.text_color_button.setStyleSheet(f"background-color: {self.watermark_config.text_color}; border: 2px solid #333;")

        # 阴影设置
        self.shadow_checkbox.setChecked(self.watermark_config.shadow_enabled)
        self.shadow_color_label.setText(self.watermark_config.shadow_color)
        self.shadow_color_button.setStyleSheet(f"background-color: {self.watermark_config.shadow_color}; border: 2px solid #333;")
        self.shadow_offset_x_spinbox.setValue(self.watermark_config.shadow_offset_x)
        self.shadow_offset_y_spinbox.setValue(self.watermark_config.shadow_offset_y)

        # 描边设置
        self.stroke_checkbox.setChecked(self.watermark_config.stroke_enabled)
        self.stroke_color_label.setText(self.watermark_config.stroke_color)
        self.stroke_color_button.setStyleSheet(f"background-color: {self.watermark_config.stroke_color}; border: 2px solid #333;")
        self.stroke_width_spinbox.setValue(self.watermark_config.stroke_width)

        # 更新控件状态
        self.on_shadow_toggled(self.watermark_config.shadow_enabled)
        self.on_stroke_toggled(self.watermark_config.stroke_enabled)

        # 更新位置信息
        self.update_position_info()

        # 更新预览
        QTimer.singleShot(100, self.update_preview)

        # 添加界面动画效果
        self.setup_animations()

        # 初始化字数显示
        self.update_char_count()

    def save_config(self):
        """保存界面配置"""
        # 字体设置
        self.watermark_config.font_family = self.font_combo.currentText()
        self.watermark_config.font_category = self.font_category_combo.currentText().split(' ', 1)[1]
        self.watermark_config.font_size = self.font_size_spinbox.value()

        # 单行字数设置
        if hasattr(self, 'single_char_count_spinbox'):
            self.watermark_config.single_char_count = self.single_char_count_spinbox.value()

        # 文字样式
        self.watermark_config.text_color = self.text_color_label.text()
        self.watermark_config.opacity = self.opacity_slider.value()

        # 阴影设置
        self.watermark_config.shadow_enabled = self.shadow_checkbox.isChecked()
        self.watermark_config.shadow_color = self.shadow_color_label.text()
        self.watermark_config.shadow_offset_x = self.shadow_offset_x_spinbox.value()
        self.watermark_config.shadow_offset_y = self.shadow_offset_y_spinbox.value()

        # 描边设置
        self.watermark_config.stroke_enabled = self.stroke_checkbox.isChecked()
        self.watermark_config.stroke_color = self.stroke_color_label.text()
        self.watermark_config.stroke_width = self.stroke_width_spinbox.value()

        # 位置设置
        self.watermark_config.position_x = self.position_x_spinbox.value()
        self.watermark_config.position_y = self.position_y_spinbox.value()

    def reset_to_default(self):
        """重置为默认配置"""
        self.watermark_config = WatermarkConfig()
        self.load_config()

    def get_config(self):
        """获取当前配置"""
        self.save_config()
        return self.watermark_config

    def accept(self):
        """确定按钮点击"""
        self.save_config()
        super().accept()


def open_video_processor_dialog(parent=None):
    """打开视频高质量去重工具对话框"""
    try:
        dialog = VideoProcessorDialog(parent)
        dialog.exec_()
        return dialog
    except Exception as e:
        error(f"打开视频高质量去重工具对话框时出错: {str(e)}")
        if parent:
            QMessageBox.critical(parent, "错误", f"打开视频高质量去重工具对话框时出错:\n{str(e)}")
        return None


# 水印设置文件管理器已删除


if __name__ == "__main__":
    import sys
    app = QApplication(sys.argv)
    dialog = VideoProcessorDialog()
    dialog.show()
    sys.exit(app.exec_())
