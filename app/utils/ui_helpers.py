#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
UI辅助工具 - 提供UI相关的辅助函数和装饰器
"""

import time
import functools
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def with_progress_dialog(title="操作进行中", cancelable=True, auto_close=True):
    """长时间运行操作的装饰器，自动显示进度对话框
    
    Args:
        title: 进度对话框标题
        cancelable: 是否可取消
        auto_close: 是否在完成时自动关闭
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            # 导入进度对话框
            from app.widgets.progress_dialog import ProgressDialog
            
            # 创建进度对话框
            progress_dialog = ProgressDialog(title, self, cancelable, auto_close)
            
            # 初始化进度
            progress_dialog.update_progress(0, "正在准备...", "")
            progress_dialog.show()
            
            # 处理Qt事件，确保对话框显示
            QApplication.processEvents()
            
            # 记录开始时间
            start_time = time.time()
            
            # 创建更新函数
            def update_progress(value, status=None, detail=None):
                progress_dialog.update_progress(value, status, detail)
            
            # 将更新函数传递给被装饰的函数
            kwargs['update_progress'] = update_progress
            
            try:
                # 执行原始函数
                result = func(self, *args, **kwargs)
                
                # 完成时更新进度
                elapsed_time = time.time() - start_time
                progress_dialog.update_progress(
                    100, 
                    "操作完成", 
                    f"用时: {elapsed_time:.2f}秒"
                )
                
                # 如果不自动关闭，则等待用户点击关闭
                if not auto_close:
                    progress_dialog.exec_()
                
                return result
                
            except Exception as e:
                # 发生异常时更新进度对话框
                logging.error(f"操作执行出错: {str(e)}")
                progress_dialog.update_progress(
                    100, 
                    "操作失败", 
                    f"错误: {str(e)}"
                )
                
                # 等待用户确认错误
                if not auto_close:
                    progress_dialog.exec_()
                else:
                    # 显示错误一段时间后关闭
                    QTimer.singleShot(3000, progress_dialog.accept)
                
                # 重新抛出异常
                raise
                
        return wrapper
    return decorator

def throttle(delay=0.1):
    """函数调用节流装饰器，限制函数调用频率
    
    Args:
        delay: 最小调用间隔(秒)
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        last_called = [0.0]  # 使用列表以便在闭包中修改
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            current_time = time.time()
            if current_time - last_called[0] >= delay:
                last_called[0] = current_time
                return func(*args, **kwargs)
            # 如果调用太频繁，则跳过
            return None
        return wrapper
    return decorator

def debounce(delay=0.3):
    """函数调用防抖装饰器，延迟执行函数直到一段时间内没有再次调用
    
    Args:
        delay: 延迟时间(秒)
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        timer = [None]  # 使用列表以便在闭包中修改
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            def call_func():
                timer[0] = None
                return func(*args, **kwargs)
            
            # 取消之前的定时器
            if timer[0]:
                timer[0].stop()
                
            # 创建新的定时器
            timer[0] = QTimer()
            timer[0].setSingleShot(True)
            timer[0].timeout.connect(call_func)
            timer[0].start(int(delay * 1000))
            
        return wrapper
    return decorator

def batch_update(batch_size=10, process_events=True):
    """批量更新装饰器，用于处理大量UI更新操作
    
    Args:
        batch_size: 每批处理的项目数
        process_events: 是否在批处理之间处理Qt事件
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, items, *args, **kwargs):
            results = []
            total_items = len(items)
            
            for i in range(0, total_items, batch_size):
                # 处理当前批次
                batch = items[i:i+batch_size]
                batch_results = func(self, batch, *args, **kwargs)
                
                if batch_results:
                    if isinstance(batch_results, list):
                        results.extend(batch_results)
                    else:
                        results.append(batch_results)
                
                # 在批次之间处理事件
                if process_events:
                    QApplication.processEvents()
                    
                # 如果提供了进度更新函数，则更新进度
                if 'update_progress' in kwargs:
                    progress = min(100, int((i + len(batch)) / total_items * 100))
                    kwargs['update_progress'](
                        progress,
                        f"处理中... {progress}%",
                        f"已处理 {i + len(batch)}/{total_items} 项"
                    )
            
            return results
        return wrapper
    return decorator
