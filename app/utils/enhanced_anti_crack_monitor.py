#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强型防破解实时监控系统
提供更强大的实时威胁检测和响应能力
"""

import os
import sys
import time
import threading
import queue
import psutil
import hashlib
import json
from datetime import datetime
from typing import Dict, List, Callable, Optional

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

try:
    from app.utils.anti_crack_config import get_config, get_dangerous_processes, get_dangerous_window_titles
    from app.utils.kamidenglu import AntiCrackDetector
except ImportError:
    print("⚠️ 无法导入防破解模块，使用独立模式")
    get_config = None
    get_dangerous_processes = lambda: []
    get_dangerous_window_titles = lambda: []
    AntiCrackDetector = None

class EnhancedAntiCrackMonitor:
    """增强型防破解监控器"""
    
    def __init__(self, 
                 check_interval: float = 5.0,  # 检查间隔（秒）
                 threat_callback: Optional[Callable] = None,
                 log_file: str = "anti_crack_monitor.log"):
        self.check_interval = check_interval
        self.threat_callback = threat_callback
        self.log_file = log_file
        self.is_monitoring = False
        self.monitor_thread = None
        self.threat_queue = queue.Queue()
        self.detection_stats = {
            'total_checks': 0,
            'threats_detected': 0,
            'last_check': None,
            'start_time': None
        }
        
        # 威胁检测历史
        self.threat_history = []
        self.process_whitelist = set()
        self.last_process_snapshot = set()
        
        # 初始化检测器
        if AntiCrackDetector:
            self.detector = AntiCrackDetector()
        else:
            self.detector = None
        
        self._load_whitelist()
        self._init_logging()
    
    def _init_logging(self):
        """初始化日志系统"""
        try:
            # 创建日志目录
            log_dir = os.path.dirname(self.log_file) if os.path.dirname(self.log_file) else "logs"
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            # 写入启动日志
            self._log("INFO", "增强型防破解监控系统初始化完成")
        except Exception as e:
            print(f"⚠️ 日志系统初始化失败: {e}")
    
    def _log(self, level: str, message: str):
        """记录日志"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] [{level}] {message}\n"
            
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
            
            # 同时输出到控制台
            if level in ['WARNING', 'ERROR', 'CRITICAL']:
                print(f"🚨 {log_entry.strip()}")
            elif level == 'INFO':
                print(f"ℹ️ {log_entry.strip()}")
                
        except Exception as e:
            print(f"⚠️ 日志记录失败: {e}")
    
    def _load_whitelist(self):
        """加载进程白名单"""
        whitelist_file = "process_whitelist.json"
        try:
            if os.path.exists(whitelist_file):
                with open(whitelist_file, 'r', encoding='utf-8') as f:
                    whitelist_data = json.load(f)
                    self.process_whitelist = set(whitelist_data.get('processes', []))
                    self._log("INFO", f"加载进程白名单: {len(self.process_whitelist)} 个进程")
            else:
                # 创建默认白名单
                default_whitelist = {
                    'processes': [
                        'explorer.exe', 'winlogon.exe', 'csrss.exe', 'wininit.exe',
                        'services.exe', 'lsass.exe', 'svchost.exe', 'dwm.exe',
                        'python.exe', 'pythonw.exe', 'cmd.exe', 'powershell.exe'
                    ]
                }
                with open(whitelist_file, 'w', encoding='utf-8') as f:
                    json.dump(default_whitelist, f, ensure_ascii=False, indent=2)
                self.process_whitelist = set(default_whitelist['processes'])
                self._log("INFO", f"创建默认进程白名单: {len(self.process_whitelist)} 个进程")
        except Exception as e:
            self._log("ERROR", f"加载进程白名单失败: {e}")
    
    def _get_current_processes(self) -> Dict[str, Dict]:
        """获取当前进程快照"""
        processes = {}
        try:
            for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline', 'create_time']):
                try:
                    proc_info = proc.info
                    if proc_info and proc_info.get('name'):
                        name = proc_info['name']
                        if name:  # 确保name不为None
                            processes[proc_info['pid']] = {
                                'name': name.lower(),
                                'exe': proc_info.get('exe', '').lower() if proc_info.get('exe') else '',
                                'cmdline': ' '.join(proc_info.get('cmdline', [])).lower() if proc_info.get('cmdline') else '',
                                'create_time': proc_info.get('create_time', 0)
                            }
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                except Exception as e:
                    # 忽略单个进程的错误
                    continue
        except Exception as e:
            self._log("ERROR", f"获取进程快照失败: {e}")
        
        return processes
    
    def _detect_new_processes(self, current_processes: Dict[str, Dict]) -> List[Dict]:
        """检测新启动的进程"""
        new_processes = []
        current_pids = set(current_processes.keys())
        
        for pid in current_pids - self.last_process_snapshot:
            if pid in current_processes:
                proc_info = current_processes[pid]
                # 检查是否为可疑进程
                if self._is_suspicious_process(proc_info):
                    new_processes.append({
                        'pid': pid,
                        'name': proc_info['name'],
                        'exe': proc_info['exe'],
                        'detection_time': datetime.now()
                    })
        
        self.last_process_snapshot = current_pids
        return new_processes
    
    def _is_suspicious_process(self, proc_info: Dict) -> bool:
        """判断进程是否可疑"""
        proc_name = proc_info['name']
        proc_exe = proc_info['exe']
        proc_cmdline = proc_info['cmdline']
        
        # 检查白名单
        if proc_name in self.process_whitelist:
            return False
        
        # 检查危险进程列表
        try:
            dangerous_processes = get_dangerous_processes() if get_dangerous_processes else []
            for dangerous_proc in dangerous_processes:
                dangerous_proc_lower = dangerous_proc.lower()
                
                # 多重匹配
                if (dangerous_proc_lower in proc_name or 
                    dangerous_proc_lower.replace('.exe', '') in proc_name or
                    dangerous_proc_lower.replace('.exe', '') in proc_exe or
                    dangerous_proc_lower.replace('.exe', '') in proc_cmdline):
                    return True
        except:
            pass
        
        # 检查可疑关键词
        suspicious_keywords = [
            'debug', 'crack', 'hack', 'cheat', 'inject', 'hook', 
            'monitor', 'spy', 'dump', 'patch', 'bypass'
        ]
        
        for keyword in suspicious_keywords:
            if (keyword in proc_name or 
                keyword in proc_exe or 
                keyword in proc_cmdline):
                return True
        
        return False
    
    def _perform_comprehensive_check(self) -> List[Dict]:
        """执行综合检查"""
        threats = []
        
        try:
            # 1. 进程检测
            current_processes = self._get_current_processes()
            new_suspicious_processes = self._detect_new_processes(current_processes)
            
            for proc in new_suspicious_processes:
                threats.append({
                    'type': 'suspicious_process',
                    'severity': 'HIGH',
                    'details': f"检测到可疑进程: {proc['name']} (PID: {proc['pid']})",
                    'timestamp': proc['detection_time'],
                    'data': proc
                })
            
            # 2. 使用防破解检测器
            try:
                from app.utils.kamidenglu import AntiCrackDetector
                detector = AntiCrackDetector()
                detection_result = detector.check_debugging_tools()

                if not detection_result:
                    # 获取检测详情
                    detection_details = getattr(detector, 'detection_results', [])
                    if detection_details:
                        threats.append({
                            'type': 'debugging_tool',
                            'severity': 'CRITICAL',
                            'details': f"检测到调试工具: {len(detection_details)} 个检测结果",
                            'timestamp': datetime.now(),
                            'data': detection_details[:5]  # 只保留前5个结果
                        })

                # 3. 行为分析
                if hasattr(detector, '_analyze_suspicious_behavior'):
                    try:
                        suspicious_count = detector._analyze_suspicious_behavior()
                        if suspicious_count > 0:
                            threats.append({
                                'type': 'suspicious_behavior',
                                'severity': 'MEDIUM',
                                'details': f"检测到 {suspicious_count} 个可疑行为",
                                'timestamp': datetime.now(),
                                'data': {'count': suspicious_count}
                            })
                    except Exception as e:
                        self._log("WARNING", f"行为分析执行失败: {e}")

            except Exception as e:
                self._log("ERROR", f"防破解检测器执行失败: {e}")
            
            # 3. 系统资源异常检测
            try:
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_percent = psutil.virtual_memory().percent
                
                if cpu_percent > 95:
                    threats.append({
                        'type': 'resource_anomaly',
                        'severity': 'MEDIUM',
                        'details': f"CPU使用率异常: {cpu_percent:.1f}%",
                        'timestamp': datetime.now(),
                        'data': {'cpu_percent': cpu_percent}
                    })
                
                if memory_percent > 95:
                    threats.append({
                        'type': 'resource_anomaly',
                        'severity': 'MEDIUM',
                        'details': f"内存使用率异常: {memory_percent:.1f}%",
                        'timestamp': datetime.now(),
                        'data': {'memory_percent': memory_percent}
                    })
            except:
                pass
            
        except Exception as e:
            self._log("ERROR", f"综合检查执行失败: {e}")
        
        return threats
    
    def _handle_threats(self, threats: List[Dict]):
        """处理检测到的威胁"""
        for threat in threats:
            # 记录威胁
            self.threat_history.append(threat)
            self.detection_stats['threats_detected'] += 1
            
            # 记录日志
            self._log("WARNING", f"检测到威胁: {threat['details']}")
            
            # 调用回调函数
            if self.threat_callback:
                try:
                    self.threat_callback(threat)
                except Exception as e:
                    self._log("ERROR", f"威胁回调函数执行失败: {e}")
            
            # 将威胁放入队列
            self.threat_queue.put(threat)
    
    def _monitor_loop(self):
        """监控主循环"""
        self._log("INFO", "开始实时监控")
        self.detection_stats['start_time'] = datetime.now()
        
        while self.is_monitoring:
            try:
                start_time = time.time()
                
                # 执行检查
                threats = self._perform_comprehensive_check()
                
                # 处理威胁
                if threats:
                    self._handle_threats(threats)
                
                # 更新统计
                self.detection_stats['total_checks'] += 1
                self.detection_stats['last_check'] = datetime.now()
                
                # 计算睡眠时间
                elapsed = time.time() - start_time
                sleep_time = max(0, self.check_interval - elapsed)
                
                if sleep_time > 0:
                    time.sleep(sleep_time)
                    
            except Exception as e:
                self._log("ERROR", f"监控循环异常: {e}")
                time.sleep(self.check_interval)
        
        self._log("INFO", "实时监控已停止")
    
    def start_monitoring(self) -> bool:
        """开始监控"""
        if self.is_monitoring:
            self._log("WARNING", "监控已在运行中")
            return False
        
        try:
            self.is_monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            self._log("INFO", "实时监控已启动")
            return True
        except Exception as e:
            self._log("ERROR", f"启动监控失败: {e}")
            self.is_monitoring = False
            return False
    
    def stop_monitoring(self) -> bool:
        """停止监控"""
        if not self.is_monitoring:
            self._log("WARNING", "监控未在运行")
            return False
        
        try:
            self.is_monitoring = False
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=5)
            self._log("INFO", "实时监控已停止")
            return True
        except Exception as e:
            self._log("ERROR", f"停止监控失败: {e}")
            return False
    
    def get_threats(self, timeout: float = 0.1) -> List[Dict]:
        """获取检测到的威胁"""
        threats = []
        try:
            while True:
                threat = self.threat_queue.get(timeout=timeout)
                threats.append(threat)
                self.threat_queue.task_done()
        except queue.Empty:
            pass
        return threats
    
    def get_statistics(self) -> Dict:
        """获取监控统计信息"""
        stats = self.detection_stats.copy()
        stats['is_monitoring'] = self.is_monitoring
        stats['threat_history_count'] = len(self.threat_history)
        stats['process_whitelist_count'] = len(self.process_whitelist)
        
        if stats['start_time']:
            runtime = datetime.now() - stats['start_time']
            stats['runtime_seconds'] = runtime.total_seconds()
            stats['checks_per_minute'] = (stats['total_checks'] / (runtime.total_seconds() / 60)) if runtime.total_seconds() > 0 else 0
        
        return stats
    
    def export_threat_report(self, filename: Optional[str] = None) -> Dict:
        """导出威胁报告"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"threat_report_{timestamp}.json"

        try:
            # 处理威胁历史中的datetime对象
            processed_threats = []
            for threat in self.threat_history:
                processed_threat = {}
                for key, value in threat.items():
                    if isinstance(value, datetime):
                        processed_threat[key] = value.isoformat()
                    else:
                        processed_threat[key] = value
                processed_threats.append(processed_threat)

            report = {
                'report_time': datetime.now().isoformat(),
                'statistics': self.get_statistics(),
                'threats': processed_threats,
                'configuration': {
                    'check_interval': self.check_interval,
                    'monitoring_enabled': self.is_monitoring
                }
            }

            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(report, f, ensure_ascii=False, indent=2)
                self._log("INFO", f"威胁报告已导出: {filename}")
            except Exception as e:
                self._log("WARNING", f"文件导出失败: {e}")

            return report

        except Exception as e:
            self._log("ERROR", f"导出威胁报告失败: {e}")
            return {}

if __name__ == "__main__":
    # 测试监控器
    def threat_handler(threat):
        print(f"🚨 威胁警报: {threat['details']}")
    
    monitor = EnhancedAntiCrackMonitor(
        check_interval=2.0,
        threat_callback=threat_handler
    )
    
    print("🔒 启动增强型防破解监控系统...")
    monitor.start_monitoring()
    
    try:
        # 运行30秒
        time.sleep(30)
        
        # 获取统计信息
        stats = monitor.get_statistics()
        print(f"\n📊 监控统计:")
        print(f"  总检查次数: {stats['total_checks']}")
        print(f"  检测到威胁: {stats['threats_detected']}")
        print(f"  运行时间: {stats.get('runtime_seconds', 0):.1f} 秒")
        print(f"  检查频率: {stats.get('checks_per_minute', 0):.1f} 次/分钟")
        
        # 导出报告
        report_file = monitor.export_threat_report()
        if report_file:
            print(f"📄 威胁报告已保存: {report_file}")
        
    except KeyboardInterrupt:
        print("\n⌨️ 用户中断")
    finally:
        monitor.stop_monitoring()
        print("👋 监控系统已关闭")
