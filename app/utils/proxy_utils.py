#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
代理工具模块 - 提供代理相关的工具函数
"""

import os
import json
import logging
import random
import time
from selenium import webdriver

logger = logging.getLogger(__name__)

def get_proxy_settings(check_enabled=True):
    """获取代理设置

    Args:
        check_enabled: 是否检查代理是否启用，默认为True

    Returns:
        dict: 代理设置字典，如果未找到设置文件或代理未启用（当check_enabled=True时）则返回None
    """
    try:
        settings_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data", "proxy_settings.json")

        if os.path.exists(settings_file):
            with open(settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

            # 如果不需要检查是否启用，或者代理已启用，则返回设置
            if not check_enabled or settings.get("enable_proxy", False):
                return settings

        return None
    except Exception as e:
        logger.error(f"获取代理设置失败: {str(e)}")
        return None

def get_proxy_url(settings=None):
    """获取代理URL

    Args:
        settings: 代理设置字典，如果为None则自动加载

    Returns:
        str: 代理URL，如果未启用代理则返回None
    """
    if settings is None:
        settings = get_proxy_settings()

    if not settings or not settings.get("enable_proxy", False):
        return None

    # 检查是否启用了IP轮换并且有代理列表
    if settings.get("enable_rotation", False) and settings.get("proxy_list", "").strip():
        # 如果启用了IP轮换，返回None，让get_current_proxy函数处理
        return None

    # 如果没有启用IP轮换或没有代理列表，使用单独的代理设置
    proxy_type = settings.get("proxy_type", "HTTP").lower()
    proxy_host = settings.get("proxy_host", "")
    proxy_port = settings.get("proxy_port", 8080)

    if not proxy_host:
        return None

    # 构建代理URL
    if settings.get("require_auth", False):
        username = settings.get("proxy_username", "")
        password = settings.get("proxy_password", "")

        if username and password:
            return f"{proxy_type}://{username}:{password}@{proxy_host}:{proxy_port}"

    return f"{proxy_type}://{proxy_host}:{proxy_port}"

def get_current_proxy():
    """获取当前代理URL

    Returns:
        str: 当前代理URL，如果未启用代理则返回None
    """
    settings = get_proxy_settings()

    if not settings or not settings.get("enable_proxy", False):
        return None

    # 如果启用了IP轮换，从代理列表中选择一个
    if settings.get("enable_rotation", False):
        proxy_list = settings.get("proxy_list", "").strip()

        if proxy_list:
            proxies = [p.strip() for p in proxy_list.split(",") if p.strip()]

            if proxies:
                return random.choice(proxies)

    # 否则返回单个代理
    return get_proxy_url(settings)

def apply_proxy_to_session(session):
    """将代理应用到requests会话

    Args:
        session: requests.Session对象

    Returns:
        bool: 是否成功应用代理
    """
    try:
        proxy = get_current_proxy()

        if proxy:
            session.proxies = {
                "http": proxy,
                "https": proxy
            }
            logger.info(f"已应用代理: {proxy}")
            return True

        return False
    except Exception as e:
        logger.error(f"应用代理失败: {str(e)}")
        return False

def apply_proxy_to_chrome_options(options):
    """将代理应用到Chrome选项

    Args:
        options: webdriver.ChromeOptions对象

    Returns:
        bool: 是否成功应用代理
    """
    try:
        proxy = get_current_proxy()

        if proxy:
            # 移除协议前缀
            if "://" in proxy:
                proxy = proxy.split("://", 1)[1]

            options.add_argument(f'--proxy-server={proxy}')
            logger.info(f"已应用代理到Chrome: {proxy}")
            return True

        return False
    except Exception as e:
        logger.error(f"应用代理到Chrome选项失败: {str(e)}")
        return False

def create_chrome_driver_with_proxy(headless=False, user_data_dir=None):
    """创建带有代理的Chrome驱动

    Args:
        headless: 是否使用无头模式
        user_data_dir: 用户数据目录

    Returns:
        webdriver.Chrome: Chrome驱动实例
    """
    try:
        options = webdriver.ChromeOptions()

        # 设置无头模式
        if headless:
            options.add_argument('--headless')
            options.add_argument('--disable-gpu')

        # 设置用户数据目录
        if user_data_dir:
            options.add_argument(f'--user-data-dir={user_data_dir}')

        # 添加其他常用选项
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option('excludeSwitches', ['enable-automation'])
        options.add_experimental_option('useAutomationExtension', False)

        # 应用代理
        apply_proxy_to_chrome_options(options)

        # 创建驱动
        from app.utils.chromedriver_manager import create_chrome_driver
        driver = create_chrome_driver(options)

        # 设置页面加载超时
        driver.set_page_load_timeout(30)

        return driver
    except Exception as e:
        logger.error(f"创建Chrome驱动失败: {str(e)}")
        raise

def rotate_proxy_if_needed(force=False):
    """如果需要或强制要求，轮换代理

    Args:
        force: 是否强制轮换，不考虑时间间隔

    Returns:
        bool: 是否轮换了代理
    """
    try:
        settings = get_proxy_settings()

        if not settings or not settings.get("enable_proxy", False):
            return False

        # 如果启用了IP轮换
        if settings.get("enable_rotation", False):
            # 获取上次轮换时间
            last_rotation_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "data", "last_proxy_rotation.txt")

            current_time = time.time()
            last_rotation_time = 0

            if os.path.exists(last_rotation_file):
                try:
                    with open(last_rotation_file, 'r') as f:
                        last_rotation_time = float(f.read().strip())
                except:
                    last_rotation_time = 0

            # 计算是否需要轮换
            rotation_interval = settings.get("rotation_interval", 30) * 60  # 转换为秒

            # 如果强制轮换或者达到轮换时间间隔
            if force or current_time - last_rotation_time >= rotation_interval:
                # 更新轮换时间
                try:
                    os.makedirs(os.path.dirname(last_rotation_file), exist_ok=True)
                    with open(last_rotation_file, 'w') as f:
                        f.write(str(current_time))
                except Exception as e:
                    logger.error(f"更新代理轮换时间失败: {str(e)}")

                logger.info("已轮换代理" + (" (强制轮换)" if force else ""))
                return True

        return False
    except Exception as e:
        logger.error(f"检查代理轮换失败: {str(e)}")
        return False

def force_rotate_proxy():
    """强制轮换代理

    Returns:
        bool: 是否成功轮换
    """
    return rotate_proxy_if_needed(force=True)

def get_random_proxy_from_list():
    """从代理列表中随机获取一个代理

    Returns:
        str: 代理URL，如果未启用代理或代理列表为空则返回None
    """
    settings = get_proxy_settings()

    if not settings or not settings.get("enable_proxy", False) or not settings.get("enable_rotation", False):
        return None

    proxy_list = settings.get("proxy_list", "").strip()

    if proxy_list:
        proxies = [p.strip() for p in proxy_list.split(",") if p.strip()]

        if proxies:
            return random.choice(proxies)

    return None

def test_proxy(proxy_url, timeout=5):
    """测试代理是否可用，增强版

    Args:
        proxy_url: 代理URL
        timeout: 超时时间(秒)

    Returns:
        bool: 代理是否可用
    """
    if not proxy_url:
        return False

    try:
        import requests

        # 构建代理字典
        proxies = {
            "http": proxy_url,
            "https": proxy_url
        }

        # 测试多个网站，确保代理稳定性
        test_urls = [
            "https://www.baidu.com",
            "https://www.toutiao.com",
            "https://www.qq.com"
        ]

        success_count = 0

        for url in test_urls:
            try:
                # 测试连接
                response = requests.get(url, proxies=proxies, timeout=timeout)

                # 检查响应状态码
                if response.status_code == 200:
                    success_count += 1
                    logger.info(f"代理 {proxy_url} 测试 {url} 成功")
                else:
                    logger.warning(f"代理 {proxy_url} 测试 {url} 失败，状态码: {response.status_code}")
            except Exception as e:
                logger.warning(f"代理 {proxy_url} 测试 {url} 失败: {str(e)}")

        # 至少有2个网站测试成功才认为代理可用
        if success_count >= 2:
            logger.info(f"代理 {proxy_url} 测试成功率: {success_count}/{len(test_urls)}")
            return True
        else:
            logger.warning(f"代理 {proxy_url} 测试成功率过低: {success_count}/{len(test_urls)}")
            return False
    except Exception as e:
        logger.error(f"测试代理 {proxy_url} 失败: {str(e)}")
        return False
