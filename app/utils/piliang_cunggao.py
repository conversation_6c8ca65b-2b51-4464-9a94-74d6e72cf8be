#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量存稿工作器 - 使用Python原生threading模式
"""

import os
import sys
import time
import json
import threading
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QColor
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from app.utils.logger import info, warning, error, debug


class RetryManager:
    """统一重试管理器 - 管理三层重试机制"""

    def __init__(self, retry_settings=None):
        """初始化重试管理器

        Args:
            retry_settings: 重试设置字典
        """
        self.retry_settings = retry_settings or {}

        # L1: 即时重试设置
        immediate_settings = self.retry_settings.get('immediate_retry', {})
        self.login_retry_count = immediate_settings.get('login_retry_count', 2)
        self.network_retry_count = immediate_settings.get('network_retry_count', 3)

        # L2: 操作重试设置
        operation_settings = self.retry_settings.get('operation_retry', {})
        self.video_upload_retry_count = operation_settings.get('video_upload_retry_count', 3)
        self.page_load_retry_count = operation_settings.get('page_load_retry_count', 2)
        self.operation_retry_delay = operation_settings.get('retry_delay', 5)

        # L3: 批次重试设置
        batch_settings = self.retry_settings.get('batch_retry', {})
        self.batch_retry_enabled = batch_settings.get('enable', True)
        self.max_batch_retry_rounds = batch_settings.get('max_rounds', 2)
        self.batch_retry_delay = batch_settings.get('retry_delay', 30)
        self.auto_batch_retry = batch_settings.get('auto_retry', False)
        self.create_failed_backup = batch_settings.get('create_failed_backup', True)

        # 向后兼容
        if not self.retry_settings.get('immediate_retry') and not self.retry_settings.get('operation_retry'):
            # 使用旧的设置项
            self.batch_retry_enabled = self.retry_settings.get('enable_retry', True)
            self.max_batch_retry_rounds = self.retry_settings.get('max_retry_count', 2)
            self.batch_retry_delay = self.retry_settings.get('retry_delay', 30)
            self.auto_batch_retry = self.retry_settings.get('auto_retry_failed', False)

        # 重试计数器
        self.immediate_retry_counts = {}  # {account_id: {operation: count}}
        self.operation_retry_counts = {}  # {account_id: {operation: count}}
        self.batch_retry_round = 0

        debug(f"重试管理器初始化完成 - L1即时重试: 登录{self.login_retry_count}次/网络{self.network_retry_count}次, "
              f"L2操作重试: 视频上传{self.video_upload_retry_count}次/页面加载{self.page_load_retry_count}次, "
              f"L3批次重试: {'启用' if self.batch_retry_enabled else '禁用'}, 最大{self.max_batch_retry_rounds}轮")

    def can_immediate_retry(self, account_id, operation_type):
        """检查是否可以进行即时重试 (L1)"""
        if account_id not in self.immediate_retry_counts:
            self.immediate_retry_counts[account_id] = {}

        current_count = self.immediate_retry_counts[account_id].get(operation_type, 0)
        max_count = getattr(self, f"{operation_type}_retry_count", 0)

        return current_count < max_count

    def record_immediate_retry(self, account_id, operation_type):
        """记录即时重试 (L1)"""
        if account_id not in self.immediate_retry_counts:
            self.immediate_retry_counts[account_id] = {}

        self.immediate_retry_counts[account_id][operation_type] = \
            self.immediate_retry_counts[account_id].get(operation_type, 0) + 1

        return self.immediate_retry_counts[account_id][operation_type]

    def can_operation_retry(self, account_id, operation_type):
        """检查是否可以进行操作重试 (L2)"""
        if account_id not in self.operation_retry_counts:
            self.operation_retry_counts[account_id] = {}

        current_count = self.operation_retry_counts[account_id].get(operation_type, 0)
        max_count = getattr(self, f"{operation_type}_retry_count", 0)

        return current_count < max_count

    def record_operation_retry(self, account_id, operation_type):
        """记录操作重试 (L2)"""
        if account_id not in self.operation_retry_counts:
            self.operation_retry_counts[account_id] = {}

        self.operation_retry_counts[account_id][operation_type] = \
            self.operation_retry_counts[account_id].get(operation_type, 0) + 1

        return self.operation_retry_counts[account_id][operation_type]

    def can_batch_retry(self):
        """检查是否可以进行批次重试 (L3)"""
        return (self.batch_retry_enabled and
                self.batch_retry_round < self.max_batch_retry_rounds)

    def record_batch_retry(self):
        """记录批次重试 (L3)"""
        self.batch_retry_round += 1
        return self.batch_retry_round

    def get_operation_retry_delay(self):
        """获取操作重试延迟时间"""
        return self.operation_retry_delay

    def get_batch_retry_delay(self):
        """获取批次重试延迟时间"""
        return self.batch_retry_delay

    def should_auto_batch_retry(self):
        """是否应该自动批次重试"""
        return self.auto_batch_retry

    def should_create_failed_backup(self):
        """是否应该创建失败账号备份"""
        return self.create_failed_backup

    def reset_account_counters(self, account_id):
        """重置账号的重试计数器"""
        if account_id in self.immediate_retry_counts:
            del self.immediate_retry_counts[account_id]
        if account_id in self.operation_retry_counts:
            del self.operation_retry_counts[account_id]

    def get_retry_statistics(self):
        """获取重试统计信息"""
        immediate_stats = {}
        operation_stats = {}

        for account_id, operations in self.immediate_retry_counts.items():
            for op_type, count in operations.items():
                if op_type not in immediate_stats:
                    immediate_stats[op_type] = {'accounts': 0, 'total_retries': 0}
                immediate_stats[op_type]['accounts'] += 1
                immediate_stats[op_type]['total_retries'] += count

        for account_id, operations in self.operation_retry_counts.items():
            for op_type, count in operations.items():
                if op_type not in operation_stats:
                    operation_stats[op_type] = {'accounts': 0, 'total_retries': 0}
                operation_stats[op_type]['accounts'] += 1
                operation_stats[op_type]['total_retries'] += count

        return {
            'immediate_retry': immediate_stats,
            'operation_retry': operation_stats,
            'batch_retry_round': self.batch_retry_round
        }


def handle_error(operation: str, exception: Exception, account_id: str = "") -> str:
    """简化的错误处理"""
    error_msg = f"{operation}失败: {str(exception)}"
    if account_id:
        error_msg = f"账号 {account_id} {error_msg}"
    error(error_msg)
    return error_msg


class BatchCunggaoWorker(QObject):
    """批量存稿工作器 - 使用Python原生threading模式"""
    # 定义信号
    status_signal = pyqtSignal(str)  # 状态信息信号
    progress_signal = pyqtSignal(int, int)  # 进度信号(当前, 总数)
    finished_signal = pyqtSignal()  # 完成信号
    status_update_signal = pyqtSignal(str)  # 状态更新信号
    error_signal = pyqtSignal(str)  # 错误信号
    clear_logs_signal = pyqtSignal()  # 清理日志信号
    account_progress_signal = pyqtSignal(str, str, str, int, int)  # 账号进度信号
    runtime_update_signal = pyqtSignal(str)  # 运行时间更新信号

    def __init__(self, table_widget, cookie_dir, draft_count=3, concurrent_count=3, headless_mode=False):
        super().__init__()
        self.table_widget = table_widget
        self.cookie_dir = cookie_dir
        self.draft_count = draft_count
        self.concurrent_count = concurrent_count
        self.headless_mode = headless_mode
        self.ban_mode = False  # 默认为存稿模式

        # 任务统计
        self.total_accounts = 0
        self.success_accounts = 0
        self.failed_accounts = 0
        self.login_failed_accounts = 0

        # 失败账号收集和重试机制
        self.failed_account_list = []  # 失败账号列表，包含失败原因
        self.current_retry_round = 0  # 当前重试轮次
        self.retry_success_accounts = 0  # 重试成功的账号数

        # 视频上传重试机制
        self.video_upload_retry_counts = {}  # 账号级别的视频上传重试计数 {account_id: retry_count}
        self.max_video_upload_retries = 3  # 每个账号最多重试3次

        # 资源管理
        self.active_drivers = []  # 活跃的WebDriver实例
        self.active_threads = []  # 活跃的子线程

        # 线程管理
        self.active_threads = []
        self.is_cancelled = False
        self.is_running = False

        # 进度跟踪
        self.completed_count = 0
        self.total_count = 0

        # 任务完成控制 - 防止重复处理
        self.task_completion_processed = False
        self.completion_check_timer = None

        # 线程安全锁
        import threading
        self.update_lock = threading.Lock()

        # 运行时间统计
        self.start_time = None  # 任务开始时间
        self.end_time = None    # 任务结束时间
        self.runtime_timer = None  # 运行时间更新定时器

        # 额外参数（通过属性设置）
        self.video_dir = None
        self.cover_dir = None
        self.video_allocation = True
        self.clean_cache_before_start = False
        self.remove_popup = True
        self.selected_accounts = None
        self.account_tab_ref = None
        self.use_fingerprint = False  # 指纹浏览器开关
        self.fingerprint_settings = None  # 指纹浏览器设置

        # 失败账号自动重试设置
        self.auto_retry_failed = False  # 是否自动重试失败账号

        # 额外的属性定义（用于动态设置）
        self.handle_publish_limit = True  # 是否处理发布限制弹窗
        self.timeout_settings = {}  # 超时设置
        self.retry_settings = {}  # 重试设置

        # 重试管理器（延迟初始化，在设置重试设置后初始化）
        self.retry_manager = None

    def run(self):
        """执行批量存稿任务 - 使用Python原生threading"""
        try:
            info("开始执行批量存稿任务")

            # 初始化重试管理器
            self.retry_manager = RetryManager(self.retry_settings)
            info(f"重试管理器初始化完成")

            # 记录任务开始时间
            import time
            self.start_time = time.time()
            self._start_runtime_timer()

            # 获取账号列表
            account_list = self._get_account_list()
            info(f"获取到账号列表，共 {len(account_list)} 个账号")

            if not account_list:
                self.status_signal.emit("❌ 没有找到有效的账号")
                warning("没有找到有效的账号，任务结束")
                # 停止运行时间统计
                self._stop_runtime_timer()
                # 延迟发送完成信号，确保UI有时间处理
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(100, self.finished_signal.emit)
                return

            # 打印账号详情
            for i, (row, account_id, cookie_file) in enumerate(account_list):
                info(f"账号 {i+1}: {account_id}, Cookie文件: {cookie_file}")

            # 设置总数 - 按账号数量计算，而不是按存稿次数
            self.total_count = len(account_list)
            self.status_signal.emit(f"📊 开始批量存稿任务，共 {len(account_list)} 个账号，每账号 {self.draft_count} 次存稿")

            # 设置运行状态
            self.is_running = True
            self.is_cancelled = False

            # 使用Python原生threading执行任务
            import threading
            task_thread = threading.Thread(
                target=self._run_batch_draft,
                args=(account_list,),
                daemon=True
            )
            task_thread.start()

            info("批量存稿任务线程已启动")

        except Exception as e:
            error_msg = handle_error("执行批量存稿任务", e)
            self.status_signal.emit(f"❌ {error_msg}")
            # 停止运行时间统计
            self._stop_runtime_timer()
            # 延迟发送完成信号，确保UI有时间处理
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(100, self.finished_signal.emit)

    def _run_batch_draft(self, account_list):
        """使用工作窃取模式执行批量存稿任务"""
        try:
            import threading
            import queue

            info(f"开始执行批量存稿，账号数量: {len(account_list)}")

            # 重置状态
            self.is_cancelled = False
            self.is_running = True
            self.completed_count = 0

            # 重置统计数据
            self.total_accounts = len(account_list)
            self.success_accounts = 0
            self.failed_accounts = 0
            self.login_failed_accounts = 0

            # 创建工作窃取队列
            self.task_queue = queue.Queue()

            # 将所有账号任务放入队列
            for account_task in account_list:
                self.task_queue.put(account_task)

            # 计算线程数
            total_accounts = len(account_list)
            concurrent_threads = min(self.concurrent_count, total_accounts, 18)  # 最大18个线程

            self.status_signal.emit(f"🧩 使用工作窃取模式，共 {total_accounts} 个账号，{concurrent_threads} 个线程")
            info(f"工作窃取模式: {concurrent_threads} 个线程处理 {total_accounts} 个账号")

            # 创建线程池
            threads = []
            for i in range(concurrent_threads):
                t = threading.Thread(
                    target=self._work_stealing_worker,
                    args=(i,),
                    daemon=True
                )
                threads.append(t)
                self.active_threads.append(t)
                t.start()
                self.status_signal.emit(f"🚀 启动工作窃取线程 #{i}")
                info(f"✅ 工作窃取线程 #{i} 已启动")

            info(f"所有工作窃取线程已启动，共 {len(threads)} 个线程")

            # 等待所有线程完成
            self.status_signal.emit(f"⏳ 等待 {len(threads)} 个工作窃取线程完成...")
            for i, t in enumerate(threads):
                info(f"等待工作窃取线程 #{i} 完成...")
                t.join()
                self.status_signal.emit(f"✅ 线程 #{i} 已完成")
                info(f"✅ 线程 #{i} 已完成")

            # 清理线程列表
            self.active_threads.clear()
            self.is_running = False

            info("所有线程已完成，准备发送完成信号")

            # 任务完成后的清理和统计
            self._cleanup_and_finalize()

        except Exception as e:
            error_msg = handle_error("批量存稿任务执行", e)
            self._safe_emit_signal("status_signal", f"❌ {error_msg}")
            error(f"批量存稿任务执行异常: {error_msg}")
            # 异常情况下也要进行清理
            self._cleanup_and_finalize()



    def _work_stealing_worker(self, thread_index):
        """工作窃取模式的工作线程"""
        driver = None
        processed_count = 0

        try:
            # 初始化浏览器
            self.status_signal.emit(f"🌐 工作窃取线程 #{thread_index} 初始化浏览器中...")
            driver = self._initialize_browser(thread_index)

            if not driver:
                self.status_signal.emit(f"❌ 工作窃取线程 #{thread_index} 浏览器初始化失败")
                return

            self.status_signal.emit(f"✅ 工作窃取线程 #{thread_index} 浏览器初始化成功，开始处理任务")

            # 持续从队列中获取任务
            while not self.is_cancelled and self.is_running:
                try:
                    # 检查对象是否仍然有效
                    if not self._is_object_valid():
                        debug(f"工作窃取线程 #{thread_index} 检测到对象已被删除，退出")
                        break

                    # 从队列中获取任务，超时1秒
                    import queue
                    account_task = self.task_queue.get(timeout=1)

                    # 再次检查停止标志
                    if self.is_cancelled or not self.is_running:
                        # 将任务放回队列
                        self.task_queue.put(account_task)
                        debug(f"工作窃取线程 #{thread_index} 检测到停止信号，退出")
                        break

                    # 解包任务数据
                    _, account_id, cookie_file = account_task
                    processed_count += 1

                    # 区分首次处理和重试处理的日志显示
                    if self.current_retry_round > 0:
                        self.status_signal.emit(f"🔄 工作窃取线程 #{thread_index} 重试处理账号 {account_id} (第{self.current_retry_round}轮重试)")
                    else:
                        self.status_signal.emit(f"🔄 工作窃取线程 #{thread_index} 开始处理账号 {account_id} (第{processed_count}个)")

                    # 清除浏览器会话（除了第一个任务）
                    if processed_count > 1:
                        self._clear_browser_session(driver)

                    # 处理账号任务
                    success = self._process_single_account(driver, account_id, cookie_file, thread_index)

                    # 标记任务完成
                    self.task_queue.task_done()

                    if success:
                        self.status_signal.emit(f"✅ 工作窃取线程 #{thread_index} 账号 {account_id} 处理成功")
                    else:
                        self.status_signal.emit(f"❌ 工作窃取线程 #{thread_index} 账号 {account_id} 处理失败")

                except queue.Empty:
                    # 队列为空，检查是否还有其他线程在工作或者是否被取消
                    if self.task_queue.empty() or self.is_cancelled or not self.is_running:
                        debug(f"工作窃取线程 #{thread_index} 队列为空或收到停止信号，准备退出")
                        break
                    continue
                except Exception as e:
                    error(f"工作窃取线程 #{thread_index} 处理任务时出错: {str(e)}")
                    # 标记任务完成（即使失败）
                    try:
                        self.task_queue.task_done()
                    except:
                        pass
                    continue

            debug(f"工作窃取线程 #{thread_index} 完成，共处理 {processed_count} 个账号")

        except Exception as e:
            error_msg = handle_error(f"工作窃取线程 #{thread_index} 执行", e)
            self.status_signal.emit(f"❌ {error_msg}")
        finally:
            # 清理浏览器
            if driver:
                try:
                    driver.quit()
                    debug(f"工作窃取线程 #{thread_index} 浏览器已关闭")
                except Exception as e:
                    debug(f"工作窃取线程 #{thread_index} 关闭浏览器时出错: {str(e)}")

    def _initialize_browser(self, thread_index):
        """初始化浏览器实例"""
        try:
            # 根据参数决定是否使用指纹浏览器
            if self.use_fingerprint and self.fingerprint_settings:
                try:
                    from app.utils.browser_proxy import BrowserProxyManager
                    self.status_signal.emit(f"🔒 线程 #{thread_index} 使用指纹浏览器配置")

                    # 创建指纹浏览器设置
                    proxy_fingerprint_settings = {
                        'enable_fingerprint': True,
                        'enable_random_ua': self.fingerprint_settings.get('enable_random_ua', True),
                        'device_type': self.fingerprint_settings.get('device_type', '自动随机'),
                        'browser_type': self.fingerprint_settings.get('browser_type', '自动随机'),
                        'resolution_type': self.fingerprint_settings.get('resolution_type', '随机分辨率'),
                        'pixel_ratio': self.fingerprint_settings.get('pixel_ratio', '随机'),
                        'browser_language': self.fingerprint_settings.get('browser_language', '中文'),
                        'randomize_canvas': self.fingerprint_settings.get('randomize_canvas', True),
                        'disable_webrtc': self.fingerprint_settings.get('disable_webrtc', True)
                    }

                    # 创建代理管理器并配置Chrome选项
                    proxy_manager = BrowserProxyManager(proxy_fingerprint_settings)
                    chrome_options = proxy_manager.configure_chrome_options(headless=self.headless_mode)

                    # 设置用户数据目录
                    import tempfile
                    import os
                    custom_user_dir = os.path.join(tempfile.gettempdir(), f"chrome_user_data_{thread_index}")
                    chrome_options.add_argument(f"--user-data-dir={custom_user_dir}")

                    # 创建WebDriver实例
                    from app.utils.chromedriver_manager import create_chrome_driver
                    driver = create_chrome_driver(chrome_options)

                    # 设置页面加载超时（与单独登录保持一致）
                    page_timeout = getattr(self, 'timeout_settings', {}).get('page_load_timeout', 15)  # 默认15秒
                    driver.set_page_load_timeout(page_timeout)

                    # 应用反检测脚本
                    if self.fingerprint_settings.get('enable_anti_detection', True):
                        try:
                            # 直接调用方法，因为我们已经确认它存在
                            proxy_manager.apply_anti_detection_script(driver)
                        except AttributeError:
                            # 如果方法不存在，使用手动应用反检测脚本
                                anti_automation_js = """
                                // 隐藏自动化特征
                                Object.defineProperty(navigator, 'webdriver', {
                                    get: () => false,
                                });

                                // 修改navigator.plugins
                                const makePluginsLookNatural = () => {
                                    if (navigator.plugins.length === 0) {
                                        Object.defineProperty(navigator, 'plugins', {
                                            get: () => [1, 2, 3, 4, 5],
                                        });
                                    }
                                };
                                makePluginsLookNatural();

                                // 随机化Canvas指纹
                                if (arguments[0] && arguments[0].randomize_canvas) {
                                    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
                                    HTMLCanvasElement.prototype.toDataURL = function(type) {
                                        if (this.width > 5 && this.height > 5) {
                                            const ctx = this.getContext('2d');
                                            const imgData = ctx.getImageData(0, 0, this.width, this.height);
                                            const data = imgData.data;

                                            // 随机修改一些像素
                                            for (let i = 0; i < data.length; i += 4) {
                                                if (Math.random() < 0.01) {
                                                    data[i] = data[i] ^ 1;
                                                }
                                            }

                                            ctx.putImageData(imgData, 0, 0);
                                        }
                                        return originalToDataURL.apply(this, arguments);
                                    };
                                }
                                """
                                driver.execute_script(anti_automation_js, self.fingerprint_settings)
                        except Exception as e:
                            self.status_signal.emit(f"⚠️ 线程 #{thread_index} 应用反检测脚本时出错: {str(e)}")

                    return driver

                except ImportError:
                    self.status_signal.emit(f"⚠️ 线程 #{thread_index} 指纹浏览器模块未找到，使用标准配置")
                    # 使用标准配置
                    return self._create_standard_browser(thread_index)
            else:
                # 使用标准浏览器配置
                return self._create_standard_browser(thread_index)

        except Exception as e:
            error(f"初始化浏览器失败: {str(e)}")
            return None

    def _create_standard_browser(self, thread_index):
        """创建标准浏览器实例"""
        try:
            self.status_signal.emit(f"🌐 线程 #{thread_index} 使用标准浏览器配置")
            from selenium.webdriver.chrome.options import Options
            chrome_options = Options()
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-extensions")
            if self.headless_mode:
                chrome_options.add_argument("--headless")

            # 设置用户数据目录
            import tempfile
            import os
            custom_user_dir = os.path.join(tempfile.gettempdir(), f"chrome_user_data_{thread_index}")
            chrome_options.add_argument(f"--user-data-dir={custom_user_dir}")

            # 创建WebDriver实例
            from app.utils.chromedriver_manager import create_chrome_driver
            driver = create_chrome_driver(chrome_options)

            # 设置页面加载超时（与单独登录保持一致）
            page_timeout = getattr(self, 'timeout_settings', {}).get('page_load_timeout', 15)  # 默认15秒
            driver.set_page_load_timeout(page_timeout)
            return driver
        except Exception as e:
            error(f"创建标准浏览器失败: {str(e)}")
            return None

    def _process_single_account(self, driver, account_id, cookie_file, thread_index):
        """处理单个账号任务"""
        try:
            # 登录账号
            login_result = self._login_with_cookie(driver, cookie_file)
            if not login_result:
                # 检查是否是因为未实名而失败
                if hasattr(self, 'account_tab_ref') and self.account_tab_ref:
                    try:
                        # 获取账号当前状态，判断是否为未实名
                        row = self.account_tab_ref.find_account_row_by_id(account_id)
                        if row is not None:
                            status_item = self.account_tab_ref.table.item(row, 2)  # 状态列
                            if status_item and status_item.text() == "未实名":
                                # 已经是未实名状态，不需要再次更新
                                self.status_signal.emit(f"❌ 账号 {account_id} 未实名认证")
                                with self.update_lock:
                                    self.failed_accounts += 1
                                    # 未实名账号不进入重试队列
                                    self._collect_failed_account(account_id, cookie_file, "未实名")
                                return False
                    except Exception as e:
                        debug(f"检查账号状态时出错: {str(e)}")

                # 普通登录失败
                self.status_signal.emit(f"❌ 账号 {account_id} 登录失败")
                # 更新表格状态为"登录失败"
                self._update_table_status(account_id, "登录失败", "login_failed")
                # 更新登录失败统计（线程安全）
                with self.update_lock:
                    self.login_failed_accounts += 1
                    # 收集失败账号信息
                    self._collect_failed_account(account_id, cookie_file, "登录失败")
                return False
            else:
                # 登录成功日志
                self.status_signal.emit(f"✅ 账号 {account_id} 登录成功")

            # 发送账号开始进度信号
            self.account_progress_signal.emit(account_id, "开始", "准备执行存稿任务", 0, self.draft_count)

            # 更新表格状态为"等待中"
            self._update_table_status(account_id, "等待中", "waiting")

            # 如果有账号标签页引用，也发送到账号标签页
            if hasattr(self, 'account_tab_ref') and self.account_tab_ref:
                try:
                    self.account_tab_ref.on_account_progress_update(account_id, "开始", "准备执行存稿任务", 0, self.draft_count)
                except Exception as e:
                    debug(f"发送账号开始进度到账号标签页失败: {str(e)}")

            # 执行存稿任务（_perform_draft_task内部已包含多次存稿循环）
            success = self._perform_draft_task(driver, account_id)

            if success:
                # 发送账号完成进度信号
                self.account_progress_signal.emit(account_id, "完成", f"已完成 {self.draft_count} 次存稿", self.draft_count, self.draft_count)

                # 更新表格状态为"存稿完成"
                self._update_table_status(account_id, "存稿完成", "success")

                # 如果有账号标签页引用，也发送到账号标签页
                if hasattr(self, 'account_tab_ref') and self.account_tab_ref:
                    try:
                        self.account_tab_ref.on_account_progress_update(account_id, "完成", f"已完成 {self.draft_count} 次存稿", self.draft_count, self.draft_count)
                    except Exception as e:
                        debug(f"发送账号完成进度到账号标签页失败: {str(e)}")

                # 更新成功统计（线程安全）
                with self.update_lock:
                    self.success_accounts += 1
                    # 如果是重试轮次，更新重试成功统计
                    if self.current_retry_round > 0:
                        self.retry_success_accounts += 1

                # 更新完成计数（按账号数量计算）
                with self.update_lock:
                    self.completed_count += 1  # 一个账号完成
                    progress_percent = int(self.completed_count / self.total_count * 100)
                    self.progress_signal.emit(self.completed_count, self.total_count)
                    self.status_signal.emit(f"📊 进度: {self.completed_count}/{self.total_count} ({progress_percent}%)")
                return True
            else:
                # 发送账号失败进度信号
                self.account_progress_signal.emit(account_id, "失败", "存稿任务失败", 0, self.draft_count)

                # 更新表格状态为"存稿失败"
                self._update_table_status(account_id, "存稿失败", "failed")

                # 如果有账号标签页引用，也发送到账号标签页
                if hasattr(self, 'account_tab_ref') and self.account_tab_ref:
                    try:
                        self.account_tab_ref.on_account_progress_update(account_id, "失败", "存稿任务失败", 0, self.draft_count)
                    except Exception as e:
                        debug(f"发送账号失败进度到账号标签页失败: {str(e)}")

                # 更新失败统计（线程安全）
                with self.update_lock:
                    self.failed_accounts += 1
                    # 收集失败账号信息
                    self._collect_failed_account(account_id, cookie_file, "存稿失败")

                # 简洁的失败日志显示
                self.status_signal.emit(f"❌ 账号 {account_id} 存稿失败")
                return False

        except Exception as e:
            # 简洁的异常日志显示
            self.status_signal.emit(f"❌ 账号 {account_id} 处理异常")
            # 收集失败账号信息
            with self.update_lock:
                self._collect_failed_account(account_id, cookie_file, "处理失败")
            return False

    def _collect_failed_account(self, account_id, cookie_file, failure_reason):
        """收集失败账号信息"""
        try:
            # 未实名账号不进入重试队列
            if failure_reason == "未实名":
                debug(f"账号 {account_id} 因未实名而失败，不加入重试队列")
                return

            # 检查是否已经在失败列表中（避免重复添加）
            for failed_account in self.failed_account_list:
                if failed_account['account_id'] == account_id:
                    # 更新失败原因
                    failed_account['failure_reason'] = failure_reason
                    return

            # 添加新的失败账号
            failed_account_info = {
                'account_id': account_id,
                'cookie_file': cookie_file,
                'failure_reason': failure_reason,
                'retry_count': 0,  # 重试次数
                'video_upload_retry_count': self.video_upload_retry_counts.get(account_id, 0)  # 视频上传重试次数
            }
            self.failed_account_list.append(failed_account_info)
            debug(f"收集失败账号: {account_id}, 原因: {failure_reason}")

            # 执行失败账号文件管理（只有可重试的失败账号才备份）
            self._manage_failed_account_files(account_id, cookie_file, failure_reason)

        except Exception as e:
            error(f"收集失败账号信息时出错: {str(e)}")

    def _manage_failed_account_files(self, account_id, cookie_file, failure_reason):
        """管理失败账号文件 - 复制到失败账号文件夹"""
        try:
            import os
            import shutil
            from app.utils.logger import info, warning, error

            # 获取cookie文件的目录路径
            cookie_dir = os.path.dirname(cookie_file)

            # 获取账号根目录（cookie目录的父目录）
            account_root_dir = os.path.dirname(cookie_dir)

            # 创建失败账号文件夹路径
            failed_accounts_dir = os.path.join(account_root_dir, "失败账号")

            # 确保失败账号文件夹存在
            if not os.path.exists(failed_accounts_dir):
                os.makedirs(failed_accounts_dir)
                info(f"创建失败账号文件夹: {failed_accounts_dir}")

            # 获取账号文件夹名称（通常是账号ID）
            account_folder_name = os.path.basename(cookie_dir)

            # 目标路径
            target_dir = os.path.join(failed_accounts_dir, account_folder_name)

            # 如果目标文件夹已存在，先删除（避免重复）
            if os.path.exists(target_dir):
                shutil.rmtree(target_dir)
                info(f"删除已存在的失败账号文件夹: {target_dir}")

            # 复制整个账号文件夹到失败账号目录
            shutil.copytree(cookie_dir, target_dir)
            info(f"失败账号 {account_id} 文件已复制到: {target_dir}")

            # 在目标文件夹中创建失败原因记录文件
            failure_record_file = os.path.join(target_dir, "失败原因.txt")
            with open(failure_record_file, 'w', encoding='utf-8') as f:
                f.write(f"账号ID: {account_id}\n")
                f.write(f"失败原因: {failure_reason}\n")
                f.write(f"失败时间: {self._get_current_time()}\n")
                f.write(f"原始路径: {cookie_dir}\n")
                f.write(f"备份路径: {target_dir}\n")
                f.write(f"文件大小: {self._get_folder_size(target_dir)} bytes\n")

            self.status_signal.emit(f"📁 失败账号 {account_id} 已备份到失败账号文件夹")
            info(f"失败账号文件管理完成: {account_id} -> {target_dir}")

        except Exception as e:
            error(f"管理失败账号文件时出错: {str(e)}")
            # 文件管理失败不应影响主流程，只记录错误
            warning(f"账号 {account_id} 文件备份失败，但不影响主流程继续")

    def _get_current_time(self):
        """获取当前时间字符串"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def _get_folder_size(self, folder_path):
        """获取文件夹大小"""
        try:
            import os
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(folder_path):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
            return total_size
        except Exception as e:
            error(f"计算文件夹大小时出错: {str(e)}")
            return 0

    def _show_retry_confirmation_dialog(self):
        """显示重试确认对话框"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            from PyQt5.QtCore import QTimer

            # 过滤出可重试的失败账号（排除未实名账号）
            retryable_accounts = [
                account for account in self.failed_account_list
                if account['failure_reason'] != "未实名"
            ]
            failed_count = len(retryable_accounts)

            # 构建失败账号信息
            failed_info = []
            for failed_account in retryable_accounts[:5]:  # 最多显示5个
                account_id = failed_account['account_id']
                failure_reason = failed_account['failure_reason']
                failed_info.append(f"• {account_id} ({failure_reason})")

            if failed_count > 5:
                failed_info.append(f"... 还有 {failed_count - 5} 个失败账号")

            failed_details = "\n".join(failed_info)

            # 使用QTimer在主线程中显示对话框
            def show_dialog():
                try:
                    # 统计未实名账号数量
                    unverified_count = len(self.failed_account_list) - failed_count

                    # 构建对话框消息
                    message = f"检测到 {failed_count} 个账号可重试：\n\n{failed_details}"
                    if unverified_count > 0:
                        message += f"\n\n注意：{unverified_count} 个未实名账号已排除（无法重试）"
                    message += "\n\n是否继续重试失败账号任务？"

                    reply = QMessageBox.question(
                        None,
                        "失败账号重试确认",
                        message,
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.Yes
                    )

                    if reply == QMessageBox.Yes:
                        # 用户选择重试
                        self._safe_emit_signal("status_signal", f"🔄 用户确认重试 {failed_count} 个失败账号")
                        info(f"用户确认重试失败账号，失败账号数: {failed_count}")
                        self._start_failed_accounts_retry()
                    else:
                        # 用户选择不重试，直接完成任务
                        total_failed = len(self.failed_account_list)
                        self._safe_emit_signal("status_signal", f"📊 用户取消重试，任务完成（{total_failed} 个账号失败）")
                        info(f"用户取消重试失败账号，最终失败账号数: {total_failed}")
                        self._finalize_task_completion()

                except Exception as e:
                    error(f"显示重试确认对话框时出错: {str(e)}")
                    # 出错时直接完成任务
                    self._finalize_task_completion()

            QTimer.singleShot(100, show_dialog)

        except Exception as e:
            error(f"准备重试确认对话框时出错: {str(e)}")
            # 出错时直接完成任务
            self._finalize_task_completion()

    def _start_failed_accounts_retry(self):
        """启动失败账号重试"""
        try:
            import os

            # 获取失败账号文件夹路径
            cookie_dir = getattr(self, 'cookie_dir', '')
            if not cookie_dir:
                error("无法获取cookie目录路径，重试失败")
                self._finalize_task_completion()
                return

            account_root_dir = os.path.dirname(cookie_dir)
            failed_accounts_dir = os.path.join(account_root_dir, "失败账号")

            if not os.path.exists(failed_accounts_dir):
                self._safe_emit_signal("status_signal", "❌ 失败账号文件夹不存在，无法重试")
                self._finalize_task_completion()
                return

            # 扫描失败账号文件夹，构建重试账号列表
            retry_account_list = []
            failed_folders = [f for f in os.listdir(failed_accounts_dir)
                            if os.path.isdir(os.path.join(failed_accounts_dir, f))]

            for folder_name in failed_folders:
                folder_path = os.path.join(failed_accounts_dir, folder_name)
                # 查找cookie文件
                cookie_files = [f for f in os.listdir(folder_path) if f.endswith('.json')]

                if cookie_files:
                    cookie_file = os.path.join(folder_path, cookie_files[0])
                    account_id = os.path.splitext(cookie_files[0])[0]

                    # 构造重试任务格式 (row, account_id, cookie_file)
                    retry_task = (-1, account_id, cookie_file)
                    retry_account_list.append(retry_task)

            if not retry_account_list:
                self._safe_emit_signal("status_signal", "❌ 失败账号文件夹中没有找到有效的账号文件")
                self._finalize_task_completion()
                return

            self._safe_emit_signal("status_signal", f"🔄 开始重试 {len(retry_account_list)} 个失败账号...")
            info(f"开始失败账号重试流程，重试账号数: {len(retry_account_list)}")
            info(f"失败账号文件夹路径: {failed_accounts_dir}")

            # 记录重试账号详情
            for retry_task in retry_account_list:
                _, account_id, cookie_file = retry_task
                info(f"准备重试账号: {account_id}, Cookie文件: {cookie_file}")

            # 清空当前失败账号列表
            self.failed_account_list.clear()

            # 重置统计数据
            self.failed_accounts = 0
            self.login_failed_accounts = 0

            # 启动重试任务
            import threading
            retry_thread = threading.Thread(
                target=self._run_batch_draft,
                args=(retry_account_list,),
                daemon=True
            )
            retry_thread.start()

        except Exception as e:
            error(f"启动失败账号重试时出错: {str(e)}")
            self._finalize_task_completion()

    def _finalize_task_completion(self):
        """最终完成任务"""
        try:
            # 统计任务结果
            self._safe_calculate_task_statistics()

            # 清理资源
            self._safe_cleanup_resources()

            # 重置界面状态
            self._safe_reset_ui_state()

            # 重置工作器状态
            self._safe_reset_worker_state()

            # 发送完成信号
            self._safe_emit_signal("status_signal", "🎉 所有批量存稿任务已完成！")
            info("批量存稿任务最终完成")

            from PyQt5.QtCore import QTimer
            QTimer.singleShot(500, self._safe_emit_finished_signal)

        except Exception as e:
            error(f"最终完成任务时出错: {str(e)}")
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(100, self._safe_emit_finished_signal)

    def _detect_video_upload_failure(self, driver, max_wait_time=30, check_interval=0.5):
        """并行检测视频上传失败状态

        Args:
            driver: WebDriver实例
            max_wait_time: 最大检测时间（秒）
            check_interval: 检查间隔（秒）

        Returns:
            bool: True表示检测到上传失败，False表示未检测到失败
        """
        try:
            start_time = time.time()

            # 视频上传失败的关键词 - 只检测明确的上传失败关键词
            failure_keywords = [
                "上传失败",
                "上传错误",
                "文件格式不支持",
                "文件过大",
                "上传超时",
                "视频格式不正确",
                "上传异常"
            ]

            # 失败状态的XPath选择器 - 只检测明确的失败文本
            failure_xpaths = [
                "//*[contains(text(), '上传失败')]",
                "//*[contains(text(), '上传错误')]",
                "//*[contains(text(), '文件格式不支持')]",
                "//*[contains(text(), '文件过大')]",
                "//*[contains(text(), '上传超时')]"
            ]

            while time.time() - start_time < max_wait_time:
                try:
                    # 方法1: 使用XPath查找失败元素，并检查元素是否可见
                    for xpath in failure_xpaths:
                        failure_elements = driver.find_elements(By.XPATH, xpath)
                        for element in failure_elements:
                            try:
                                # 检查元素是否可见
                                if element.is_displayed():
                                    self.status_signal.emit("❌ 检测到视频上传失败元素 (XPath方式)")
                                    return True
                            except:
                                continue

                    # 方法2: 使用JavaScript检查页面可见文本中的失败关键词
                    js_result = driver.execute_script("""
                        var failureKeywords = arguments[0];
                        var bodyText = document.body.innerText.toLowerCase();
                        for (var i = 0; i < failureKeywords.length; i++) {
                            var keyword = failureKeywords[i].toLowerCase();
                            if (bodyText.includes(keyword)) {
                                // 检查关键词是否在可见的错误提示中
                                var elements = document.querySelectorAll('*');
                                for (var j = 0; j < elements.length; j++) {
                                    var element = elements[j];
                                    if (element.innerText && element.innerText.toLowerCase().includes(keyword)) {
                                        var style = window.getComputedStyle(element);
                                        if (style.display !== 'none' && style.visibility !== 'hidden') {
                                            return keyword;
                                        }
                                    }
                                }
                            }
                        }
                        return null;
                    """, failure_keywords)

                    if js_result:
                        self.status_signal.emit(f"❌ 检测到视频上传失败关键词: {js_result} (JavaScript可见文本方式)")
                        return True



                    # 方法4: 检查错误弹窗或提示框
                    error_popup_selectors = [
                        "div[class*='error']",
                        "div[class*='fail']",
                        "div[class*='alert']",
                        ".error-message",
                        ".upload-error",
                        ".error-popup"
                    ]

                    for selector in error_popup_selectors:
                        try:
                            error_elements = driver.find_elements(By.CSS_SELECTOR, selector)
                            for element in error_elements:
                                if element.is_displayed():
                                    element_text = element.text.lower()
                                    for keyword in failure_keywords:
                                        if keyword.lower() in element_text:
                                            self.status_signal.emit(f"❌ 检测到错误弹窗: {keyword}")
                                            return True
                        except:
                            continue

                    # 等待检查间隔
                    time.sleep(check_interval)

                except Exception as e:
                    # 检查是否是连接错误（浏览器已关闭）
                    if "connection" in str(e).lower() or "session" in str(e).lower():
                        debug("检测视频上传失败状态时检测到连接错误，浏览器可能已关闭，退出检测")
                        return False
                    # 其他错误，记录但不输出警告日志，避免持续输出
                    debug(f"检测视频上传失败状态时出错: {str(e)}")
                    time.sleep(check_interval)

            return False  # 未检测到失败状态

        except Exception as e:
            warning(f"视频上传失败检测异常: {str(e)}")
            return False

    def _detect_video_upload_failure_with_stop(self, driver, upload_status, max_wait_time=30, check_interval=0.5):
        """带停止检测的视频上传失败检测方法

        Args:
            driver: WebDriver实例
            upload_status: 共享状态字典，包含stop标志
            max_wait_time: 最大检测时间（秒）
            check_interval: 检查间隔（秒）

        Returns:
            bool: True表示检测到失败，False表示未检测到失败或被停止
        """
        try:
            from selenium.webdriver.common.by import By
            import time

            # 失败关键词 - 只检测明确的上传失败关键词
            failure_keywords = [
                "上传失败", "upload failed", "上传错误",
                "网络错误", "network error", "连接超时",
                "文件格式不支持", "unsupported format", "文件过大", "file too large",
                "上传超时"
            ]

            # 失败元素的XPath - 只检测明确的上传失败文本
            failure_xpaths = [
                "//*[contains(text(), '上传失败')]",
                "//*[contains(text(), 'upload failed')]",
                "//*[contains(text(), '上传错误')]",
                "//*[contains(text(), '文件格式不支持')]",
                "//*[contains(text(), '文件过大')]",
                "//*[contains(text(), '上传超时')]"
            ]

            start_time = time.time()

            while time.time() - start_time < max_wait_time:
                try:
                    # 检查停止标志
                    if upload_status["stop"]:
                        debug("视频上传失败检测收到停止信号，退出")
                        return False

                    # 检查工作器是否已被取消或停止
                    if self.is_cancelled or not self.is_running:
                        upload_status["stop"] = True
                        debug("视频上传失败检测检测到工作器停止信号，退出")
                        return False

                    # 检查对象是否仍然有效
                    if not self._is_object_valid():
                        upload_status["stop"] = True
                        debug("视频上传失败检测检测到对象已被删除，退出")
                        return False

                    # 方法1: 使用XPath查找失败元素，并检查元素是否可见
                    for xpath in failure_xpaths:
                        failure_elements = driver.find_elements(By.XPATH, xpath)
                        for element in failure_elements:
                            try:
                                # 检查元素是否可见
                                if element.is_displayed():
                                    debug("检测到视频上传失败元素 (XPath方式)")
                                    return True
                            except:
                                continue

                    # 方法2: 使用JavaScript检查页面可见文本中的失败关键词
                    js_result = driver.execute_script("""
                        var failureKeywords = arguments[0];
                        var bodyText = document.body.innerText.toLowerCase();
                        for (var i = 0; i < failureKeywords.length; i++) {
                            var keyword = failureKeywords[i].toLowerCase();
                            if (bodyText.includes(keyword)) {
                                // 检查关键词是否在可见的错误提示中
                                var elements = document.querySelectorAll('*');
                                for (var j = 0; j < elements.length; j++) {
                                    var element = elements[j];
                                    if (element.innerText && element.innerText.toLowerCase().includes(keyword)) {
                                        var style = window.getComputedStyle(element);
                                        if (style.display !== 'none' && style.visibility !== 'hidden') {
                                            return keyword;
                                        }
                                    }
                                }
                            }
                        }
                        return null;
                    """, failure_keywords)

                    if js_result:
                        debug(f"检测到视频上传失败关键词: {js_result} (JavaScript可见文本方式)")
                        return True

                    # 等待检查间隔
                    time.sleep(check_interval)

                except Exception as e:
                    # 检查是否是连接错误（浏览器已关闭）
                    if "connection" in str(e).lower() or "session" in str(e).lower():
                        upload_status["stop"] = True
                        debug("视频上传失败检测检测到连接错误，浏览器可能已关闭，退出")
                        return False
                    # 其他错误，记录但不输出警告日志，避免持续输出
                    debug(f"检测视频上传失败状态时出错: {str(e)}")
                    time.sleep(check_interval)

            return False  # 未检测到失败状态

        except Exception as e:
            debug(f"视频上传失败检测异常: {str(e)}")
            return False

    def _handle_video_upload_failure(self, driver, account_id, cookie_file):
        """处理视频上传失败 - 使用新的重试管理器

        Args:
            driver: WebDriver实例
            account_id: 账号ID
            cookie_file: Cookie文件路径

        Returns:
            bool: True表示可以重试，False表示超过重试次数
        """
        try:
            # 确保重试管理器已初始化
            if not self.retry_manager:
                self.retry_manager = RetryManager(self.retry_settings)

            # 使用重试管理器检查是否可以重试
            if not self.retry_manager.can_operation_retry(account_id, 'video_upload'):
                current_count = self.retry_manager.operation_retry_counts.get(account_id, {}).get('video_upload', 0)
                max_count = self.retry_manager.video_upload_retry_count
                self.status_signal.emit(f"❌ 账号 {account_id} 视频上传失败，已达到最大重试次数 ({max_count})")
                # 更新表格状态为"上传失败"
                self._update_table_status(account_id, "上传失败", "failed")
                # 收集到最终失败账号列表
                self._collect_failed_account(account_id, cookie_file, "上传失败")
                return False

            # 记录重试并获取当前重试次数
            retry_count = self.retry_manager.record_operation_retry(account_id, 'video_upload')
            self.status_signal.emit(f"🔄 账号 {account_id} 视频上传失败，开始第 {retry_count} 次重试...")

            # 等待重试间隔
            retry_delay = self.retry_manager.get_operation_retry_delay()
            if retry_delay > 0:
                self.status_signal.emit(f"⏱️ 账号 {account_id} 等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)

            # 尝试返回视频上传界面
            if self._navigate_back_to_upload_page(driver, account_id):
                self.status_signal.emit(f"✅ 账号 {account_id} 已返回视频上传界面，准备重试")
                return True
            else:
                self.status_signal.emit(f"❌ 账号 {account_id} 无法返回视频上传界面")
                self._collect_failed_account(account_id, cookie_file, f"无法返回上传界面(重试{retry_count}次)")
                return False

        except Exception as e:
            error(f"处理视频上传失败时出错: {str(e)}")
            return False

    def _navigate_back_to_upload_page(self, driver, account_id):
        """导航回到视频上传页面

        Args:
            driver: WebDriver实例
            account_id: 账号ID

        Returns:
            bool: 是否成功返回上传页面
        """
        try:
            self.status_signal.emit(f"🔄 账号 {account_id} 尝试返回视频上传页面...")

            # 方法1: 刷新页面
            try:
                driver.refresh()
                time.sleep(3)  # 等待页面加载

                # 检查是否回到了上传页面
                if self._check_upload_page_loaded(driver):
                    self.status_signal.emit(f"✅ 账号 {account_id} 通过刷新页面成功返回上传界面")
                    return True
            except Exception as e:
                warning(f"刷新页面失败: {str(e)}")

            # 方法2: 点击返回按钮或重新上传按钮
            try:
                back_button_selectors = [
                    "//button[contains(text(), '重新上传')]",
                    "//button[contains(text(), '返回')]",
                    "//button[contains(text(), '重试')]",
                    "//a[contains(text(), '重新上传')]",
                    "//*[@class='back-button']",
                    "//*[@class='retry-button']"
                ]

                for selector in back_button_selectors:
                    try:
                        back_button = driver.find_element(By.XPATH, selector)
                        if back_button.is_displayed():
                            back_button.click()
                            time.sleep(2)

                            if self._check_upload_page_loaded(driver):
                                self.status_signal.emit(f"✅ 账号 {account_id} 通过点击按钮成功返回上传界面")
                                return True
                    except:
                        continue
            except Exception as e:
                warning(f"点击返回按钮失败: {str(e)}")

            # 方法3: 重新导航到上传页面URL
            try:
                upload_url = "https://mp.toutiao.com/profile_v4/graphic/publish"
                driver.get(upload_url)
                time.sleep(3)

                if self._check_upload_page_loaded(driver):
                    self.status_signal.emit(f"✅ 账号 {account_id} 通过URL导航成功返回上传界面")
                    return True
            except Exception as e:
                warning(f"URL导航失败: {str(e)}")

            return False

        except Exception as e:
            error(f"导航回上传页面时出错: {str(e)}")
            return False

    def _check_upload_page_loaded(self, driver):
        """检查上传页面是否已加载

        Args:
            driver: WebDriver实例

        Returns:
            bool: 上传页面是否已加载
        """
        try:
            # 检查上传页面的特征元素
            upload_indicators = [
                "input[type='file']",
                "//*[contains(text(), '上传视频')]",
                "//*[contains(text(), '选择文件')]",
                "//*[contains(@class, 'upload')]"
            ]

            for indicator in upload_indicators:
                try:
                    if indicator.startswith("//"):
                        elements = driver.find_elements(By.XPATH, indicator)
                    else:
                        elements = driver.find_elements(By.CSS_SELECTOR, indicator)

                    if elements and len(elements) > 0:
                        return True
                except:
                    continue

            return False

        except Exception as e:
            warning(f"检查上传页面加载状态时出错: {str(e)}")
            return False

    def _monitor_video_upload_status(self, driver, account_id, video_file, max_wait_time=300):
        """并行监测视频上传成功和失败状态

        Args:
            driver: WebDriver实例
            account_id: 账号ID
            video_file: 视频文件名
            max_wait_time: 最大等待时间（秒），默认5分钟

        Returns:
            str: "success"表示上传成功，"failure"表示上传失败，"timeout"表示超时
        """
        try:
            import threading
            import time

            self.status_signal.emit(f"🔍 账号 {account_id} 开始并行监测视频上传状态...")

            # 共享状态变量
            upload_status = {"result": None, "stop": False}

            def monitor_success():
                """监测上传成功的线程"""
                try:
                    check_interval = 0.5
                    start_time = time.time()

                    while time.time() - start_time < max_wait_time and not upload_status["stop"]:
                        try:
                            # 方法1: 使用XPath查找包含"上传成功"的元素
                            success_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '上传成功')]")
                            if success_elements and len(success_elements) > 0:
                                upload_status["result"] = "success"
                                upload_status["stop"] = True
                                return

                            # 方法2: 检查页面源代码
                            if "上传成功" in driver.page_source:
                                upload_status["result"] = "success"
                                upload_status["stop"] = True
                                return

                            # 方法3: 使用JavaScript检查
                            js_result = driver.execute_script("""
                                return document.body.innerText.includes('上传成功');
                            """)
                            if js_result:
                                upload_status["result"] = "success"
                                upload_status["stop"] = True
                                return

                            # 方法4: 检查"开始填写"文本
                            start_fill_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '开始填写')]")
                            if start_fill_elements and len(start_fill_elements) > 0:
                                upload_status["result"] = "success"
                                upload_status["stop"] = True
                                return

                            time.sleep(check_interval)

                        except Exception as e:
                            # 检查是否是连接错误（浏览器已关闭）
                            if "connection" in str(e).lower() or "session" in str(e).lower():
                                upload_status["stop"] = True
                                debug("监测上传成功线程检测到连接错误，浏览器可能已关闭，退出")
                                return
                            # 检查工作器是否已被取消或停止
                            if self.is_cancelled or not self.is_running:
                                upload_status["stop"] = True
                                debug("监测上传成功线程检测到工作器停止信号，退出")
                                return
                            # 其他错误，继续监测
                            time.sleep(check_interval)

                except Exception as e:
                    debug(f"监测上传成功时出错: {str(e)}")

            def monitor_failure():
                """监测上传失败的线程"""
                try:
                    # 使用修改后的失败检测方法，传递停止状态
                    if self._detect_video_upload_failure_with_stop(driver, upload_status, max_wait_time, 0.5):
                        upload_status["result"] = "failure"
                        upload_status["stop"] = True
                except Exception as e:
                    debug(f"监测上传失败时出错: {str(e)}")

            # 启动并行监测线程
            success_thread = threading.Thread(target=monitor_success, daemon=True)
            failure_thread = threading.Thread(target=monitor_failure, daemon=True)

            success_thread.start()
            failure_thread.start()

            # 等待任一线程完成或超时
            start_time = time.time()
            while time.time() - start_time < max_wait_time:
                if upload_status["result"] is not None:
                    break
                time.sleep(0.1)

            # 停止监测
            upload_status["stop"] = True

            # 等待线程结束（最多等待1秒）
            success_thread.join(timeout=1)
            failure_thread.join(timeout=1)

            result = upload_status["result"]
            if result == "success":
                self.status_signal.emit(f"✅ 账号 {account_id} 视频上传成功检测完成")
            elif result == "failure":
                self.status_signal.emit(f"❌ 账号 {account_id} 视频上传失败检测完成")
            else:
                self.status_signal.emit(f"⏰ 账号 {account_id} 视频上传状态检测超时")
                result = "timeout"

            return result

        except Exception as e:
            error(f"并行监测视频上传状态时出错: {str(e)}")
            return "timeout"

    def _should_retry_failed_accounts(self):
        """检查是否应该重试失败账号 - 使用新的重试管理器"""
        try:
            # 确保重试管理器已初始化
            if not self.retry_manager:
                self.retry_manager = RetryManager(self.retry_settings)

            # 检查是否有失败账号
            if not self.failed_account_list:
                return False

            # 使用重试管理器检查是否可以批次重试
            if not self.retry_manager.can_batch_retry():
                max_rounds = self.retry_manager.max_batch_retry_rounds
                current_round = self.retry_manager.batch_retry_round
                self._safe_emit_signal("status_signal", f"📊 已达到最大重试轮数 ({max_rounds})，停止重试")
                return False

            return True
        except Exception as e:
            error(f"检查重试条件时出错: {str(e)}")
            return False

    def _start_retry_process(self):
        """启动重试流程 - 使用新的重试管理器"""
        try:
            if not self.failed_account_list:
                return

            # 确保重试管理器已初始化
            if not self.retry_manager:
                self.retry_manager = RetryManager(self.retry_settings)

            # 记录批次重试
            retry_round = self.retry_manager.record_batch_retry()
            retry_delay = self.retry_manager.get_batch_retry_delay()

            self._safe_emit_signal("status_signal", f"🔄 第 {retry_round} 轮重试开始，等待 {retry_delay} 秒...")
            info(f"开始第 {retry_round} 轮重试，失败账号数: {len(self.failed_account_list)}")

            # 等待重试间隔
            import time
            time.sleep(retry_delay)

            # 准备重试账号列表
            retry_account_list = []
            for failed_account in self.failed_account_list:
                account_id = failed_account['account_id']
                cookie_file = failed_account['cookie_file']
                failure_reason = failed_account['failure_reason']

                # 构造重试任务格式 (row, account_id, cookie_file)
                # row 设为 -1 表示重试任务
                retry_task = (-1, account_id, cookie_file)
                retry_account_list.append(retry_task)

                # 更新重试次数
                failed_account['retry_count'] += 1

                self._safe_emit_signal("status_signal", f"🔄 重试账号 {account_id} (原因: {failure_reason})")

            # 清空失败账号列表，准备收集新的失败账号
            self.failed_account_list.clear()

            # 重置统计数据（保留原有成功数据）
            retry_failed_accounts = self.failed_accounts
            retry_login_failed_accounts = self.login_failed_accounts
            self.failed_accounts = 0
            self.login_failed_accounts = 0

            # 启动重试任务
            import threading
            retry_thread = threading.Thread(
                target=self._run_batch_draft,
                args=(retry_account_list,),
                daemon=True
            )
            retry_thread.start()

        except Exception as e:
            error(f"启动重试流程时出错: {str(e)}")
            # 重试失败时直接完成任务
            self._safe_emit_signal("status_signal", f"❌ 重试启动失败: {str(e)}")
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(100, self._safe_emit_finished_signal)

    def stop(self):
        """停止批量存稿任务 - 增强版本"""
        try:
            # 检查对象是否仍然有效
            if not self._is_object_valid():
                info("BatchCunggaoWorker对象已被删除，停止操作已完成")
                return

            self.is_cancelled = True
            self.is_running = False

            # 安全地发送状态信号
            self._safe_emit_signal("status_signal", "🛑 正在停止批量存稿任务...")

            # 设置停止标志，让所有线程知道需要停止
            self.is_cancelled = True
            self.is_running = False

            # 等待所有活动线程结束
            for thread in self.active_threads:
                try:
                    if thread.is_alive():
                        thread.join(timeout=2)  # 减少等待时间到2秒
                        if thread.is_alive():
                            debug(f"线程 {thread} 未能在2秒内停止")
                except Exception as e:
                    debug(f"等待线程停止时出错: {str(e)}")

            self.active_threads.clear()

            # 停止完成检查定时器
            if hasattr(self, 'completion_check_timer') and self.completion_check_timer:
                try:
                    if self.completion_check_timer.isActive():
                        self.completion_check_timer.stop()
                    self.completion_check_timer.deleteLater()
                    self.completion_check_timer = None
                    debug("已停止并删除完成检查定时器")
                except Exception as e:
                    debug(f"停止完成检查定时器时出错: {str(e)}")

            # 安全地发送停止完成信号
            self._safe_emit_signal("status_signal", "🛑 批量存稿任务已停止")

        except RuntimeError as e:
            if "wrapped C/C++ object" in str(e):
                info("对象已被删除，停止操作已完成")
            else:
                error(f"停止批量存稿任务时出错: {str(e)}")
        except Exception as e:
            error(f"停止批量存稿任务时出错: {str(e)}")

    def _cleanup_and_finalize(self):
        """任务完成后的清理和统计"""
        try:
            # 检查对象是否仍然有效
            if not self._is_object_valid():
                info("BatchCunggaoWorker对象已被删除，跳过清理过程")
                return

            # 立即设置停止标志，确保所有线程停止
            self.is_cancelled = True
            self.is_running = False
            info("已设置停止标志，所有工作窃取线程将停止")

            # 停止运行时间统计
            self._stop_runtime_timer()

            # 确保重试管理器已初始化
            if not self.retry_manager:
                self.retry_manager = RetryManager(self.retry_settings)

            # 检查是否需要重试失败账号
            if not self.is_cancelled and self._should_retry_failed_accounts():
                self._safe_emit_signal("status_signal", "🔄 检测到失败账号，准备启动重试...")
                # 启动重试流程
                self._start_retry_process()
                return  # 重试过程中不发送完成信号

            # 检查是否有失败账号需要处理（在没有自动重试的情况下）
            if not self.is_cancelled and self.failed_account_list and not self._should_retry_failed_accounts():
                # 使用重试管理器检查是否启用了自动重试失败账号功能
                auto_retry_failed = self.retry_manager.should_auto_batch_retry()

                if auto_retry_failed:
                    # 自动重试模式：直接使用失败账号文件夹中的账号重新执行
                    # 过滤出可重试的失败账号（排除未实名账号）
                    retryable_accounts = [
                        account for account in self.failed_account_list
                        if account['failure_reason'] != "未实名"
                    ]
                    failed_count = len(retryable_accounts)
                    total_failed = len(self.failed_account_list)

                    if failed_count > 0:
                        self._safe_emit_signal("status_signal", f"🔄 自动重试失败账号模式已启用，开始重试 {failed_count} 个失败账号...")
                        info(f"自动重试失败账号模式启用，可重试账号数: {failed_count}，总失败账号数: {total_failed}")
                        self._start_failed_accounts_retry()
                    else:
                        self._safe_emit_signal("status_signal", f"📊 无可重试账号（{total_failed} 个账号因未实名等原因无法重试）")
                        info(f"无可重试账号，总失败账号数: {total_failed}")
                        self._finalize_task_completion()
                    return  # 重试过程中不发送完成信号
                else:
                    # 手动确认模式：弹出对话框询问用户
                    failed_count = len(self.failed_account_list)
                    info(f"手动确认模式，显示重试确认对话框，失败账号数: {failed_count}")
                    self._show_retry_confirmation_dialog()
                    return  # 等待用户确认，不立即发送完成信号

            # 统计任务结果
            self._safe_calculate_task_statistics()

            # 清理资源
            self._safe_cleanup_resources()

            # 重置界面状态
            self._safe_reset_ui_state()

            # 重置工作器状态，确保下次可以正常启动
            self._safe_reset_worker_state()

            # 发送完成信号
            if not self.is_cancelled:
                self._safe_emit_signal("status_signal", "🎉 所有批量存稿任务已完成！")
                info("批量存稿任务正常完成")
                # 使用QTimer延迟发送完成信号，确保UI有时间处理
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(500, self._safe_emit_finished_signal)
            else:
                self._safe_emit_signal("status_signal", "🛑 批量存稿任务已取消")
                info("批量存稿任务被取消")
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(100, self._safe_emit_finished_signal)

        except Exception as e:
            error(f"清理和统计过程出错: {str(e)}")
            # 即使清理出错也要发送完成信号
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(100, self._safe_emit_finished_signal)

    def _is_object_valid(self):
        """检查PyQt对象是否仍然有效"""
        try:
            # 尝试访问一个基本属性来检查对象是否有效
            _ = self.objectName()
            return True
        except RuntimeError:
            # 对象已被删除
            return False
        except Exception:
            # 其他异常，假设对象仍然有效
            return True

    def _safe_emit_signal(self, signal_name, *args):
        """安全地发送信号"""
        try:
            if self._is_object_valid() and hasattr(self, signal_name):
                signal = getattr(self, signal_name)
                signal.emit(*args)
        except RuntimeError as e:
            if "wrapped C/C++ object" in str(e):
                debug(f"对象已被删除，跳过信号发送: {signal_name}")
            else:
                error(f"发送信号时出错: {str(e)}")
        except Exception as e:
            error(f"发送信号时出错: {str(e)}")

    def _safe_emit_finished_signal(self):
        """安全地发送完成信号"""
        self._safe_emit_signal("finished_signal")

    def _safe_stop_timer(self, timer):
        """安全地停止定时器"""
        try:
            if timer and hasattr(timer, 'isActive') and timer.isActive():
                # 使用QTimer.singleShot确保在正确线程中停止
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(0, timer.stop)
                debug("定时器已安全停止")
        except Exception as e:
            debug(f"停止定时器时出错: {str(e)}")

    def _disconnect_all_signals(self):
        """断开所有信号连接"""
        try:
            signals = [
                'status_signal', 'progress_signal', 'finished_signal',
                'status_update_signal', 'error_signal', 'account_progress_signal'
            ]

            for signal_name in signals:
                if hasattr(self, signal_name):
                    signal = getattr(self, signal_name)
                    try:
                        signal.disconnect()
                        debug(f"已断开信号连接: {signal_name}")
                    except Exception:
                        pass

        except Exception as e:
            debug(f"断开信号连接时出错: {str(e)}")

    def _safe_calculate_task_statistics(self):
        """安全地计算任务统计"""
        try:
            if not self._is_object_valid():
                return
            self._calculate_task_statistics()
        except RuntimeError as e:
            if "wrapped C/C++ object" in str(e):
                debug("对象已被删除，跳过统计计算")
            else:
                error(f"计算统计时出错: {str(e)}")
        except Exception as e:
            error(f"计算统计时出错: {str(e)}")

    def _safe_cleanup_resources(self):
        """安全地清理资源"""
        try:
            if not self._is_object_valid():
                return
            self._cleanup_resources()
        except RuntimeError as e:
            if "wrapped C/C++ object" in str(e):
                debug("对象已被删除，跳过资源清理")
            else:
                error(f"清理资源时出错: {str(e)}")
        except Exception as e:
            error(f"清理资源时出错: {str(e)}")

    def _safe_reset_ui_state(self):
        """安全地重置界面状态"""
        try:
            if not self._is_object_valid():
                return
            self._reset_ui_state()
        except RuntimeError as e:
            if "wrapped C/C++ object" in str(e):
                debug("对象已被删除，跳过界面状态重置")
            else:
                error(f"重置界面状态时出错: {str(e)}")
        except Exception as e:
            error(f"重置界面状态时出错: {str(e)}")

    def _safe_reset_worker_state(self):
        """安全地重置工作器状态"""
        try:
            if not self._is_object_valid():
                return
            self._reset_worker_state()
        except RuntimeError as e:
            if "wrapped C/C++ object" in str(e):
                debug("对象已被删除，跳过工作器状态重置")
            else:
                error(f"重置工作器状态时出错: {str(e)}")
        except Exception as e:
            error(f"重置工作器状态时出错: {str(e)}")

    def _calculate_task_statistics(self):
        """计算任务统计信息"""
        try:
            # 使用实时统计数据，而不是扫描表格
            # 这样可以确保统计的是本次任务的结果，而不是历史数据

            # 发送统计日志
            mode_text = "发布" if (hasattr(self, 'ban_mode') and self.ban_mode) else "存稿"

            # 计算总的处理账号数（成功+失败+登录失败）
            total_processed = self.success_accounts + self.failed_accounts + self.login_failed_accounts

            self.status_signal.emit(f"📊 任务统计: 总账号 {total_processed} 个")

            if self.success_accounts > 0:
                self.status_signal.emit(f"📊 {mode_text}成功: {self.success_accounts} 个账号")

            if self.login_failed_accounts > 0:
                self.status_signal.emit(f"📊 登录失败: {self.login_failed_accounts} 个账号")

            if self.failed_accounts > 0:
                self.status_signal.emit(f"📊 {mode_text}失败: {self.failed_accounts} 个账号")

            # 显示重试统计信息
            if self.current_retry_round > 0:
                self.status_signal.emit(f"📊 重试轮次: {self.current_retry_round}")
                if self.retry_success_accounts > 0:
                    self.status_signal.emit(f"📊 重试成功: {self.retry_success_accounts} 个账号")

            # 显示视频上传重试统计
            video_retry_stats = self._calculate_video_upload_retry_stats()
            if video_retry_stats['total_retries'] > 0:
                self.status_signal.emit(f"📊 视频上传重试统计:")
                self.status_signal.emit(f"   - 重试账号数: {video_retry_stats['retry_accounts']} 个")
                self.status_signal.emit(f"   - 总重试次数: {video_retry_stats['total_retries']} 次")
                self.status_signal.emit(f"   - 重试后成功: {video_retry_stats['retry_success']} 个")
                self.status_signal.emit(f"   - 最终失败: {video_retry_stats['final_failed']} 个")

            # 记录详细统计到日志文件
            info(f"批量存稿任务统计 - 总计: {total_processed}, 成功: {self.success_accounts}, 登录失败: {self.login_failed_accounts}, {mode_text}失败: {self.failed_accounts}, 重试轮次: {self.current_retry_round}, 重试成功: {self.retry_success_accounts}, 视频重试: {video_retry_stats}")

        except Exception as e:
            error(f"计算任务统计时出错: {str(e)}")

    def _calculate_video_upload_retry_stats(self):
        """计算视频上传重试统计信息

        Returns:
            dict: 包含重试统计信息的字典
        """
        try:
            stats = {
                'retry_accounts': 0,      # 有重试的账号数
                'total_retries': 0,       # 总重试次数
                'retry_success': 0,       # 重试后成功的账号数
                'final_failed': 0         # 最终失败的账号数
            }

            # 统计当前重试计数
            for account_id, retry_count in self.video_upload_retry_counts.items():
                if retry_count > 0:
                    stats['retry_accounts'] += 1
                    stats['total_retries'] += retry_count

                    # 检查该账号是否在失败列表中
                    is_failed = False
                    for failed_account in self.failed_account_list:
                        if failed_account['account_id'] == account_id:
                            if "视频上传失败" in failed_account['failure_reason']:
                                stats['final_failed'] += 1
                                is_failed = True
                                break

                    # 如果不在失败列表中，说明重试后成功了
                    if not is_failed:
                        stats['retry_success'] += 1

            return stats

        except Exception as e:
            error(f"计算视频上传重试统计时出错: {str(e)}")
            return {
                'retry_accounts': 0,
                'total_retries': 0,
                'retry_success': 0,
                'final_failed': 0
            }

    def _cleanup_resources(self):
        """清理所有资源"""
        try:
            # 停止完成检查定时器
            if hasattr(self, 'completion_check_timer') and self.completion_check_timer:
                self._safe_stop_timer(self.completion_check_timer)
                self.completion_check_timer = None
                debug("完成检查定时器已停止")

            # 清理活跃的WebDriver实例
            for driver in self.active_drivers:
                try:
                    driver.quit()
                    debug("WebDriver实例已关闭")
                except Exception as e:
                    debug(f"关闭WebDriver时出错: {str(e)}")
            self.active_drivers.clear()

            # 清理活跃的线程
            for thread in self.active_threads:
                if thread.is_alive():
                    thread.join(timeout=3)  # 增加等待时间到3秒
                    if thread.is_alive():
                        debug(f"线程 {thread.name} 未在3秒内停止")
            self.active_threads.clear()

            # 清理临时文件
            self._cleanup_temp_files()

            # 断开信号连接
            self._disconnect_all_signals()

            # 强制垃圾回收
            import gc
            gc.collect()

            self._safe_emit_signal("status_signal", "🧹 资源清理完成")

        except Exception as e:
            error(f"清理资源时出错: {str(e)}")

    def _cleanup_temp_files(self):
        """清理临时文件"""
        try:
            import tempfile
            import shutil
            temp_dir = tempfile.gettempdir()

            # 清理Chrome用户数据目录
            cleaned_count = 0
            for i in range(50):  # 增加清理范围
                temp_user_dir = os.path.join(temp_dir, f"chrome_user_data_{i}")
                if os.path.exists(temp_user_dir):
                    try:
                        shutil.rmtree(temp_user_dir, ignore_errors=True)
                        cleaned_count += 1
                        debug(f"已清理临时目录: {temp_user_dir}")
                    except Exception as e:
                        debug(f"清理临时目录失败 {temp_user_dir}: {str(e)}")

            if cleaned_count > 0:
                debug(f"共清理了 {cleaned_count} 个临时Chrome用户数据目录")

        except Exception as e:
            debug(f"清理临时文件时出错: {str(e)}")

    def _reset_ui_state(self):
        """重置界面状态"""
        try:
            # 重置账号表格状态
            if hasattr(self, 'account_tab_ref') and self.account_tab_ref:
                table = self.account_tab_ref.table
                for row in range(table.rowCount()):
                    # 将所有账号状态重置为默认状态（但保留最终结果）
                    status_item = table.item(row, 2)
                    if status_item:
                        status_text = status_item.text()
                        # 只重置处理中的状态，保留最终结果
                        if "正在" in status_text or "等待" in status_text or "处理中" in status_text:
                            from PyQt5.QtGui import QColor
                            from PyQt5.QtWidgets import QTableWidgetItem

                            default_item = QTableWidgetItem("就绪")
                            # 使用数值常量代替Qt.AlignCenter以避免导入问题
                            default_item.setTextAlignment(0x0004)
                            default_item.setBackground(QColor(240, 240, 240))
                            default_item.setForeground(QColor(100, 100, 100))
                            table.setItem(row, 2, default_item)

            self.status_signal.emit("🔄 界面状态已重置")

        except Exception as e:
            error(f"重置界面状态时出错: {str(e)}")

    def _reset_worker_state(self):
        """重置工作器状态，确保下次可以正常启动"""
        try:
            # 重置运行状态
            self.is_running = False
            self.is_cancelled = False

            # 重置统计数据
            self.total_accounts = 0
            self.success_accounts = 0
            self.failed_accounts = 0
            self.login_failed_accounts = 0
            self.completed_count = 0

            # 清理资源列表
            self.active_drivers.clear()
            self.active_threads.clear()

            self.status_signal.emit("🔄 工作器状态已重置")

        except Exception as e:
            error(f"重置工作器状态时出错: {str(e)}")

    def _start_runtime_timer(self):
        """启动运行时间统计定时器"""
        try:
            from PyQt5.QtCore import QTimer
            self.runtime_timer = QTimer()
            self.runtime_timer.timeout.connect(self._update_runtime)
            self.runtime_timer.start(1000)  # 每秒更新一次
            info("运行时间统计定时器已启动")
        except Exception as e:
            error(f"启动运行时间统计定时器失败: {str(e)}")

    def _stop_runtime_timer(self):
        """停止运行时间统计定时器"""
        try:
            if self.runtime_timer and hasattr(self.runtime_timer, 'isActive') and self.runtime_timer.isActive():
                self.runtime_timer.stop()
                self.runtime_timer = None

            # 记录任务结束时间
            import time
            self.end_time = time.time()

            # 发送最终运行时间
            if self.start_time and self.end_time:
                total_runtime = self.end_time - self.start_time
                runtime_str = self._format_runtime(total_runtime)
                self.runtime_update_signal.emit(runtime_str)
                self.status_signal.emit(f"⏱️ 总运行时间: {runtime_str}")
                info(f"批量存稿任务总运行时间: {runtime_str}")

        except Exception as e:
            error(f"停止运行时间统计定时器失败: {str(e)}")

    def _update_runtime(self):
        """更新运行时间显示"""
        try:
            if self.start_time:
                import time
                current_time = time.time()
                elapsed_time = current_time - self.start_time
                runtime_str = self._format_runtime(elapsed_time)
                self.runtime_update_signal.emit(runtime_str)
        except Exception as e:
            debug(f"更新运行时间失败: {str(e)}")

    def _format_runtime(self, seconds):
        """格式化运行时间为 HH:MM:SS 或 DD天 HH:MM:SS 格式"""
        try:
            total_seconds = int(seconds)

            # 计算天、小时、分钟、秒
            days = total_seconds // 86400
            hours = (total_seconds % 86400) // 3600
            minutes = (total_seconds % 3600) // 60
            secs = total_seconds % 60

            if days > 0:
                return f"{days}天 {hours:02d}:{minutes:02d}:{secs:02d}"
            else:
                return f"{hours:02d}:{minutes:02d}:{secs:02d}"
        except Exception as e:
            error(f"格式化运行时间失败: {str(e)}")
            return "00:00:00"

    def _get_account_list(self):
        """获取账号列表 - 1:1照抄备份文件的逻辑"""
        account_list = []
        try:
            self.status_signal.emit(f"🔍 开始获取账号列表，Cookie目录: {self.cookie_dir}")
            print(f"批量存稿任务: 开始获取账号列表，Cookie目录: {self.cookie_dir}")

            if self.selected_accounts:
                self.status_signal.emit(f"📋 使用选中的账号列表，共 {len(self.selected_accounts)} 个账号")
                print(f"批量存稿任务: 使用选中的账号列表，共 {len(self.selected_accounts)} 个账号")

                for i, account_info in enumerate(self.selected_accounts):
                    row = account_info['row']
                    account_id = account_info['account_id']
                    cookie_file = account_info.get('cookie_file') or os.path.join(self.cookie_dir, f"{account_id}.txt")

                    print(f"批量存稿任务: 第{i+1}个账号 - ID: {account_id}, Cookie文件: {cookie_file}")
                    self.status_signal.emit(f"🔍 检查账号 {account_id} 的Cookie文件: {cookie_file}")

                    cookie_exists = os.path.exists(cookie_file)
                    print(f"批量存稿任务: 账号 {account_id} Cookie文件存在: {cookie_exists}")

                    if cookie_exists:
                        account_list.append((row, account_id, cookie_file))
                        self.status_signal.emit(f"✅ 账号 {account_id} Cookie文件存在")
                        print(f"批量存稿任务: ✅ 添加账号 {account_id} 到有效列表")
                    else:
                        self.status_signal.emit(f"❌ 账号 {account_id} Cookie文件不存在: {cookie_file}")
                        print(f"批量存稿任务: ❌ 账号 {account_id} Cookie文件不存在")
            else:
                self.status_signal.emit(f"📋 从表格获取账号列表，表格行数: {self.table_widget.rowCount()}")
                print(f"批量存稿任务: 从表格获取账号列表，表格行数: {self.table_widget.rowCount()}")

                for row in range(self.table_widget.rowCount()):
                    account_item = self.table_widget.item(row, 1)
                    if account_item and account_item.text():
                        account_id = account_item.text()
                        cookie_file = os.path.join(self.cookie_dir, f"{account_id}.txt")

                        print(f"批量存稿任务: 表格第{row}行 - 账号ID: {account_id}, Cookie文件: {cookie_file}")
                        self.status_signal.emit(f"🔍 检查账号 {account_id} 的Cookie文件: {cookie_file}")

                        cookie_exists = os.path.exists(cookie_file)
                        print(f"批量存稿任务: 账号 {account_id} Cookie文件存在: {cookie_exists}")

                        if cookie_exists:
                            account_list.append((row, account_id, cookie_file))
                            self.status_signal.emit(f"✅ 账号 {account_id} Cookie文件存在")
                            print(f"批量存稿任务: ✅ 添加账号 {account_id} 到有效列表")
                        else:
                            self.status_signal.emit(f"❌ 账号 {account_id} Cookie文件不存在: {cookie_file}")
                            print(f"批量存稿任务: ❌ 账号 {account_id} Cookie文件不存在")

            self.status_signal.emit(f"📊 最终获取到 {len(account_list)} 个有效账号")
            print(f"批量存稿任务: 📊 最终获取到 {len(account_list)} 个有效账号")

            # 打印前10个有效账号的详情
            if account_list:
                print(f"批量存稿任务: 有效账号列表（前10个）:")
                for i, (row, account_id, cookie_file) in enumerate(account_list[:10]):
                    print(f"  {i+1}. 行{row}: {account_id} -> {cookie_file}")
                if len(account_list) > 10:
                    print(f"  ... 还有 {len(account_list) - 10} 个账号")

            return account_list
        except Exception as e:
            error(f"获取账号列表失败: {str(e)}")
            print(f"批量存稿任务: 获取账号列表失败: {str(e)}")
            import traceback
            error(f"详细错误: {traceback.format_exc()}")
            print(f"批量存稿任务: 详细错误: {traceback.format_exc()}")
            return []

    def _clear_browser_session(self, driver):
        """清理浏览器会话（与单独登录保持一致）"""
        try:
            # 清除所有Cookie
            driver.delete_all_cookies()

            # 打开空白页面
            driver.get("about:blank")

            # 清除缓存和存储（与单独登录保持一致）
            driver.execute_script("""
                try {
                    // 清除localStorage
                    if (window.localStorage) {
                        localStorage.clear();
                    }

                    // 清除sessionStorage
                    if (window.sessionStorage) {
                        sessionStorage.clear();
                    }

                    // 清除IndexedDB
                    if (window.indexedDB && window.indexedDB.databases) {
                        var dbs = window.indexedDB.databases();
                        if (dbs && dbs.then) {
                            dbs.then(function(databases) {
                                databases.forEach(function(db) {
                                    window.indexedDB.deleteDatabase(db.name);
                                });
                            });
                        }
                    }

                    // 清除Service Workers
                    if (navigator.serviceWorker && navigator.serviceWorker.getRegistrations) {
                        navigator.serviceWorker.getRegistrations().then(function(registrations) {
                            for (let registration of registrations) {
                                registration.unregister();
                            }
                        });
                    }

                    // 清除缓存
                    if (window.caches && window.caches.keys) {
                        window.caches.keys().then(function(keyList) {
                            return Promise.all(keyList.map(function(key) {
                                return window.caches.delete(key);
                            }));
                        });
                    }

                    console.log('浏览器缓存清理完成');
                } catch (e) {
                    console.error('清除缓存失败:', e);
                }
            """)

            # 再次打开空白页面，确保清除操作生效
            driver.get("about:blank")
            time.sleep(1)

            debug("浏览器会话数据已清除（完整清理）")
            return True
        except Exception as e:
            warning(f"清理浏览器会话时出错: {str(e)}")
            return False

    def _login_with_cookie(self, driver, cookie_file):
        """使用Cookie登录账号 - 使用统一登录管理器"""
        try:
            # 提取账号ID
            account_id = os.path.splitext(os.path.basename(cookie_file))[0]

            # 使用统一登录管理器
            from app.utils.unified_login_manager import get_login_manager

            login_manager = get_login_manager()
            success, message = login_manager.login_account(driver, cookie_file, account_id)

            if success:
                self._safe_emit_signal("status_signal", f"✅ 账号 {account_id} 登录成功")

                # 检测实名认证状态
                try:
                    from app.utils.realname_detector import detect_realname_status
                    is_verified, detection_message = detect_realname_status(driver, account_id)

                    if not is_verified:
                        # 未实名认证，停止任务
                        self._safe_emit_signal("status_signal", f"⚠️ 账号 {account_id} 未实名认证，停止任务")

                        # 更新账号表格状态
                        try:
                            if hasattr(self, 'account_tab_ref') and self.account_tab_ref:
                                # 更新实名状态
                                self.account_tab_ref.update_realname_status_by_id(account_id, False)
                                # 更新任务状态为未实名
                                self.account_tab_ref.update_account_status_by_id(account_id, "未实名", "failed")
                        except Exception as e:
                            warning(f"更新账号表格状态失败: {str(e)}")

                        return False  # 返回False表示登录失败（因为未实名）
                    else:
                        # 已实名认证，更新状态
                        try:
                            if hasattr(self, 'account_tab_ref') and self.account_tab_ref:
                                self.account_tab_ref.update_realname_status_by_id(account_id, True)
                        except Exception as e:
                            warning(f"更新账号表格实名状态失败: {str(e)}")

                        info(f"✅ 账号 {account_id} 已实名认证，继续任务")

                except Exception as e:
                    warning(f"检测账号 {account_id} 实名状态时出错: {str(e)}")
                    # 检测出错时继续执行，不影响正常流程

                return True
            else:
                self._safe_emit_signal("status_signal", f"❌ 账号 {account_id} 登录失败: {message}")
                return False

        except Exception as e:
            warning(f"Cookie登录失败: {str(e)}")
            self._safe_emit_signal("status_signal", f"❌ Cookie登录异常: {str(e)}")
            return False

    def _perform_draft_task(self, driver, account_id):
        """执行存稿任务 (并发版本) - 1:1照抄批量存稿2

        Args:
            driver: WebDriver实例
            account_id: 账号ID

        Returns:
            bool: 是否成功
        """
        try:
            # 确认已登录
            operation_text = "存稿"
            self.status_signal.emit(f"账号 {account_id} 开始执行{operation_text}任务...")

            # 立即更新表格状态为"处理中"
            row = self._get_account_row(account_id)
            if row is not None:
                self._update_table_status(row, f"{operation_text}中...", QColor(255, 255, 200))

            # 检查视频目录是否设置
            if hasattr(self, 'video_dir') and self.video_dir and os.path.exists(self.video_dir):
                # 获取视频文件列表
                video_files = [f for f in os.listdir(self.video_dir)
                             if f.lower().endswith(('.mp4', '.avi', '.mov', '.wmv', '.flv'))]

                if video_files:
                    # 根据次数循环执行任务
                    for draft_index in range(self.draft_count):
                        if not self.is_running:
                            self.status_signal.emit(f"任务已中断，停止{operation_text}")
                            break

                        # 显示当前正在进行第几次任务
                        draft_num = draft_index + 1
                        self.status_signal.emit(f"账号 {account_id} 开始第 {draft_num}/{self.draft_count} 次{operation_text}")

                        # 发送账号进度信号
                        self.account_progress_signal.emit(account_id, "进行中", f"第 {draft_num}/{self.draft_count} 次存稿", draft_index, self.draft_count)

                        # 更新表格状态为"正在存稿"
                        self._update_table_status(account_id, f"正在存稿 ({draft_num}/{self.draft_count})", "processing")

                        # 如果有账号标签页引用，也发送到账号标签页
                        if hasattr(self, 'account_tab_ref') and self.account_tab_ref:
                            try:
                                self.account_tab_ref.on_account_progress_update(account_id, "进行中", f"第 {draft_num}/{self.draft_count} 次存稿", draft_index, self.draft_count)
                            except Exception as e:
                                debug(f"发送账号进行中进度到账号标签页失败: {str(e)}")

                        # 第一次存稿需要完整流程，后续存稿直接跳转到上传界面
                        if draft_index == 0:
                            # 首次存稿的页面跳转逻辑 - 标准模式
                            try:
                                driver.get("https://mp.toutiao.com/profile_v4/xigua/upload-video")
                                self.status_signal.emit(f"🔄 标准模式：正在导航到视频上传页面...")
                                time.sleep(3)

                                # 等待页面加载完成
                                from selenium.webdriver.support.ui import WebDriverWait
                                from selenium.webdriver.support import expected_conditions as EC
                                from selenium.webdriver.common.by import By
                                login_timeout = getattr(self, 'timeout_settings', {}).get('login_verify_timeout', 10)
                                WebDriverWait(driver, login_timeout).until(
                                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                                )
                            except Exception as nav_error:
                                self.status_signal.emit(f"❌ 无法导航到视频上传页面: {str(nav_error)}")
                                return False
                        else:
                            # 非首次存稿，直接导航到上传页面
                            try:
                                self.status_signal.emit(f"直接导航到上传界面")
                                driver.get("https://mp.toutiao.com/profile_v4/xigua/upload-video")
                            except Exception as nav_error:
                                self.status_signal.emit(f"❌ 重复存稿时无法导航到上传页面: {str(nav_error)}")
                                return False

                        # 为当前存稿获取一个视频文件
                        if hasattr(self, 'video_allocation') and self.video_allocation:
                            # 使用视频分配逻辑获取视频
                            video_file = self._get_video_for_account(account_id)
                            if not video_file:
                                warning(f"账号 {account_id} 无法获取视频文件，使用第一个可用视频")
                                video_file = video_files[0]
                        else:
                            # 不启用视频分配，直接使用第一个视频
                            video_file = video_files[0]

                        video_path = os.path.join(self.video_dir, video_file)
                        self.status_signal.emit(f"账号 {account_id} 选择视频文件: {video_file}")

                        # 继续视频上传流程...
                        upload_result = self._handle_video_upload_batch2_style(driver, account_id, video_path, video_file)
                        if upload_result == "draft_completed":
                            # 存稿已在视频上传过程中完成，直接返回特殊值
                            return "draft_completed"
                        elif not upload_result:
                            return False

                        # 点击存稿按钮 - 使用批量存稿2的逻辑
                        draft_button_result = self._click_draft_button_batch2_style(driver, account_id)
                        if not draft_button_result:
                            self.status_signal.emit(f"❌ 账号 {account_id} 点击存稿按钮失败")
                            return False

                        # 记录存稿完成
                        mode_text = "发布" if (hasattr(self, 'ban_mode') and self.ban_mode) else "存稿"
                        self.status_signal.emit(f"✅ 账号 {account_id} 第{draft_num}次{mode_text}成功")

                        if draft_index < self.draft_count - 1:
                            # 中间步骤只记录调试信息
                            debug(f"账号 {account_id} 第 {draft_num}/{self.draft_count} 次存稿完成")
                            time.sleep(2)  # 在下一次存稿前等待
                        else:
                            # 最后一次存稿
                            final_message = f"账号 {account_id}: {operation_text}完成 ({self.draft_count}/{self.draft_count})"
                            info(final_message)

                    # 所有存稿次数执行完成，返回成功
                    return True
            else:
                warning(f"视频文件目录未设置或不存在")
                self.status_signal.emit("⚠️ 视频文件目录未设置或不存在")

            # 记录任务完成
            self.status_signal.emit(f"账号 {account_id} {operation_text}操作完成")
            info(f"账号 {account_id} {operation_text}任务完成")
            return True

        except Exception as e:
            error(f"账号 {account_id} 存稿任务异常: {str(e)}")
            return False

    def _get_account_row(self, account_id):
        """根据账号ID获取表格行索引"""
        try:
            for row in range(self.table_widget.rowCount()):
                account_item = self.table_widget.item(row, 1)  # 账号ID在第2列(索引1)，不是昵称列
                if account_item and account_item.text() == account_id:
                    return row
            return None
        except Exception as e:
            debug(f"获取账号行索引出错: {str(e)}")
            return None

    def _update_table_status(self, account_id, status_text, status_type="info"):
        """更新表格中账号的状态显示

        Args:
            account_id: 账号ID
            status_text: 状态文本
            status_type: 状态类型 ("waiting", "processing", "success", "failed", "login_failed")
        """
        try:
            # 如果有账号标签页引用，更新表格状态
            if hasattr(self, 'account_tab_ref') and self.account_tab_ref:
                # 查找账号对应的行
                row = self._find_account_row(account_id)
                if row is not None:
                    # 根据状态类型设置颜色
                    color_map = {
                        "waiting": (QColor(255, 230, 200), QColor(153, 76, 0)),      # 浅橙色背景，深橙色文字
                        "processing": (QColor(200, 230, 255), QColor(0, 76, 153)),   # 浅蓝色背景，深蓝色文字
                        "success": (QColor(200, 255, 200), QColor(0, 128, 0)),       # 浅绿色背景，深绿色文字
                        "failed": (QColor(255, 200, 200), QColor(153, 0, 0)),        # 浅红色背景，深红色文字
                        "login_failed": (QColor(255, 180, 180), QColor(128, 0, 0))   # 更深的红色，表示登录失败
                    }

                    bg_color, text_color = color_map.get(status_type, color_map["waiting"])

                    # 调用账号标签页的状态更新方法
                    self.account_tab_ref.update_account_table_status(row, status_text, bg_color, text_color)

        except Exception as e:
            debug(f"更新表格状态出错: {str(e)}")

    def _find_account_row(self, account_id):
        """查找账号在表格中的行索引

        Args:
            account_id: 账号ID

        Returns:
            int: 行索引，如果未找到返回None
        """
        try:
            if hasattr(self, 'account_tab_ref') and self.account_tab_ref:
                table = self.account_tab_ref.table
                for row in range(table.rowCount()):
                    # 检查账号列（第1列，索引1）
                    account_item = table.item(row, 1)
                    if account_item and account_item.text().strip() == account_id:
                        return row
            return None
        except Exception as e:
            debug(f"查找账号行索引出错: {str(e)}")
            return None

    def _get_video_for_account(self, account_id):
        """为账号获取视频文件"""
        try:
            # 简化版本：随机选择一个视频文件
            if hasattr(self, 'video_dir') and self.video_dir and os.path.exists(self.video_dir):
                video_files = [f for f in os.listdir(self.video_dir)
                             if f.lower().endswith(('.mp4', '.avi', '.mov', '.wmv', '.flv'))]
                if video_files:
                    import random
                    return random.choice(video_files)
            return None
        except Exception as e:
            debug(f"获取账号视频文件出错: {str(e)}")
            return None

    def _handle_video_upload_batch2_style(self, driver, account_id, video_path, video_file):
        """处理视频上传 - 批量存稿2风格"""
        try:
            # 使用多种方式查找和上传文件
            file_input = None
            upload_success = False

            # 检测特定元素存在后立即开始视频上传
            target_xpath = "/html/body/div[1]/div/div[3]/section/main/div[2]/div/div/div[2]/div/div/div/div[2]/div/div[1]/div/div/div/div[2]/div/div/div/div/div/div"
            self.status_signal.emit("🔍 检测目标元素是否存在...")

            try:
                # 等待目标元素出现，使用配置的超时时间
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.webdriver.common.by import By

                element_timeout = getattr(self, 'timeout_settings', {}).get('element_wait_timeout', 30)
                target_element = WebDriverWait(driver, element_timeout).until(
                    EC.presence_of_element_located((By.XPATH, target_xpath))
                )
                self.status_signal.emit("✅ 检测到目标元素，立即开始视频上传...")
            except Exception as e:
                self.status_signal.emit(f"⚠️ 目标元素检测失败，使用备用方案: {str(e)[:100]}")
                # 如果目标元素检测失败，等待更长时间让页面完全加载
                self.status_signal.emit("⏳ 等待页面完全加载...")
                time.sleep(5)  # 增加等待时间

            # 方法1: 传统文件输入查找（增加重试机制）
            try:
                self.status_signal.emit("🔍 查找文件输入元素(方法1: CSS选择器)...")
                element_timeout = getattr(self, 'timeout_settings', {}).get('element_wait_timeout', 30)

                # 增加重试机制
                for retry in range(3):
                    try:
                        file_input = WebDriverWait(driver, element_timeout).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
                        )
                        self.status_signal.emit("✅ 方法1成功找到文件输入元素")
                        upload_success = True
                        break
                    except Exception as retry_e:
                        if retry < 2:  # 不是最后一次重试
                            debug(f"方法1第{retry+1}次尝试失败，等待后重试")  # 使用debug级别，不显示在失败日志中
                            time.sleep(3)  # 等待3秒后重试
                        else:
                            raise retry_e

            except Exception as e1:
                self.status_signal.emit(f"⚠️ 方法1查找失败: {str(e1)[:100]}")

            # 方法2: 使用JavaScript显示隐藏的文件输入元素
            if not upload_success:
                try:
                    self.status_signal.emit("🔍 查找文件输入元素(方法2: JavaScript显示)...")
                    driver.execute_script("""
                        var inputs = document.querySelectorAll('input[type="file"]');
                        console.log('找到文件输入元素数量:', inputs.length);
                        for(var i = 0; i < inputs.length; i++) {
                            inputs[i].style.display = 'block';
                            inputs[i].style.opacity = '1';
                            inputs[i].style.visibility = 'visible';
                            inputs[i].style.height = '100px';
                            inputs[i].style.width = '100px';
                            inputs[i].style.position = 'absolute';
                            inputs[i].style.top = '300px';
                            inputs[i].style.left = '500px';
                            inputs[i].style.zIndex = '9999';
                            inputs[i].style.border = '2px solid red';
                        }
                        return inputs.length;
                    """)
                    time.sleep(2)

                    element_timeout = getattr(self, 'timeout_settings', {}).get('element_wait_timeout', 30)
                    file_input = WebDriverWait(driver, element_timeout).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
                    )
                    self.status_signal.emit("✅ 方法2成功找到文件输入元素")
                    upload_success = True
                except Exception as e2:
                    self.status_signal.emit(f"⚠️ 方法2查找失败: {str(e2)[:100]}")

            # 方法3: 查找并点击上传按钮
            if not upload_success:
                try:
                    self.status_signal.emit("🔍 查找上传按钮(方法3: 点击上传按钮)...")
                    # 更全面的上传按钮查找
                    upload_selectors = [
                        "//*[contains(text(), '上传视频')]",
                        "//*[contains(text(), '选择文件')]",
                        "//*[contains(text(), '上传')]",
                        "//*[contains(@class, 'upload')]",
                        "//button[contains(@class, 'upload')]",
                        "//div[contains(@class, 'upload')]",
                        "//*[@role='button' and contains(text(), '上传')]"
                    ]

                    upload_button_found = False
                    for selector in upload_selectors:
                        try:
                            upload_buttons = driver.find_elements(By.XPATH, selector)
                            if upload_buttons:
                                self.status_signal.emit(f"✅ 找到 {len(upload_buttons)} 个上传按钮 (选择器: {selector[:50]})")
                                upload_buttons[0].click()
                                time.sleep(2)
                                upload_button_found = True
                                break
                        except Exception:
                            continue

                    if upload_button_found:
                        # 再次查找文件输入
                        file_input = WebDriverWait(driver, 30).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
                        )
                        self.status_signal.emit("✅ 方法3成功找到文件输入元素")
                        upload_success = True
                    else:
                        self.status_signal.emit("⚠️ 方法3未找到可点击的上传按钮")
                except Exception as e3:
                    self.status_signal.emit(f"⚠️ 方法3查找失败: {str(e3)[:100]}")

            # 如果所有方法都失败，尝试最后的备用方案
            if not upload_success:
                self.status_signal.emit("⚠️ 常规方法失败，尝试最后的备用方案...")
                try:
                    # 最后的备用方案：等待更长时间后重试
                    time.sleep(5)
                    file_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='file']")
                    if file_inputs:
                        file_input = file_inputs[0]
                        upload_success = True
                        self.status_signal.emit("✅ 备用方案成功找到文件输入元素")
                    else:
                        self.status_signal.emit("❌ 无法找到文件输入元素，所有查找方法都失败")
                        self.status_signal.emit("⚠️ 可能页面结构发生变化，建议检查网站更新")
                        return False
                except Exception as final_e:
                    self.status_signal.emit(f"❌ 最后备用方案也失败: {str(final_e)[:100]}")
                    return False

            # 如果找到文件输入元素，发送文件路径
            if file_input:
                self.status_signal.emit("✅ 准备发送文件路径...")

                # 确保文件路径是绝对路径
                abs_path = os.path.abspath(video_path)
                self.status_signal.emit(f"📂 文件绝对路径: {abs_path}")

                try:
                    # 使用JavaScript设置文件路径，更可靠
                    driver.execute_script("arguments[0].style.display = 'block';", file_input)
                    time.sleep(1)

                    # 尝试发送文件路径
                    file_input.send_keys(abs_path)
                    self.status_signal.emit("✅ 已通过传统方式发送文件路径")
                except Exception as path_e:
                    # 捕获stale element reference错误但不显示警告
                    if "stale element reference" in str(path_e):
                        debug(f"文件输入元素已过时，但可能已成功上传: {str(path_e)}")
                        self.status_signal.emit("✅ 文件处理中...")
                    else:
                        self.status_signal.emit(f"❌ 发送文件路径失败: {str(path_e)}")
                        return False

                # 立即滚动到页面底部，不等待
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight)")
                self.status_signal.emit("✅ 已滚动到页面底部")

                # 等待视频开始上传（短暂等待）
                self.status_signal.emit("⏳ 等待视频开始上传...")
                time.sleep(1.5)
            else:
                self.status_signal.emit("❌ 未找到任何文件输入元素")
                return False

            # 视频上传后立即查找封面元素，避免stale element reference错误
            try:
                self.status_signal.emit("🔍 准备处理封面，立即查找封面元素...")

                # 重新定位封面元素
                specific_xpath = '//*[@id="root"]/div/div/div[2]/div/div[1]/div/div/div/div[2]/div/div[2]/div[1]/div[2]/div[4]/div[2]/div[1]/div/div/div'
                try:
                    # 立即滚动到页面底部，确保封面区域可见
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight)")

                    # 立即尝试找到并点击封面元素
                    cover_element = WebDriverWait(driver, 20).until(
                        EC.presence_of_element_located((By.XPATH, specific_xpath))
                    )

                    # 滚动到元素位置确保可见
                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", cover_element)
                    time.sleep(1)  # 等待滚动完成

                    # 使用JavaScript点击，避免元素被遮挡的问题
                    self.status_signal.emit("✅ 找到封面元素，使用JavaScript点击...")
                    try:
                        # 先尝试普通点击
                        cover_element.click()
                        self.status_signal.emit("✅ 普通点击成功")
                    except Exception as click_error:
                        if "element click intercepted" in str(click_error):
                            # 如果被拦截，使用JavaScript点击
                            debug("元素被遮挡，自动使用JavaScript点击")  # 使用debug级别，不显示在失败日志中
                            driver.execute_script("arguments[0].click();", cover_element)
                            self.status_signal.emit("✅ JavaScript点击成功")
                        else:
                            raise click_error

                    self.status_signal.emit("✅ 已点击封面元素")

                    # 处理封面上传部分 - 使用批量存稿2的逻辑
                    cover_result = self._handle_cover_upload(driver, account_id, video_file)
                    # 只有当封面上传过程中完成了存稿才返回特殊值
                    if cover_result == "draft_completed":
                        # 存稿已在封面上传过程中完成，直接返回特殊值
                        return "draft_completed"
                    else:
                        # 封面处理完成，现在开始监测视频上传状态
                        self.status_signal.emit("✅ 封面处理完成，开始监测视频上传状态...")

                        # 并行监测视频上传成功和失败状态
                        upload_result = self._monitor_video_upload_status(driver, account_id, video_file)
                        if upload_result == "failure":
                            # 检测到上传失败，尝试重试
                            if self._handle_video_upload_failure(driver, account_id, video_file):
                                # 可以重试，重新执行上传
                                self.status_signal.emit(f"🔄 账号 {account_id} 开始重新上传视频...")
                                return self._handle_video_upload_batch2_style(driver, account_id, video_path, video_file)
                            else:
                                # 超过重试次数，返回失败
                                return False
                        elif upload_result == "success":
                            self.status_signal.emit("✅ 视频上传成功确认")
                        else:
                            # 超时或其他情况，继续执行（保持原有逻辑）
                            self.status_signal.emit("⚠️ 视频上传状态检测超时，继续执行后续流程")

                        # 返回True继续后续的存稿按钮点击流程
                        return True
                except Exception as e:
                    self.status_signal.emit(f"⚠️ 封面元素处理失败: {str(e)}")
                    # 封面处理失败，仍需要监测视频上传状态
                    self.status_signal.emit("⚠️ 封面处理失败，开始监测视频上传状态...")

                    # 并行监测视频上传成功和失败状态
                    upload_result = self._monitor_video_upload_status(driver, account_id, video_file)
                    if upload_result == "failure":
                        # 检测到上传失败，尝试重试
                        if self._handle_video_upload_failure(driver, account_id, video_file):
                            # 可以重试，重新执行上传
                            self.status_signal.emit(f"🔄 账号 {account_id} 开始重新上传视频...")
                            return self._handle_video_upload_batch2_style(driver, account_id, video_path, video_file)
                        else:
                            # 超过重试次数，返回失败
                            return False
                    elif upload_result == "success":
                        self.status_signal.emit("✅ 视频上传成功确认")
                    else:
                        # 超时或其他情况，继续执行（保持原有逻辑）
                        self.status_signal.emit("⚠️ 视频上传状态检测超时，继续执行后续流程")

                    return True
            except Exception as e:
                # 封面处理失败，仍需要监测视频上传状态
                self.status_signal.emit(f"⚠️ 封面处理异常: {str(e)[:100]}")
                self.status_signal.emit("⚠️ 跳过封面处理，开始监测视频上传状态...")

                # 并行监测视频上传成功和失败状态
                upload_result = self._monitor_video_upload_status(driver, account_id, video_file)
                if upload_result == "failure":
                    # 检测到上传失败，尝试重试
                    if self._handle_video_upload_failure(driver, account_id, video_file):
                        # 可以重试，重新执行上传
                        self.status_signal.emit(f"🔄 账号 {account_id} 开始重新上传视频...")
                        return self._handle_video_upload_batch2_style(driver, account_id, video_path, video_file)
                    else:
                        # 超过重试次数，返回失败
                        return False
                elif upload_result == "success":
                    self.status_signal.emit("✅ 视频上传成功确认")
                else:
                    # 超时或其他情况，继续执行（保持原有逻辑）
                    self.status_signal.emit("⚠️ 视频上传状态检测超时，继续执行后续流程")

                return True

        except Exception as e:
            self.status_signal.emit(f"❌ 视频上传过程发生异常: {str(e)}")
            return False

    def _click_draft_button_batch2_style(self, driver, account_id):
        """点击存稿或发布按钮 - 根据模式选择"""
        try:
            # 等待短暂时间后点击按钮
            time.sleep(2)  # 等待2秒确保页面稳定

            # 根据模式选择不同的按钮
            if hasattr(self, 'ban_mode') and self.ban_mode:
                # 封号模式：点击发布按钮
                button_xpath = "/html/body/div[1]/div/div[3]/section/main/div[2]/div/div/div[2]/div/div/div/div[2]/div/div[1]/div/div/div/div[2]/div/div[2]/div[3]/div/button[3]"
                button_name = "发布按钮"
                mode_text = "封号模式"
                button_text = "发布"
            else:
                # 存稿模式：点击存稿按钮
                button_xpath = '//*[@id="root"]/div/div/div[2]/div/div[1]/div/div/div/div[2]/div/div[2]/div[3]/div/button[1]'
                button_name = "存稿按钮"
                mode_text = "存稿模式"
                button_text = "存稿"

            self.status_signal.emit(f"🎯 {mode_text}: 尝试点击{button_name}...")

            # 滚动到页面底部以确保按钮可见
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")

            try:
                # 使用JavaScript查找并点击按钮
                button_click_result = driver.execute_script("""
                    try {
                        var xpath = arguments[0];
                        var result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                        var button = result.singleNodeValue;

                        if (button) {
                            // 确保按钮在视图中
                            button.scrollIntoView({behavior: 'smooth', block: 'center'});
                            setTimeout(function() {
                                button.click();
                            }, 500);
                            return arguments[1] + '点击成功';
                        } else {
                            return '未找到' + arguments[1];
                        }
                    } catch (e) {
                        return '点击按钮出错: ' + e.message;
                    }
                """, button_xpath, button_name)

                self.status_signal.emit(f"📝 {button_name}点击结果: {button_click_result}")

                # 如果JavaScript方法失败，尝试使用Selenium方法
                if "成功" not in button_click_result:
                    self.status_signal.emit(f"🔍 尝试使用备用方法点击{button_name}...")

                    # 根据模式查找不同的按钮
                    from selenium.webdriver.common.by import By
                    if hasattr(self, 'ban_mode') and self.ban_mode:
                        # 封号模式：查找包含"发布"文本的按钮
                        target_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), '发布')]")
                        search_text = "发布"
                    else:
                        # 存稿模式：查找包含"存稿"文本的按钮
                        target_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), '存稿')]")
                        search_text = "存稿"

                    if target_buttons and len(target_buttons) > 0:
                        # 找到了目标按钮，点击第一个
                        driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", target_buttons[0])
                        time.sleep(0.5)
                        driver.execute_script("arguments[0].click();", target_buttons[0])
                        self.status_signal.emit(f"✅ 使用备用方法成功点击{button_name}")
                    else:
                        self.status_signal.emit(f"⚠️ 未找到任何包含'{search_text}'文本的按钮")

                # 点击按钮后的处理逻辑
                if hasattr(self, 'ban_mode') and self.ban_mode:
                    # 封号模式：点击发布后直接完成
                    self.status_signal.emit(f"✅ 已点击{button_name}，封号模式操作完成")
                    self.status_signal.emit("🚀 立即开始下一个任务")
                else:
                    # 存稿模式：检测"全部保存成功"文字
                    self.status_signal.emit(f"✅ 已点击{button_name}，开始检测保存状态...")

                    if self._detect_save_success(driver):
                        self.status_signal.emit("✅ 检测到'全部保存成功'，存稿操作完成")
                        self.status_signal.emit("🚀 立即开始下一个任务")
                    else:
                        self.status_signal.emit("ℹ️ 未检测到'全部保存成功'，但视为存稿完成")
                        self.status_signal.emit("🚀 立即开始下一个任务")

                return True

            except Exception as e:
                warning(f"尝试点击存稿按钮时出错: {str(e)}")
                self.status_signal.emit(f"⚠️ 点击存稿按钮时出错: {str(e)}")
                return False

        except Exception as e:
            self.status_signal.emit(f"❌ 存稿按钮点击失败: {str(e)}")
            return False

    def _navigate_to_upload_page(self, driver):
        """导航到上传页面"""
        try:
            # 访问头条号上传页面
            driver.get("https://mp.toutiao.com/profile_v4/xigua/upload-video")
            time.sleep(3)
            return True
        except Exception as e:
            warning(f"导航到上传页面失败: {str(e)}")
            return False

    def _upload_video(self, driver, account_id):
        """上传视频 - 批量存稿2风格"""
        try:
            # 首先检测并处理发布限制弹窗
            self._handle_publish_limit_popup(driver, account_id)

            # 获取视频文件
            video_file = self._get_video_file()
            if not video_file:
                self.status_signal.emit(f"❌ 没有找到可用的视频文件")
                return False

            video_path = video_file
            video_filename = os.path.basename(video_file)

            # 使用多种方式查找和上传文件
            file_input = None
            upload_success = False

            # 检测特定元素存在后立即开始视频上传
            target_xpath = "/html/body/div[1]/div/div[3]/section/main/div[2]/div/div/div[2]/div/div/div/div[2]/div/div[1]/div/div/div/div[2]/div/div/div/div/div/div"
            self.status_signal.emit("🔍 检测目标元素是否存在...")

            try:
                # 等待目标元素出现，使用配置的超时时间
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.webdriver.common.by import By

                element_timeout = getattr(self, 'timeout_settings', {}).get('element_wait_timeout', 30)
                target_element = WebDriverWait(driver, element_timeout).until(
                    EC.presence_of_element_located((By.XPATH, target_xpath))
                )
                self.status_signal.emit("✅ 检测到目标元素，立即开始视频上传...")
            except Exception as e:
                self.status_signal.emit(f"⚠️ 目标元素检测失败，使用备用方案: {str(e)[:100]}")
                # 如果目标元素检测失败，等待更长时间让页面完全加载
                self.status_signal.emit("⏳ 等待页面完全加载...")
                time.sleep(5)  # 增加等待时间

            # 方法1: 传统文件输入查找（增加重试机制）
            try:
                self.status_signal.emit("🔍 查找文件输入元素(方法1: CSS选择器)...")
                element_timeout = getattr(self, 'timeout_settings', {}).get('element_wait_timeout', 30)

                # 增加重试机制
                for retry in range(3):
                    try:
                        file_input = WebDriverWait(driver, element_timeout).until(
                            EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
                        )
                        self.status_signal.emit("✅ 方法1成功找到文件输入元素")
                        upload_success = True
                        break
                    except Exception as retry_e:
                        if retry < 2:  # 不是最后一次重试
                            debug(f"方法1第{retry+1}次尝试失败，等待后重试")  # 使用debug级别，不显示在失败日志中
                            time.sleep(3)  # 等待3秒后重试
                        else:
                            raise retry_e

            except Exception as e1:
                self.status_signal.emit(f"⚠️ 方法1查找失败: {str(e1)[:100]}")

            # 方法2: 使用JavaScript显示隐藏的文件输入元素
            if not upload_success:
                try:
                    self.status_signal.emit("🔍 查找文件输入元素(方法2: JavaScript显示)...")
                    driver.execute_script("""
                        var inputs = document.querySelectorAll('input[type="file"]');
                        console.log('找到文件输入元素数量:', inputs.length);
                        for(var i = 0; i < inputs.length; i++) {
                            inputs[i].style.display = 'block';
                            inputs[i].style.opacity = '1';
                            inputs[i].style.visibility = 'visible';
                            inputs[i].style.height = '100px';
                            inputs[i].style.width = '100px';
                            inputs[i].style.position = 'absolute';
                            inputs[i].style.top = '300px';
                            inputs[i].style.left = '500px';
                            inputs[i].style.zIndex = '9999';
                            inputs[i].style.border = '2px solid red';
                        }
                        return inputs.length;
                    """)
                    time.sleep(2)

                    file_input = WebDriverWait(driver, 30).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='file']"))
                    )
                    self.status_signal.emit("✅ 方法2成功找到文件输入元素")
                    upload_success = True
                except Exception as e2:
                    self.status_signal.emit(f"⚠️ 方法2查找失败: {str(e2)[:100]}")

            # 如果所有方法都失败，尝试最后的备用方案
            if not upload_success:
                self.status_signal.emit("⚠️ 常规方法失败，尝试最后的备用方案...")
                try:
                    # 最后的备用方案：等待更长时间后重试
                    time.sleep(5)
                    file_inputs = driver.find_elements(By.CSS_SELECTOR, "input[type='file']")
                    if file_inputs:
                        file_input = file_inputs[0]
                        upload_success = True
                        self.status_signal.emit("✅ 备用方案成功找到文件输入元素")
                    else:
                        self.status_signal.emit("❌ 无法找到文件输入元素，所有查找方法都失败")
                        self.status_signal.emit("⚠️ 可能页面结构发生变化，建议检查网站更新")
                        return False
                except Exception as final_e:
                    self.status_signal.emit(f"❌ 最后备用方案也失败: {str(final_e)[:100]}")
                    return False

            # 如果找到文件输入元素，发送文件路径
            if file_input:
                self.status_signal.emit("✅ 准备发送文件路径...")

                # 确保文件路径是绝对路径
                abs_path = os.path.abspath(video_path)
                self.status_signal.emit(f"📂 文件绝对路径: {abs_path}")

                try:
                    # 使用JavaScript设置文件路径，更可靠
                    driver.execute_script("arguments[0].style.display = 'block';", file_input)
                    time.sleep(1)

                    # 尝试发送文件路径
                    file_input.send_keys(abs_path)
                    self.status_signal.emit("✅ 已通过传统方式发送文件路径")
                except Exception as path_e:
                    # 捕获stale element reference错误但不显示警告
                    if "stale element reference" in str(path_e):
                        debug(f"文件输入元素已过时，但可能已成功上传: {str(path_e)}")
                        self.status_signal.emit("✅ 文件处理中...")
                    else:
                        self.status_signal.emit(f"❌ 发送文件路径失败: {str(path_e)}")
                        return False

                # 立即滚动到页面底部，不等待
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight)")
                self.status_signal.emit("✅ 已滚动到页面底部")

                # 直接假设视频上传成功，不等待进度条消失
                self.status_signal.emit("✅ 视频上传成功")

                # 立即处理封面
                return self._handle_cover_upload_after_video(driver, account_id, video_filename)
            else:
                self.status_signal.emit("❌ 未找到任何文件输入元素")
                return False

        except Exception as e:
            warning(f"上传视频失败: {str(e)}")
            return False

    def _handle_cover_upload_after_video(self, driver, account_id, video_file):
        """视频上传后立即处理封面"""
        try:
            self.status_signal.emit("🔍 准备处理封面，立即查找封面元素...")

            # 重新定位封面元素
            specific_xpath = '//*[@id="root"]/div/div/div[2]/div/div[1]/div/div/div/div[2]/div/div[2]/div[1]/div[2]/div[4]/div[2]/div[1]/div/div/div'
            try:
                # 立即滚动到页面底部，确保封面区域可见
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight)")

                # 立即尝试找到并点击封面元素
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.webdriver.common.by import By

                cover_element = WebDriverWait(driver, 20).until(
                    EC.presence_of_element_located((By.XPATH, specific_xpath))
                )

                # 滚动到元素位置确保可见
                driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", cover_element)
                time.sleep(1)  # 等待滚动完成

                # 使用JavaScript点击，避免元素被遮挡的问题
                self.status_signal.emit("✅ 找到封面元素，使用JavaScript点击...")
                try:
                    # 先尝试普通点击
                    cover_element.click()
                    self.status_signal.emit("✅ 普通点击成功")
                except Exception as click_error:
                    if "element click intercepted" in str(click_error):
                        # 如果被拦截，使用JavaScript点击
                        debug("元素被遮挡，自动使用JavaScript点击")  # 使用debug级别，不显示在失败日志中
                        driver.execute_script("arguments[0].click();", cover_element)
                        self.status_signal.emit("✅ JavaScript点击成功")
                    else:
                        raise click_error

                self.status_signal.emit("✅ 已点击封面元素")

                # 处理封面上传部分
                cover_result = self._handle_cover_upload(driver, account_id, video_file)
                if cover_result:
                    self.status_signal.emit("✅ 封面处理完成，继续存稿流程")
                    return True
                else:
                    self.status_signal.emit("⚠️ 封面处理失败，但继续存稿流程")
                    return True
            except Exception as e:
                self.status_signal.emit(f"⚠️ 封面元素处理失败: {str(e)}")
                return True  # 封面处理失败不影响存稿
        except Exception as e:
            self.status_signal.emit(f"⚠️ 封面处理异常: {str(e)}")
            return True  # 封面处理失败不影响存稿

    def _handle_cover_upload(self, driver, account_id, video_file):
        """处理封面上传流程 - 1:1照抄批量存稿2的逻辑"""
        try:
            # 等待封面上传界面出现
            self.status_signal.emit(f"🔍 账号 {account_id}: 等待封面上传界面出现...")

            # 等待封面上传对话框显示
            self.status_signal.emit(f"⏳ 账号 {account_id}: 等待封面上传对话框显示...")
            try:
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC
                from selenium.webdriver.common.by import By

                cover_dialog = WebDriverWait(driver, 20).until(
                    EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'modal') or contains(@class, 'dialog')]"))
                )
                self.status_signal.emit(f"✅ 账号 {account_id}: 封面上传对话框已显示")
            except Exception as e:
                self.status_signal.emit(f"⚠️ 账号 {account_id}: 封面上传对话框未显示: {str(e)}")
                return False

            # 处理本地封面选项
            self.status_signal.emit(f"🔍 账号 {account_id}: 查找本地封面选项...")

            # 查找包含"本地上传"或"从本地上传"的元素
            local_cover_elements = driver.find_elements(By.XPATH,
                "//*[contains(text(), '本地上传') or contains(text(), '从本地上传') or contains(text(), '本地')]")

            local_option_clicked = False
            if local_cover_elements:
                # 立即点击第一个找到的本地上传选项
                try:
                    self.status_signal.emit("✅ 找到本地封面选项，立即点击...")
                    local_cover_elements[0].click()
                    self.status_signal.emit("✅ 已点击本地封面选项")
                    local_option_clicked = True
                except Exception as e:
                    self.status_signal.emit(f"⚠️ 点击本地封面选项失败: {str(e)[:50]}")

            if not local_option_clicked:
                # 尝试通过XPath找到本地上传选项
                self.status_signal.emit("🔍 通过XPath查找本地上传选项...")
                try:
                    local_cover_xpath = '/html/body/div[7]/div/div[2]/div/div[1]/ul/li[2]'
                    local_cover_element = WebDriverWait(driver, 20).until(
                        EC.presence_of_element_located((By.XPATH, local_cover_xpath))
                    )
                    self.status_signal.emit("✅ 通过XPath找到本地上传选项，正在点击...")
                    local_cover_element.click()
                    self.status_signal.emit("✅ 已点击本地上传选项")
                    local_option_clicked = True
                except Exception as e:
                    self.status_signal.emit(f"⚠️ 通过XPath查找本地上传选项失败: {str(e)[:50]}")
                    # 尝试点击对话框中任何可能的上传按钮
                    upload_buttons = driver.find_elements(By.XPATH,
                        "//div[contains(@class, 'modal') or contains(@class, 'dialog')]//button[contains(@class, 'upload') or contains(text(), '上传')]")
                    if upload_buttons:
                        try:
                            self.status_signal.emit("✅ 找到上传按钮，点击中...")
                            upload_buttons[0].click()
                            self.status_signal.emit("✅ 已点击上传按钮")
                            local_option_clicked = True
                        except Exception as e:
                            self.status_signal.emit(f"⚠️ 点击上传按钮失败: {str(e)[:50]}")

            if not local_option_clicked:
                self.status_signal.emit("⚠️ 无法找到或点击本地上传选项")
                return False

            # 检测封面上传区域并上传同名封面文件
            self.status_signal.emit(f"🔍 账号 {account_id}: 准备上传封面...")

            # 查找与视频同名的封面文件
            video_name = os.path.splitext(os.path.basename(video_file))[0]  # 获取视频文件名（不含扩展名）
            cover_file = None

            # 在封面目录中查找与视频同名的图片文件
            if hasattr(self, 'cover_dir') and self.cover_dir and os.path.exists(self.cover_dir):
                possible_extensions = ['.jpg', '.jpeg', '.png', '.webp', '.bmp']
                for ext in possible_extensions:
                    potential_cover = os.path.join(self.cover_dir, f"{video_name}{ext}")
                    if os.path.exists(potential_cover):
                        cover_file = potential_cover
                        break

                # 如果没有找到同名封面，使用第一个封面文件
                if not cover_file:
                    cover_files = [f for f in os.listdir(self.cover_dir)
                                if f.lower().endswith(tuple(possible_extensions))]
                    if cover_files:
                        cover_file = os.path.join(self.cover_dir, cover_files[0])

            # 如果找到封面文件，尝试上传
            if cover_file and os.path.exists(cover_file):
                self.status_signal.emit(f"✅ 账号 {account_id}: 找到封面文件: {os.path.basename(cover_file)}")

                # 确保文件路径是绝对路径
                abs_cover_path = os.path.abspath(cover_file)

                # 等待上传界面中的文件输入元素出现
                self.status_signal.emit("🔍 等待文件输入元素出现...")
                try:
                    # 立即查找文件输入元素，限制在当前对话框内查找
                    self.status_signal.emit("🔍 在封面上传对话框中查找文件输入元素...")

                    # 尝试在对话框中查找文件输入元素
                    dialog_file_inputs = None
                    try:
                        # 方法1: 使用对话框元素作为范围查找
                        dialog_file_inputs = cover_dialog.find_elements(By.CSS_SELECTOR, "input[type='file']")
                        if dialog_file_inputs and len(dialog_file_inputs) > 0:
                            self.status_signal.emit(f"✅ 在对话框中找到 {len(dialog_file_inputs)} 个文件输入元素")
                    except Exception:
                        debug("无法直接在对话框中查找，尝试其他方法")  # 使用debug级别，不显示在失败日志中

                    # 方法2: 如果对话框直接查找失败，使用更具体的XPath
                    if not dialog_file_inputs or len(dialog_file_inputs) == 0:
                        try:
                            dialog_file_inputs = driver.find_elements(By.XPATH,
                                "//div[contains(@class, 'modal') or contains(@class, 'dialog')]//input[@type='file']")
                            if dialog_file_inputs and len(dialog_file_inputs) > 0:
                                self.status_signal.emit(f"✅ 方法2成功：通过XPath在对话框中找到 {len(dialog_file_inputs)} 个文件输入元素")
                        except Exception as method2_error:
                            debug(f"方法2失败: {str(method2_error)}")

                    # 方法3: 增强的元素定位策略 - 查找所有可见的文件输入元素
                    if not dialog_file_inputs or len(dialog_file_inputs) == 0:
                        try:
                            all_file_inputs = driver.find_elements(By.XPATH, "//input[@type='file']")
                            # 过滤出可见或可交互的元素
                            dialog_file_inputs = []
                            for elem in all_file_inputs:
                                try:
                                    if elem.is_displayed() or elem.is_enabled():
                                        dialog_file_inputs.append(elem)
                                except:
                                    continue
                            if dialog_file_inputs:
                                self.status_signal.emit(f"✅ 方法3成功：找到 {len(dialog_file_inputs)} 个可用的文件输入元素")
                        except Exception as method3_error:
                            debug(f"方法3失败: {str(method3_error)}")

                    # 方法4: 使用CSS选择器查找
                    if not dialog_file_inputs or len(dialog_file_inputs) == 0:
                        try:
                            dialog_file_inputs = driver.find_elements(By.CSS_SELECTOR,
                                "div.modal input[type='file'], div.dialog input[type='file'], [role='dialog'] input[type='file']")
                            if dialog_file_inputs:
                                self.status_signal.emit(f"✅ 方法4成功：通过CSS选择器找到 {len(dialog_file_inputs)} 个文件输入元素")
                        except Exception as method4_error:
                            debug(f"方法4失败: {str(method4_error)}")

                    # 方法5: 等待元素出现后再查找
                    if not dialog_file_inputs or len(dialog_file_inputs) == 0:
                        try:
                            debug("等待文件输入元素出现")  # 使用debug级别，不显示在失败日志中
                            wait = WebDriverWait(driver, 5)
                            file_input_element = wait.until(
                                EC.presence_of_element_located((By.XPATH, "//input[@type='file']"))
                            )
                            if file_input_element:
                                dialog_file_inputs = [file_input_element]
                                self.status_signal.emit("✅ 方法5成功：等待后找到文件输入元素")
                        except:
                            debug("方法5失败: 等待文件输入元素超时")

                    # 方法6: 如果都失败，尝试创建临时文件输入元素
                    if not dialog_file_inputs or len(dialog_file_inputs) == 0:
                        debug("未找到对话框中的文件输入元素，尝试创建临时元素")  # 使用debug级别，不显示在失败日志中
                        js_code = """
                            // 在封面上传对话框中创建文件输入元素
                            var dialogs = document.querySelectorAll('div.modal, div.dialog, div[role="dialog"]');
                            if (dialogs.length > 0) {
                                var dialog = dialogs[dialogs.length - 1]; // 使用最后一个对话框
                                var input = document.createElement('input');
                                input.type = 'file';
                                input.style.position = 'absolute';
                                input.style.opacity = '0';
                                input.style.zIndex = '9999';
                                input.id = 'custom-cover-input';
                                dialog.appendChild(input);
                                return "已在对话框中创建文件输入元素";
                            } else {
                                return "未找到对话框";
                            }
                        """
                        result = driver.execute_script(js_code)
                        self.status_signal.emit(f"JavaScript结果: {result}")

                        # 尝试获取创建的元素
                        time.sleep(1)
                        dialog_file_inputs = driver.find_elements(By.ID, "custom-cover-input")
                        if dialog_file_inputs and len(dialog_file_inputs) > 0:
                            self.status_signal.emit("✅ 成功创建并找到临时文件输入元素")

                    # 如果找到了对话框中的文件输入元素
                    if dialog_file_inputs and len(dialog_file_inputs) > 0:
                        file_input = dialog_file_inputs[0]
                        self.status_signal.emit("✅ 找到封面上传对话框中的文件输入元素")

                        try:
                            # 直接发送文件路径
                            self.status_signal.emit("✅ 发送封面文件路径...")
                            file_input.send_keys(abs_cover_path)
                            self.status_signal.emit("✅ 已发送封面文件路径")
                            self.status_signal.emit("✅ 封面上传成功")

                            # 点击封面提交按钮
                            submit_result = self._click_cover_submit_button(driver, account_id)
                            if not submit_result:
                                self.status_signal.emit(f"❌ 账号 {account_id}: 点击封面提交按钮失败")
                                self.status_signal.emit(f"⚠️ 账号 {account_id}: 跳过封面提交，继续存稿流程")
                                return True  # 封面提交失败不影响存稿

                            return True  # 封面上传成功
                        except Exception as e:
                            self.status_signal.emit(f"❌ 发送文件路径失败: {str(e)}")

                            # 备用方法：临时使元素可见
                            try:
                                debug("尝试备用方法，使元素完全可见")  # 使用debug级别，不显示在失败日志中
                                driver.execute_script("""
                                    arguments[0].style.display = 'block';
                                    arguments[0].style.visibility = 'visible';
                                    arguments[0].style.opacity = '1';
                                    arguments[0].style.position = 'static';
                                    arguments[0].style.width = '300px';
                                    arguments[0].style.height = '40px';
                                    arguments[0].style.zIndex = '999999';
                                """, file_input)
                                file_input.send_keys(abs_cover_path)
                                self.status_signal.emit("✅ 使用备用方法发送成功")
                                self.status_signal.emit("✅ 封面上传成功")

                                # 尝试点击提交按钮
                                submit_result = self._click_cover_submit_button(driver, account_id)
                                if not submit_result:
                                    self.status_signal.emit(f"❌ 账号 {account_id}: 点击封面提交按钮失败")
                                    self.status_signal.emit(f"⚠️ 账号 {account_id}: 跳过封面提交，继续存稿流程")
                                    return True  # 封面提交失败不影响存稿

                                return True  # 封面上传成功
                            except Exception as backup_e:
                                self.status_signal.emit(f"❌ 备用方法也失败: {str(backup_e)}")
                                return False
                    else:
                        self.status_signal.emit("❌ 未找到封面上传对话框中的文件输入元素")
                        return False
                except Exception as find_e:
                    self.status_signal.emit(f"❌ 查找文件输入元素失败: {str(find_e)}")
                    return False
            else:
                self.status_signal.emit(f"⚠️ 账号 {account_id}: 未找到封面文件，跳过封面上传")
                return True

        except Exception as e:
            self.status_signal.emit(f"⚠️ 账号 {account_id} 封面上传失败: {str(e)}")
            return True  # 封面上传失败不影响主流程

    def _click_cover_submit_button(self, driver, account_id="未知"):
        """点击封面提交按钮 - 1:1照抄批量存稿2的逻辑"""
        try:
            self.status_signal.emit(f"🔍 账号 {account_id}: 准备点击封面确认按钮...")

            # 增加等待，确保封面上传已完成并且按钮可点击
            time.sleep(1)

            # 第一个按钮的XPath（方法1）
            first_button_xpath = '/html/body/div[6]/div/div[2]/div/div[1]/div/div[2]/div[2]/div[3]/div[3]/button[2]'

            # 第一个按钮的XPath（方法2）
            first_button_xpath_method2 = '/html/body/div[7]/div/div[2]/div/div[1]/div/div[2]/div[2]/div[3]/div[3]/button[2]'

            # 第二个按钮的XPath（封面提交元素）
            second_button_xpath = '/html/body/div[7]/div/div[2]/div/div[2]/button[2]'

            # 新增方法2的第二个按钮XPath
            second_button_xpath_method2 = '/html/body/div[8]/div/div[2]/div/div[2]/button[2]'

            try:
                # 尝试点击第一个按钮（方法1）
                self.status_signal.emit(f"🔍 账号 {account_id}: 尝试点击第一个封面确认按钮(方法1)...")

                # 使用JavaScript点击第一个按钮（方法1）
                first_result = driver.execute_script("""
                    try {
                        var xpath = arguments[0];
                        var result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                        var button = result.singleNodeValue;

                        if (button) {
                            button.click();
                            return '第一个按钮点击成功(方法1)';
                        } else {
                            return '第一个按钮未找到(方法1)';
                        }
                    } catch (e) {
                        return '第一个按钮点击失败(方法1): ' + e.message;
                    }
                """, first_button_xpath)

                self.status_signal.emit(f"第一个按钮结果(方法1): {first_result}")

                # 尝试点击第一个按钮（方法2）
                self.status_signal.emit("🔍 尝试点击第一个封面确认按钮(方法2)...")

                first_result_method2 = driver.execute_script("""
                    try {
                        var xpath = arguments[0];
                        var result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                        var button = result.singleNodeValue;

                        if (button) {
                            button.click();
                            return '第一个按钮点击成功(方法2)';
                        } else {
                            return '第一个按钮未找到(方法2)';
                        }
                    } catch (e) {
                        return '第一个按钮点击失败(方法2): ' + e.message;
                    }
                """, first_button_xpath_method2)

                self.status_signal.emit(f"第一个按钮结果(方法2): {first_result_method2}")

                # 尝试点击第二个按钮（方法1）
                self.status_signal.emit("🔍 尝试点击第二个封面确认按钮(方法1)...")

                second_result = driver.execute_script("""
                    try {
                        var xpath = arguments[0];
                        var result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                        var button = result.singleNodeValue;

                        if (button) {
                            button.click();
                            return '第二个按钮点击成功(方法1)';
                        } else {
                            return '第二个按钮未找到(方法1)';
                        }
                    } catch (e) {
                        return '第二个按钮点击失败(方法1): ' + e.message;
                    }
                """, second_button_xpath)

                self.status_signal.emit(f"第二个按钮结果(方法1): {second_result}")

                # 尝试点击第二个按钮（方法2）
                self.status_signal.emit("🔍 尝试点击第二个封面确认按钮(方法2)...")

                second_result_method2 = driver.execute_script("""
                    try {
                        var xpath = arguments[0];
                        var result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                        var button = result.singleNodeValue;

                        if (button) {
                            button.click();
                            return '第二个按钮点击成功(方法2)';
                        } else {
                            return '第二个按钮未找到(方法2)';
                        }
                    } catch (e) {
                        return '第二个按钮点击失败(方法2): ' + e.message;
                    }
                """, second_button_xpath_method2)

                self.status_signal.emit(f"第二个按钮结果(方法2): {second_result_method2}")

                # 检查按钮的点击结果
                button_success = False
                if "成功" in first_result and "成功" in second_result:
                    self.status_signal.emit("✅ 所有封面确认按钮点击成功")
                    button_success = True
                elif "成功" in first_result or "成功" in first_result_method2:
                    self.status_signal.emit("ℹ️ 第一个按钮点击成功")
                    button_success = True
                elif "成功" in second_result or "成功" in second_result_method2:
                    self.status_signal.emit("⚠️ 第二个按钮点击成功")
                    button_success = True
                else:
                    self.status_signal.emit(f"⚠️ 账号 {account_id}: 所有封面确认按钮都未点击成功")

                # 判断按钮点击是否成功
                if not button_success:
                    self.status_signal.emit(f"❌ 账号 {account_id}: 所有点击确认按钮方法均失败")
                    self.status_signal.emit(f"⚠️ 账号 {account_id}: 跳过封面确认，继续存稿流程")
                    return True  # 封面处理失败不影响存稿，继续后续流程

                # 等待弹窗消失
                self.status_signal.emit("🔍 等待弹窗消失...")

                # 弹窗元素的XPath
                dialog_xpath1 = "/html/body/div[6]/div/div[2]/div"
                dialog_xpath2 = "/html/body/div[7]/div/div[2]/div/div[1]"

                try:
                    from selenium.webdriver.support.ui import WebDriverWait
                    from selenium.webdriver.support import expected_conditions as EC
                    from selenium.webdriver.common.by import By

                    # 等待第一个弹窗消失，最多等待60秒
                    WebDriverWait(driver, 60, poll_frequency=0.01).until(
                        EC.invisibility_of_element_located((By.XPATH, dialog_xpath1))
                    )
                    self.status_signal.emit("✅ 第一个弹窗已消失")

                    # 等待第二个弹窗消失，最多等待60秒
                    WebDriverWait(driver, 60, poll_frequency=0.01).until(
                        EC.invisibility_of_element_located((By.XPATH, dialog_xpath2))
                    )
                    self.status_signal.emit("✅ 第二个弹窗已消失")
                except Exception as wait_e:
                    self.status_signal.emit(f"⚠️ 等待弹窗消失超时: {str(wait_e)}")

                # 1:1照抄批量存稿2的检测逻辑
                self.status_signal.emit("🔍 检测封面上传成功元素...")

                # 封面上传成功元素的XPath
                cover_success_xpath = '/html/body/div[1]/div/div[3]/section/main/div[2]/div/div/div[2]/div/div/div/div[2]/div/div[1]/div/div/div/div[2]/div/div[2]/div[1]/div[2]/div[4]/div[2]/div[1]/div/div/div[2]/span[3]'

                # 设置最大等待时间和检查间隔
                max_wait_time = 1800  # 最大等待30分钟
                check_interval = 0.5   # 每0.5秒检查一次
                start_time = time.time()
                cover_element_found = False

                while time.time() - start_time < max_wait_time:
                    try:
                        # 方法1: 使用XPath查找封面上传成功元素
                        cover_elements = driver.find_elements(By.XPATH, cover_success_xpath)
                        if cover_elements and len(cover_elements) > 0:
                            self.status_signal.emit("✅ 已检测到封面上传成功元素")
                            cover_element_found = True
                            break

                        # 方法2: 使用JavaScript检查元素是否存在
                        js_result = driver.execute_script("""
                            try {
                                var xpath = arguments[0];
                                var result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                                var element = result.singleNodeValue;
                                return element ? true : false;
                            } catch (e) {
                                return false;
                            }
                        """, cover_success_xpath)

                        if js_result:
                            self.status_signal.emit("✅ 已通过JavaScript检测到封面上传成功元素")
                            cover_element_found = True
                            break

                        # 等待指定的检查间隔
                        time.sleep(check_interval)

                    except Exception as e:
                        warning(f"检测封面上传成功元素出错: {str(e)}")
                        time.sleep(check_interval)

                if cover_element_found:
                    self.status_signal.emit("✅ 确认检测到封面上传成功元素!")
                else:
                    self.status_signal.emit("⚠️ 未检测到封面上传成功元素，继续检查上传成功文本")

                # 接着立即检测"上传成功"文本
                self.status_signal.emit("🔍 立即检测上传成功文本...")

                upload_success = False
                start_time = time.time()

                while time.time() - start_time < max_wait_time:
                    try:
                        # 方法1: 使用XPath查找包含"上传成功"的元素
                        success_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '上传成功')]")
                        if success_elements and len(success_elements) > 0:
                            self.status_signal.emit("✅ 已检测到上传成功提示 (XPath方式)")
                            upload_success = True
                            break

                        # 方法2: 检查页面源代码
                        if "上传成功" in driver.page_source:
                            self.status_signal.emit("✅ 已检测到上传成功提示 (页面源代码方式)")
                            upload_success = True
                            break

                        # 方法3: 使用JavaScript检查
                        js_result = driver.execute_script("""
                            return document.body.innerText.includes('上传成功');
                        """)
                        if js_result:
                            self.status_signal.emit("✅ 已检测到上传成功提示 (JavaScript方式)")
                            upload_success = True
                            break

                        # 方法4: 也检查"开始填写"文本，它也可能表示上传成功
                        start_fill_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '开始填写')]")
                        if start_fill_elements and len(start_fill_elements) > 0:
                            self.status_signal.emit("✅ 已检测到'开始填写'提示，表明上传已成功")
                            upload_success = True
                            break

                        # 等待指定的检查间隔
                        time.sleep(check_interval)

                    except Exception as e:
                        warning(f"检查上传成功提示时出错: {str(e)}")
                        time.sleep(check_interval)

                if upload_success:
                    self.status_signal.emit("✅ 确认检测到上传成功提示!")
                else:
                    self.status_signal.emit("⚠️ 未检测到上传成功提示，但封面处理流程已完成")

                self.status_signal.emit("✅ 封面提交完成")
                return True

            except Exception as e:
                warning(f"点击封面提交按钮时出错: {str(e)}")
                self.status_signal.emit(f"⚠️ 点击封面提交按钮时出错: {str(e)}")
                return False

        except Exception as e:
            self.status_signal.emit(f"❌ 封面提交按钮处理失败: {str(e)}")
            return False

    def _click_save_draft_button(self, driver):
        """点击存稿按钮 - 批量存稿2风格"""
        try:
            # 等待短暂时间后点击存稿按钮
            time.sleep(2)  # 等待2秒确保页面稳定

            button_name = "存稿按钮"
            self.status_signal.emit(f"🔍 尝试点击{button_name}...")

            # 滚动到页面底部以确保存稿按钮可见
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")

            # 存稿按钮的XPath
            draft_button_xpath = '//*[@id="root"]/div/div/div[2]/div/div[1]/div/div/div/div[2]/div/div[2]/div[3]/div/button[1]'

            try:
                # 使用JavaScript查找并点击存稿按钮
                draft_click_result = driver.execute_script("""
                    try {
                        var xpath = arguments[0];
                        var result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                        var button = result.singleNodeValue;

                        if (button) {
                            // 确保按钮在视图中
                            button.scrollIntoView({behavior: 'smooth', block: 'center'});
                            setTimeout(function() {
                                button.click();
                            }, 500);
                            return arguments[1] + '点击成功';
                        } else {
                            return '未找到' + arguments[1];
                        }
                    } catch (e) {
                        return '点击存稿按钮出错: ' + e.message;
                    }
                """, draft_button_xpath, button_name)

                self.status_signal.emit(f"📝 {button_name}点击结果: {draft_click_result}")

                # 如果JavaScript方法失败，尝试使用Selenium方法
                if "成功" not in draft_click_result:
                    self.status_signal.emit(f"🔍 尝试使用备用方法点击{button_name}...")

                    # 查找包含"存稿"文本的按钮
                    from selenium.webdriver.common.by import By
                    target_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), '存稿')]")

                    if target_buttons and len(target_buttons) > 0:
                        # 找到了目标按钮，点击第一个
                        driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", target_buttons[0])
                        time.sleep(0.5)
                        driver.execute_script("arguments[0].click();", target_buttons[0])
                        self.status_signal.emit(f"✅ 使用备用方法成功点击{button_name}")
                    else:
                        self.status_signal.emit(f"⚠️ 未找到任何包含'存稿'文本的按钮")

                # 点击按钮后立即开始检测"全部保存成功"文字
                self.status_signal.emit(f"✅ 已点击{button_name}，开始检测保存状态...")

                return True

            except Exception as e:
                warning(f"尝试点击存稿按钮时出错: {str(e)}")
                self.status_signal.emit(f"⚠️ 点击存稿按钮时出错: {str(e)}")
                return False

        except Exception as e:
            self.status_signal.emit(f"❌ 存稿按钮点击失败: {str(e)}")
            return False

    def _wait_for_draft_completion(self, driver):
        """等待存稿完成 - 检测'全部保存成功'文字"""
        try:
            # 检测"全部保存成功"文字
            if self._detect_save_success(driver):
                self.status_signal.emit("✅ 检测到'全部保存成功'，存稿操作完成")
                self.status_signal.emit("🚀 立即开始下一个任务")
                return True
            else:
                self.status_signal.emit("ℹ️ 未检测到'全部保存成功'，但视为存稿完成")
                self.status_signal.emit("🚀 立即开始下一个任务")
                return True

        except Exception as e:
            warning(f"等待存稿完成失败: {str(e)}")
            return True  # 即使检测失败也视为成功

    def _detect_save_success(self, driver):
        """检测页面中是否出现'全部保存成功'文字"""
        max_wait_time = 30  # 最大等待30秒
        check_interval = 0.3  # 每0.3秒检测一次
        start_time = time.time()

        self.status_signal.emit("🔍 开始检测'全部保存成功'文字...")

        while time.time() - start_time < max_wait_time:
            try:
                # 方法1：检查页面源代码
                if "全部保存成功" in driver.page_source:
                    self.status_signal.emit("✅ 在页面源代码中检测到'全部保存成功'")
                    return True

                # 方法2：使用XPath查找包含该文字的元素
                from selenium.webdriver.common.by import By
                success_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '全部保存成功')]")
                if success_elements:
                    self.status_signal.emit("✅ 通过XPath检测到'全部保存成功'元素")
                    return True

                # 方法3：使用JavaScript检查
                js_result = driver.execute_script("""
                    return document.body.innerText.includes('全部保存成功');
                """)
                if js_result:
                    self.status_signal.emit("✅ 通过JavaScript检测到'全部保存成功'")
                    return True

                # 等待0.3秒后继续检测
                time.sleep(check_interval)

            except Exception as e:
                self.status_signal.emit(f"⚠️ 检测'全部保存成功'时出错: {str(e)}")
                time.sleep(check_interval)

        # 超时未检测到
        elapsed_time = time.time() - start_time
        self.status_signal.emit(f"ℹ️ 检测超时({elapsed_time:.1f}秒)，未发现'全部保存成功'文字")
        return False

    def _get_video_file(self):
        """获取视频文件"""
        try:
            # 检查视频目录设置
            if not hasattr(self, 'video_dir') or not self.video_dir:
                self.status_signal.emit("⚠️ 未设置视频目录")
                return None

            if not os.path.exists(self.video_dir):
                self.status_signal.emit(f"⚠️ 视频目录不存在: {self.video_dir}")
                return None

            # 查找视频文件
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv']
            video_files = []

            self.status_signal.emit(f"🔍 在目录中查找视频文件: {self.video_dir}")

            for file in os.listdir(self.video_dir):
                if any(file.lower().endswith(ext) for ext in video_extensions):
                    video_files.append(os.path.join(self.video_dir, file))

            self.status_signal.emit(f"📊 找到 {len(video_files)} 个视频文件")

            if video_files:
                # 随机选择一个视频文件
                import random
                selected_video = random.choice(video_files)
                self.status_signal.emit(f"✅ 选择视频文件: {os.path.basename(selected_video)}")
                return selected_video
            else:
                self.status_signal.emit("❌ 视频目录中没有找到视频文件")
                return None
        except Exception as e:
            self.status_signal.emit(f"❌ 获取视频文件时出错: {str(e)}")
            return None

    def _get_cover_file(self):
        """获取封面文件"""
        try:
            if not hasattr(self, 'cover_dir') or not self.cover_dir:
                return None

            if not os.path.exists(self.cover_dir):
                return None

            # 查找封面文件
            cover_extensions = ['.jpg', '.jpeg', '.png', '.webp', '.bmp']
            cover_files = []

            for file in os.listdir(self.cover_dir):
                if any(file.lower().endswith(ext) for ext in cover_extensions):
                    cover_files.append(os.path.join(self.cover_dir, file))

            if cover_files:
                # 随机选择一个封面文件
                import random
                return random.choice(cover_files)
            else:
                return None
        except Exception:
            return None

    def _handle_publish_limit_popup(self, driver, account_id):
        """检测并处理发布限制弹窗"""
        try:
            # 检查是否启用了发布限制弹窗处理
            if not getattr(self, 'handle_publish_limit', True):
                debug(f"账号 {account_id}: 发布限制弹窗处理已禁用")
                return True

            self.status_signal.emit(f"🔍 账号 {account_id}: 检测发布限制弹窗...")

            # 检测发布限制相关的文本
            limit_keywords = [
                "无法发布",
                "今日发布次数已达上限",
                "发布次数已达上限",
                "今日发布已达上限",
                "发布限制",
                "达到发布上限"
            ]

            # 方法1: 检查页面源代码中是否包含限制关键词
            page_source = driver.page_source
            found_limit_text = None
            for keyword in limit_keywords:
                if keyword in page_source:
                    found_limit_text = keyword
                    break

            if found_limit_text:
                self.status_signal.emit(f"⚠️ 账号 {account_id}: 检测到发布限制弹窗 - '{found_limit_text}'")

                # 尝试点击"无法发布"按钮
                button_xpath = "/html/body/div[7]/div[2]/div/div[2]/div[3]/button"

                try:
                    from selenium.webdriver.common.by import By
                    from selenium.webdriver.support.ui import WebDriverWait
                    from selenium.webdriver.support import expected_conditions as EC

                    # 等待按钮出现并点击
                    button_element = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, button_xpath))
                    )

                    # 滚动到按钮位置
                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", button_element)
                    time.sleep(1)

                    # 点击按钮
                    button_element.click()
                    self.status_signal.emit(f"✅ 账号 {account_id}: 成功点击发布限制弹窗按钮")

                    # 等待弹窗消失
                    time.sleep(2)
                    return True

                except Exception as click_error:
                    self.status_signal.emit(f"⚠️ 账号 {account_id}: 点击发布限制按钮失败: {str(click_error)[:100]}")

                    # 备用方法：使用JavaScript点击
                    try:
                        js_result = driver.execute_script("""
                            try {
                                var xpath = arguments[0];
                                var result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                                var button = result.singleNodeValue;

                                if (button) {
                                    button.click();
                                    return '成功点击发布限制按钮';
                                } else {
                                    return '未找到发布限制按钮';
                                }
                            } catch (e) {
                                return '点击失败: ' + e.message;
                            }
                        """, button_xpath)

                        self.status_signal.emit(f"🔧 账号 {account_id}: JavaScript点击结果: {js_result}")

                        if "成功" in js_result:
                            self.status_signal.emit(f"✅ 账号 {account_id}: 使用JavaScript成功处理发布限制弹窗")
                            time.sleep(2)
                            return True
                        else:
                            self.status_signal.emit(f"⚠️ 账号 {account_id}: JavaScript点击也失败，继续存稿流程")
                            return True  # 即使处理失败也继续流程

                    except Exception as js_error:
                        self.status_signal.emit(f"⚠️ 账号 {account_id}: JavaScript备用方法失败: {str(js_error)[:100]}")
                        return True  # 即使处理失败也继续流程
            else:
                debug(f"账号 {account_id}: 未检测到发布限制弹窗")
                return True

        except Exception as e:
            self.status_signal.emit(f"⚠️ 账号 {account_id}: 发布限制弹窗检测异常: {str(e)[:100]}")
            return True  # 异常时也继续流程


def run_batch_cunggao(table, cookie_dir, video_dir=None, cover_dir=None, parent=None, headless_mode=False, concurrent_count=1, draft_count=1, video_allocation=True, clean_cache_before_start=False, remove_popup=True, ban_mode=False, handle_publish_limit=True, selected_accounts=None, account_tab_ref=None, use_fingerprint=False, fingerprint_settings=None, timeout_settings=None, retry_settings=None):
    """运行批量存稿任务

    Args:
        table: 账号表格控件
        cookie_dir: Cookie文件目录
        video_dir: 视频文件目录
        cover_dir: 封面文件目录
        parent: 父窗口
        headless_mode: 是否启用无头模式
        concurrent_count: 并发数量
        draft_count: 存稿次数
        video_allocation: 是否启用视频公平分配，默认为True
        clean_cache_before_start: 是否在开始前清理浏览器缓存
        remove_popup: 是否去除活动弹窗，默认为True
        ban_mode: 是否启用封号模式（点击发布而不是存稿），默认为False
        handle_publish_limit: 是否自动处理发布限制弹窗，默认为True
        selected_accounts: 选中的账号列表，如果为None则处理所有账号
        account_tab_ref: 账号标签页引用，用于进度回调
        use_fingerprint: 是否使用指纹浏览器，默认为False
        fingerprint_settings: 指纹浏览器设置字典，默认为None
        timeout_settings: 超时设置字典，默认为None
        retry_settings: 重试设置字典，默认为None

    Returns:
        tuple: (thread, worker) 线程和工作对象
    """
    try:
        from PyQt5.QtCore import QThread
        info(f"创建批量存稿任务 - 无头模式: {headless_mode}, 并发数: {concurrent_count}, 存稿次数: {draft_count}")

        # 创建工作对象
        worker = BatchCunggaoWorker(table, cookie_dir, draft_count, concurrent_count, headless_mode)
        # 设置额外参数
        worker.video_dir = video_dir
        worker.cover_dir = cover_dir
        worker.video_allocation = video_allocation
        worker.clean_cache_before_start = clean_cache_before_start
        worker.remove_popup = remove_popup
        worker.ban_mode = ban_mode  # 设置封号模式
        worker.handle_publish_limit = handle_publish_limit  # 设置发布限制弹窗处理
        worker.selected_accounts = selected_accounts
        worker.account_tab_ref = account_tab_ref
        worker.use_fingerprint = use_fingerprint  # 设置指纹浏览器开关
        worker.fingerprint_settings = fingerprint_settings  # 设置指纹浏览器配置

        # 设置超时参数
        if timeout_settings:
            worker.timeout_settings = timeout_settings
        else:
            # 使用默认超时设置（与单独登录保持一致）
            worker.timeout_settings = {
                'page_load_timeout': 15,  # 与单独登录保持一致
                'element_wait_timeout': 30,
                'login_verify_timeout': 10,
                'operation_delay': 3
            }

        # 设置重试参数
        if retry_settings:
            worker.retry_settings = retry_settings
            # 设置自动重试失败账号标志
            worker.auto_retry_failed = retry_settings.get('auto_retry_failed', False)
        else:
            # 使用默认重试设置
            worker.retry_settings = {
                'enable_retry': True,  # 默认启用重试
                'max_retry_count': 2,  # 默认最大重试2次
                'retry_delay': 10,  # 默认重试间隔10秒
                'auto_retry_failed': False  # 默认不自动重试失败账号
            }
            worker.auto_retry_failed = False

        # 创建工作线程
        thread = QThread()

        # 移动工作对象到线程
        worker.moveToThread(thread)

        # 连接信号槽 - 使用更安全的连接方式
        thread.started.connect(worker.run)

        # 修复对象生命周期管理
        def on_worker_finished():
            """工作完成时的清理函数"""
            try:
                if thread and thread.isRunning():
                    thread.quit()
                    thread.wait(5000)  # 等待最多5秒
            except Exception as e:
                warning(f"清理线程时出错: {str(e)}")

        def on_thread_finished():
            """线程完成时的清理函数"""
            try:
                if worker:
                    worker.deleteLater()
                if thread:
                    thread.deleteLater()
            except Exception as e:
                warning(f"删除对象时出错: {str(e)}")

        # 连接清理信号
        worker.finished_signal.connect(on_worker_finished)
        thread.finished.connect(on_thread_finished)

        # 启动线程
        thread.start()

        info("批量存稿线程已启动")
        return thread, worker

    except Exception as e:
        error_msg = handle_error("创建批量存稿任务", e)
        if parent:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(parent, "操作失败", error_msg)
        return None, None
