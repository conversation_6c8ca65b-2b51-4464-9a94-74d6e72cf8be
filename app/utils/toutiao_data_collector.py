#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
头条自媒体数据采集模块
用于采集账号数据并更新到主界面
"""

import os
import time
import json
import traceback
import re
from PyQt5.QtCore import QObject, pyqtSignal, QThread, QMutex
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QTableWidgetItem
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from PyQt5.QtGui import QBrush, QColor
from PyQt5.QtWidgets import QApplication
from selenium.webdriver.chrome.service import Service
import sys
from datetime import datetime
from app.utils.logger import logger, info, success, error, warning, debug

class ToutiaoDataCollector(QObject):
    """头条数据采集器类，负责采集头条自媒体账号数据"""
    # 信号定义
    status_update_signal = pyqtSignal(str)
    data_updated_signal = pyqtSignal(str)
    log_message_signal = pyqtSignal(str, str)
    error_message_signal = pyqtSignal(str, str)
    all_threads_finished_signal = pyqtSignal()

    def __init__(self, table, cookie_dir="./accounts", data_dir="./data", thread_count=3):
        """初始化数据采集器

        Args:
            table: 账号表格控件(QTableWidget)
            cookie_dir: Cookie文件存储目录
            data_dir: 数据存储目录
            thread_count: 采集线程数量
        """
        super().__init__()
        self.table = table
        self.cookie_dir = cookie_dir
        self.data_dir = data_dir
        self.thread_count = thread_count
        self.active_threads = []
        self.threads_finished_count = 0

        # 账号队列和锁
        self.account_queue = []
        self.queue_lock = QMutex()
        self.current_index = 0
        self.total_accounts = 0

        # 添加失败账号统计
        self.failed_accounts = set()  # 存储失败的账号
        self.failed_accounts_count = 0  # 失败账号计数
        self.success_accounts_count = 0  # 成功账号计数

        # 添加调试日志
        self.log_message_signal.emit(f"初始化数据采集器，线程数: {self.thread_count}", "INFO")

        # 确保数据目录存在
        try:
            os.makedirs(self.data_dir, exist_ok=True)
        except Exception as e:
            self.log_message_signal.emit(f"创建数据目录时出错：{str(e)}", "WARNING")

    def set_thread_count(self, count):
        """设置采集线程数量"""
        self.thread_count = max(1, min(18, count))  # 限制在1-18之间
        self.log_message_signal.emit(f"采集线程数量已设置为: {self.thread_count}", "INFO")

    def get_next_account(self):
        """从队列获取下一个要处理的账号，线程安全

        Returns:
            tuple: (row_index, account_name, cookie_path) 或 None表示没有更多账号
        """
        # 使用互斥锁确保线程安全
        self.queue_lock.lock()
        try:
            # 添加调试日志
            try:
                caller_thread = QThread.currentThread()
                thread_name = getattr(caller_thread, "objectName", lambda: "未命名线程")()
            except:
                thread_name = "未命名线程"
            self.log_message_signal.emit(f"[{thread_name}] 尝试获取下一个账号，当前索引: {self.current_index}，队列长度: {len(self.account_queue)}", "INFO")

            # 检查是否还有账号可处理
            if self.current_index < len(self.account_queue):
                # 获取下一个账号
                account_info = self.account_queue[self.current_index]
                # 增加索引，为下一个线程准备
                self.current_index += 1

                # 添加调试日志
                row, account_name, cookie_path = account_info
                self.log_message_signal.emit(f"[{thread_name}] 获取到账号: {account_name}，行索引: {row}，当前索引更新为: {self.current_index}", "INFO")

                return account_info
            else:
                # 添加调试日志
                self.log_message_signal.emit(f"[{thread_name}] 没有更多账号可处理，当前索引: {self.current_index}，队列长度: {len(self.account_queue)}", "INFO")
                return None
        finally:
            # 确保锁被释放
            self.queue_lock.unlock()

    def start_data_collection(self, specific_rows=None):
        """启动数据采集线程

        Args:
            specific_rows: 指定要采集的行索引列表，如果为None则采集所有行
        """
        # 获取表格中的账号总数
        total_rows = self.table.rowCount()

        if total_rows == 0:
            self.error_message_signal.emit("错误", "没有账号可供采集")
            return

        # 确定要处理的行
        if specific_rows is not None:
            rows_to_process = specific_rows
        else:
            rows_to_process = list(range(total_rows))

        # 清空之前的线程记录和账号队列
        self.active_threads = []
        self.threads_finished_count = 0
        self.account_queue = []
        self.current_index = 0

        # 记录cookie目录信息
        self.log_message_signal.emit(f"使用Cookie目录: {self.cookie_dir}", "INFO")
        if not os.path.exists(self.cookie_dir):
            self.log_message_signal.emit(f"警告: Cookie目录不存在，尝试创建", "WARNING")
            try:
                os.makedirs(self.cookie_dir, exist_ok=True)
            except Exception as e:
                self.log_message_signal.emit(f"创建Cookie目录失败: {str(e)}", "ERROR")

        # 添加调试日志
        self.log_message_signal.emit(f"开始准备账号队列，总行数: {len(rows_to_process)}", "INFO")

        # 准备账号队列 - 按表格顺序
        for row in rows_to_process:
            # 添加调试日志
            self.log_message_signal.emit(f"处理第 {row+1} 行", "INFO")

            account_item = self.table.item(row, 1)  # 账号列

            # 添加调试日志
            if account_item is None:
                self.log_message_signal.emit(f"第 {row+1} 行的账号列为空", "WARNING")
                continue

            account_text = account_item.text().strip() if account_item else ""
            if not account_text:
                self.log_message_signal.emit(f"第 {row+1} 行的账号为空文本", "WARNING")
                continue

            account_name = account_text
            self.log_message_signal.emit(f"找到账号: {account_name}", "INFO")

            # 构建cookie文件路径
            possible_cookie_paths = [
                os.path.join(self.cookie_dir, f"{account_name}.txt"),
                os.path.join(self.cookie_dir, f"{account_name}.json")
            ]

            # 寻找第一个存在的Cookie文件
            cookie_path = None
            for path in possible_cookie_paths:
                if os.path.exists(path):
                    cookie_path = path
                    self.log_message_signal.emit(f"找到账号 {account_name} 的Cookie文件: {path}", "INFO")
                    break

            # 如果没有找到，使用默认的.txt后缀
            if not cookie_path:
                cookie_path = possible_cookie_paths[0]
                self.log_message_signal.emit(f"未找到账号 {account_name} 的Cookie文件，将使用默认路径: {cookie_path}", "WARNING")

            # 将行索引、账号名和cookie路径一起存储在队列中
            self.account_queue.append((row, account_name, cookie_path))
            self.log_message_signal.emit(f"已将账号 {account_name} 添加到队列，当前队列长度: {len(self.account_queue)}", "INFO")

        # 记录总账号数
        self.total_accounts = len(self.account_queue)
        if self.total_accounts == 0:
            self.error_message_signal.emit("错误", "没有有效的账号可供采集")
            return

        # 发送初始计数信号，确保UI正确显示
        self.status_update_signal.emit(f"更新已完成计数:0:{self.total_accounts}")
        self.status_update_signal.emit(f"更新总体进度:准备中:0:{self.total_accounts}:正在准备数据采集")

        self.log_message_signal.emit(f"准备采集 {self.total_accounts} 个账号的数据，按账号顺序依次处理", "INFO")

        # 确保线程数不超过实际需要的数量
        actual_thread_count = min(self.thread_count, self.total_accounts)

        # 添加调试日志
        self.log_message_signal.emit(f"将启动 {actual_thread_count} 个线程处理 {self.total_accounts} 个账号", "INFO")

        # 打印账号队列信息
        if self.total_accounts > 0:
            account_names = [account[1] for account in self.account_queue]
            if len(account_names) <= 10:
                self.log_message_signal.emit(f"账号队列: {', '.join(account_names)}", "INFO")
            else:
                self.log_message_signal.emit(f"账号队列(前10个): {', '.join(account_names[:10])}... 等共{len(account_names)}个", "INFO")

        # 创建并启动多个采集线程
        for i in range(actual_thread_count):
            thread_name = f"采集线程-{i+1}"
            self.log_message_signal.emit(f"启动 {thread_name}", "INFO")

            # 创建线程和工作器
            collector_thread = QThread()
            collector_thread.setObjectName(thread_name)  # 设置线程名称，便于调试
            worker = ToutiaoDataCollectionWorker(self.table, self.cookie_dir, self.data_dir, thread_name)

            # 设置数据收集器的引用，以便工作线程可以获取下一个账号
            worker.set_data_collector(self)

            # 添加调试日志
            self.log_message_signal.emit(f"已创建工作线程: {thread_name}", "INFO")

            # 连接信号
            worker.status_update_signal.connect(self.status_update_signal)
            worker.data_updated_signal.connect(self.data_updated_signal)
            worker.log_message_signal.connect(self.log_message_signal)
            worker.error_message_signal.connect(self.error_message_signal)
            worker.finished.connect(self._on_thread_finished)

            # 将工作器移动到线程中并启动
            worker.moveToThread(collector_thread)
            collector_thread.started.connect(worker.run)
            worker.finished.connect(collector_thread.quit)
            worker.finished.connect(worker.deleteLater)
            collector_thread.finished.connect(collector_thread.deleteLater)

            # 保存线程和工作器的引用
            self.active_threads.append({
                'thread': collector_thread,
                'worker': worker,
                'name': thread_name
            })

            # 启动线程
            collector_thread.start()

            # 添加短暂延迟，避免线程同时启动导致的问题
            time.sleep(0.1)

        if not self.active_threads:
            self.error_message_signal.emit("错误", "没有创建任何采集线程")

    def _on_thread_finished(self):
        """处理线程完成事件"""
        self.threads_finished_count += 1
        self.log_message_signal.emit(f"一个采集线程已完成，剩余 {len(self.active_threads) - self.threads_finished_count} 个线程", "INFO")

        # 检查是否所有线程都已完成
        if self.threads_finished_count >= len(self.active_threads):
            # 输出统计结果
            success_message = f"所有采集线程已完成。成功: {self.success_accounts_count} 账号，失败: {self.failed_accounts_count} 账号"
            if self.failed_accounts_count > 0:
                failed_list = ", ".join(self.failed_accounts)
                success_message += f"，失败账号: {failed_list}"
            self.log_message_signal.emit(success_message, "SUCCESS")

            # 确保更新UI
            self.status_update_signal.emit(f"更新已完成计数:{self.success_accounts_count + self.failed_accounts_count}:{self.total_accounts}")
            self.status_update_signal.emit(f"更新总体进度:完成:{self.success_accounts_count + self.failed_accounts_count}:{self.total_accounts}:采集完成")

            # 保存所有采集的数据到统一的JSON文件
            self.save_all_data_to_json()

            # 导出Excel文件
            self.export_data_to_excel()

            # 发送完成信号
            self.all_threads_finished_signal.emit()

    def export_data_to_excel(self):
        """导出数据到Excel"""
        try:
            # 导入Excel导出工具
            try:
                from app.utils.excel_exporter import export_data_to_excel
                excel_exporter_available = True
            except ImportError:
                self.log_message_signal.emit("Excel导出工具不可用，请确保已安装openpyxl库", "WARNING")
                excel_exporter_available = False
                return

            if not excel_exporter_available:
                return

            # 导出Excel文件
            self.log_message_signal.emit("正在导出数据到Excel文件...", "INFO")
            success, result = export_data_to_excel(self.data_dir)

            if success:
                self.log_message_signal.emit(f"数据已成功导出到Excel文件: {result}", "SUCCESS")
            else:
                self.log_message_signal.emit(f"导出数据到Excel失败: {result}", "ERROR")

        except Exception as e:
            self.log_message_signal.emit(f"导出数据到Excel时出错: {str(e)}", "ERROR")
            import traceback
            self.log_message_signal.emit(traceback.format_exc(), "ERROR")

    def save_all_data_to_json(self):
        """将所有采集的数据保存到一个统一的JSON文件中"""
        try:
            # 获取数据目录（使用统一的路径获取方法）
            data_dir = self.get_data_directory()
            os.makedirs(data_dir, exist_ok=True)

            # 统一的JSON文件路径
            json_file_path = os.path.join(data_dir, "accounts_data.json")

            # 收集所有账号数据
            all_accounts_data = {}

            # 如果文件已存在，先读取现有数据
            if os.path.exists(json_file_path):
                try:
                    with open(json_file_path, 'r', encoding='utf-8') as f:
                        all_accounts_data = json.load(f)
                    self.log_message_signal.emit(f"已读取现有账号数据，共 {len(all_accounts_data)} 个账号", "INFO")
                except Exception as read_err:
                    self.log_message_signal.emit(f"读取现有账号数据出错: {str(read_err)}，将创建新文件", "WARNING")
                    all_accounts_data = {}

            # 遍历表格中的所有行
            for row in range(self.table.rowCount()):
                # 获取账号ID
                account_id_item = self.table.item(row, 1)
                if not account_id_item:
                    continue

                account_id = account_id_item.text().strip()
                if not account_id:
                    continue

                # 创建账号数据字典
                account_data = {
                    "account_id": account_id,
                    "username": self.table.item(row, 0).text() if self.table.item(row, 0) else account_id,
                    "status": self.table.item(row, 2).text() if self.table.item(row, 2) else "正常"
                }

                # 添加其他数据
                field_to_column = {
                    "credit_score": 3,
                    "draft_count": 4,
                    "yesterday_fans": 5,
                    "total_fans": 6,
                    "total_play_count": 7,
                    "yesterday_play": 8,
                    "total_income": 9,
                    "yesterday_income": 10,
                    "seven_days_total": 11,
                    "withdrawable_amount": 12,
                    "register_days": 13,
                    "verification_status": 14,
                    "total_withdraw": 15,
                    "withdraw_date": 16,
                    "recent_withdraw": 17
                }

                for field, column in field_to_column.items():
                    item = self.table.item(row, column)
                    if item:
                        account_data[field] = item.text()

                # 添加到总数据字典
                all_accounts_data[account_id] = account_data

            # 保存到JSON文件
            with open(json_file_path, 'w', encoding='utf-8') as f:
                json.dump(all_accounts_data, f, ensure_ascii=False, indent=4)

            self.log_message_signal.emit(f"已将所有账号数据保存到统一JSON文件: {json_file_path}", "SUCCESS")

        except Exception as e:
            self.log_message_signal.emit(f"保存账号数据到统一JSON文件时出错: {str(e)}", "ERROR")

    def get_data_directory(self):
        """获取数据目录路径（使用统一的路径获取方法）"""
        try:
            # 尝试从主窗口获取数据目录
            from PyQt5.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                for widget in app.allWidgets():
                    if hasattr(widget, 'setting_tab') and hasattr(widget.setting_tab, 'data_path'):
                        data_path = widget.setting_tab.data_path.text()
                        if data_path and os.path.exists(data_path):
                            return data_path

            # 尝试从设置文件读取
            try:
                settings_path = os.path.join("data", "settings.json")
                if os.path.exists(settings_path):
                    with open(settings_path, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                        data_path = settings.get("data_path", "")
                        if data_path and os.path.exists(data_path):
                            return data_path
            except Exception:
                pass

            # 使用默认路径
            default_paths = [
                "D:\\头条全自动\\数据",
                "E:\\软件共享\\data",
                os.path.join(os.getcwd(), "data")
            ]

            for path in default_paths:
                try:
                    os.makedirs(path, exist_ok=True)
                    return path
                except Exception:
                    continue

            # 如果所有路径都失败，使用当前目录下的data文件夹
            fallback_path = os.path.join(os.getcwd(), "data")
            os.makedirs(fallback_path, exist_ok=True)
            return fallback_path

        except Exception as e:
            # 最后的备选方案
            fallback_path = os.path.join(os.getcwd(), "data")
            try:
                os.makedirs(fallback_path, exist_ok=True)
            except Exception:
                pass
            return fallback_path

    def save_account_data_to_json(self, account_id, row):
        """将单个账号的数据保存到统一的JSON配置文件中

        Args:
            account_id: 账号ID
            row: 表格行索引
        """
        try:
            # 获取数据目录（使用统一的路径获取方法）
            data_dir = self.get_data_directory()
            os.makedirs(data_dir, exist_ok=True)

            # 统一的JSON文件路径
            json_file_path = os.path.join(data_dir, "accounts_data.json")

            # 初始化账号数据字典
            all_accounts_data = {}

            # 如果文件已存在，先读取现有数据
            if os.path.exists(json_file_path):
                try:
                    with open(json_file_path, 'r', encoding='utf-8') as f:
                        all_accounts_data = json.load(f)
                except Exception as read_err:
                    self.log_message_signal.emit(f"读取现有账号数据出错: {str(read_err)}，将创建新文件", "WARNING")

            # 创建账号数据字典
            account_data = {
                "account_id": account_id,
                "username": self.table.item(row, 0).text() if self.table.item(row, 0) else account_id,
                "status": self.table.item(row, 2).text() if self.table.item(row, 2) else "正常"
            }

            # 添加其他数据
            field_to_column = {
                "credit_score": 3,
                "draft_count": 4,
                "yesterday_fans": 5,
                "total_fans": 6,
                "total_play_count": 7,
                "yesterday_play": 8,
                "total_income": 9,
                "yesterday_income": 10,
                "seven_days_total": 11,
                "withdrawable_amount": 12,
                "register_days": 13,
                "verification_status": 14,
                "total_withdraw": 15,
                "withdraw_date": 16,
                "recent_withdraw": 17
            }

            for field, column in field_to_column.items():
                item = self.table.item(row, column)
                if item:
                    account_data[field] = item.text()

            # 更新账号数据
            all_accounts_data[account_id] = account_data

            # 保存到JSON文件
            with open(json_file_path, 'w', encoding='utf-8') as f:
                json.dump(all_accounts_data, f, ensure_ascii=False, indent=4)

            self.log_message_signal.emit(f"已将账号 {account_id} 的数据保存到统一JSON文件", "INFO")
            return True

        except Exception as e:
            self.log_message_signal.emit(f"保存账号 {account_id} 数据到统一JSON文件时出错: {str(e)}", "ERROR")
            return False

    def stop_all_threads(self):
        """停止所有采集线程"""
        for thread_info in self.active_threads:
            try:
                if thread_info['worker']:
                    thread_info['worker'].stop()
                    self.log_message_signal.emit(f"已发送停止信号给 {thread_info['name']}", "INFO")
            except Exception as e:
                self.log_message_signal.emit(f"停止线程 {thread_info['name']} 时出错: {str(e)}", "ERROR")

        self.log_message_signal.emit("已向所有线程发送停止信号，请等待它们安全终止", "WARNING")

class ToutiaoDataCollectionWorker(QObject):
    """头条数据采集工作线程"""
    status_update_signal = pyqtSignal(str)
    data_updated_signal = pyqtSignal(str)
    error_message_signal = pyqtSignal(str, str)
    log_message_signal = pyqtSignal(str, str)
    finished = pyqtSignal()

    def __init__(self, table, cookie_dir, data_dir, thread_name="采集线程"):
        """初始化工作线程

        Args:
            table: 账号表格控件
            cookie_dir: Cookie文件目录
            data_dir: 数据存储目录
            thread_name: 线程名称，用于日志标识
        """
        super().__init__()
        self.table = table
        self.cookie_dir = cookie_dir
        self.data_dir = data_dir
        self.thread_name = thread_name
        self.stop_flag = False  # 添加停止标志
        self.data_collector = None  # 设置引用以获取下一个账号

    def set_data_collector(self, collector):
        """设置数据收集器的引用"""
        self.data_collector = collector

    def stop(self):
        """设置停止标志来中断任务"""
        self.stop_flag = True
        self.log_message_signal.emit(f"[{self.thread_name}] 收到停止信号，正在安全终止...", "WARNING")

    def _clear_browser_session(self, driver):
        """清理浏览器会话，重置缓存和cookie"""
        try:
            # 清理所有cookie
            driver.delete_all_cookies()

            # 安全地清理localStorage和sessionStorage
            driver.execute_script("""
                try {
                    if (window.localStorage) {
                        window.localStorage.clear();
                    }
                } catch (e) {
                    console.log('localStorage清理失败:', e.message);
                }

                try {
                    if (window.sessionStorage) {
                        window.sessionStorage.clear();
                    }
                } catch (e) {
                    console.log('sessionStorage清理失败:', e.message);
                }
            """)

            # 打开空白页重置状态
            driver.get("about:blank")
            time.sleep(1)

            return True
        except Exception as e:
            # 只记录到日志，不发送到UI（避免重复的错误信息）
            self.emit_log(f"清理浏览器会话出错: {str(e)}", "WARNING")
            return False

    def run(self):
        """线程执行入口，开始采集过程"""
        self.emit_log(f"[{self.thread_name}] 启动数据采集流程...", "INFO")
        driver = None
        accounts_processed = 0

        # 创建计时器，用于控制UI更新频率
        last_ui_update = time.time()
        update_interval = 0.5  # 最小UI更新间隔（秒）

        try:
            # 创建浏览器实例
            driver = self.create_browser_instance()

            if driver is None:
                self.emit_log("无法创建浏览器实例，采集中止", "ERROR")
                self.finished.emit()
                return

            # 从队列获取账号并处理，直到队列为空或收到停止信号
            while not self.stop_flag:
                # 检查任务管理器
                if self.data_collector is None:
                    self.emit_log("错误：数据采集器未设置", "ERROR")
                    break

                # 从队列获取下一个账号
                account = self.data_collector.get_next_account()
                if account is None:
                    self.emit_log(f"[{self.thread_name}] 没有更多账号需要处理", "INFO")
                    break

                # 添加线程标识到日志
                self.emit_log(f"[{self.thread_name}] 获取到账号: {account[1]}", "INFO")

                # 解析账号信息
                row, account_name, cookie_path = account

                # 计算总进度百分比
                accounts_processed += 1
                total_accounts = getattr(self.data_collector, 'total_accounts', 0)
                if total_accounts <= 0:
                    total_accounts = 1  # 防止除以零错误

                progress_percent = min(100, (accounts_processed / total_accounts) * 100)

                # 控制UI更新频率
                current_time = time.time()
                if current_time - last_ui_update >= update_interval:
                    self.emit_log(f"正在处理账号 {accounts_processed}/{total_accounts} ({progress_percent:.1f}%): {account_name}", "INFO")
                    last_ui_update = current_time

                    # 发送进度更新信号 - 确保使用线程名+账号名的唯一标识
                    self.status_update_signal.emit(f"更新浏览器进度:{self.thread_name}:{account_name}:{progress_percent:.1f}")

                    # 更新总体进度
                    self.status_update_signal.emit(f"更新总体进度:{account_name}:{accounts_processed}:{total_accounts}:正在查询数据")

                # 确保清除旧cookies和会话
                try:
                    self._clear_browser_session(driver)
                    self.emit_log(f"已清理浏览器会话，准备处理账号 {account_name}", "INFO")
                except Exception as e:
                    self.emit_log(f"清理浏览器会话出错: {str(e)}", "WARNING")

                # 加载Cookie
                login_result = self.load_cookies_for_account(driver, account_name, cookie_path, row)

                # 初始化collection_result变量
                collection_result = False

                if login_result:
                    # 登录成功，开始采集数据
                    self.emit_log(f"账号 {account_name} 登录成功，开始采集数据", "SUCCESS")

                    # 设置表格状态为"采集中"
                    status_item = QTableWidgetItem("采集中")
                    status_item.setForeground(QBrush(QColor(0, 0, 255)))  # 蓝色
                    self.table.setItem(row, 2, status_item)

                    # 采集数据
                    collection_result = self.collect_data_from_profile(driver, account_name, row)

                    if collection_result:
                        # 采集成功
                        self.emit_log(f"账号 {account_name} 数据采集成功", "SUCCESS")
                        status_item = QTableWidgetItem("采集成功")
                        status_item.setForeground(QBrush(QColor(0, 150, 0)))  # 绿色
                        self.table.setItem(row, 2, status_item)

                        # 更新成功计数
                        if hasattr(self.data_collector, 'success_accounts_count'):
                            self.data_collector.success_accounts_count += 1

                        # 实时保存账号数据到JSON文件
                        self.save_account_data(row, account_name)

                        # 更新进度状态
                        self.status_update_signal.emit(f"更新浏览器进度:{self.thread_name}:{account_name}:100")
                        self.status_update_signal.emit(f"更新总体进度:{account_name}:{accounts_processed}:{total_accounts}:数据采集成功")
                    else:
                        # 采集失败
                        self.emit_log(f"账号 {account_name} 数据采集失败", "ERROR")
                        status_item = QTableWidgetItem("采集失败")
                        status_item.setForeground(QBrush(QColor(255, 0, 0)))  # 红色
                        self.table.setItem(row, 2, status_item)

                        # 更新失败账号统计
                        if hasattr(self.data_collector, 'failed_accounts'):
                            self.data_collector.failed_accounts.add(account_name)
                            self.data_collector.failed_accounts_count += 1

                        # 更新进度状态（即使失败）
                        self.status_update_signal.emit(f"更新浏览器进度:{self.thread_name}:{account_name}:100")
                        self.status_update_signal.emit(f"更新总体进度:{account_name}:{accounts_processed}:{total_accounts}:数据采集失败")

                        # 实时保存账号数据到JSON文件
                        self.save_account_data(row, account_name)
                else:
                    # 登录失败
                    self.emit_log(f"账号 {account_name} 登录失败", "ERROR")
                    status_item = QTableWidgetItem("登录失败")
                    status_item.setForeground(QBrush(QColor(255, 0, 0)))  # 红色
                    self.table.setItem(row, 2, status_item)

                    # 更新失败账号统计
                    if hasattr(self.data_collector, 'failed_accounts'):
                        self.data_collector.failed_accounts.add(account_name)
                        self.data_collector.failed_accounts_count += 1

                    # 更新进度状态（即使失败）
                    self.status_update_signal.emit(f"更新浏览器进度:{self.thread_name}:{account_name}:100")
                    self.status_update_signal.emit(f"更新总体进度:{account_name}:{accounts_processed}:{total_accounts}:登录失败")

                    # 实时保存账号数据到JSON文件
                    self.save_account_data(row, account_name)

                # 发送已完成计数更新信号 - 确保仅在账号完全处理完毕后发送
                # 状态文本根据采集结果决定
                status_suffix = "数据采集成功"
                if not collection_result and not login_result:
                    status_suffix = "登录失败"
                elif not collection_result:
                    status_suffix = "数据采集失败"

                # 发送完成状态的更新信号
                self.status_update_signal.emit(f"更新已完成计数:{accounts_processed}:{total_accounts}")
                self.status_update_signal.emit(f"更新总体进度:{account_name}:{accounts_processed}:{total_accounts}:{status_suffix}")

                # 账号间短暂间隔，避免请求过快
                time.sleep(0.5)

                # 处理UI事件
                QApplication.processEvents()

            # 处理退出循环
            if self.stop_flag:
                self.emit_log("已收到停止信号，中止数据采集", "INFO")
            else:
                self.emit_log("已完成所有账号的数据采集", "SUCCESS")

        except Exception as e:
            self.emit_log(f"数据采集过程中出错: {str(e)}", "ERROR")
            self.error_message_signal.emit("错误", f"数据采集过程中出错: {str(e)}")

        finally:
            # 关闭浏览器
            if driver is not None:
                try:
                    driver.quit()
                    self.emit_log("已关闭浏览器", "INFO")
                except Exception as e:
                    self.emit_log(f"关闭浏览器时出错: {str(e)}", "ERROR")

            # 发送线程完成信号
            self.emit_log("采集线程已完成", "INFO")
            self.finished.emit()

    def save_account_data(self, row, account_name):
        """保存单个账号的数据到统一的JSON文件

        Args:
            row: 表格行索引
            account_name: 账号名称
        """
        try:
            # 获取账号ID
            account_id_item = self.table.item(row, 1)
            if not account_id_item:
                self.emit_log(f"无法获取账号 {account_name} 的ID，跳过保存", "WARNING")
                return False

            account_id = account_id_item.text().strip()
            if not account_id:
                self.emit_log(f"账号 {account_name} 的ID为空，跳过保存", "WARNING")
                return False

            # 调用数据收集器的保存方法
            if hasattr(self.data_collector, 'save_account_data_to_json'):
                result = self.data_collector.save_account_data_to_json(account_id, row)
                if result:
                    self.emit_log(f"已实时保存账号 {account_name} 的数据", "SUCCESS")
                return result
            else:
                self.emit_log(f"数据收集器没有保存方法，无法保存账号 {account_name} 的数据", "WARNING")
                return False

        except Exception as e:
            self.emit_log(f"保存账号 {account_name} 数据时出错: {str(e)}", "ERROR")
            return False

    def emit_log(self, message, level="INFO"):
        """发送日志和状态更新信号

        Args:
            message: 日志消息
            level: 日志级别，可选值: INFO, WARNING, ERROR, SUCCESS
        """
        # 根据级别添加前缀图标
        prefix = {
            "INFO": "ℹ️ ",
            "WARNING": "⚠️ ",
            "ERROR": "❌ ",
            "SUCCESS": "✅ "
        }.get(level, "")

        # 打印到控制台
        print(f"[{level}] {message}")

        # 发送信号
        full_message = f"{prefix}{message}"
        if hasattr(self, 'status_update_signal') and self.status_update_signal:
            self.status_update_signal.emit(full_message)

        # 如果有状态信号函数，发送对应级别的消息
        if level == "ERROR" and hasattr(self, 'error_message_signal') and self.error_message_signal:
            self.error_message_signal.emit("错误", message)
        elif hasattr(self, 'log_message_signal') and self.log_message_signal:
            self.log_message_signal.emit(message, level)

    def create_browser_instance(self):
        """创建一个优化的浏览器实例，始终使用无头模式"""
        options = webdriver.ChromeOptions()

        # 设置默认用户代理
        default_user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        options.add_argument(f"user-agent={default_user_agent}")

        # 添加其他选项
        options.add_argument("--window-size=1280,800")  # 正常窗口大小
        options.add_argument("--disable-notifications")
        options.add_argument("--disable-infobars")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")

        # 始终启用无头模式
        options.add_argument("--headless=new")  # 使用新的无头模式

        # 添加必要的无头模式参数
        options.add_argument("--disable-gpu")  # 在Windows上需要
        options.add_argument("--disable-dev-shm-usage")  # 解决内存不足问题
        options.add_argument("--disable-software-rasterizer")  # 避免GPU问题

        # 绕过检测
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument("--disable-blink-features=AutomationControlled")

        try:
            # 使用ChromeDriver管理器创建浏览器
            from app.utils.chromedriver_manager import create_chrome_driver
            driver = create_chrome_driver(options)
            self.emit_log("成功创建Chrome浏览器", "SUCCESS")

            # 设置全局超时时间
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(5)

            return driver
        except Exception as e:
            self.emit_log(f"创建浏览器实例失败: {str(e)}", "ERROR")
            return None

    def load_cookies_for_account(self, driver, account_name, cookie_path, row=None):
        """从文件加载cookie并登录（使用统一登录管理器）"""
        try:
            self.emit_log(f"🔑 使用统一登录管理器登录账号 {account_name}", "INFO")

            # 使用统一登录管理器
            from app.utils.unified_login_manager import get_login_manager

            login_manager = get_login_manager()
            success, message = login_manager.login_account(driver, cookie_path, account_name)

            if success:
                self.emit_log(f"✅ 账号 {account_name} 登录成功: {message}", "SUCCESS")

                # 检测实名认证状态
                try:
                    from app.utils.realname_detector import detect_realname_status
                    is_verified, detection_message = detect_realname_status(driver, account_name)

                    if not is_verified:
                        # 未实名认证，停止采集
                        self.emit_log(f"⚠️ 账号 {account_name} 未实名认证，停止数据采集", "WARNING")

                        # 更新账号表格状态
                        if row is not None:
                            try:
                                # 更新实名状态列（第14列）
                                realname_item = QTableWidgetItem("未实名")
                                realname_item.setTextAlignment(Qt.AlignCenter)
                                realname_item.setForeground(QBrush(QColor(200, 0, 0)))  # 红色
                                self.table.setItem(row, 14, realname_item)

                                # 更新状态列（第2列）
                                status_item = QTableWidgetItem("未实名")
                                status_item.setForeground(QBrush(QColor(200, 0, 0)))  # 红色
                                self.table.setItem(row, 2, status_item)

                                QApplication.processEvents()  # 确保UI更新
                            except Exception as e:
                                self.emit_log(f"更新表格状态失败: {str(e)}", "ERROR")

                        return False  # 返回False表示登录失败（因为未实名）
                    else:
                        # 已实名认证，更新状态并继续
                        if row is not None:
                            try:
                                # 更新实名状态列（第14列）
                                realname_item = QTableWidgetItem("已实名")
                                realname_item.setTextAlignment(Qt.AlignCenter)
                                realname_item.setForeground(QBrush(QColor(0, 150, 0)))  # 绿色
                                self.table.setItem(row, 14, realname_item)

                                QApplication.processEvents()  # 确保UI更新
                            except Exception as e:
                                self.emit_log(f"更新实名状态失败: {str(e)}", "ERROR")

                        self.emit_log(f"✅ 账号 {account_name} 已实名认证，继续数据采集", "SUCCESS")

                except Exception as e:
                    self.emit_log(f"检测账号 {account_name} 实名状态时出错: {str(e)}", "WARNING")
                    # 检测出错时继续执行，不影响正常流程

                return True
            else:
                self.emit_log(f"❌ 账号 {account_name} 登录失败: {message}", "ERROR")
                return False

        except Exception as e:
            self.emit_log(f"登录过程出错: {str(e)}", "ERROR")
            import traceback
            self.emit_log(traceback.format_exc(), "ERROR")
            return False

    def collect_data_from_profile(self, driver, account_name, row):
        """从个人主页采集数据

        Args:
            driver: WebDriver实例
            account_name: 账号名称
            row: 表格行索引

        Returns:
            bool: 采集是否成功
        """
        try:
            # 初始化账号数据，确保包含所有必要字段
            account_data = {
                "username": account_name,   # 默认使用账号名作为用户名
                "total_fans": "0",          # 总粉丝数
                "total_play_count": "0",    # 总播放量
                "total_income": "0",        # 总收益
                "status": "正在采集"         # 状态
            }

            # 访问数据概览页面
            self.status_update_signal.emit("🔄 正在访问数据概览页面...")
            try:
                driver.get("https://mp.toutiao.com/profile_v4/index")
                time.sleep(3)  # 增加等待时间
            except Exception as e:
                self.status_update_signal.emit(f"⚠️ 访问数据概览页面出错: {str(e)}")

            # 首先检查账号状态 - 是否未实名认证
            try:
                # 尝试查找未实名认证的提示元素
                unverified_xpath = "/html/body/div[1]/div/div[2]/button"
                unverified_elems = driver.find_elements(By.XPATH, unverified_xpath)

                # 默认设置为已实名
                is_verified = True
                verification_status = "已实名"

                if unverified_elems and len(unverified_elems) > 0:
                    # 检查元素内容是否包含实名认证相关文字
                    for elem in unverified_elems:
                        if elem.text and ("认证" in elem.text or "实名" in elem.text or "验证" in elem.text):
                            self.status_update_signal.emit("⚠️ 检测到未实名认证账号，跳过采集")
                            self.emit_log(f"账号未实名认证: {account_name}", "WARNING")

                            # 更新账号状态为未实名
                            account_data["status"] = "未实名"
                            is_verified = False
                            verification_status = "未实名"

                            # 更新表格中的状态列(第3列)
                            status_item = QTableWidgetItem("未实名")
                            status_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                            status_item.setForeground(QBrush(QColor(200, 0, 0)))  # 红色表示未实名
                            self.table.setItem(row, 2, status_item)
                            QApplication.processEvents()  # 确保UI更新

                            # 实时保存账号数据到JSON文件
                            self.save_account_data(row, account_name)

                            # 跳过后续采集
                            return True

                # 更新实名状态到第15列
                self.emit_log(f"账号 {account_name} 实名状态: {verification_status}", "INFO")
                verification_item = QTableWidgetItem(verification_status)
                verification_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

                # 设置颜色：已实名为绿色，未实名为红色
                if is_verified:
                    verification_item.setForeground(QBrush(QColor(0, 150, 0)))  # 绿色表示已实名
                else:
                    verification_item.setForeground(QBrush(QColor(200, 0, 0)))  # 红色表示未实名

                # 更新到第15列（索引为14）
                self.table.setItem(row, 14, verification_item)
                QApplication.processEvents()  # 确保UI更新

                # 保存到账号数据中
                account_data["verification_status"] = verification_status

            except Exception as e:
                self.emit_log(f"检查账号实名状态时出错: {str(e)}", "WARNING")
                # 出错时继续执行，不影响正常采集

                # 即使出错，也默认设置为已实名
                verification_item = QTableWidgetItem("已实名")
                verification_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                verification_item.setForeground(QBrush(QColor(0, 150, 0)))  # 绿色表示已实名
                self.table.setItem(row, 14, verification_item)
                QApplication.processEvents()  # 确保UI更新

                # 保存到账号数据中
                account_data["verification_status"] = "已实名"

            # 采集用户名/昵称 - 使用用户提供的XPath
            try:
                nickname_xpath = "/html/body/div[1]/div/div[1]/div/div/div[3]/div[1]/a/span/div/div/div[1]"
                nickname_elem = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, nickname_xpath))
                )
                if nickname_elem and nickname_elem.text.strip():
                    account_data["username"] = nickname_elem.text.strip()
                    self.status_update_signal.emit(f"✅ 已获取昵称: {account_data['username']}")
            except Exception as e:
                self.emit_log(f"使用XPath获取昵称失败: {str(e)}", "WARNING")
                # 尝试备用方法获取昵称
                try:
                    # 使用JavaScript查找昵称元素
                    js_script = """
                    var elements = document.querySelectorAll('div, span');
                    for(var i=0; i<elements.length; i++) {
                        if(elements[i].textContent && elements[i].textContent.length > 0 &&
                           elements[i].textContent.length < 20 &&
                           elements[i].parentElement &&
                           elements[i].parentElement.tagName.toLowerCase() === 'a') {
                            return elements[i].textContent;
                        }
                    }
                    return '';
                    """
                    nickname = driver.execute_script(js_script)
                    if nickname:
                        account_data["username"] = nickname.strip()
                        self.status_update_signal.emit(f"✅ 已通过JavaScript获取昵称: {account_data['username']}")
                except Exception as js_err:
                    self.emit_log(f"JavaScript获取昵称失败: {str(js_err)}", "WARNING")

            # 采集总粉丝数 - 使用用户提供的XPath
            try:
                fans_xpath = "/html/body/div[1]/div/div[3]/section/main/div/div/div/div[2]/div/div/div/div[1]/div[1]/div[1]/div[1]/div/div[2]/a"
                fans_elem = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, fans_xpath))
                )
                if fans_elem and fans_elem.text.strip():
                    account_data["total_fans"] = fans_elem.text.strip()
                    self.status_update_signal.emit(f"✅ 已获取总粉丝数: {account_data['total_fans']}")
            except Exception as e:
                self.emit_log(f"使用XPath获取总粉丝数失败: {str(e)}", "WARNING")
                # 尝试备用方法获取总粉丝数
                try:
                    # 使用文本内容查找
                    fans_elements = driver.find_elements(By.XPATH, "//div[contains(text(), '粉丝')]/../div[2]/a")
                    if fans_elements and len(fans_elements) > 0:
                        for elem in fans_elements:
                            if elem.text.strip():
                                account_data["total_fans"] = elem.text.strip()
                                self.status_update_signal.emit(f"✅ 已通过备用XPath获取总粉丝数: {account_data['total_fans']}")
                                break
                except Exception as backup_err:
                    self.emit_log(f"备用方法获取总粉丝数失败: {str(backup_err)}", "WARNING")

            # 采集昨日粉丝数 - 使用用户提供的XPath
            try:
                yesterday_fans_xpath = "/html/body/div[1]/div/div[3]/section/main/div/div/div/div[2]/div/div/div/div[1]/div[1]/div[1]/div[1]/div/div[3]/p"
                yesterday_fans_elem = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, yesterday_fans_xpath))
                )
                if yesterday_fans_elem and yesterday_fans_elem.text.strip():
                    # 提取文本，可能包含"昨日 +X"或"昨日 -X"格式
                    text = yesterday_fans_elem.text.strip()
                    # 提取数字部分（包括正负号）
                    import re
                    match = re.search(r'([+-]?\d+)', text)
                    if match:
                        yesterday_fans = match.group(1)  # 获取匹配的数字部分（包括正负号）
                        account_data["yesterday_fans"] = yesterday_fans
                        self.status_update_signal.emit(f"✅ 已获取昨日粉丝数: {account_data['yesterday_fans']}")
                    else:
                        self.emit_log(f"无法从文本中提取昨日粉丝数: {text}", "WARNING")
            except Exception as e:
                self.emit_log(f"使用XPath获取昨日粉丝数失败: {str(e)}", "WARNING")
                # 尝试备用方法获取昨日粉丝数
                try:
                    # 使用JavaScript查找包含"昨日"的元素
                    js_script = """
                    var elements = document.querySelectorAll('p');
                    for(var i=0; i<elements.length; i++) {
                        if(elements[i].textContent && elements[i].textContent.includes('昨日')) {
                            return elements[i].textContent;
                        }
                    }
                    return '';
                    """
                    text = driver.execute_script(js_script)
                    if text:
                        # 提取数字部分（包括正负号）
                        import re
                        match = re.search(r'([+-]?\d+)', text)
                        if match:
                            yesterday_fans = match.group(1)  # 获取匹配的数字部分（包括正负号）
                            account_data["yesterday_fans"] = yesterday_fans
                            self.status_update_signal.emit(f"✅ 已通过JavaScript获取昨日粉丝数: {account_data['yesterday_fans']}")
                except Exception as backup_err:
                    self.emit_log(f"备用方法获取昨日粉丝数失败: {str(backup_err)}", "WARNING")

            # 采集累计播放量 - 使用用户提供的XPath
            try:
                play_xpath = "/html/body/div[1]/div/div[3]/section/main/div/div/div/div[2]/div/div/div/div[1]/div[1]/div[1]/div[2]/div/div[2]/a"
                play_elem = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, play_xpath))
                )
                if play_elem and play_elem.text.strip():
                    account_data["total_play_count"] = play_elem.text.strip()
                    self.status_update_signal.emit(f"✅ 已获取累计播放量: {account_data['total_play_count']}")
            except Exception as e:
                self.emit_log(f"使用XPath获取累计播放量失败: {str(e)}", "WARNING")
                # 尝试备用方法获取累计播放量
                try:
                    # 使用文本内容查找
                    play_elements = driver.find_elements(By.XPATH, "//div[contains(text(), '播放')]/../div[2]/a")
                    if play_elements and len(play_elements) > 0:
                        for elem in play_elements:
                            if elem.text.strip():
                                account_data["total_play_count"] = elem.text.strip()
                                self.status_update_signal.emit(f"✅ 已通过备用XPath获取累计播放量: {account_data['total_play_count']}")
                                break
                except Exception as backup_err:
                    self.emit_log(f"备用方法获取累计播放量失败: {str(backup_err)}", "WARNING")

            # 采集昨日播放量 - 使用用户提供的XPath
            try:
                yesterday_play_xpath = "/html/body/div[1]/div/div[3]/section/main/div/div/div/div[2]/div/div/div/div[1]/div[1]/div[1]/div[2]/div/div[3]/p"
                yesterday_play_elem = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, yesterday_play_xpath))
                )
                if yesterday_play_elem and yesterday_play_elem.text.strip():
                    # 提取文本，可能包含"昨日 +X"或"昨日 -X"格式
                    text = yesterday_play_elem.text.strip()
                    # 提取数字部分（包括正负号）
                    import re
                    match = re.search(r'([+-]?\d+)', text)
                    if match:
                        yesterday_play = match.group(1)  # 获取匹配的数字部分（包括正负号）
                        account_data["yesterday_play"] = yesterday_play
                        self.status_update_signal.emit(f"✅ 已获取昨日播放量: {account_data['yesterday_play']}")
                    else:
                        self.emit_log(f"无法从文本中提取昨日播放量: {text}", "WARNING")
            except Exception as e:
                self.emit_log(f"使用XPath获取昨日播放量失败: {str(e)}", "WARNING")
                # 尝试备用方法获取昨日播放量
                try:
                    # 使用JavaScript查找包含"昨日"的元素，在播放量区域
                    js_script = """
                    var playElements = document.querySelectorAll('div');
                    for(var i=0; i<playElements.length; i++) {
                        if(playElements[i].textContent && playElements[i].textContent.includes('播放')) {
                            // 找到播放元素后，查找其附近的昨日元素
                            var parent = playElements[i].parentElement;
                            if(parent) {
                                var yesterdayElems = parent.querySelectorAll('p');
                                for(var j=0; j<yesterdayElems.length; j++) {
                                    if(yesterdayElems[j].textContent && yesterdayElems[j].textContent.includes('昨日')) {
                                        return yesterdayElems[j].textContent;
                                    }
                                }
                            }
                        }
                    }
                    return '';
                    """
                    text = driver.execute_script(js_script)
                    if text:
                        # 提取数字部分（包括正负号）
                        import re
                        match = re.search(r'([+-]?\d+)', text)
                        if match:
                            yesterday_play = match.group(1)  # 获取匹配的数字部分（包括正负号）
                            account_data["yesterday_play"] = yesterday_play
                            self.status_update_signal.emit(f"✅ 已通过JavaScript获取昨日播放量: {account_data['yesterday_play']}")
                except Exception as backup_err:
                    self.emit_log(f"备用方法获取昨日播放量失败: {str(backup_err)}", "WARNING")

            # 采集累计收益 - 使用用户提供的XPath
            try:
                income_xpath = "/html/body/div[1]/div/div[3]/section/main/div/div/div/div[2]/div/div/div/div[1]/div[1]/div[1]/div[3]/div/div[2]/a"
                income_elem = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, income_xpath))
                )
                if income_elem and income_elem.text.strip():
                    account_data["total_income"] = income_elem.text.strip()
                    self.status_update_signal.emit(f"✅ 已获取累计收益: {account_data['total_income']}")
            except Exception as e:
                self.emit_log(f"使用XPath获取累计收益失败: {str(e)}", "WARNING")
                # 尝试备用方法获取累计收益
                try:
                    # 使用文本内容查找
                    income_elements = driver.find_elements(By.XPATH, "//div[contains(text(), '收益')]/../div[2]/a")
                    if income_elements and len(income_elements) > 0:
                        for elem in income_elements:
                            if elem.text.strip():
                                account_data["total_income"] = elem.text.strip()
                                self.status_update_signal.emit(f"✅ 已通过备用XPath获取累计收益: {account_data['total_income']}")
                                break
                except Exception as backup_err:
                    self.emit_log(f"备用方法获取累计收益失败: {str(backup_err)}", "WARNING")

            # 采集昨日收益 - 使用用户提供的XPath
            try:
                yesterday_income_xpath = "/html/body/div[1]/div/div[3]/section/main/div/div/div/div[2]/div/div/div/div[1]/div[1]/div[1]/div[3]/div/div[3]/p"
                yesterday_income_elem = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, yesterday_income_xpath))
                )
                if yesterday_income_elem and yesterday_income_elem.text.strip():
                    # 提取文本，可能包含"昨日 +X"或"昨日 -X"格式
                    text = yesterday_income_elem.text.strip()
                    # 提取数字部分（包括正负号）
                    import re
                    match = re.search(r'([+-]?\d+(?:\.\d+)?)', text)  # 支持小数点
                    if match:
                        yesterday_income = match.group(1)  # 获取匹配的数字部分（包括正负号）
                        account_data["yesterday_income"] = yesterday_income
                        self.status_update_signal.emit(f"✅ 已获取昨日收益: {account_data['yesterday_income']}")
                    else:
                        self.emit_log(f"无法从文本中提取昨日收益: {text}", "WARNING")
            except Exception as e:
                self.emit_log(f"使用XPath获取昨日收益失败: {str(e)}", "WARNING")
                # 尝试备用方法获取昨日收益
                try:
                    # 使用JavaScript查找包含"昨日"的元素，在收益区域
                    js_script = """
                    var incomeElements = document.querySelectorAll('div');
                    for(var i=0; i<incomeElements.length; i++) {
                        if(incomeElements[i].textContent && incomeElements[i].textContent.includes('收益')) {
                            // 找到收益元素后，查找其附近的昨日元素
                            var parent = incomeElements[i].parentElement;
                            if(parent) {
                                var yesterdayElems = parent.querySelectorAll('p');
                                for(var j=0; j<yesterdayElems.length; j++) {
                                    if(yesterdayElems[j].textContent && yesterdayElems[j].textContent.includes('昨日')) {
                                        return yesterdayElems[j].textContent;
                                    }
                                }
                            }
                        }
                    }
                    return '';
                    """
                    text = driver.execute_script(js_script)
                    if text:
                        # 提取数字部分（包括正负号）
                        import re
                        match = re.search(r'([+-]?\d+(?:\.\d+)?)', text)  # 支持小数点
                        if match:
                            yesterday_income = match.group(1)  # 获取匹配的数字部分（包括正负号）
                            account_data["yesterday_income"] = yesterday_income
                            self.status_update_signal.emit(f"✅ 已通过JavaScript获取昨日收益: {account_data['yesterday_income']}")
                except Exception as backup_err:
                    self.emit_log(f"备用方法获取昨日收益失败: {str(backup_err)}", "WARNING")

            # 采集账号注册天数 - 使用用户提供的XPath
            try:
                register_days_xpath = "/html/body/div[1]/div/div[1]/div/div/div[2]"
                register_days_elem = WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.XPATH, register_days_xpath))
                )
                if register_days_elem and register_days_elem.text.strip():
                    # 提取文本中的数字和"天"字
                    import re
                    text = register_days_elem.text.strip()
                    # 使用正则表达式提取数字和"天"字
                    match = re.search(r'(\d+)\s*天', text)
                    if match:
                        register_days = match.group(0)  # 获取匹配的完整字符串（数字+天）
                        account_data["register_days"] = register_days
                        self.status_update_signal.emit(f"✅ 已获取账号注册天数: {account_data['register_days']}")
                    else:
                        self.emit_log(f"无法从文本中提取注册天数: {text}", "WARNING")
            except Exception as e:
                self.emit_log(f"使用XPath获取账号注册天数失败: {str(e)}", "WARNING")
                # 尝试备用方法获取账号注册天数
                try:
                    # 使用JavaScript查找包含"天"的元素
                    js_script = """
                    var elements = document.querySelectorAll('div');
                    for(var i=0; i<elements.length; i++) {
                        if(elements[i].textContent && elements[i].textContent.includes('天') &&
                           /\\d+/.test(elements[i].textContent)) {
                            return elements[i].textContent;
                        }
                    }
                    return '';
                    """
                    text = driver.execute_script(js_script)
                    if text:
                        # 使用正则表达式提取数字和"天"字
                        import re
                        match = re.search(r'(\d+)\s*天', text)
                        if match:
                            register_days = match.group(0)  # 获取匹配的完整字符串（数字+天）
                            account_data["register_days"] = register_days
                            self.status_update_signal.emit(f"✅ 已通过JavaScript获取账号注册天数: {account_data['register_days']}")
                except Exception as backup_err:
                    self.emit_log(f"备用方法获取账号注册天数失败: {str(backup_err)}", "WARNING")

            # 跳转到创作者计划页面采集信用分
            self.status_update_signal.emit("🔄 正在跳转到创作者计划页面采集信用分...")
            try:
                # 访问创作者计划页面
                driver.get("https://mp.toutiao.com/profile_v4/personal/creator-plan/index")

                # 等待页面加载
                time.sleep(3)  # 增加等待时间确保页面完全加载

                # 采集信用分 - 使用用户提供的XPath
                credit_score_xpath = "/html/body/div[1]/div/div[3]/section/main/div/div/div/div[2]/div/div/div/div[1]/div[2]/div[2]/div/div[2]/span"
                credit_score_elem = WebDriverWait(driver, 8).until(
                    EC.presence_of_element_located((By.XPATH, credit_score_xpath))
                )

                if credit_score_elem and credit_score_elem.text.strip():
                    # 提取信用分数值
                    credit_score = credit_score_elem.text.strip()
                    # 确保只保留数字部分
                    import re
                    match = re.search(r'(\d+(?:\.\d+)?)', credit_score)
                    if match:
                        credit_score = match.group(1)  # 获取匹配的数字部分

                    account_data["credit_score"] = credit_score
                    self.status_update_signal.emit(f"✅ 已获取信用分: {account_data['credit_score']}")
                else:
                    self.emit_log("信用分元素为空或不存在", "WARNING")
            except Exception as e:
                self.emit_log(f"采集信用分失败: {str(e)}", "WARNING")
                # 尝试备用方法获取信用分
                try:
                    # 使用JavaScript查找包含信用分的元素
                    js_script = """
                    var elements = document.querySelectorAll('span, div');
                    for(var i=0; i<elements.length; i++) {
                        if(elements[i].textContent && /^\\d+(\\.\\d+)?$/.test(elements[i].textContent.trim())) {
                            var parent = elements[i].parentElement;
                            if(parent && (parent.textContent.includes('信用分') ||
                               (parent.parentElement && parent.parentElement.textContent.includes('信用分')))) {
                                return elements[i].textContent.trim();
                            }
                        }
                    }
                    return '';
                    """
                    credit_score = driver.execute_script(js_script)
                    if credit_score:
                        account_data["credit_score"] = credit_score
                        self.status_update_signal.emit(f"✅ 已通过JavaScript获取信用分: {account_data['credit_score']}")
                except Exception as backup_err:
                    self.emit_log(f"备用方法获取信用分失败: {str(backup_err)}", "WARNING")

            # 跳转到草稿界面采集草稿数量
            self.status_update_signal.emit("🔄 正在跳转到草稿界面采集草稿数量...")
            try:
                # 访问草稿界面
                driver.get("https://mp.toutiao.com/profile_v4/manage/draft")

                # 等待页面加载
                time.sleep(3)  # 增加等待时间确保页面完全加载

                # 采集草稿数量 - 使用用户提供的XPath
                draft_count_xpath = "/html/body/div[1]/div/div[3]/section/main/div[2]/div/div[1]/div/div/div[10]/span/span"
                draft_count_elem = WebDriverWait(driver, 8).until(
                    EC.presence_of_element_located((By.XPATH, draft_count_xpath))
                )

                if draft_count_elem and draft_count_elem.text.strip():
                    # 提取草稿数量
                    draft_count = draft_count_elem.text.strip()
                    # 确保只保留数字部分
                    import re
                    match = re.search(r'(\d+)', draft_count)
                    if match:
                        draft_count = match.group(1)  # 获取匹配的数字部分

                    account_data["draft_count"] = draft_count
                    self.status_update_signal.emit(f"✅ 已获取草稿数量: {account_data['draft_count']}")
                else:
                    self.emit_log("草稿数量元素为空或不存在", "WARNING")
                    # 尝试获取页面上的其他数字信息
                    try:
                        # 查找页面上可能显示的草稿总数
                        total_count_elems = driver.find_elements(By.XPATH, "//span[contains(text(), '共') and contains(text(), '条')]")
                        if total_count_elems and len(total_count_elems) > 0:
                            for elem in total_count_elems:
                                text = elem.text.strip()
                                match = re.search(r'共\s*(\d+)\s*条', text)
                                if match:
                                    draft_count = match.group(1)
                                    account_data["draft_count"] = draft_count
                                    self.status_update_signal.emit(f"✅ 已通过总数文本获取草稿数量: {account_data['draft_count']}")
                                    break
                    except Exception as e:
                        self.emit_log(f"尝试获取草稿总数文本失败: {str(e)}", "WARNING")
            except Exception as e:
                self.emit_log(f"采集草稿数量失败: {str(e)}", "WARNING")
                # 尝试备用方法获取草稿数量
                try:
                    # 使用JavaScript查找包含草稿数量的元素
                    js_script = """
                    // 尝试查找显示草稿数量的元素
                    var elements = document.querySelectorAll('span');
                    for(var i=0; i<elements.length; i++) {
                        // 查找包含数字的span元素
                        if(elements[i].textContent && /\\d+/.test(elements[i].textContent)) {
                            var parent = elements[i].parentElement;
                            // 检查父元素或当前元素是否包含"草稿"或"共"等关键词
                            if((parent && (parent.textContent.includes('草稿') || parent.textContent.includes('共'))) ||
                               elements[i].textContent.includes('共')) {
                                return elements[i].textContent;
                            }
                        }
                    }

                    // 尝试查找页面上任何可能显示数量的元素
                    var countElements = document.querySelectorAll('div.count, span.count, div.total, span.total');
                    for(var i=0; i<countElements.length; i++) {
                        if(countElements[i].textContent && /\\d+/.test(countElements[i].textContent)) {
                            return countElements[i].textContent;
                        }
                    }

                    return '';
                    """
                    draft_count_text = driver.execute_script(js_script)
                    if draft_count_text:
                        # 提取数字部分
                        import re
                        match = re.search(r'(\d+)', draft_count_text)
                        if match:
                            draft_count = match.group(1)
                            account_data["draft_count"] = draft_count
                            self.status_update_signal.emit(f"✅ 已通过JavaScript获取草稿数量: {account_data['draft_count']}")
                except Exception as backup_err:
                    self.emit_log(f"备用方法获取草稿数量失败: {str(backup_err)}", "WARNING")

            # 跳转到收益数据界面采集可提现金额
            self.status_update_signal.emit("🔄 正在跳转到收益数据界面采集可提现金额...")
            try:
                # 访问收益数据界面
                driver.get("https://mp.toutiao.com/profile_v4/analysis/income-overview")

                # 等待页面加载
                time.sleep(3)  # 增加等待时间确保页面完全加载

                # 采集可提现金额 - 使用用户提供的XPath
                withdrawable_xpath = "/html/body/div[1]/div/div[3]/section/main/div[3]/div/div/div[2]/div/div/div[2]/div[1]/div[3]/div/div[2]"
                withdrawable_elem = WebDriverWait(driver, 8).until(
                    EC.presence_of_element_located((By.XPATH, withdrawable_xpath))
                )

                if withdrawable_elem and withdrawable_elem.text.strip():
                    # 提取可提现金额
                    withdrawable_amount = withdrawable_elem.text.strip()
                    # 确保只保留数字和小数点部分
                    import re
                    match = re.search(r'([\d.,]+)', withdrawable_amount)
                    if match:
                        withdrawable_amount = match.group(1)  # 获取匹配的数字部分
                        # 移除可能的千位分隔符
                        withdrawable_amount = withdrawable_amount.replace(',', '')

                    account_data["withdrawable_amount"] = withdrawable_amount
                    self.status_update_signal.emit(f"✅ 已获取可提现金额: {account_data['withdrawable_amount']}")
                else:
                    self.emit_log("可提现金额元素为空或不存在", "WARNING")
            except Exception as e:
                self.emit_log(f"采集可提现金额失败: {str(e)}", "WARNING")
                # 尝试备用方法获取可提现金额
                try:
                    # 使用JavaScript查找包含可提现金额的元素
                    js_script = """
                    // 尝试查找显示可提现金额的元素
                    var elements = document.querySelectorAll('div, span');

                    // 首先尝试查找包含"可提现"文本的元素
                    for(var i=0; i<elements.length; i++) {
                        if(elements[i].textContent && elements[i].textContent.includes('可提现')) {
                            // 找到包含"可提现"的元素后，查找其附近的数字元素
                            var parent = elements[i].parentElement;
                            if(parent) {
                                var numberElems = parent.querySelectorAll('div, span');
                                for(var j=0; j<numberElems.length; j++) {
                                    if(numberElems[j].textContent && /[\\d.,]+/.test(numberElems[j].textContent)) {
                                        return numberElems[j].textContent;
                                    }
                                }
                            }
                        }
                    }

                    // 如果上面的方法失败，尝试查找页面上任何可能显示金额的元素
                    var amountElements = document.querySelectorAll('.amount, .money, .balance, .withdrawable');
                    for(var i=0; i<amountElements.length; i++) {
                        if(amountElements[i].textContent && /[\\d.,]+/.test(amountElements[i].textContent)) {
                            return amountElements[i].textContent;
                        }
                    }

                    return '';
                    """
                    withdrawable_text = driver.execute_script(js_script)
                    if withdrawable_text:
                        # 提取数字部分
                        import re
                        match = re.search(r'([\d.,]+)', withdrawable_text)
                        if match:
                            withdrawable_amount = match.group(1)
                            # 移除可能的千位分隔符
                            withdrawable_amount = withdrawable_amount.replace(',', '')
                            account_data["withdrawable_amount"] = withdrawable_amount
                            self.status_update_signal.emit(f"✅ 已通过JavaScript获取可提现金额: {account_data['withdrawable_amount']}")
                except Exception as backup_err:
                    self.emit_log(f"备用方法获取可提现金额失败: {str(backup_err)}", "WARNING")

            # 采集七天收益数据
            self.status_update_signal.emit("🔄 正在采集七天收益数据...")
            try:
                # 确保我们仍在收益数据界面
                if "income-overview" not in driver.current_url:
                    driver.get("https://mp.toutiao.com/profile_v4/analysis/income-overview")
                    time.sleep(3)  # 等待页面加载

                # 定义七天收益的XPath列表
                seven_days_income_xpaths = [
                    "/html/body/div[1]/div/div[3]/section/main/div[3]/div/div/div[2]/div/div/div[4]/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[2]/div",
                    "/html/body/div[1]/div/div[3]/section/main/div[3]/div/div/div[2]/div/div/div[4]/div/div/div/div/div/div/div/div/table/tbody/tr[2]/td[2]/div",
                    "/html/body/div[1]/div/div[3]/section/main/div[3]/div/div/div[2]/div/div/div[4]/div/div/div/div/div/div/div/div/table/tbody/tr[3]/td[2]/div",
                    "/html/body/div[1]/div/div[3]/section/main/div[3]/div/div/div[2]/div/div/div[4]/div/div/div/div/div/div/div/div/table/tbody/tr[4]/td[2]/div",
                    "/html/body/div[1]/div/div[3]/section/main/div[3]/div/div/div[2]/div/div/div[4]/div/div/div/div/div/div/div/div/table/tbody/tr[5]/td[2]/div",
                    "/html/body/div[1]/div/div[3]/section/main/div[3]/div/div/div[2]/div/div/div[4]/div/div/div/div/div/div/div/div/table/tbody/tr[6]/td[2]/div",
                    "/html/body/div[1]/div/div[3]/section/main/div[3]/div/div/div[2]/div/div/div[4]/div/div/div/div/div/div/div/div/table/tbody/tr[7]/td[2]/div"
                ]

                # 初始化七天收益列表和总和
                seven_days_income = []
                seven_days_total = 0.0

                # 遍历XPath列表，采集每天的收益
                for i, xpath in enumerate(seven_days_income_xpaths):
                    try:
                        # 尝试查找元素
                        income_elem = WebDriverWait(driver, 3).until(
                            EC.presence_of_element_located((By.XPATH, xpath))
                        )

                        if income_elem and income_elem.text.strip():
                            # 提取收益金额
                            income_text = income_elem.text.strip()
                            # 确保只保留数字和小数点部分
                            import re
                            match = re.search(r'([\d.,]+)', income_text)
                            if match:
                                income_value = match.group(1)
                                # 移除可能的千位分隔符
                                income_value = income_value.replace(',', '')
                                # 转换为浮点数
                                try:
                                    income_float = float(income_value)
                                    seven_days_income.append(income_float)
                                    seven_days_total += income_float
                                    self.status_update_signal.emit(f"✅ 第{i+1}天收益: {income_value}")
                                except ValueError:
                                    self.emit_log(f"无法将第{i+1}天收益转换为数字: {income_value}", "WARNING")
                            else:
                                self.emit_log(f"无法从文本中提取第{i+1}天收益: {income_text}", "WARNING")
                        else:
                            self.emit_log(f"第{i+1}天收益元素为空或不存在", "WARNING")
                    except Exception as e:
                        self.emit_log(f"采集第{i+1}天收益失败: {str(e)}", "WARNING")

                # 如果成功采集了至少一天的收益，更新七天总收益
                if seven_days_income:
                    # 格式化总收益为两位小数
                    account_data["seven_days_total"] = f"{seven_days_total:.2f}"
                    self.status_update_signal.emit(f"✅ 七天总收益: {account_data['seven_days_total']}")
                else:
                    self.emit_log("未能采集到任何一天的收益数据", "WARNING")

                    # 尝试使用备用方法获取七天收益
                    try:
                        # 使用JavaScript查找表格中的收益数据
                        js_script = """
                        var incomeValues = [];
                        var total = 0;

                        // 尝试查找表格中的收益数据
                        var rows = document.querySelectorAll('table tbody tr');
                        for(var i=0; i<rows.length && i<7; i++) {
                            var cells = rows[i].querySelectorAll('td');
                            if(cells.length >= 2) {
                                var incomeCell = cells[1];  // 第二列通常是收益
                                if(incomeCell && incomeCell.textContent) {
                                    var text = incomeCell.textContent.trim();
                                    var match = text.match(/(\\d+(\\.\\d+)?)/);
                                    if(match) {
                                        var value = parseFloat(match[1]);
                                        if(!isNaN(value)) {
                                            incomeValues.push(value);
                                            total += value;
                                        }
                                    }
                                }
                            }
                        }

                        return {values: incomeValues, total: total.toFixed(2)};
                        """
                        result = driver.execute_script(js_script)

                        if result and 'total' in result and result['total']:
                            account_data["seven_days_total"] = result['total']
                            self.status_update_signal.emit(f"✅ 已通过JavaScript获取七天总收益: {account_data['seven_days_total']}")
                    except Exception as backup_err:
                        self.emit_log(f"备用方法获取七天收益失败: {str(backup_err)}", "WARNING")
            except Exception as e:
                self.emit_log(f"采集七天收益数据失败: {str(e)}", "WARNING")

            # 跳转到提现界面采集提现相关数据
            self.status_update_signal.emit("🔄 正在跳转到提现界面采集提现相关数据...")
            try:
                # 访问提现界面
                driver.get("https://mp.toutiao.com/profile_v4/personal/checkout-center")

                # 等待页面加载
                time.sleep(3)  # 增加等待时间确保页面完全加载

                # 采集总提现金额
                try:
                    total_withdraw_xpath = "/html/body/div[1]/div/div[3]/section/main/div/div/div/div[2]/div/div/div[2]/div[1]/div[3]/div/div[2]/span"
                    total_withdraw_elem = WebDriverWait(driver, 8).until(
                        EC.presence_of_element_located((By.XPATH, total_withdraw_xpath))
                    )

                    if total_withdraw_elem and total_withdraw_elem.text.strip():
                        # 提取总提现金额
                        total_withdraw = total_withdraw_elem.text.strip()
                        # 确保只保留数字和小数点部分
                        import re
                        match = re.search(r'([\d.,]+)', total_withdraw)
                        if match:
                            total_withdraw = match.group(1)
                            # 移除可能的千位分隔符
                            total_withdraw = total_withdraw.replace(',', '')

                        account_data["total_withdraw"] = total_withdraw
                        self.status_update_signal.emit(f"✅ 已获取总提现金额: {account_data['total_withdraw']}")
                    else:
                        self.emit_log("总提现金额元素为空或不存在", "WARNING")
                except Exception as e:
                    self.emit_log(f"采集总提现金额失败: {str(e)}", "WARNING")
                    # 尝试备用方法
                    try:
                        js_script = """
                        var elements = document.querySelectorAll('span, div');
                        for(var i=0; i<elements.length; i++) {
                            if(elements[i].textContent && /^[¥￥]?\\s*[\\d.,]+$/.test(elements[i].textContent.trim())) {
                                var parent = elements[i].parentElement;
                                if(parent && (parent.textContent.includes('总提现') ||
                                   (parent.parentElement && parent.parentElement.textContent.includes('总提现')))) {
                                    return elements[i].textContent.trim();
                                }
                            }
                        }
                        return '';
                        """
                        total_withdraw = driver.execute_script(js_script)
                        if total_withdraw:
                            # 提取数字部分
                            match = re.search(r'([\d.,]+)', total_withdraw)
                            if match:
                                total_withdraw = match.group(1).replace(',', '')
                                account_data["total_withdraw"] = total_withdraw
                                self.status_update_signal.emit(f"✅ 已通过JavaScript获取总提现金额: {account_data['total_withdraw']}")
                    except Exception as backup_err:
                        self.emit_log(f"备用方法获取总提现金额失败: {str(backup_err)}", "WARNING")

                # 采集提现日期和最近提现金额
                try:
                    # 检查是否有提现记录
                    withdraw_date_xpath = "/html/body/div[1]/div/div[3]/section/main/div/div/div/div[2]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[1]/div"
                    withdraw_date_elem = WebDriverWait(driver, 5).until(
                        EC.presence_of_element_located((By.XPATH, withdraw_date_xpath))
                    )

                    if withdraw_date_elem and withdraw_date_elem.text.strip():
                        # 提取提现日期
                        withdraw_date = withdraw_date_elem.text.strip()
                        account_data["withdraw_date"] = withdraw_date
                        self.status_update_signal.emit(f"✅ 已获取提现日期: {account_data['withdraw_date']}")

                        # 采集最近提现金额
                        recent_withdraw_xpath = "/html/body/div[1]/div/div[3]/section/main/div/div/div/div[2]/div/div/div[2]/div[3]/div/div/div/div/div/div/div/div/table/tbody/tr[1]/td[5]/div"
                        recent_withdraw_elem = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, recent_withdraw_xpath))
                        )

                        if recent_withdraw_elem and recent_withdraw_elem.text.strip():
                            # 提取最近提现金额
                            recent_withdraw = recent_withdraw_elem.text.strip()
                            # 确保只保留数字和小数点部分
                            match = re.search(r'([\d.,]+)', recent_withdraw)
                            if match:
                                recent_withdraw = match.group(1)
                                # 移除可能的千位分隔符
                                recent_withdraw = recent_withdraw.replace(',', '')

                            account_data["recent_withdraw"] = recent_withdraw
                            self.status_update_signal.emit(f"✅ 已获取最近提现金额: {account_data['recent_withdraw']}")
                        else:
                            self.emit_log("最近提现金额元素为空或不存在", "WARNING")
                    else:
                        self.emit_log("未找到提现记录", "INFO")
                        account_data["withdraw_date"] = "无记录"
                        account_data["recent_withdraw"] = "0"
                except Exception as e:
                    self.emit_log(f"采集提现记录失败: {str(e)}", "WARNING")
                    # 尝试备用方法
                    try:
                        js_script = """
                        var result = {date: '', amount: ''};

                        // 尝试查找表格中的提现记录
                        var rows = document.querySelectorAll('table tbody tr');
                        if(rows.length > 0) {
                            var firstRow = rows[0];
                            var cells = firstRow.querySelectorAll('td');

                            // 第一列通常是日期
                            if(cells.length >= 1) {
                                var dateCell = cells[0];
                                if(dateCell && dateCell.textContent) {
                                    result.date = dateCell.textContent.trim();
                                }
                            }

                            // 第五列通常是金额
                            if(cells.length >= 5) {
                                var amountCell = cells[4];
                                if(amountCell && amountCell.textContent) {
                                    result.amount = amountCell.textContent.trim();
                                }
                            }
                        } else {
                            // 如果没有找到记录
                            result.date = '无记录';
                            result.amount = '0';
                        }

                        return result;
                        """
                        result = driver.execute_script(js_script)

                        if result and 'date' in result and result['date']:
                            account_data["withdraw_date"] = result['date']
                            self.status_update_signal.emit(f"✅ 已通过JavaScript获取提现日期: {account_data['withdraw_date']}")

                        if result and 'amount' in result and result['amount']:
                            # 提取数字部分
                            match = re.search(r'([\d.,]+)', result['amount'])
                            if match:
                                recent_withdraw = match.group(1).replace(',', '')
                                account_data["recent_withdraw"] = recent_withdraw
                                self.status_update_signal.emit(f"✅ 已通过JavaScript获取最近提现金额: {account_data['recent_withdraw']}")
                    except Exception as backup_err:
                        self.emit_log(f"备用方法获取提现记录失败: {str(backup_err)}", "WARNING")
                        # 设置默认值
                        if "withdraw_date" not in account_data:
                            account_data["withdraw_date"] = "无记录"
                        if "recent_withdraw" not in account_data:
                            account_data["recent_withdraw"] = "0"
            except Exception as e:
                self.emit_log(f"采集提现相关数据失败: {str(e)}", "WARNING")
                # 设置默认值
                if "total_withdraw" not in account_data:
                    account_data["total_withdraw"] = "0"
                if "withdraw_date" not in account_data:
                    account_data["withdraw_date"] = "无记录"
                if "recent_withdraw" not in account_data:
                    account_data["recent_withdraw"] = "0"

            # 更新表格中的数据
            self.update_table_data(row, account_data)

            # 设置状态为正常
            account_data["status"] = "正常"

            # 返回成功
            return True

        except Exception as e:
            self.emit_log(f"采集数据过程中出错: {str(e)}", "ERROR")
            return False

    def update_table_data(self, row, account_data):
        """更新表格中的账号数据

        Args:
            row: 行索引
            account_data: 账号数据字典
        """
        try:
            # 更新昵称列(0列)
            if "username" in account_data and account_data["username"]:
                username_item = QTableWidgetItem(account_data["username"])
                username_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.table.setItem(row, 0, username_item)

            # 更新信用分(4列)
            if "credit_score" in account_data and account_data["credit_score"]:
                credit_score_item = QTableWidgetItem(account_data["credit_score"])
                credit_score_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                # 根据信用分值设置颜色
                try:
                    score_value = float(account_data["credit_score"])
                    if score_value >= 90:
                        credit_score_item.setForeground(QBrush(QColor(0, 150, 0)))  # 绿色表示高分
                    elif score_value >= 70:
                        credit_score_item.setForeground(QBrush(QColor(0, 0, 200)))  # 蓝色表示中等分数
                    else:
                        credit_score_item.setForeground(QBrush(QColor(200, 0, 0)))  # 红色表示低分
                except ValueError:
                    # 如果无法转换为浮点数，使用默认颜色
                    pass
                self.table.setItem(row, 3, credit_score_item)

            # 更新草稿数量(5列)
            if "draft_count" in account_data and account_data["draft_count"]:
                draft_count_item = QTableWidgetItem(account_data["draft_count"])
                draft_count_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                # 根据草稿数量设置颜色
                try:
                    count_value = int(account_data["draft_count"])
                    if count_value > 50:
                        draft_count_item.setForeground(QBrush(QColor(200, 0, 0)))  # 红色表示草稿过多
                    elif count_value > 20:
                        draft_count_item.setForeground(QBrush(QColor(200, 150, 0)))  # 橙色表示草稿较多
                    else:
                        draft_count_item.setForeground(QBrush(QColor(0, 150, 0)))  # 绿色表示草稿数量正常
                except ValueError:
                    # 如果无法转换为整数，使用默认颜色
                    pass
                self.table.setItem(row, 4, draft_count_item)

            # 更新昨日粉丝数(6列)
            if "yesterday_fans" in account_data and account_data["yesterday_fans"]:
                yesterday_fans_item = QTableWidgetItem(account_data["yesterday_fans"])
                yesterday_fans_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                # 根据正负值设置颜色
                if account_data["yesterday_fans"].startswith("+"):
                    yesterday_fans_item.setForeground(QBrush(QColor(0, 150, 0)))  # 绿色表示增长
                elif account_data["yesterday_fans"].startswith("-"):
                    yesterday_fans_item.setForeground(QBrush(QColor(200, 0, 0)))  # 红色表示减少
                self.table.setItem(row, 5, yesterday_fans_item)

            # 更新总粉丝数(7列)
            if "total_fans" in account_data and account_data["total_fans"]:
                fans_item = QTableWidgetItem(account_data["total_fans"])
                fans_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.table.setItem(row, 6, fans_item)

            # 更新累计播放量(8列)
            if "total_play_count" in account_data and account_data["total_play_count"]:
                play_item = QTableWidgetItem(account_data["total_play_count"])
                play_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.table.setItem(row, 7, play_item)

            # 更新昨日播放量(9列)
            if "yesterday_play" in account_data and account_data["yesterday_play"]:
                yesterday_play_item = QTableWidgetItem(account_data["yesterday_play"])
                yesterday_play_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                # 根据正负值设置颜色
                if account_data["yesterday_play"].startswith("+"):
                    yesterday_play_item.setForeground(QBrush(QColor(0, 150, 0)))  # 绿色表示增长
                elif account_data["yesterday_play"].startswith("-"):
                    yesterday_play_item.setForeground(QBrush(QColor(200, 0, 0)))  # 红色表示减少
                self.table.setItem(row, 8, yesterday_play_item)

            # 更新累计收益(10列)
            if "total_income" in account_data and account_data["total_income"]:
                income_item = QTableWidgetItem(account_data["total_income"])
                income_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.table.setItem(row, 9, income_item)

            # 更新昨日收益(11列)
            if "yesterday_income" in account_data and account_data["yesterday_income"]:
                yesterday_income_item = QTableWidgetItem(account_data["yesterday_income"])
                yesterday_income_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                # 根据正负值设置颜色
                if account_data["yesterday_income"].startswith("+"):
                    yesterday_income_item.setForeground(QBrush(QColor(0, 150, 0)))  # 绿色表示增长
                elif account_data["yesterday_income"].startswith("-"):
                    yesterday_income_item.setForeground(QBrush(QColor(200, 0, 0)))  # 红色表示减少
                self.table.setItem(row, 10, yesterday_income_item)

            # 更新七天总收益(12列)
            if "seven_days_total" in account_data and account_data["seven_days_total"]:
                seven_days_item = QTableWidgetItem(account_data["seven_days_total"])
                seven_days_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                # 根据金额设置颜色
                try:
                    amount_value = float(account_data["seven_days_total"])
                    if amount_value >= 50:
                        seven_days_item.setForeground(QBrush(QColor(0, 150, 0)))  # 绿色表示收益较高
                    elif amount_value >= 10:
                        seven_days_item.setForeground(QBrush(QColor(0, 0, 200)))  # 蓝色表示收益中等
                    elif amount_value > 0:
                        seven_days_item.setForeground(QBrush(QColor(100, 100, 100)))  # 灰色表示收益较低
                    else:
                        seven_days_item.setForeground(QBrush(QColor(200, 0, 0)))  # 红色表示无收益
                except ValueError:
                    # 如果无法转换为浮点数，使用默认颜色
                    pass
                self.table.setItem(row, 11, seven_days_item)

            # 更新可提现金额(13列)
            if "withdrawable_amount" in account_data and account_data["withdrawable_amount"]:
                withdrawable_item = QTableWidgetItem(account_data["withdrawable_amount"])
                withdrawable_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                # 根据金额设置颜色
                try:
                    amount_value = float(account_data["withdrawable_amount"])
                    if amount_value >= 100:
                        withdrawable_item.setForeground(QBrush(QColor(0, 150, 0)))  # 绿色表示金额较高
                    elif amount_value >= 10:
                        withdrawable_item.setForeground(QBrush(QColor(0, 0, 200)))  # 蓝色表示金额中等
                    else:
                        withdrawable_item.setForeground(QBrush(QColor(100, 100, 100)))  # 灰色表示金额较低
                except ValueError:
                    # 如果无法转换为浮点数，使用默认颜色
                    pass
                self.table.setItem(row, 12, withdrawable_item)

            # 更新账号注册天数(14列)
            if "register_days" in account_data and account_data["register_days"]:
                days_item = QTableWidgetItem(account_data["register_days"])
                days_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.table.setItem(row, 13, days_item)

            # 更新实名状态(15列)
            if "verification_status" in account_data and account_data["verification_status"]:
                verification_status = account_data["verification_status"]
            else:
                # 如果没有设置过实名状态，默认设置为已实名
                verification_status = "已实名"
                account_data["verification_status"] = verification_status

            # 创建实名状态项
            verification_item = QTableWidgetItem(verification_status)
            verification_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

            # 设置颜色：已实名为绿色，未实名为红色
            if verification_status == "已实名":
                verification_item.setForeground(QBrush(QColor(0, 150, 0)))  # 绿色表示已实名
            else:
                verification_item.setForeground(QBrush(QColor(200, 0, 0)))  # 红色表示未实名

            # 更新到第15列（索引为14）
            self.table.setItem(row, 14, verification_item)
            # 使用账号ID（从表格第2列获取）
            account_id_item = self.table.item(row, 1)
            account_id = account_id_item.text() if account_id_item else "未知账号"
            self.emit_log(f"更新账号 {account_id} 实名状态: {verification_status}", "INFO")

            # 更新总提现金额(16列)
            if "total_withdraw" in account_data and account_data["total_withdraw"]:
                total_withdraw_item = QTableWidgetItem(account_data["total_withdraw"])
                total_withdraw_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                # 根据金额设置颜色
                try:
                    amount_value = float(account_data["total_withdraw"])
                    if amount_value >= 1000:
                        total_withdraw_item.setForeground(QBrush(QColor(0, 150, 0)))  # 绿色表示金额较高
                    elif amount_value >= 100:
                        total_withdraw_item.setForeground(QBrush(QColor(0, 0, 200)))  # 蓝色表示金额中等
                    elif amount_value > 0:
                        total_withdraw_item.setForeground(QBrush(QColor(100, 100, 100)))  # 灰色表示金额较低
                    else:
                        total_withdraw_item.setForeground(QBrush(QColor(200, 0, 0)))  # 红色表示无提现
                except ValueError:
                    # 如果无法转换为浮点数，使用默认颜色
                    pass
                self.table.setItem(row, 15, total_withdraw_item)

            # 更新提现日期(17列)
            if "withdraw_date" in account_data and account_data["withdraw_date"]:
                withdraw_date_item = QTableWidgetItem(account_data["withdraw_date"])
                withdraw_date_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                # 如果是"无记录"，设置为灰色
                if account_data["withdraw_date"] == "无记录":
                    withdraw_date_item.setForeground(QBrush(QColor(150, 150, 150)))
                self.table.setItem(row, 16, withdraw_date_item)

            # 更新最近提现金额(18列)
            if "recent_withdraw" in account_data and account_data["recent_withdraw"]:
                recent_withdraw_item = QTableWidgetItem(account_data["recent_withdraw"])
                recent_withdraw_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                # 根据金额设置颜色
                try:
                    amount_value = float(account_data["recent_withdraw"])
                    if amount_value >= 500:
                        recent_withdraw_item.setForeground(QBrush(QColor(0, 150, 0)))  # 绿色表示金额较高
                    elif amount_value >= 100:
                        recent_withdraw_item.setForeground(QBrush(QColor(0, 0, 200)))  # 蓝色表示金额中等
                    elif amount_value > 0:
                        recent_withdraw_item.setForeground(QBrush(QColor(100, 100, 100)))  # 灰色表示金额较低
                    else:
                        recent_withdraw_item.setForeground(QBrush(QColor(200, 0, 0)))  # 红色表示无提现
                except ValueError:
                    # 如果无法转换为浮点数，使用默认颜色
                    pass
                self.table.setItem(row, 17, recent_withdraw_item)

            # 处理UI事件，确保表格更新
            QApplication.processEvents()

        except Exception as e:
            self.emit_log(f"更新表格数据时出错: {str(e)}", "ERROR")
