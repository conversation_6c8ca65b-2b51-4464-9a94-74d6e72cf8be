#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
路径管理器 - 提供路径管理、验证和预设功能
支持视频处理、账号管理等多种路径设置的统一管理
"""

import os
import json
import logging
import shutil
from typing import Dict, List, Optional, Tuple, Any

logger = logging.getLogger(__name__)

class PathManager:
    """路径管理器类，用于管理和验证路径设置"""

    def __init__(self, config_file: str):
        """初始化路径管理器

        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.paths = {}
        self.presets = {}
        self.load_config()

    def load_config(self) -> bool:
        """从配置文件加载路径设置

        Returns:
            bool: 是否成功加载
        """
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 加载路径设置
                if 'paths' in config:
                    self.paths = config['paths']
                    # 统一路径格式
                    for key, path in self.paths.items():
                        if path:
                            self.paths[key] = self._normalize_path(path)

                # 加载预设
                if 'presets' in config:
                    self.presets = config['presets']
                    # 统一预设中的路径格式
                    for preset_name, preset_paths in self.presets.items():
                        for key, path in preset_paths.items():
                            if path:
                                self.presets[preset_name][key] = self._normalize_path(path)

                logger.info(f"已从 {self.config_file} 加载路径设置")
                return True
            else:
                logger.warning(f"配置文件不存在: {self.config_file}")
                return False
        except Exception as e:
            logger.error(f"加载路径设置失败: {str(e)}")
            return False

    def save_config(self) -> bool:
        """保存路径设置到配置文件

        Returns:
            bool: 是否成功保存
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)

            # 准备配置数据
            config = {
                'paths': self.paths,
                'presets': self.presets
            }

            # 保存到文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=4)

            logger.info(f"已保存路径设置到 {self.config_file}")
            return True
        except Exception as e:
            logger.error(f"保存路径设置失败: {str(e)}")
            return False

    def set_path(self, key: str, path: str) -> None:
        """设置路径

        Args:
            key: 路径键名
            path: 路径值
        """
        if path:
            self.paths[key] = self._normalize_path(path)
        else:
            self.paths[key] = ""

    def get_path(self, key: str, default: str = "") -> str:
        """获取路径

        Args:
            key: 路径键名
            default: 默认值

        Returns:
            str: 路径值
        """
        return self.paths.get(key, default)

    def add_preset(self, name: str, paths: Dict[str, str]) -> None:
        """添加路径预设

        Args:
            name: 预设名称
            paths: 路径字典
        """
        # 统一路径格式
        normalized_paths = {}
        for key, path in paths.items():
            if path:
                normalized_paths[key] = self._normalize_path(path)
            else:
                normalized_paths[key] = ""

        self.presets[name] = normalized_paths
        logger.info(f"已添加路径预设: {name}")

    def get_preset(self, name: str) -> Dict[str, str]:
        """获取路径预设

        Args:
            name: 预设名称

        Returns:
            Dict[str, str]: 路径字典
        """
        return self.presets.get(name, {})

    def remove_preset(self, name: str) -> bool:
        """删除路径预设

        Args:
            name: 预设名称

        Returns:
            bool: 是否成功删除
        """
        if name in self.presets:
            del self.presets[name]
            logger.info(f"已删除路径预设: {name}")
            return True
        return False

    def get_preset_names(self) -> List[str]:
        """获取所有预设名称

        Returns:
            List[str]: 预设名称列表
        """
        return list(self.presets.keys())

    def apply_preset(self, name: str) -> bool:
        """应用路径预设

        Args:
            name: 预设名称

        Returns:
            bool: 是否成功应用
        """
        if name in self.presets:
            preset_paths = self.presets[name]
            for key, path in preset_paths.items():
                self.set_path(key, path)
            logger.info(f"已应用路径预设: {name}")
            return True
        logger.warning(f"预设不存在: {name}")
        return False

    def validate_path(self, path: str, check_exists: bool = True, check_writable: bool = False) -> Tuple[bool, str]:
        """验证路径是否有效

        Args:
            path: 路径
            check_exists: 是否检查路径存在
            check_writable: 是否检查路径可写

        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if not path:
            return False, "路径不能为空"

        # 统一路径格式
        path = self._normalize_path(path)

        # 检查路径是否存在
        if check_exists and not os.path.exists(path):
            return False, f"路径不存在: {path}"

        # 检查路径是否可写
        if check_writable:
            if os.path.exists(path):
                if not os.access(path, os.W_OK):
                    return False, f"路径不可写: {path}"
            else:
                # 如果路径不存在，检查父目录是否可写
                parent_dir = os.path.dirname(path)
                if parent_dir and not os.access(parent_dir, os.W_OK):
                    return False, f"父目录不可写: {parent_dir}"

        return True, ""

    def create_directory(self, path: str) -> Tuple[bool, str]:
        """创建目录

        Args:
            path: 目录路径

        Returns:
            Tuple[bool, str]: (是否成功, 错误信息)
        """
        if not path:
            return False, "路径不能为空"

        # 统一路径格式
        path = self._normalize_path(path)

        try:
            os.makedirs(path, exist_ok=True)
            return True, ""
        except Exception as e:
            return False, f"创建目录失败: {str(e)}"

    def get_disk_space(self, path: str) -> Tuple[bool, float, str]:
        """获取磁盘剩余空间

        Args:
            path: 路径

        Returns:
            Tuple[bool, float, str]: (是否成功, 剩余空间(GB), 错误信息)
        """
        if not path:
            return False, 0, "路径不能为空"

        # 统一路径格式
        path = self._normalize_path(path)

        try:
            import shutil
            # 获取驱动器
            drive = os.path.splitdrive(os.path.abspath(path))[0] or os.path.abspath(path)
            # 获取剩余空间
            free_space = shutil.disk_usage(drive).free / (1024 * 1024 * 1024)  # GB
            return True, free_space, ""
        except Exception as e:
            return False, 0, f"获取磁盘空间失败: {str(e)}"

    def _normalize_path(self, path: str) -> str:
        """统一路径格式，使用正斜杠

        Args:
            path: 原始路径

        Returns:
            str: 统一格式后的路径
        """
        if path:
            # 移除多余的空格和换行符
            path = path.strip().replace("\n", "").replace("\r", "")
            # 统一使用正斜杠
            path = path.replace("\\", "/")
        return path

    # ===== 视频处理相关方法 =====

    def get_video_paths(self) -> Dict[str, str]:
        """获取视频处理相关的所有路径

        Returns:
            Dict[str, str]: 路径字典，包含视频输入、输出、封面等路径
        """
        video_paths = {
            'video_input_dir': self.get_path('video_input_dir', ''),
            'video_output_dir': self.get_path('video_output_dir', ''),
            'cover_output_dir': self.get_path('cover_output_dir', '')
        }
        return video_paths

    def set_video_paths(self, input_dir: str, video_dir: str, cover_dir: str) -> None:
        """设置视频处理相关的所有路径

        Args:
            input_dir: 视频输入目录
            video_dir: 视频输出目录
            cover_dir: 封面输出目录
        """
        self.set_path('video_input_dir', input_dir)
        self.set_path('video_output_dir', video_dir)
        self.set_path('cover_output_dir', cover_dir)

    def save_video_preset(self, name: str, settings: Dict[str, Any]) -> bool:
        """保存视频处理预设

        Args:
            name: 预设名称
            settings: 预设设置，包含路径和处理选项

        Returns:
            bool: 是否成功保存
        """
        try:
            # 提取路径部分
            paths = {
                'video_input_dir': settings.get('input_dir', ''),
                'video_output_dir': settings.get('video_dir', ''),
                'cover_output_dir': settings.get('cover_dir', '')
            }

            # 保存为路径预设
            self.add_preset(f"video_{name}", paths)

            # 保存完整设置到预设元数据
            if 'preset_metadata' not in self.paths:
                self.paths['preset_metadata'] = {}

            if 'video_presets' not in self.paths['preset_metadata']:
                self.paths['preset_metadata']['video_presets'] = {}

            self.paths['preset_metadata']['video_presets'][name] = settings

            # 保存配置
            return self.save_config()
        except Exception as e:
            logger.error(f"保存视频预设失败: {str(e)}")
            return False

    def get_video_preset(self, name: str) -> Dict[str, Any]:
        """获取视频处理预设

        Args:
            name: 预设名称

        Returns:
            Dict[str, Any]: 预设设置，包含路径和处理选项
        """
        try:
            # 尝试从元数据中获取完整设置
            if ('preset_metadata' in self.paths and
                'video_presets' in self.paths['preset_metadata'] and
                name in self.paths['preset_metadata']['video_presets']):
                return self.paths['preset_metadata']['video_presets'][name]

            # 如果元数据中没有，尝试从路径预设中获取
            preset_name = f"video_{name}"
            if preset_name in self.presets:
                paths = self.presets[preset_name]
                return {
                    'input_dir': paths.get('video_input_dir', ''),
                    'video_dir': paths.get('video_output_dir', ''),
                    'cover_dir': paths.get('cover_output_dir', '')
                }

            return {}
        except Exception as e:
            logger.error(f"获取视频预设失败: {str(e)}")
            return {}

    def get_video_preset_names(self) -> List[str]:
        """获取所有视频处理预设名称

        Returns:
            List[str]: 预设名称列表
        """
        try:
            # 从元数据中获取
            if ('preset_metadata' in self.paths and
                'video_presets' in self.paths['preset_metadata']):
                return list(self.paths['preset_metadata']['video_presets'].keys())

            # 从路径预设中筛选
            video_presets = []
            for name in self.presets.keys():
                if name.startswith('video_'):
                    video_presets.append(name[6:])  # 去掉'video_'前缀

            return video_presets
        except Exception as e:
            logger.error(f"获取视频预设名称失败: {str(e)}")
            return []

    def remove_video_preset(self, name: str) -> bool:
        """删除视频处理预设

        Args:
            name: 预设名称

        Returns:
            bool: 是否成功删除
        """
        try:
            # 从元数据中删除
            if ('preset_metadata' in self.paths and
                'video_presets' in self.paths['preset_metadata'] and
                name in self.paths['preset_metadata']['video_presets']):
                del self.paths['preset_metadata']['video_presets'][name]

            # 从路径预设中删除
            preset_name = f"video_{name}"
            if preset_name in self.presets:
                del self.presets[preset_name]

            # 保存配置
            return self.save_config()
        except Exception as e:
            logger.error(f"删除视频预设失败: {str(e)}")
            return False

    def check_video_directories(self, input_dir: str, video_dir: str, cover_dir: str) -> Dict[str, Tuple[bool, str]]:
        """检查视频处理相关目录是否有效

        Args:
            input_dir: 视频输入目录
            video_dir: 视频输出目录
            cover_dir: 封面输出目录

        Returns:
            Dict[str, Tuple[bool, str]]: 检查结果，键为目录类型，值为(是否有效, 错误信息)
        """
        results = {}

        # 检查输入目录
        if input_dir:
            input_valid, input_msg = self.validate_path(input_dir, check_exists=True, check_writable=False)
            results['input_dir'] = (input_valid, input_msg)
        else:
            results['input_dir'] = (False, "输入目录不能为空")

        # 检查视频输出目录
        if video_dir:
            # 如果目录不存在，尝试创建
            if not os.path.exists(video_dir):
                try:
                    os.makedirs(video_dir, exist_ok=True)
                except Exception as e:
                    results['video_dir'] = (False, f"创建视频输出目录失败: {str(e)}")

            video_valid, video_msg = self.validate_path(video_dir, check_exists=False, check_writable=True)
            results['video_dir'] = (video_valid, video_msg)
        else:
            results['video_dir'] = (False, "视频输出目录不能为空")

        # 检查封面输出目录
        if cover_dir:
            # 如果目录不存在，尝试创建
            if not os.path.exists(cover_dir):
                try:
                    os.makedirs(cover_dir, exist_ok=True)
                except Exception as e:
                    results['cover_dir'] = (False, f"创建封面输出目录失败: {str(e)}")

            cover_valid, cover_msg = self.validate_path(cover_dir, check_exists=False, check_writable=True)
            results['cover_dir'] = (cover_valid, cover_msg)
        else:
            results['cover_dir'] = (False, "封面输出目录不能为空")

        return results

    def check_disk_space(self, video_dir: str, cover_dir: str, min_space_gb: float = 1.0) -> Dict[str, Tuple[bool, float, str]]:
        """检查磁盘剩余空间是否足够

        Args:
            video_dir: 视频输出目录
            cover_dir: 封面输出目录
            min_space_gb: 最小所需空间(GB)

        Returns:
            Dict[str, Tuple[bool, float, str]]: 检查结果，键为目录类型，值为(是否足够, 剩余空间(GB), 错误信息)
        """
        results = {}

        # 检查视频输出目录所在磁盘
        if video_dir:
            success, free_space, error = self.get_disk_space(video_dir)
            if success:
                results['video_dir'] = (free_space >= min_space_gb, free_space,
                                       "" if free_space >= min_space_gb else f"磁盘空间不足: {free_space:.2f}GB")
            else:
                results['video_dir'] = (False, 0, error)
        else:
            results['video_dir'] = (False, 0, "视频输出目录不能为空")

        # 检查封面输出目录所在磁盘
        if cover_dir:
            success, free_space, error = self.get_disk_space(cover_dir)
            if success:
                results['cover_dir'] = (free_space >= min_space_gb, free_space,
                                       "" if free_space >= min_space_gb else f"磁盘空间不足: {free_space:.2f}GB")
            else:
                results['cover_dir'] = (False, 0, error)
        else:
            results['cover_dir'] = (False, 0, "封面输出目录不能为空")

        return results

    def clear_output_directories(self, video_dir: str, cover_dir: str) -> Dict[str, Tuple[bool, int, str]]:
        """清空输出目录

        Args:
            video_dir: 视频输出目录
            cover_dir: 封面输出目录

        Returns:
            Dict[str, Tuple[bool, int, str]]: 清空结果，键为目录类型，值为(是否成功, 删除文件数, 错误信息)
        """
        results = {}

        # 清空视频输出目录
        if video_dir and os.path.exists(video_dir):
            try:
                deleted_count = 0
                for file in os.listdir(video_dir):
                    file_path = os.path.join(video_dir, file)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        deleted_count += 1
                results['video_dir'] = (True, deleted_count, "")
            except Exception as e:
                results['video_dir'] = (False, 0, f"清空视频输出目录失败: {str(e)}")
        else:
            results['video_dir'] = (False, 0, "视频输出目录不存在")

        # 清空封面输出目录
        if cover_dir and os.path.exists(cover_dir):
            try:
                deleted_count = 0
                for file in os.listdir(cover_dir):
                    file_path = os.path.join(cover_dir, file)
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        deleted_count += 1
                results['cover_dir'] = (True, deleted_count, "")
            except Exception as e:
                results['cover_dir'] = (False, 0, f"清空封面输出目录失败: {str(e)}")
        else:
            results['cover_dir'] = (False, 0, "封面输出目录不存在")

        return results
