#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
养号模块代理管理工具 - 支持全局代理和本地代理
"""

import os
import random
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

# 导入代理工具
try:
    from app.utils.proxy_utils import (
        get_proxy_settings,
        get_current_proxy,
        apply_proxy_to_chrome_options,
        force_rotate_proxy,
        get_random_proxy_from_list,
        test_proxy
    )
except ImportError:
    # 如果导入失败，提供空函数
    def get_proxy_settings():
        return None

    def get_current_proxy():
        return None

    def apply_proxy_to_chrome_options(options):
        return False

    def force_rotate_proxy():
        return False

    def get_random_proxy_from_list():
        return None

    def test_proxy(proxy_url, timeout=5):
        return False

logger = logging.getLogger(__name__)

class NurtureProxyManager:
    """养号模块代理管理器"""

    def __init__(self, settings):
        """初始化代理管理器

        Args:
            settings: 养号设置字典
        """
        self.settings = settings
        self.use_global_proxy = settings.get('use_global_proxy', False)
        self.enable_proxy = settings.get('enable_proxy', False)
        self.proxy_type = settings.get('proxy_type', 'HTTP')
        self.proxy_list = settings.get('proxy_list', [])
        self.auto_switch_proxy = settings.get('auto_switch_proxy', True)
        self.proxy_switch_interval = settings.get('proxy_switch_interval', '每账号切换一次')

        # 记录当前使用的代理
        self.current_proxy = None

        # 初始化日志
        if self.use_global_proxy:
            logger.info("使用全局代理设置")
        elif self.enable_proxy:
            logger.info(f"使用本地代理设置: {self.proxy_type}, 代理数量: {len(self.proxy_list)}")
        else:
            logger.info("未启用代理")

    def get_random_proxy(self):
        """获取随机代理

        Returns:
            dict: 包含代理类型和地址的字典，如果未启用代理则返回None
        """
        # 如果使用全局代理
        if self.use_global_proxy:
            # 获取全局代理设置
            try:
                # 尝试使用新参数
                global_settings = get_proxy_settings(check_enabled=False)
            except TypeError:
                # 如果旧版本不支持check_enabled参数，使用默认调用
                global_settings = get_proxy_settings()

            if not global_settings:
                logger.warning("未找到全局代理设置")
                return None

            # 强制轮换全局代理
            force_rotate_proxy()

            # 获取全局代理
            # 如果启用了IP轮换，从代理列表中选择一个
            if global_settings.get("enable_rotation", False):
                proxy_list = global_settings.get("proxy_list", "").strip()

                if proxy_list:
                    proxies = [p.strip() for p in proxy_list.split(",") if p.strip()]

                    if proxies:
                        proxy_url = random.choice(proxies)
                        logger.info(f"从全局代理列表中选择代理: {proxy_url}")
                    else:
                        proxy_url = None
                else:
                    proxy_url = None
            else:
                # 否则使用单个代理
                proxy_type = global_settings.get("proxy_type", "HTTP").lower()
                proxy_host = global_settings.get("proxy_host", "")
                proxy_port = global_settings.get("proxy_port", 8080)

                if not proxy_host:
                    logger.warning("全局代理设置中未指定服务器地址")
                    return None

                # 构建代理URL
                if global_settings.get("require_auth", False):
                    username = global_settings.get("proxy_username", "")
                    password = global_settings.get("proxy_password", "")

                    if username and password:
                        proxy_url = f"{proxy_type}://{username}:{password}@{proxy_host}:{proxy_port}"
                    else:
                        proxy_url = f"{proxy_type}://{proxy_host}:{proxy_port}"
                else:
                    proxy_url = f"{proxy_type}://{proxy_host}:{proxy_port}"

                logger.info(f"使用全局单一代理: {proxy_url}")
            if proxy_url:
                # 记录当前使用的代理
                self.current_proxy = proxy_url
                logger.info(f"使用全局代理: {proxy_url}")

                # 从URL中提取类型和地址
                if '://' in proxy_url:
                    proxy_type, address = proxy_url.split('://', 1)
                    return {'type': proxy_type, 'address': address}
                else:
                    # 如果没有协议前缀，默认为HTTP
                    return {'type': 'http', 'address': proxy_url}

            return None

        # 如果使用本地代理
        if self.enable_proxy and self.proxy_list:
            # 从代理列表中随机选择一个
            proxy_address = random.choice(self.proxy_list)

            # 记录当前使用的代理
            self.current_proxy = f"{self.proxy_type.lower()}://{proxy_address}"
            logger.info(f"使用本地代理: {self.proxy_type} {proxy_address}")

            return {'type': self.proxy_type, 'address': proxy_address}

        return None

    def apply_proxy_settings(self, chrome_options):
        """应用代理设置到Chrome选项

        Args:
            chrome_options: Chrome选项对象

        Returns:
            Options: 更新后的Chrome选项
        """
        # 如果使用全局代理
        if self.use_global_proxy:
            # 获取全局代理设置
            try:
                # 尝试使用新参数
                global_settings = get_proxy_settings(check_enabled=False)
            except TypeError:
                # 如果旧版本不支持check_enabled参数，使用默认调用
                global_settings = get_proxy_settings()

            if not global_settings:
                logger.warning("未找到全局代理设置")
                return chrome_options

            # 如果启用了IP轮换，从代理列表中选择一个
            if global_settings.get("enable_rotation", False):
                proxy_list = global_settings.get("proxy_list", "").strip()

                if proxy_list:
                    proxies = [p.strip() for p in proxy_list.split(",") if p.strip()]

                    if proxies:
                        proxy_url = random.choice(proxies)
                        logger.info(f"从全局代理列表中选择代理: {proxy_url}")

                        # 移除协议前缀
                        if "://" in proxy_url:
                            proxy_address = proxy_url.split("://", 1)[1]
                        else:
                            proxy_address = proxy_url

                        chrome_options.add_argument(f'--proxy-server={proxy_address}')
                        self.current_proxy = proxy_url
                        logger.info(f"已应用全局代理到Chrome: {proxy_url}")
            else:
                # 否则使用单个代理
                proxy_type = global_settings.get("proxy_type", "HTTP").lower()
                proxy_host = global_settings.get("proxy_host", "")
                proxy_port = global_settings.get("proxy_port", 8080)

                if proxy_host:
                    # 构建代理URL
                    if global_settings.get("require_auth", False):
                        username = global_settings.get("proxy_username", "")
                        password = global_settings.get("proxy_password", "")

                        if username and password:
                            proxy_url = f"{proxy_type}://{username}:{password}@{proxy_host}:{proxy_port}"
                            proxy_address = f"{username}:{password}@{proxy_host}:{proxy_port}"
                        else:
                            proxy_url = f"{proxy_type}://{proxy_host}:{proxy_port}"
                            proxy_address = f"{proxy_host}:{proxy_port}"
                    else:
                        proxy_url = f"{proxy_type}://{proxy_host}:{proxy_port}"
                        proxy_address = f"{proxy_host}:{proxy_port}"

                    chrome_options.add_argument(f'--proxy-server={proxy_address}')
                    self.current_proxy = proxy_url
                    logger.info(f"已应用全局单一代理到Chrome: {proxy_url}")

            return chrome_options

        # 如果使用本地代理
        if not self.enable_proxy:
            return chrome_options

        proxy = self.get_random_proxy()
        if not proxy:
            return chrome_options

        proxy_type = proxy['type'].lower()
        proxy_address = proxy['address']

        if proxy_type == 'http':
            chrome_options.add_argument(f'--proxy-server=http://{proxy_address}')
        elif proxy_type == 'socks5':
            chrome_options.add_argument(f'--proxy-server=socks5://{proxy_address}')
        elif proxy_type == '动态代理池':
            # 对于动态代理池，假设是HTTP代理
            chrome_options.add_argument(f'--proxy-server=http://{proxy_address}')

        return chrome_options

    def should_switch_proxy(self, operation_type=None):
        """判断是否应该切换代理

        Args:
            operation_type: 操作类型，可以是'account'或'operation'

        Returns:
            bool: 是否应该切换代理
        """
        if not self.auto_switch_proxy:
            return False

        if self.proxy_switch_interval == '每账号切换一次' and operation_type == 'account':
            return True
        elif self.proxy_switch_interval == '每操作切换一次' and operation_type == 'operation':
            return True
        elif self.proxy_switch_interval == '每30分钟切换一次':
            # 这里需要时间检查逻辑，简化处理
            return False
        elif self.proxy_switch_interval == '每60分钟切换一次':
            # 这里需要时间检查逻辑，简化处理
            return False

        return False

    def configure_chrome_options(self, headless=False):
        """配置Chrome选项

        Args:
            headless: 是否使用无头模式

        Returns:
            Options: 配置好的Chrome选项
        """
        chrome_options = Options()

        # 无头模式设置
        if headless:
            chrome_options.add_argument('--headless')

        # 禁用GPU加速，避免某些环境下的问题
        chrome_options.add_argument('--disable-gpu')

        # 添加代理设置
        chrome_options = self.apply_proxy_settings(chrome_options)

        # 其他通用设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-infobars')
        chrome_options.add_argument('--log-level=3')  # 只显示严重错误

        return chrome_options

    def create_driver(self, driver_path=None, headless=False):
        """创建Chrome WebDriver

        Args:
            driver_path: ChromeDriver路径
            headless: 是否使用无头模式

        Returns:
            WebDriver: 配置好的WebDriver对象
        """
        options = self.configure_chrome_options(headless)

        # 创建Service对象
        if driver_path:
            service = Service(executable_path=driver_path)
            driver = webdriver.Chrome(service=service, options=options)
        else:
            # 使用ChromeDriver管理器创建浏览器
            from app.utils.chromedriver_manager import create_chrome_driver
            driver = create_chrome_driver(options)

        # 设置窗口大小
        driver.set_window_size(1200, 900)

        return driver
