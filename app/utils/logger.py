#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
全局日志系统 - 用于记录应用程序各个部分的日志
"""

import logging
import os
import datetime
from PyQt5.QtCore import QObject, pyqtSignal

# 定义日志级别
DEBUG = "DEBUG"
INFO = "INFO"
SUCCESS = "SUCCESS"  # 自定义成功级别
WARNING = "WARNING"
ERROR = "ERROR"
CRITICAL = "CRITICAL"
ACCOUNT_FAIL = "ACCOUNT_FAIL"  # 自定义账号失败级别

# 日志等级中文翻译
LOG_LEVEL_NAMES = {
    DEBUG: "调试",
    INFO: "信息",
    SUCCESS: "成功",
    WARNING: "警告",
    ERROR: "错误",
    CRITICAL: "严重错误",
    ACCOUNT_FAIL: "账号失败"
}

class Logger(QObject):
    """全局日志管理器"""

    # 定义日志信号，可以被UI订阅
    log_added = pyqtSignal(str, str)  # (message, level)

    _instance = None

    @classmethod
    def instance(cls):
        """单例模式获取日志实例"""
        if cls._instance is None:
            cls._instance = Logger()
        return cls._instance

    def __init__(self):
        """初始化日志记录器"""
        super().__init__()

        # 创建Python标准日志记录器
        self.logger = logging.getLogger("toutiao")
        self.logger.setLevel(logging.DEBUG)

        # 当前选择的日志等级，None表示显示所有等级
        self.selected_level = None

        # 启动静默模式，禁用UI日志输出
        self.startup_silent_mode = True

        # 确保日志目录存在
        log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "logs")
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 创建文件处理器
        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        log_file = os.path.join(log_dir, f"toutiao_{current_date}.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)

        # 设置日志格式
        formatter = logging.Formatter('[%(asctime)s] [%(levelname)s] %(message)s')
        file_handler.setFormatter(formatter)

        # 添加处理器
        self.logger.addHandler(file_handler)

    def set_level(self, level):
        """设置当前选择的日志等级

        Args:
            level: 日志等级，如DEBUG、INFO等，None表示显示所有等级
        """
        self.selected_level = level

    def enable_ui_logging(self):
        """启用UI日志输出（结束启动静默模式）"""
        self.startup_silent_mode = False

    def disable_ui_logging(self):
        """禁用UI日志输出（启动静默模式）"""
        self.startup_silent_mode = True

    def should_emit_log(self, level):
        """检查当前日志是否应该发送

        Args:
            level: 日志等级

        Returns:
            bool: 是否应该发送日志
        """
        # 如果处于启动静默模式，不发送UI日志
        if self.startup_silent_mode:
            return False

        # 如果未设置选择的日志等级，或者日志等级与选择的相符，则发送
        return self.selected_level is None or level == self.selected_level

    def debug(self, message):
        """记录调试信息"""
        self.logger.debug(message)
        if self.should_emit_log(DEBUG):
            self.log_added.emit(message, DEBUG)

    def info(self, message):
        """记录一般信息"""
        self.logger.info(message)
        if self.should_emit_log(INFO):
            self.log_added.emit(message, INFO)

    def success(self, message):
        """记录成功信息"""
        # Python logging没有success级别，映射到info
        self.logger.info(f"[SUCCESS] {message}")
        if self.should_emit_log(SUCCESS):
            self.log_added.emit(message, SUCCESS)

    def warning(self, message):
        """记录警告信息"""
        self.logger.warning(message)
        if self.should_emit_log(WARNING):
            self.log_added.emit(message, WARNING)

    def error(self, message):
        """记录错误信息"""
        self.logger.error(message)
        if self.should_emit_log(ERROR):
            self.log_added.emit(message, ERROR)

    def critical(self, message):
        """记录严重错误信息"""
        self.logger.critical(message)
        if self.should_emit_log(CRITICAL):
            self.log_added.emit(message, CRITICAL)

    def account_fail(self, message):
        """记录账号失败信息"""
        # Python logging没有account_fail级别，映射到error
        self.logger.error(f"[ACCOUNT_FAIL] {message}")
        if self.should_emit_log(ACCOUNT_FAIL):
            self.log_added.emit(message, ACCOUNT_FAIL)

# 创建全局实例
logger = Logger.instance()

# 便捷函数，可以直接导入使用
def debug(message):
    logger.debug(message)

def info(message):
    logger.info(message)

def success(message):
    logger.success(message)

def warning(message):
    logger.warning(message)

def error(message):
    logger.error(message)

def critical(message):
    logger.critical(message)

def account_fail(message):
    logger.account_fail(message)