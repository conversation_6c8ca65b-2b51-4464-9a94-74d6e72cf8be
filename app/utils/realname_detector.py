#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
实名认证状态检测器

用于检测头条媒体平台账号的实名认证状态
通过检测页面中的「立即完善」文本来判断账号是否已实名认证
"""

import time
from typing import Tuple, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from app.utils.logger import info, warning, error, debug


class RealnameDetector:
    """实名认证状态检测器"""
    
    # 检测关键词
    UNVERIFIED_KEYWORDS = [
        "立即完善",
        "完善信息", 
        "实名认证",
        "身份认证",
        "认证信息",
        "完善资料"
    ]
    
    # 检测超时时间（秒）
    DETECTION_TIMEOUT = 10
    
    def __init__(self):
        """初始化检测器"""
        self.detection_count = 0
        self.success_count = 0
        self.failed_count = 0
    
    def detect_realname_status(self, driver: webdriver.Chrome, account_id: str = None) -> <PERSON><PERSON>[bool, str]:
        """
        检测账号实名认证状态
        
        Args:
            driver: WebDriver实例
            account_id: 账号ID（用于日志）
        
        Returns:
            Tuple[bool, str]: (是否已实名, 检测消息)
            - True: 已实名认证
            - False: 未实名认证
        """
        try:
            self.detection_count += 1
            account_info = f"账号 {account_id}" if account_id else "当前账号"
            
            info(f"🔍 开始检测 {account_info} 的实名认证状态...")
            
            # 等待页面加载完成
            try:
                WebDriverWait(driver, 5).until(
                    EC.presence_of_element_located((By.TAG_NAME, "body"))
                )
            except TimeoutException:
                warning("页面加载超时，继续检测...")
            
            # 方法1: 检测页面文本内容
            is_verified, message = self._detect_by_page_text(driver)
            if message:
                info(f"📋 文本检测结果: {message}")
                if not is_verified:
                    self.failed_count += 1
                    warning(f"❌ {account_info} 未实名认证")
                    return False, f"检测到未实名认证: {message}"
            
            # 方法2: 检测特定元素
            is_verified_elem, elem_message = self._detect_by_elements(driver)
            if elem_message:
                info(f"🔍 元素检测结果: {elem_message}")
                if not is_verified_elem:
                    self.failed_count += 1
                    warning(f"❌ {account_info} 未实名认证")
                    return False, f"检测到未实名认证: {elem_message}"
            
            # 方法3: 检测URL特征
            url_verified, url_message = self._detect_by_url(driver)
            if url_message:
                info(f"🌐 URL检测结果: {url_message}")
            
            # 综合判断
            if is_verified and is_verified_elem and url_verified:
                self.success_count += 1
                info(f"✅ {account_info} 已实名认证")
                return True, "账号已实名认证"
            elif not is_verified or not is_verified_elem:
                self.failed_count += 1
                warning(f"❌ {account_info} 未实名认证")
                return False, "检测到未实名认证标识"
            else:
                # 默认认为已实名（保守策略）
                self.success_count += 1
                info(f"✅ {account_info} 默认判断为已实名")
                return True, "未检测到未实名标识，默认为已实名"
                
        except Exception as e:
            error_msg = f"检测实名状态时出错: {str(e)}"
            error(error_msg)
            # 出错时默认认为已实名（保守策略）
            return True, f"检测出错，默认为已实名: {str(e)}"
    
    def _detect_by_page_text(self, driver: webdriver.Chrome) -> Tuple[bool, str]:
        """通过页面文本检测实名状态"""
        try:
            # 获取页面文本内容
            page_text = driver.execute_script("return document.body.innerText;")
            
            if not page_text:
                return True, "无法获取页面文本"
            
            # 检查是否包含未实名关键词
            for keyword in self.UNVERIFIED_KEYWORDS:
                if keyword in page_text:
                    debug(f"在页面文本中发现未实名关键词: {keyword}")
                    return False, f"发现未实名关键词: {keyword}"
            
            return True, "页面文本检测通过"
            
        except Exception as e:
            debug(f"页面文本检测出错: {str(e)}")
            return True, f"文本检测出错: {str(e)}"
    
    def _detect_by_elements(self, driver: webdriver.Chrome) -> Tuple[bool, str]:
        """通过页面元素检测实名状态"""
        try:
            # 常见的未实名认证元素选择器
            unverified_selectors = [
                # 按钮文本包含关键词 (XPath)
                "//button[contains(text(), '立即完善')]",
                "//button[contains(text(), '完善信息')]",
                "//button[contains(text(), '实名认证')]",
                "//a[contains(text(), '立即完善')]",
                "//a[contains(text(), '完善信息')]",
                "//a[contains(text(), '实名认证')]",
                # 通用文本包含关键词 (XPath)
                "//*[contains(text(), '立即完善')]",
                "//*[contains(text(), '完善信息')]",
                "//*[contains(text(), '实名认证')]",
                # CSS选择器 (移除不支持的:contains伪选择器)
                ".realname-verify",
                ".identity-verify",
                "[data-testid*='realname']",
                "[class*='verify']"
            ]
            
            for selector in unverified_selectors:
                try:
                    if selector.startswith('//') or selector.startswith('/'):
                        # XPath选择器
                        elements = driver.find_elements(By.XPATH, selector)
                    else:
                        # CSS选择器
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    if elements:
                        for element in elements:
                            if element.is_displayed():
                                element_text = element.text.strip()
                                if element_text:
                                    debug(f"发现未实名元素: {selector} -> {element_text}")
                                    return False, f"发现未实名元素: {element_text}"
                except Exception as e:
                    debug(f"检测选择器 {selector} 时出错: {str(e)}")
                    continue
            
            return True, "元素检测通过"
            
        except Exception as e:
            debug(f"元素检测出错: {str(e)}")
            return True, f"元素检测出错: {str(e)}"
    
    def _detect_by_url(self, driver: webdriver.Chrome) -> Tuple[bool, str]:
        """通过URL特征检测实名状态"""
        try:
            current_url = driver.current_url
            
            # 未实名认证相关的URL特征
            unverified_url_patterns = [
                "verify",
                "realname", 
                "identity",
                "certification",
                "auth"
            ]
            
            for pattern in unverified_url_patterns:
                if pattern in current_url.lower():
                    debug(f"URL包含未实名特征: {pattern}")
                    return False, f"URL包含未实名特征: {pattern}"
            
            return True, "URL检测通过"
            
        except Exception as e:
            debug(f"URL检测出错: {str(e)}")
            return True, f"URL检测出错: {str(e)}"
    
    def get_detection_stats(self) -> dict:
        """获取检测统计信息"""
        return {
            "total_detections": self.detection_count,
            "verified_accounts": self.success_count,
            "unverified_accounts": self.failed_count,
            "success_rate": (self.success_count / self.detection_count * 100) if self.detection_count > 0 else 0
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.detection_count = 0
        self.success_count = 0
        self.failed_count = 0


# 全局检测器实例
_detector_instance = None


def get_realname_detector() -> RealnameDetector:
    """获取全局实名检测器实例"""
    global _detector_instance
    if _detector_instance is None:
        _detector_instance = RealnameDetector()
    return _detector_instance


def detect_realname_status(driver: webdriver.Chrome, account_id: str = None) -> Tuple[bool, str]:
    """
    便捷函数：检测账号实名认证状态
    
    Args:
        driver: WebDriver实例
        account_id: 账号ID
    
    Returns:
        Tuple[bool, str]: (是否已实名, 检测消息)
    """
    detector = get_realname_detector()
    return detector.detect_realname_status(driver, account_id)


def get_detection_stats() -> dict:
    """获取检测统计信息"""
    detector = get_realname_detector()
    return detector.get_detection_stats()
