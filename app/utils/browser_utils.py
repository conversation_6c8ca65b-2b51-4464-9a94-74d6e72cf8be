#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
浏览器工具模块
提供浏览器相关的功能函数
"""

import os
import shutil
import tempfile
import stat
import logging

def clean_browser_cache(user_data_dir=None):
    """清理浏览器缓存文件

    Args:
        user_data_dir: 自定义的用户数据目录，如果提供则清理该目录

    Returns:
        tuple: (清理的文件数量, 清理的总大小(MB))
    """
    cleaned_count = 0
    cleaned_size = 0

    try:
        # 清理Chrome临时目录中的缓存文件夹
        temp_path = tempfile.gettempdir()

        # 记录清理前的磁盘空间使用情况
        try:
            c_drive_free_before = shutil.disk_usage("C:").free
        except Exception as disk_error:
            logging.warning(f"获取磁盘空间信息失败: {str(disk_error)}")
            c_drive_free_before = 0

        # 清理临时目录中的Chrome和Selenium相关文件夹
        chrome_temp_patterns = [
            "scoped_dir", "chrome_", "_MEI", "chromedriver",
            ".org.chromium.Chromium", ".com.google.Chrome"
        ]

        for item in os.listdir(temp_path):
            for pattern in chrome_temp_patterns:
                if pattern in item:
                    item_path = os.path.join(temp_path, item)
                    item_size = 0

                    # 获取文件夹大小
                    try:
                        if os.path.isdir(item_path):
                            for dirpath, dirnames, filenames in os.walk(item_path):
                                for f in filenames:
                                    fp = os.path.join(dirpath, f)
                                    if os.path.exists(fp):
                                        item_size += os.path.getsize(fp)
                        else:
                            item_size = os.path.getsize(item_path)
                    except:
                        pass

                    # 删除文件或文件夹
                    try:
                        def remove_readonly(func, path, _):
                            "清除文件的只读属性"
                            os.chmod(path, stat.S_IWRITE)
                            func(path)

                        if os.path.isdir(item_path):
                            shutil.rmtree(item_path, onerror=remove_readonly)
                        else:
                            os.remove(item_path)

                        cleaned_count += 1
                        cleaned_size += item_size / (1024 * 1024)  # 转换为MB
                        logging.info(f"已清理缓存项: {item}, 大小: {item_size/(1024*1024):.2f}MB")
                    except Exception as e:
                        logging.debug(f"清理缓存项 {item} 时出错: {str(e)}")

                    break  # 如果匹配任何模式就跳出内循环

        # 如果提供了用户数据目录，也清理它
        if user_data_dir and os.path.exists(user_data_dir):
            try:
                user_dir_size = 0
                for dirpath, dirnames, filenames in os.walk(user_data_dir):
                    for f in filenames:
                        fp = os.path.join(dirpath, f)
                        if os.path.exists(fp):
                            user_dir_size += os.path.getsize(fp)

                shutil.rmtree(user_data_dir, onerror=remove_readonly)
                cleaned_count += 1
                cleaned_size += user_dir_size / (1024 * 1024)  # 转换为MB
                logging.info(f"已清理用户数据目录: {user_data_dir}, 大小: {user_dir_size/(1024*1024):.2f}MB")
            except Exception as e:
                logging.debug(f"清理用户数据目录 {user_data_dir} 时出错: {str(e)}")

        # 计算磁盘空间变化
        c_drive_free_after = shutil.disk_usage("C:").free
        freed_space = (c_drive_free_after - c_drive_free_before) / (1024 * 1024)  # MB

        if freed_space > 0:
            logging.info(f"清理缓存后释放了 {freed_space:.2f}MB 磁盘空间")

        return (cleaned_count, cleaned_size)

    except Exception as e:
        logging.error(f"清理浏览器缓存出错: {str(e)}")
        return (cleaned_count, cleaned_size)