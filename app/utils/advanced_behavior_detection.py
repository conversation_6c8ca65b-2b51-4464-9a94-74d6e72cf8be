#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级行为检测模块
实现更智能的威胁行为分析和检测
"""

import os
import sys
import time
import ctypes
from typing import List, Dict, Tuple, Optional
from datetime import datetime, timedelta

# 尝试导入可选依赖
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    import win32gui
    import win32con
    import win32process
    WIN32_AVAILABLE = True
except ImportError:
    WIN32_AVAILABLE = False

from .behavior_security_detector import ThreatEvent, ThreatType, ThreatLevel

class AdvancedBehaviorDetector:
    """高级行为检测器"""
    
    def __init__(self):
        self.current_pid = os.getpid()
        self.monitoring_start_time = time.time()
        
        # 内存操作检测配置
        self.memory_scan_patterns = [
            b'\x48\x89\x5C\x24',  # 常见的内存修改模式
            b'\x48\x8B\x05',      # 内存读取模式
            b'\x48\x89\x05',      # 内存写入模式
        ]
        
        # 可疑窗口标题关键词
        self.suspicious_window_keywords = [
            'cheat engine', 'memory scanner', 'process hacker', 'hex editor',
            'ollydbg', 'x64dbg', 'x32dbg', 'windbg', 'ida pro', 'ida disassembler',
            'memory editor', 'game hacker', 'artmoney', 'tsearch', 'scanmem'
        ]
        
        # 可疑进程行为模式
        self.suspicious_behaviors = {
            'high_memory_access': {'threshold': 50, 'weight': 0.7},
            'frequent_process_enum': {'threshold': 10, 'weight': 0.8},
            'debug_api_usage': {'threshold': 5, 'weight': 0.9},
            'memory_protection_change': {'threshold': 3, 'weight': 0.95}
        }
    
    def detect_memory_manipulation_behavior(self) -> List[ThreatEvent]:
        """检测内存操作行为"""
        threats = []

        if not PSUTIL_AVAILABLE:
            return threats

        try:
            # 检测可疑的内存访问模式
            for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
                try:
                    proc_info = proc.info
                    pid = proc_info.get('pid')
                    name = proc_info.get('name', '').lower()
                    memory_info = proc_info.get('memory_info')
                    
                    if pid == self.current_pid or not memory_info:
                        continue
                    
                    # 检测异常的内存使用模式
                    if self._is_suspicious_memory_pattern(proc, memory_info):
                        threat = ThreatEvent(
                            timestamp=datetime.now(),
                            threat_type=ThreatType.MEMORY_MANIPULATION,
                            threat_level=ThreatLevel.MEDIUM,
                            process_name=name,
                            process_id=pid,
                            description=f"检测到进程 {name} 存在可疑的内存操作行为",
                            evidence={
                                'memory_usage': memory_info.rss,
                                'virtual_memory': memory_info.vms,
                                'suspicious_pattern': True
                            },
                            confidence=0.75
                        )
                        threats.append(threat)
                
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            print(f"内存操作检测失败: {e}")
        
        return threats
    
    def detect_process_injection_behavior(self) -> List[ThreatEvent]:
        """检测进程注入行为"""
        threats = []

        if not PSUTIL_AVAILABLE:
            return threats

        try:
            # 检测可疑的进程间操作
            for proc in psutil.process_iter(['pid', 'name', 'num_handles']):
                try:
                    proc_info = proc.info
                    pid = proc_info.get('pid')
                    name = proc_info.get('name', '').lower()
                    num_handles = proc_info.get('num_handles', 0)
                    
                    if pid == self.current_pid:
                        continue
                    
                    # 检测异常的句柄数量（可能表示进程注入）
                    if self._is_suspicious_handle_count(proc, num_handles):
                        # 进一步检查是否有跨进程操作
                        if self._detect_cross_process_operations(pid):
                            threat = ThreatEvent(
                                timestamp=datetime.now(),
                                threat_type=ThreatType.PROCESS_INJECTION,
                                threat_level=ThreatLevel.HIGH,
                                process_name=name,
                                process_id=pid,
                                description=f"检测到进程 {name} 可能正在进行进程注入",
                                evidence={
                                    'handle_count': num_handles,
                                    'cross_process_detected': True
                                },
                                confidence=0.85
                            )
                            threats.append(threat)
                
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            print(f"进程注入检测失败: {e}")
        
        return threats
    
    def detect_suspicious_window_behavior(self) -> List[ThreatEvent]:
        """检测可疑窗口行为"""
        threats = []
        try:
            def enum_windows_callback(hwnd, results):
                try:
                    if win32gui.IsWindowVisible(hwnd) and win32gui.IsWindow(hwnd):
                        window_title = win32gui.GetWindowText(hwnd).lower()
                        if window_title:
                            for keyword in self.suspicious_window_keywords:
                                if keyword in window_title:
                                    # 获取窗口所属进程
                                    _, pid = win32process.GetWindowThreadProcessId(hwnd)
                                    
                                    try:
                                        proc = psutil.Process(pid)
                                        proc_name = proc.name().lower()
                                        
                                        # 检查窗口是否处于活跃状态
                                        if self._is_window_actively_used(hwnd):
                                            threat = ThreatEvent(
                                                timestamp=datetime.now(),
                                                threat_type=ThreatType.CRACK_TOOL_ACTIVE,
                                                threat_level=ThreatLevel.HIGH,
                                                process_name=proc_name,
                                                process_id=pid,
                                                description=f"检测到活跃的可疑工具窗口: {window_title}",
                                                evidence={
                                                    'window_title': window_title,
                                                    'window_handle': hwnd,
                                                    'is_active': True,
                                                    'keyword_matched': keyword
                                                },
                                                confidence=0.9
                                            )
                                            results.append(threat)
                                            break
                    except:
                        pass
                except:
                    pass
            
            win32gui.EnumWindows(enum_windows_callback, threats)
            
        except Exception as e:
            print(f"可疑窗口检测失败: {e}")
        
        return threats
    
    def detect_debugging_api_usage(self) -> List[ThreatEvent]:
        """检测调试API使用"""
        threats = []
        try:
            # 检测当前进程是否被调试
            if self._is_current_process_debugged():
                threat = ThreatEvent(
                    timestamp=datetime.now(),
                    threat_type=ThreatType.DEBUGGER_ATTACHMENT,
                    threat_level=ThreatLevel.CRITICAL,
                    process_name="current_process",
                    process_id=self.current_pid,
                    description="检测到当前进程正在被调试器附加",
                    evidence={
                        'debugger_present': True,
                        'detection_method': 'api_check'
                    },
                    confidence=0.95
                )
                threats.append(threat)
            
            # 检测其他进程的调试状态
            debug_sessions = self._detect_active_debug_sessions()
            for session in debug_sessions:
                threat = ThreatEvent(
                    timestamp=datetime.now(),
                    threat_type=ThreatType.ACTIVE_DEBUGGING,
                    threat_level=ThreatLevel.HIGH,
                    process_name=session['debugger_name'],
                    process_id=session['debugger_pid'],
                    description=f"检测到活跃的调试会话: {session['debugger_name']} -> {session['target_name']}",
                    evidence=session,
                    confidence=0.88
                )
                threats.append(threat)
        
        except Exception as e:
            print(f"调试API检测失败: {e}")
        
        return threats
    
    def detect_reverse_engineering_behavior(self) -> List[ThreatEvent]:
        """检测逆向工程行为"""
        threats = []
        try:
            # 检测文件分析行为
            analysis_tools = self._detect_file_analysis_tools()
            for tool_info in analysis_tools:
                threat = ThreatEvent(
                    timestamp=datetime.now(),
                    threat_type=ThreatType.REVERSE_ENGINEERING,
                    threat_level=ThreatLevel.MEDIUM,
                    process_name=tool_info['name'],
                    process_id=tool_info['pid'],
                    description=f"检测到逆向分析工具: {tool_info['name']}",
                    evidence=tool_info,
                    confidence=0.8
                )
                threats.append(threat)
        
        except Exception as e:
            print(f"逆向工程检测失败: {e}")
        
        return threats
    
    def _is_suspicious_memory_pattern(self, proc, memory_info) -> bool:
        """检测可疑的内存使用模式"""
        try:
            # 检查内存使用是否异常
            rss_mb = memory_info.rss / (1024 * 1024)  # 转换为MB
            vms_mb = memory_info.vms / (1024 * 1024)
            
            # 异常的内存使用模式
            if rss_mb > 500 and vms_mb > 1000:  # 大量内存使用
                return True
            
            # 检查内存增长速度
            try:
                current_memory = proc.memory_info().rss
                time.sleep(0.1)  # 短暂等待
                new_memory = proc.memory_info().rss
                growth_rate = (new_memory - current_memory) / 0.1  # 每秒增长
                
                if growth_rate > 10 * 1024 * 1024:  # 每秒增长超过10MB
                    return True
            except:
                pass
            
            return False
        except:
            return False
    
    def _is_suspicious_handle_count(self, proc, num_handles) -> bool:
        """检测可疑的句柄数量"""
        try:
            # 正常进程的句柄数量通常在几十到几百之间
            if num_handles > 1000:  # 异常高的句柄数
                return True
            
            # 检查句柄增长速度
            try:
                current_handles = proc.num_handles()
                time.sleep(0.5)
                new_handles = proc.num_handles()
                growth_rate = (new_handles - current_handles) / 0.5
                
                if growth_rate > 50:  # 每秒增长超过50个句柄
                    return True
            except:
                pass
            
            return False
        except:
            return False
    
    def _detect_cross_process_operations(self, pid) -> bool:
        """检测跨进程操作"""
        try:
            # 尝试检测进程是否在访问其他进程的内存
            PROCESS_QUERY_INFORMATION = 0x0400
            PROCESS_VM_READ = 0x0010
            
            handle = ctypes.windll.kernel32.OpenProcess(
                PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, False, pid
            )
            
            if handle:
                ctypes.windll.kernel32.CloseHandle(handle)
                # 如果能够打开其他进程的句柄，可能存在跨进程操作
                return True
            
            return False
        except:
            return False
    
    def _is_window_actively_used(self, hwnd) -> bool:
        """检查窗口是否被积极使用"""
        try:
            # 检查窗口是否可见且不是最小化状态
            if not win32gui.IsWindowVisible(hwnd) or win32gui.IsIconic(hwnd):
                return False
            
            # 检查窗口是否在前台或最近活跃
            foreground_window = win32gui.GetForegroundWindow()
            if hwnd == foreground_window:
                return True
            
            # 检查窗口大小（最小化的窗口通常很小）
            try:
                rect = win32gui.GetWindowRect(hwnd)
                width = rect[2] - rect[0]
                height = rect[3] - rect[1]
                
                # 如果窗口太小，可能不是活跃使用
                if width < 100 or height < 100:
                    return False
                
                return True
            except:
                return False
        except:
            return False
    
    def _is_current_process_debugged(self) -> bool:
        """检测当前进程是否被调试"""
        try:
            # 使用多种方法检测调试器
            kernel32 = ctypes.windll.kernel32
            
            # 方法1: IsDebuggerPresent
            if kernel32.IsDebuggerPresent():
                return True
            
            # 方法2: CheckRemoteDebuggerPresent
            debug_present = ctypes.c_bool()
            if kernel32.CheckRemoteDebuggerPresent(
                kernel32.GetCurrentProcess(), ctypes.byref(debug_present)
            ):
                if debug_present.value:
                    return True
            
            # 方法3: NtQueryInformationProcess
            try:
                debug_port = ctypes.c_ulong()
                status = ctypes.windll.ntdll.NtQueryInformationProcess(
                    kernel32.GetCurrentProcess(), 7, 
                    ctypes.byref(debug_port), ctypes.sizeof(debug_port), None
                )
                if status == 0 and debug_port.value != 0:
                    return True
            except:
                pass
            
            return False
        except:
            return False
    
    def _detect_active_debug_sessions(self) -> List[Dict]:
        """检测活跃的调试会话"""
        sessions = []
        try:
            # 检测已知的调试器进程
            debugger_names = [
                'ollydbg.exe', 'x64dbg.exe', 'x32dbg.exe', 'windbg.exe',
                'ida.exe', 'ida64.exe', 'idaq.exe', 'idaq64.exe'
            ]
            
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    name = proc_info.get('name', '').lower()
                    pid = proc_info.get('pid')
                    cmdline = proc_info.get('cmdline', [])
                    
                    if name in debugger_names:
                        # 检查命令行参数，看是否在调试其他进程
                        target_info = self._analyze_debugger_cmdline(cmdline)
                        if target_info:
                            sessions.append({
                                'debugger_name': name,
                                'debugger_pid': pid,
                                'target_name': target_info.get('target_name', 'unknown'),
                                'target_pid': target_info.get('target_pid', 0),
                                'cmdline': ' '.join(cmdline) if cmdline else ''
                            })
                
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            print(f"检测调试会话失败: {e}")
        
        return sessions
    
    def _analyze_debugger_cmdline(self, cmdline) -> Optional[Dict]:
        """分析调试器命令行参数"""
        try:
            if not cmdline:
                return None
            
            cmdline_str = ' '.join(cmdline).lower()
            
            # 检查是否包含目标进程信息
            if '-p' in cmdline_str or '--pid' in cmdline_str:
                # 尝试提取PID
                import re
                pid_match = re.search(r'-p\s+(\d+)|--pid\s+(\d+)', cmdline_str)
                if pid_match:
                    target_pid = int(pid_match.group(1) or pid_match.group(2))
                    try:
                        target_proc = psutil.Process(target_pid)
                        return {
                            'target_pid': target_pid,
                            'target_name': target_proc.name()
                        }
                    except:
                        return {'target_pid': target_pid, 'target_name': 'unknown'}
            
            # 检查是否包含可执行文件路径
            for arg in cmdline[1:]:  # 跳过第一个参数（程序本身）
                if arg.endswith('.exe') or '\\' in arg:
                    return {
                        'target_name': os.path.basename(arg),
                        'target_path': arg
                    }
            
            return None
        except:
            return None
    
    def _detect_file_analysis_tools(self) -> List[Dict]:
        """检测文件分析工具"""
        tools = []
        try:
            analysis_tool_names = [
                'ida.exe', 'ida64.exe', 'idaq.exe', 'idaq64.exe',
                'ghidra.exe', 'radare2.exe', 'r2.exe',
                'pestudio.exe', 'pe-bear.exe', 'cff explorer.exe'
            ]
            
            for proc in psutil.process_iter(['pid', 'name', 'exe', 'create_time']):
                try:
                    proc_info = proc.info
                    name = proc_info.get('name', '').lower()
                    pid = proc_info.get('pid')
                    exe_path = proc_info.get('exe', '')
                    create_time = proc_info.get('create_time', 0)
                    
                    if name in analysis_tool_names:
                        # 检查是否最近启动
                        if time.time() - create_time < 300:  # 5分钟内启动
                            tools.append({
                                'name': name,
                                'pid': pid,
                                'exe_path': exe_path,
                                'create_time': create_time,
                                'recently_started': True
                            })
                
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
        except Exception as e:
            print(f"检测文件分析工具失败: {e}")
        
        return tools
