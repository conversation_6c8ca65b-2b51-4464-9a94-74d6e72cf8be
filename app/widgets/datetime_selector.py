#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的日期时间选择组件
提供完整的日期时间选择功能，支持星期选择、日期范围、时区等
"""

import os
import sys
from datetime import datetime, timedelta, time
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                            QLabel, QTimeEdit, QDateEdit, QCheckBox, QPushButton,
                            QListWidget, QListWidgetItem, QComboBox, QSpinBox,
                            QCalendarWidget, QDateTimeEdit, QMessageBox, QFrame,
                            QGridLayout, QScrollArea, QButtonGroup, QRadioButton)
from PyQt5.QtCore import Qt, QTime, QDate, QDateTime, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QColor, QPalette

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from app.utils.logger import info, warning, error, debug


class WeekdaySelector(QWidget):
    """星期选择器"""
    
    selection_changed = pyqtSignal(list)  # 选择变更信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("选择执行日期")
        title_label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(title_label)
        
        # 快速选择按钮
        quick_layout = QHBoxLayout()
        
        self.all_days_btn = QPushButton("全选")
        self.all_days_btn.clicked.connect(self.select_all_days)
        quick_layout.addWidget(self.all_days_btn)
        
        self.weekdays_btn = QPushButton("工作日")
        self.weekdays_btn.clicked.connect(self.select_weekdays)
        quick_layout.addWidget(self.weekdays_btn)
        
        self.weekends_btn = QPushButton("周末")
        self.weekends_btn.clicked.connect(self.select_weekends)
        quick_layout.addWidget(self.weekends_btn)
        
        self.clear_btn = QPushButton("清空")
        self.clear_btn.clicked.connect(self.clear_selection)
        quick_layout.addWidget(self.clear_btn)
        
        layout.addLayout(quick_layout)
        
        # 星期复选框
        self.weekday_checkboxes = {}
        weekdays = [
            (1, "周一"), (2, "周二"), (3, "周三"), (4, "周四"),
            (5, "周五"), (6, "周六"), (0, "周日")  # 0表示周日
        ]
        
        weekday_layout = QGridLayout()
        for i, (day_num, day_name) in enumerate(weekdays):
            checkbox = QCheckBox(day_name)
            checkbox.stateChanged.connect(self.on_selection_changed)
            self.weekday_checkboxes[day_num] = checkbox
            
            row = i // 4
            col = i % 4
            weekday_layout.addWidget(checkbox, row, col)
        
        weekday_widget = QWidget()
        weekday_widget.setLayout(weekday_layout)
        layout.addWidget(weekday_widget)
        
        # 设置样式
        self.setStyleSheet("""
            QPushButton {
                padding: 4px 8px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                background-color: #ecf0f1;
            }
            QPushButton:hover {
                background-color: #d5dbdb;
            }
            QPushButton:pressed {
                background-color: #bdc3c7;
            }
            QCheckBox {
                spacing: 5px;
            }
        """)
        
        self.setLayout(layout)
        
        # 默认选择工作日
        self.select_weekdays()

    def select_all_days(self):
        """选择所有天"""
        for checkbox in self.weekday_checkboxes.values():
            checkbox.setChecked(True)

    def select_weekdays(self):
        """选择工作日（周一到周五）"""
        for day_num, checkbox in self.weekday_checkboxes.items():
            checkbox.setChecked(1 <= day_num <= 5)

    def select_weekends(self):
        """选择周末（周六和周日）"""
        for day_num, checkbox in self.weekday_checkboxes.items():
            checkbox.setChecked(day_num in [0, 6])

    def clear_selection(self):
        """清空选择"""
        for checkbox in self.weekday_checkboxes.values():
            checkbox.setChecked(False)

    def on_selection_changed(self):
        """选择变更处理"""
        selected_days = self.get_selected_days()
        self.selection_changed.emit(selected_days)

    def get_selected_days(self):
        """获取选中的星期"""
        selected = []
        for day_num, checkbox in self.weekday_checkboxes.items():
            if checkbox.isChecked():
                selected.append(day_num)
        return sorted(selected)

    def set_selected_days(self, days):
        """设置选中的星期"""
        for day_num, checkbox in self.weekday_checkboxes.items():
            checkbox.setChecked(day_num in days)


class DateRangeSelector(QWidget):
    """日期范围选择器"""
    
    range_changed = pyqtSignal(QDate, QDate)  # 日期范围变更信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("执行日期范围")
        title_label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(title_label)
        
        # 启用日期范围复选框
        self.enable_range = QCheckBox("启用日期范围限制")
        self.enable_range.stateChanged.connect(self.on_enable_changed)
        layout.addWidget(self.enable_range)
        
        # 日期选择区域
        date_layout = QGridLayout()
        
        # 开始日期
        date_layout.addWidget(QLabel("开始日期:"), 0, 0)
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate())
        self.start_date.setCalendarPopup(True)
        self.start_date.dateChanged.connect(self.on_date_changed)
        date_layout.addWidget(self.start_date, 0, 1)
        
        # 结束日期
        date_layout.addWidget(QLabel("结束日期:"), 1, 0)
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate().addDays(30))  # 默认30天后
        self.end_date.setCalendarPopup(True)
        self.end_date.dateChanged.connect(self.on_date_changed)
        date_layout.addWidget(self.end_date, 1, 1)
        
        # 快速选择按钮
        quick_layout = QHBoxLayout()
        
        self.week_btn = QPushButton("一周")
        self.week_btn.clicked.connect(lambda: self.set_range_days(7))
        quick_layout.addWidget(self.week_btn)
        
        self.month_btn = QPushButton("一个月")
        self.month_btn.clicked.connect(lambda: self.set_range_days(30))
        quick_layout.addWidget(self.month_btn)
        
        self.quarter_btn = QPushButton("三个月")
        self.quarter_btn.clicked.connect(lambda: self.set_range_days(90))
        quick_layout.addWidget(self.quarter_btn)
        
        self.unlimited_btn = QPushButton("无限制")
        self.unlimited_btn.clicked.connect(self.disable_range)
        quick_layout.addWidget(self.unlimited_btn)
        
        date_layout.addLayout(quick_layout, 2, 0, 1, 2)
        
        date_widget = QWidget()
        date_widget.setLayout(date_layout)
        layout.addWidget(date_widget)
        
        # 设置样式
        self.setStyleSheet("""
            QDateEdit {
                padding: 4px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
            }
            QPushButton {
                padding: 4px 8px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                background-color: #ecf0f1;
            }
            QPushButton:hover {
                background-color: #d5dbdb;
            }
        """)
        
        self.setLayout(layout)
        
        # 初始状态
        self.on_enable_changed()

    def on_enable_changed(self):
        """启用状态变更处理"""
        enabled = self.enable_range.isChecked()
        self.start_date.setEnabled(enabled)
        self.end_date.setEnabled(enabled)
        self.week_btn.setEnabled(enabled)
        self.month_btn.setEnabled(enabled)
        self.quarter_btn.setEnabled(enabled)
        
        if enabled:
            self.on_date_changed()

    def on_date_changed(self):
        """日期变更处理"""
        if self.enable_range.isChecked():
            start_date = self.start_date.date()
            end_date = self.end_date.date()
            
            # 确保结束日期不早于开始日期
            if end_date < start_date:
                self.end_date.setDate(start_date.addDays(1))
                end_date = self.end_date.date()
            
            self.range_changed.emit(start_date, end_date)

    def set_range_days(self, days):
        """设置日期范围（从今天开始的天数）"""
        self.enable_range.setChecked(True)
        self.start_date.setDate(QDate.currentDate())
        self.end_date.setDate(QDate.currentDate().addDays(days))
        self.on_enable_changed()

    def disable_range(self):
        """禁用日期范围"""
        self.enable_range.setChecked(False)
        self.on_enable_changed()

    def get_date_range(self):
        """获取日期范围"""
        if self.enable_range.isChecked():
            return self.start_date.date(), self.end_date.date()
        return None, None

    def set_date_range(self, start_date, end_date):
        """设置日期范围"""
        if start_date and end_date:
            self.enable_range.setChecked(True)
            self.start_date.setDate(start_date)
            self.end_date.setDate(end_date)
        else:
            self.enable_range.setChecked(False)
        self.on_enable_changed()


class CustomTimePointWidget(QWidget):
    """自定义时间点组件"""
    
    remove_requested = pyqtSignal()  # 删除请求信号
    time_changed = pyqtSignal()      # 时间变更信号
    
    def __init__(self, datetime_value=None, parent=None):
        super().__init__(parent)
        self.init_ui()
        
        if datetime_value:
            self.set_datetime(datetime_value)
        else:
            # 默认设置为当前时间的下一个小时
            next_hour = datetime.now().replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
            self.set_datetime(next_hour)

    def init_ui(self):
        """初始化用户界面"""
        layout = QHBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 日期时间选择器
        self.datetime_edit = QDateTimeEdit()
        self.datetime_edit.setDateTime(QDateTime.currentDateTime())
        self.datetime_edit.setCalendarPopup(True)
        self.datetime_edit.setDisplayFormat("yyyy-MM-dd hh:mm")
        self.datetime_edit.dateTimeChanged.connect(self.time_changed.emit)
        layout.addWidget(self.datetime_edit)
        
        # 删除按钮
        self.remove_btn = QPushButton("删除")
        self.remove_btn.clicked.connect(self.remove_requested.emit)
        self.remove_btn.setMaximumWidth(60)
        layout.addWidget(self.remove_btn)
        
        # 设置样式
        self.setStyleSheet("""
            QDateTimeEdit {
                padding: 4px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                min-width: 150px;
            }
            QPushButton {
                padding: 4px 8px;
                border: 1px solid #e74c3c;
                border-radius: 3px;
                background-color: #e74c3c;
                color: white;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        self.setLayout(layout)

    def get_datetime(self):
        """获取日期时间"""
        return self.datetime_edit.dateTime().toPyDateTime()

    def set_datetime(self, datetime_value):
        """设置日期时间"""
        if isinstance(datetime_value, datetime):
            qt_datetime = QDateTime.fromSecsSinceEpoch(int(datetime_value.timestamp()))
            self.datetime_edit.setDateTime(qt_datetime)


class CustomTimeSelector(QWidget):
    """自定义时间选择器"""
    
    times_changed = pyqtSignal(list)  # 时间列表变更信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.time_widgets = []
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 标题和添加按钮
        header_layout = QHBoxLayout()
        
        title_label = QLabel("自定义执行时间")
        title_label.setFont(QFont("Arial", 10, QFont.Bold))
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        self.add_btn = QPushButton("+ 添加时间")
        self.add_btn.clicked.connect(self.add_time_point)
        header_layout.addWidget(self.add_btn)
        
        layout.addLayout(header_layout)
        
        # 滚动区域用于显示时间点列表
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setMaximumHeight(300)
        
        self.time_list_widget = QWidget()
        self.time_list_layout = QVBoxLayout(self.time_list_widget)
        self.time_list_layout.addStretch()
        
        self.scroll_area.setWidget(self.time_list_widget)
        layout.addWidget(self.scroll_area)
        
        # 快速添加按钮
        quick_layout = QHBoxLayout()
        
        self.add_now_btn = QPushButton("添加当前时间")
        self.add_now_btn.clicked.connect(self.add_current_time)
        quick_layout.addWidget(self.add_now_btn)
        
        self.add_tomorrow_btn = QPushButton("添加明天同一时间")
        self.add_tomorrow_btn.clicked.connect(self.add_tomorrow_time)
        quick_layout.addWidget(self.add_tomorrow_btn)
        
        self.clear_all_btn = QPushButton("清空所有")
        self.clear_all_btn.clicked.connect(self.clear_all_times)
        quick_layout.addWidget(self.clear_all_btn)
        
        layout.addLayout(quick_layout)
        
        # 设置样式
        self.setStyleSheet("""
            QPushButton {
                padding: 6px 12px;
                border: 1px solid #3498db;
                border-radius: 3px;
                background-color: #3498db;
                color: white;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QScrollArea {
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                background-color: #f8f9fa;
            }
        """)
        
        self.setLayout(layout)
        
        # 添加一个默认时间点
        self.add_time_point()

    def add_time_point(self, datetime_value=None):
        """添加时间点"""
        time_widget = CustomTimePointWidget(datetime_value)
        time_widget.remove_requested.connect(lambda: self.remove_time_point(time_widget))
        time_widget.time_changed.connect(self.on_times_changed)
        
        # 插入到stretch之前
        self.time_list_layout.insertWidget(self.time_list_layout.count() - 1, time_widget)
        self.time_widgets.append(time_widget)
        
        self.on_times_changed()

    def remove_time_point(self, widget):
        """删除时间点"""
        if len(self.time_widgets) <= 1:
            QMessageBox.warning(self, "警告", "至少需要保留一个时间点")
            return
            
        self.time_list_layout.removeWidget(widget)
        self.time_widgets.remove(widget)
        widget.deleteLater()
        
        self.on_times_changed()

    def add_current_time(self):
        """添加当前时间"""
        self.add_time_point(datetime.now())

    def add_tomorrow_time(self):
        """添加明天同一时间"""
        tomorrow = datetime.now() + timedelta(days=1)
        self.add_time_point(tomorrow)

    def clear_all_times(self):
        """清空所有时间"""
        if QMessageBox.question(self, "确认", "确定要清空所有时间点吗？") == QMessageBox.Yes:
            for widget in self.time_widgets[:]:
                self.remove_time_point(widget)
            # 添加一个默认时间点
            self.add_time_point()

    def on_times_changed(self):
        """时间变更处理"""
        times = self.get_times()
        self.times_changed.emit(times)

    def get_times(self):
        """获取所有时间点"""
        times = []
        for widget in self.time_widgets:
            times.append(widget.get_datetime())
        return sorted(times)

    def set_times(self, times):
        """设置时间点列表"""
        # 清空现有时间点
        for widget in self.time_widgets[:]:
            self.time_list_layout.removeWidget(widget)
            widget.deleteLater()
        self.time_widgets.clear()
        
        # 添加新时间点
        if times:
            for time_value in times:
                self.add_time_point(time_value)
        else:
            # 如果没有时间点，添加一个默认的
            self.add_time_point()


class TimezoneSelector(QWidget):
    """时区选择器"""

    timezone_changed = pyqtSignal(str)  # 时区变更信号

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        layout = QHBoxLayout()

        # 时区标签
        timezone_label = QLabel("时区:")
        layout.addWidget(timezone_label)

        # 时区选择下拉框
        self.timezone_combo = QComboBox()
        self.timezone_combo.addItems([
            "UTC+8 (北京时间)",
            "UTC+0 (格林威治时间)",
            "UTC-5 (美国东部时间)",
            "UTC-8 (美国太平洋时间)",
            "UTC+9 (日本时间)",
            "UTC+1 (欧洲中部时间)",
            "UTC+3 (莫斯科时间)",
            "UTC+5:30 (印度时间)"
        ])
        self.timezone_combo.setCurrentText("UTC+8 (北京时间)")
        self.timezone_combo.currentTextChanged.connect(self.on_timezone_changed)
        layout.addWidget(self.timezone_combo)

        layout.addStretch()

        self.setLayout(layout)

    def on_timezone_changed(self):
        """时区变更处理"""
        timezone = self.get_timezone()
        self.timezone_changed.emit(timezone)

    def get_timezone(self):
        """获取当前选择的时区"""
        text = self.timezone_combo.currentText()
        # 提取时区偏移量
        if "UTC+8" in text:
            return "+08:00"
        elif "UTC+0" in text:
            return "+00:00"
        elif "UTC-5" in text:
            return "-05:00"
        elif "UTC-8" in text:
            return "-08:00"
        elif "UTC+9" in text:
            return "+09:00"
        elif "UTC+1" in text:
            return "+01:00"
        elif "UTC+3" in text:
            return "+03:00"
        elif "UTC+5:30" in text:
            return "+05:30"
        return "+08:00"  # 默认北京时间

    def set_timezone(self, timezone_offset):
        """设置时区"""
        timezone_map = {
            "+08:00": "UTC+8 (北京时间)",
            "+00:00": "UTC+0 (格林威治时间)",
            "-05:00": "UTC-5 (美国东部时间)",
            "-08:00": "UTC-8 (美国太平洋时间)",
            "+09:00": "UTC+9 (日本时间)",
            "+01:00": "UTC+1 (欧洲中部时间)",
            "+03:00": "UTC+3 (莫斯科时间)",
            "+05:30": "UTC+5:30 (印度时间)"
        }

        if timezone_offset in timezone_map:
            self.timezone_combo.setCurrentText(timezone_map[timezone_offset])


class TimePreviewWidget(QWidget):
    """时间预览组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

        # 定时器用于更新预览
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_preview)
        self.update_timer.start(1000)  # 每秒更新一次

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("执行时间预览")
        title_label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(title_label)

        # 当前时间显示
        self.current_time_label = QLabel()
        self.current_time_label.setStyleSheet("""
            QLabel {
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                background-color: #ecf0f1;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        layout.addWidget(self.current_time_label)

        # 下次执行时间显示
        self.next_time_label = QLabel()
        self.next_time_label.setStyleSheet("""
            QLabel {
                padding: 5px;
                border: 1px solid #27ae60;
                border-radius: 3px;
                background-color: #d5f4e6;
                font-family: 'Consolas', 'Monaco', monospace;
                color: #27ae60;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.next_time_label)

        # 倒计时显示
        self.countdown_label = QLabel()
        self.countdown_label.setStyleSheet("""
            QLabel {
                padding: 5px;
                border: 1px solid #3498db;
                border-radius: 3px;
                background-color: #ebf3fd;
                font-family: 'Consolas', 'Monaco', monospace;
                color: #3498db;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.countdown_label)

        self.setLayout(layout)

        # 初始化显示
        self.next_execution_time = None
        self.update_preview()

    def set_next_execution_time(self, next_time):
        """设置下次执行时间"""
        self.next_execution_time = next_time
        self.update_preview()

    def update_preview(self):
        """更新预览显示"""
        try:
            now = datetime.now()

            # 更新当前时间
            self.current_time_label.setText(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")

            # 更新下次执行时间
            if self.next_execution_time:
                self.next_time_label.setText(f"下次执行: {self.next_execution_time.strftime('%Y-%m-%d %H:%M:%S')}")

                # 计算倒计时
                if self.next_execution_time > now:
                    delta = self.next_execution_time - now
                    days = delta.days
                    hours, remainder = divmod(delta.seconds, 3600)
                    minutes, seconds = divmod(remainder, 60)

                    if days > 0:
                        countdown_text = f"倒计时: {days}天 {hours:02d}:{minutes:02d}:{seconds:02d}"
                    else:
                        countdown_text = f"倒计时: {hours:02d}:{minutes:02d}:{seconds:02d}"

                    self.countdown_label.setText(countdown_text)
                else:
                    self.countdown_label.setText("倒计时: 已过期")
            else:
                self.next_time_label.setText("下次执行: 未设置")
                self.countdown_label.setText("倒计时: --:--:--")

        except Exception as e:
            error(f"更新时间预览失败: {str(e)}")


class TimeValidator:
    """时间验证器"""

    @staticmethod
    def validate_time_interval(times, min_interval_minutes=30):
        """验证时间间隔"""
        if len(times) < 2:
            return True, "时间点数量正常"

        sorted_times = sorted(times)
        for i in range(1, len(sorted_times)):
            delta = sorted_times[i] - sorted_times[i-1]
            if delta.total_seconds() < min_interval_minutes * 60:
                return False, f"时间点间隔过短，建议至少间隔{min_interval_minutes}分钟"

        return True, "时间间隔正常"

    @staticmethod
    def validate_future_time(times):
        """验证时间是否在未来"""
        now = datetime.now()
        past_times = [t for t in times if t <= now]

        if past_times:
            return False, f"发现{len(past_times)}个过期时间点，请选择未来的时间"

        return True, "所有时间点都在未来"

    @staticmethod
    def validate_reasonable_frequency(times, max_daily_executions=24):
        """验证执行频率是否合理"""
        if len(times) > max_daily_executions:
            return False, f"执行频率过高，建议每天不超过{max_daily_executions}次"

        return True, "执行频率合理"

    @staticmethod
    def validate_weekday_selection(weekdays):
        """验证星期选择"""
        if not weekdays:
            return False, "请至少选择一天"

        return True, "星期选择正常"

    @staticmethod
    def validate_date_range(start_date, end_date):
        """验证日期范围"""
        if start_date and end_date:
            if end_date < start_date:
                return False, "结束日期不能早于开始日期"

            if start_date < QDate.currentDate():
                return False, "开始日期不能早于今天"

            delta_days = start_date.daysTo(end_date)
            if delta_days > 365:
                return False, "日期范围不能超过一年"

        return True, "日期范围正常"


class ConflictDetector:
    """时间冲突检测器"""

    @staticmethod
    def detect_time_conflicts(times, buffer_minutes=15):
        """检测时间冲突"""
        if len(times) < 2:
            return []

        conflicts = []
        sorted_times = sorted(times)

        for i in range(1, len(sorted_times)):
            delta = sorted_times[i] - sorted_times[i-1]
            if delta.total_seconds() < buffer_minutes * 60:
                conflicts.append({
                    'time1': sorted_times[i-1],
                    'time2': sorted_times[i],
                    'interval': delta.total_seconds() / 60
                })

        return conflicts

    @staticmethod
    def suggest_time_adjustments(conflicts):
        """建议时间调整"""
        suggestions = []

        for conflict in conflicts:
            suggestion = {
                'original_time': conflict['time2'],
                'suggested_time': conflict['time1'] + timedelta(minutes=30),
                'reason': f"与{conflict['time1'].strftime('%H:%M')}间隔过短"
            }
            suggestions.append(suggestion)

        return suggestions
