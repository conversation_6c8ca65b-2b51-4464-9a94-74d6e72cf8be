#!/usr/bin/env python
# -*- coding: utf-8 -*-
# pyright: reportAssignmentType=false
# pyright: reportOptionalMemberAccess=false
# pyright: reportAttributeAccessIssue=false
# pyright: reportUnusedImport=false
# pyright: reportUnknownVariableType=false
# pyright: reportUnknownArgumentType=false
"""
头条号养号模块
实现自动化养号功能，保持账号活跃度
"""

import os
import sys
import time
import random
import logging
import json
import traceback
import threading
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal, QThread, QMetaObject, Qt, pyqtSlot, QTimer
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# 尝试导入浏览器代理管理器
try:
    from app.utils.browser_proxy import BrowserProxyManager
except ImportError:
    BrowserProxyManager = None

# 尝试导入养号代理管理器
try:
    from app.utils.nurture_proxy import NurtureProxyManager
except ImportError:
    NurtureProxyManager = None

# 尝试导入批量存稿模块的代理工具
try:
    from app.utils.proxy_utils import (
        apply_proxy_to_chrome_options,
        rotate_proxy_if_needed,
        get_current_proxy,
        force_rotate_proxy,
        get_random_proxy_from_list,
        test_proxy
    )
    import logging
    logging.info("成功导入批量存稿代理工具模块")
except ImportError:
    # 如果导入失败，提供空函数
    def apply_proxy_to_chrome_options(options):
        return False

    def rotate_proxy_if_needed(force=False):
        return False

    def force_rotate_proxy():
        return False

    def get_current_proxy():
        return None

    def get_random_proxy_from_list():
        return None

    def test_proxy(proxy_url, timeout=5):
        return False

    import logging
    logging.warning("未找到批量存稿代理工具模块，代理功能将不可用")

class NurtureWorker(QObject):
    """养号工作类"""
    # 定义信号
    status_update = pyqtSignal(str)  # 状态更新信号
    progress_update = pyqtSignal(int, int)  # 进度更新信号 (当前, 总数)
    log_message = pyqtSignal(str, str)  # 日志信号 (消息, 级别)
    error_message = pyqtSignal(str, str)  # 错误信号 (标题, 消息)
    finished = pyqtSignal()  # 完成信号
    account_finished = pyqtSignal(int)  # 单个账号完成信号

    def __init__(self, accounts, settings):
        """初始化养号工作类

        Args:
            accounts: 账号列表
            settings: 养号设置
        """
        super().__init__()
        self.accounts = accounts
        self.settings = settings
        self.running = False
        self.current_account = None

        # 多线程相关
        self.max_threads = self.settings.get('max_threads', 3)  # 默认最多3个线程
        self.active_threads = 0
        self.thread_lock = threading.Lock()
        self.account_queue = []
        self.completed_accounts = 0
        self.total_accounts = len(accounts)

        # 添加账号状态跟踪集合
        self.processing_accounts = set()  # 正在处理的账号
        self.processed_accounts = set()   # 已处理完成的账号
        self.retry_count = {}             # 重试次数跟踪
        self.max_retries = self.settings.get('max_retries', 3)  # 从设置中获取最大重试次数

        # 添加线程启动锁，防止多个线程同时启动
        self.thread_start_lock = threading.Lock()

        # 添加线程启动标志，防止重复启动
        self.thread_starting = False

        # 创建浏览器代理管理器
        self.proxy_manager = None
        self.use_batch_proxy = settings.get('use_batch_proxy', True)  # 默认使用批量存稿代理

        # 优先使用批量存稿代理
        if self.use_batch_proxy:
            self.log_message.emit("使用批量存稿代理设置", "INFO")
        # 其次使用养号专用代理管理器
        elif NurtureProxyManager and settings.get('use_global_proxy', False):
            self.proxy_manager = NurtureProxyManager(settings)
            self.log_message.emit("使用养号专用代理管理器（支持全局代理）", "INFO")
        # 最后使用通用浏览器代理管理器
        elif BrowserProxyManager:
            self.proxy_manager = BrowserProxyManager(settings)
            self.log_message.emit("使用通用浏览器代理管理器", "INFO")

        # 打印养号设置
        self.print_settings()

    def create_timer_callback(self, func, *args, **kwargs):
        """创建一个无返回值的回调函数，用于QTimer.singleShot

        Args:
            func: 要调用的函数
            *args: 传递给函数的位置参数
            **kwargs: 传递给函数的关键字参数

        Returns:
            function: 无返回值的回调函数
        """
        def callback():
            func(*args, **kwargs)
        return callback

    def print_settings(self):
        """打印养号设置信息"""
        self.log_message.emit("当前养号设置:", "INFO")
        self.log_message.emit(f"- 浏览文章数: {self.settings.get('browse_count', 8)}", "INFO")
        self.log_message.emit(f"- 平均浏览时长: {self.settings.get('browse_duration', 60)}秒", "INFO")
        self.log_message.emit(f"- 点赞概率: {self.settings.get('like_probability', 70)}%", "INFO")
        self.log_message.emit(f"- 评论概率: {self.settings.get('comment_probability', 30)}%", "INFO")
        self.log_message.emit(f"- 关注概率: {self.settings.get('follow_probability', 20)}%", "INFO")

        # 输出伪装设置信息
        if self.settings.get('enable_fingerprint', False):
            self.log_message.emit("已启用浏览器指纹伪装", "INFO")

            if self.settings.get('resolution_type', '随机分辨率') == '固定分辨率':
                width = self.settings.get('resolution_width', 1920)
                height = self.settings.get('resolution_height', 1080)
                self.log_message.emit(f"- 固定分辨率: {width}x{height}", "INFO")
            else:
                self.log_message.emit(f"- 分辨率类型: {self.settings.get('resolution_type', '随机分辨率')}", "INFO")

            self.log_message.emit(f"- 设备类型: {self.settings.get('device_type', '自动随机')}", "INFO")
            self.log_message.emit(f"- 浏览器类型: {self.settings.get('browser_type', '自动随机')}", "INFO")

        # 代理设置信息
        if self.settings.get('use_global_proxy', False):
            self.log_message.emit("已启用全局代理设置", "INFO")
            self.log_message.emit("- 使用全局代理池中的代理地址", "INFO")

            # 尝试获取全局代理信息
            try:
                from app.utils.proxy_utils import get_proxy_settings
                global_settings = get_proxy_settings()
                if global_settings:
                    proxy_type = global_settings.get("proxy_type", "HTTP")
                    has_rotation = global_settings.get("enable_rotation", False)
                    proxy_list = global_settings.get("proxy_list", "").strip()
                    proxy_count = len(proxy_list.split(",")) if proxy_list else 0

                    self.log_message.emit(f"- 全局代理类型: {proxy_type}", "INFO")
                    if has_rotation and proxy_count > 0:
                        self.log_message.emit(f"- 全局IP轮换: 已启用", "INFO")
                        self.log_message.emit(f"- 全局代理数量: {proxy_count}个", "INFO")
            except Exception as e:
                self.log_message.emit(f"获取全局代理信息失败: {str(e)}", "WARNING")
        elif self.settings.get('enable_proxy', False):
            proxy_count = len(self.settings.get('proxy_list', []))
            self.log_message.emit(f"已启用本地IP代理，共 {proxy_count} 个代理地址", "INFO")
            self.log_message.emit(f"- 代理类型: {self.settings.get('proxy_type', 'HTTP')}", "INFO")
            self.log_message.emit(f"- 自动切换: {self.settings.get('auto_switch_proxy', True)}", "INFO")
            self.log_message.emit(f"- 切换频率: {self.settings.get('proxy_switch_interval', '每账号切换一次')}", "INFO")

        if self.settings.get('enable_random_ua', True):
            ua_count = len(self.settings.get('custom_ua_list', []))
            if ua_count > 0:
                self.log_message.emit(f"已启用随机User-Agent，共 {ua_count} 个自定义UA", "INFO")
            else:
                self.log_message.emit("已启用随机User-Agent，使用系统内置UA池", "INFO")

    def start(self):
        """开始养号任务"""
        self.running = True
        self.status_update.emit("开始执行养号任务...")

        try:
            # 初始化账号队列和计数器
            self.account_queue = list(enumerate(self.accounts))
            self.completed_accounts = 0
            self.total_accounts = len(self.accounts)
            self.active_threads = 0

            # 连接账号完成信号
            self.account_finished.connect(self.on_account_finished)

            # 启动初始线程 - 优化线程启动策略
            initial_threads = min(self.max_threads, self.total_accounts)
            self.log_message.emit(f"启动 {initial_threads} 个并发线程处理账号", "INFO")

            # 使用动态间隔启动初始线程，避免资源竞争
            # 获取线程启动间隔设置（秒）
            thread_start_interval = self.settings.get('thread_start_interval', 5)
            self.log_message.emit(f"线程启动间隔: {thread_start_interval}秒", "INFO")

            # 重置线程计数和状态
            self.active_threads = 0
            self.processing_accounts = set()
            self.processed_accounts = set()

            # 直接启动第一个线程，然后使用QTimer启动后续线程
            self.log_message.emit("立即启动第1个线程", "INFO")
            self.start_initial_thread()  # 直接调用，不使用QTimer

            # 如果需要启动多个线程，使用QTimer启动后续线程
            if initial_threads > 1:
                for i in range(1, initial_threads):
                    # 使用更短的间隔时间，进一步加快启动速度
                    delay = i * 200  # 200毫秒间隔
                    self.log_message.emit(f"计划在 {delay/1000:.1f}秒 后启动第 {i+1} 个线程", "INFO")
                    QTimer.singleShot(delay, self.create_timer_callback(self.start_initial_thread))

            # 使用事件驱动方式等待所有账号处理完成
            # 创建监控定时器，定期检查任务状态，减少CPU使用
            self.status_check_timer = QTimer()
            self.status_check_timer.timeout.connect(self.check_task_status)
            self.status_check_timer.start(2000)  # 每2秒检查一次状态

        except Exception as e:
            self.error_message.emit("养号任务出错", str(e))
            self.log_message.emit(f"养号任务出错: {str(e)}", "ERROR")
            traceback.print_exc()

        finally:
            self.running = False
            self.finished.emit()

    def check_task_status(self):
        """检查任务状态，更新进度，并在完成时停止定时器"""
        if not self.running:
            # 如果任务已停止，停止定时器
            if hasattr(self, 'status_check_timer'):
                self.status_check_timer.stop()
                self.log_message.emit("任务已停止，停止状态检查定时器", "INFO")
            return

        # 检查是否所有账号都已处理完成
        if self.completed_accounts >= self.total_accounts:
            self.log_message.emit("所有账号处理完成", "INFO")
            self.status_update.emit("养号任务执行完成")

            # 停止定时器
            if hasattr(self, 'status_check_timer'):
                self.status_check_timer.stop()
                self.log_message.emit("任务完成，停止状态检查定时器", "INFO")

            # 设置运行状态为False
            self.running = False
            self.finished.emit()
            return

        # 输出当前进度
        self.log_message.emit(f"养号任务进行中: 已完成 {self.completed_accounts}/{self.total_accounts}, 活跃线程: {self.active_threads}, 队列长度: {len(self.account_queue)}", "INFO")

        # 检查是否有活跃线程，如果没有但队列不为空，尝试启动新线程
        if self.active_threads == 0 and self.account_queue and self.running:
            self.log_message.emit("检测到没有活跃线程但队列中还有账号，尝试启动新线程", "INFO")
            self.thread_starting = False  # 重置线程启动标志

            # 直接调用start_initial_thread方法，不使用QTimer延迟
            self.start_initial_thread()

            # 如果仍然没有活跃线程，可能是线程启动失败，再次尝试
            if self.active_threads == 0 and self.account_queue and self.running:
                self.log_message.emit("首次尝试启动线程失败，再次尝试", "WARNING")
                # 使用短延迟再次尝试
                QTimer.singleShot(200, self.create_timer_callback(self.start_initial_thread))

    def start_initial_thread(self):
        """启动初始线程"""
        self.log_message.emit("开始执行start_initial_thread方法", "INFO")

        # 记录当前状态，便于调试
        with self.thread_lock:
            current_active = self.active_threads
            queue_length = len(self.account_queue)
            completed = self.completed_accounts
            self.log_message.emit(f"当前状态: 活跃线程数={current_active}, 队列长度={queue_length}, 已完成账号数={completed}", "INFO")

        if self.running and self.account_queue:
            # 记录启动前的线程数
            self.log_message.emit(f"启动新线程前，当前活跃线程数: {self.active_threads}", "INFO")

            # 检查是否已达到最大线程数
            if self.active_threads >= self.max_threads:
                self.log_message.emit(f"已达到最大线程数 {self.max_threads}，暂不启动新线程", "INFO")
                return

            # 尝试启动新线程
            success = self.start_next_account_thread()

            if success:
                # 获取下一个账号信息用于日志
                if self.account_queue:
                    next_index, next_account = self.account_queue[0]
                    next_name = next_account.get('account_name', f'账号{next_index+1}')
                    self.log_message.emit(f"启动线程处理账号 {next_name} ({next_index+1}/{self.total_accounts})，当前活跃线程: {self.active_threads}", "INFO")
                else:
                    self.log_message.emit(f"成功启动线程，当前活跃线程: {self.active_threads}", "INFO")

                # 如果还有账号，并且活跃线程数小于最大线程数，继续启动更多线程
                if self.account_queue and self.active_threads < self.max_threads and self.running:
                    self.log_message.emit(f"队列中还有账号，继续启动更多线程，当前活跃线程: {self.active_threads}", "INFO")
                    # 使用短延迟启动下一个线程，避免资源竞争
                    QTimer.singleShot(200, self.create_timer_callback(self.start_initial_thread))
            else:
                self.log_message.emit("启动初始账号处理线程失败", "WARNING")
                # 如果启动失败但队列不为空，等待一段时间后重试
                if self.account_queue and self.running:
                    self.log_message.emit("将在1秒后重试启动线程", "INFO")
                    QTimer.singleShot(1000, self.create_timer_callback(self.start_initial_thread))

    def start_next_account_thread(self):
        """启动下一个账号处理线程"""
        with self.thread_lock:
            if not self.account_queue or not self.running:
                self.log_message.emit("无法启动新线程：队列为空或任务已停止", "WARNING")
                return False

            # 检查是否已达到最大线程数
            if self.active_threads >= self.max_threads:
                self.log_message.emit(f"已达到最大线程数 {self.max_threads}，暂不启动新线程", "INFO")
                return False

            # 从队列中获取下一个账号
            i, account = self.account_queue.pop(0)

            # 检查账号是否已在处理中
            if hasattr(self, 'processing_accounts') and i in self.processing_accounts:
                self.log_message.emit(f"账号 {i+1} 已在处理中，跳过", "WARNING")
                return False

            # 检查账号是否已处理完成
            if hasattr(self, 'processed_accounts') and i in self.processed_accounts:
                self.log_message.emit(f"账号 {i+1} 已处理完成，跳过", "WARNING")
                return False

            # 标记账号为正在处理中
            if hasattr(self, 'processing_accounts'):
                self.processing_accounts.add(i)

            # 增加活跃线程计数
            self.active_threads += 1

            # 创建并启动线程
            account_name = account.get('account_name', f'账号{i+1}')
            self.log_message.emit(f"启动线程处理账号 {account_name} ({i+1}/{self.total_accounts})，当前活跃线程: {self.active_threads}", "INFO")
            self.progress_update.emit(i+1, self.total_accounts)

            try:
                # 创建线程前先检查是否已经停止运行
                if not self.running:
                    self.log_message.emit("任务已停止，不再创建新线程", "INFO")
                    self.active_threads -= 1  # 减少活跃线程计数
                    if hasattr(self, 'processing_accounts') and i in self.processing_accounts:
                        self.processing_accounts.remove(i)
                    # 将账号放回队列
                    self.account_queue.insert(0, (i, account))
                    return False

                thread = threading.Thread(
                    target=self.process_account_thread,
                    args=(i, account),
                    daemon=True
                )
                thread.start()
                self.log_message.emit(f"成功创建并启动线程处理账号 {account_name}", "INFO")
                return True
            except Exception as e:
                # 如果线程创建失败，回滚状态
                self.active_threads -= 1
                if hasattr(self, 'processing_accounts') and i in self.processing_accounts:
                    self.processing_accounts.remove(i)

                # 将账号放回队列首位
                self.account_queue.insert(0, (i, account))
                self.log_message.emit(f"创建线程处理账号 {account_name} 时出错: {str(e)}", "ERROR")

                # 添加短暂延迟后重试
                if self.running:
                    self.log_message.emit("将在1秒后重试启动线程", "INFO")
                    QTimer.singleShot(1000, self.create_timer_callback(self.start_next_account_thread))

                return False

    def process_account_thread(self, index, account):
        """在线程中处理单个账号

        Args:
            index: 账号索引
            account: 账号信息
        """
        account_name = account.get('account_name', f'账号{index+1}')
        try:
            self.process_account(account)
        except Exception as e:
            self.log_message.emit(f"处理账号 {account_name} 时出错: {str(e)}", "ERROR")
            traceback.print_exc()
        finally:
            # 通知账号处理完成
            self.account_finished.emit(index)

    def on_account_finished(self, index):
        """账号处理完成回调

        Args:
            index: 完成处理的账号索引
        """
        with self.thread_lock:
            # 更新账号状态
            self.completed_accounts += 1
            self.active_threads -= 1

            # 从处理中集合中移除
            if hasattr(self, 'processing_accounts') and index in self.processing_accounts:
                self.processing_accounts.remove(index)

            # 添加到已处理集合
            if hasattr(self, 'processed_accounts'):
                self.processed_accounts.add(index)

            self.log_message.emit(f"账号 {index+1} 处理完成，已完成 {self.completed_accounts}/{self.total_accounts}，剩余队列长度: {len(self.account_queue)}", "INFO")
            self.log_message.emit(f"当前活跃线程数: {self.active_threads}, 最大线程数: {self.max_threads}", "INFO")

            # 启动下一个账号处理线程
            if self.account_queue and self.running:
                # 检查是否已达到最大线程数
                if self.active_threads < self.max_threads:
                    self.log_message.emit(f"准备启动下一个账号处理线程，当前活跃线程: {self.active_threads}", "INFO")

                    # 记录下一个账号的信息，便于调试
                    if self.account_queue:
                        next_account_index, next_account = self.account_queue[0]
                        next_account_name = next_account.get('account_name', f'账号{next_account_index+1}')
                        self.log_message.emit(f"下一个账号: {next_account_name} ({next_account_index+1}/{self.total_accounts})", "INFO")

                    # 使用QTimer延迟启动下一个账号，避免在回调中直接启动可能导致的问题
                    # 进一步减少延迟时间，加快线程启动速度
                    QTimer.singleShot(100, self.create_timer_callback(self.start_next_account_thread))
                else:
                    self.log_message.emit(f"已达到最大线程数 {self.max_threads}，暂不启动新线程", "INFO")
            else:
                if not self.account_queue:
                    self.log_message.emit("账号队列为空，不再启动新线程", "INFO")
                if not self.running:
                    self.log_message.emit("养号任务已停止，不再启动新线程", "INFO")

            # 更新进度
            self.progress_update.emit(self.completed_accounts, self.total_accounts)

            # 检查是否所有账号都已处理完成
            if self.completed_accounts >= self.total_accounts:
                self.log_message.emit("所有账号处理完成", "INFO")
                # 在主线程中安全地发出完成信号
                QMetaObject.invokeMethod(self, "finish_task", Qt.ConnectionType.QueuedConnection)

    def delayed_start_next_account(self):
        """延迟启动下一个账号处理线程，避免在回调中直接启动可能导致的问题"""
        if not self.account_queue or not self.running:
            return

        # 启动下一个账号
        success = self.start_next_account_thread()
        if success:
            self.log_message.emit("成功启动下一个账号处理线程", "INFO")
        else:
            self.log_message.emit("启动下一个账号处理线程失败，请检查账号队列和运行状态", "WARNING")
            # 检查可能的失败原因
            if not self.account_queue:
                self.log_message.emit("失败原因: 账号队列为空", "WARNING")
            if not self.running:
                self.log_message.emit("失败原因: 养号任务已停止运行", "WARNING")

    def optimize_memory(self):
        """执行内存优化，清理系统资源"""
        try:
            import gc
            import psutil

            # 强制执行Python垃圾回收
            gc.collect()

            # 获取当前进程
            process = psutil.Process()

            # 记录优化前内存使用情况
            before_memory = process.memory_info().rss / 1024 / 1024  # 转换为MB

            # 尝试释放内存（仅在Windows系统上有效）
            if sys.platform == 'win32':
                try:
                    import ctypes
                    ctypes.windll.kernel32.SetProcessWorkingSetSize(-1, -1)
                    self.log_message.emit("已执行Windows内存释放", "INFO")
                except Exception as e:
                    self.log_message.emit(f"Windows内存释放失败: {str(e)[:100]}", "WARNING")

            # 检查是否启用了浏览器缓存清理
            clear_browser_cache = self.settings.get('clear_browser_cache', True)
            if clear_browser_cache:
                # 清理Chrome缓存目录
                try:
                    # 获取用户主目录
                    home_dir = os.path.expanduser("~")

                    # Chrome缓存目录路径
                    cache_paths = [
                        os.path.join(home_dir, "AppData", "Local", "Google", "Chrome", "User Data", "Default", "Cache"),
                        os.path.join(home_dir, "AppData", "Local", "Google", "Chrome", "User Data", "Default", "Code Cache"),
                        os.path.join(home_dir, "AppData", "Local", "Google", "Chrome", "User Data", "Default", "GPUCache")
                    ]

                    # 清理临时文件
                    cleared_files = 0
                    for cache_path in cache_paths:
                        if os.path.exists(cache_path):
                            try:
                                # 获取目录大小
                                dir_size = sum(os.path.getsize(os.path.join(dirpath, filename))
                                              for dirpath, _, filenames in os.walk(cache_path)
                                              for filename in filenames) / 1024 / 1024  # MB

                                # 只清理大于50MB的缓存目录
                                if dir_size > 50:
                                    # 删除目录中的文件，但保留目录结构
                                    for root, _, files in os.walk(cache_path):  # 使用_忽略未使用的dirs变量
                                        for file in files:
                                            try:
                                                file_path = os.path.join(root, file)
                                                # 只删除超过1小时的文件
                                                if time.time() - os.path.getmtime(file_path) > 3600:
                                                    os.remove(file_path)
                                                    cleared_files += 1
                                            except:
                                                pass

                                    self.log_message.emit(f"已清理Chrome缓存目录: {cache_path} ({dir_size:.1f}MB)", "INFO")
                            except Exception as e:
                                self.log_message.emit(f"清理缓存目录失败: {str(e)[:100]}", "WARNING")

                    if cleared_files > 0:
                        self.log_message.emit(f"共清理了 {cleared_files} 个缓存文件", "INFO")
                    else:
                        self.log_message.emit("没有需要清理的缓存文件", "INFO")

                except Exception as e:
                    self.log_message.emit(f"浏览器缓存清理失败: {str(e)[:100]}", "WARNING")

            # 记录优化后内存使用情况
            after_memory = process.memory_info().rss / 1024 / 1024  # 转换为MB
            memory_diff = before_memory - after_memory

            if memory_diff > 0:
                self.log_message.emit(f"内存优化成功，释放了 {memory_diff:.1f}MB 内存", "INFO")
            else:
                self.log_message.emit(f"内存使用情况: {after_memory:.1f}MB", "INFO")

        except Exception as e:
            self.log_message.emit(f"内存优化过程中出错: {str(e)}", "WARNING")

    @pyqtSlot()
    def finish_task(self):
        """完成任务，在主线程中安全地发出完成信号"""
        if hasattr(self, 'running') and self.running:
            self.running = False
            self.log_message.emit("养号任务已完成", "INFO")
            self.status_update.emit("养号任务已完成")
            self.finished.emit()

    def stop(self):
        """停止养号任务"""
        self.running = False
        self.log_message.emit("正在停止养号任务...", "WARNING")
        self.status_update.emit("正在停止养号任务...")

    def process_account(self, account):
        """处理单个账号

        Args:
            account: 账号信息
        """
        driver = None
        account_name = account.get('account_name', '未知账号')

        try:
            self.log_message.emit(f"开始处理账号: {account_name}", "INFO")

            # 检查是否启用了智能内存管理
            smart_memory_management = self.settings.get('smart_memory_management', True)
            if smart_memory_management:
                self.log_message.emit("智能内存管理已启用，执行内存优化...", "INFO")
                self.optimize_memory()

            # 在启动新浏览器前，智能检查并清理可能残留的Chrome进程
            try:
                # 检查是否启用了强制关闭浏览器进程的选项
                force_close_browser = self.settings.get('force_close_browser', False)  # 默认改为False，减少不必要的进程终止

                if force_close_browser:
                    import psutil
                    chrome_processes = []
                    chrome_count = 0

                    # 首先收集所有Chrome进程信息
                    for proc in psutil.process_iter(['pid', 'name', 'create_time']):
                        if 'chrome' in proc.info['name'].lower():
                            chrome_count += 1
                            try:
                                # 收集进程创建时间，用于识别僵尸进程
                                proc_info = {
                                    'pid': proc.info['pid'],
                                    'name': proc.info['name'],
                                    'create_time': proc.info['create_time'],
                                    'running_time': time.time() - proc.info['create_time']
                                }
                                chrome_processes.append(proc_info)
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                pass

                    # 只有当Chrome进程数量超过阈值时才进行清理
                    if chrome_count > 8:  # 增加阈值，避免过早清理
                        self.log_message.emit(f"检测到 {chrome_count} 个Chrome进程，进行智能清理...", "WARNING")

                        # 按运行时间排序，优先关闭运行时间长的进程
                        chrome_processes.sort(key=lambda x: x['running_time'], reverse=True)

                        # 只关闭一部分最老的进程，而不是全部
                        processes_to_kill = chrome_processes[:chrome_count-5]  # 保留5个最新的进程

                        for proc_info in processes_to_kill:
                            try:
                                proc = psutil.Process(proc_info['pid'])
                                proc.kill()
                                self.log_message.emit(f"智能关闭Chrome进程: {proc_info['pid']}，运行时间: {int(proc_info['running_time'])}秒", "WARNING")
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                pass

                        # 等待进程关闭，减少等待时间
                        time.sleep(1)
                else:
                    self.log_message.emit("已禁用强制关闭浏览器进程功能，跳过Chrome进程清理", "INFO")
            except Exception as e:
                self.log_message.emit(f"清理Chrome进程时出错: {str(e)}", "WARNING")

            # 获取ChromeDriver路径
            driver_path = self.get_chrome_driver_path()
            self.log_message.emit(f"ChromeDriver路径: {driver_path if driver_path else '使用系统默认'}", "INFO")

            # 配置Chrome选项
            headless_mode = self.settings.get('headless_mode', False)
            self.log_message.emit(f"无头模式: {'启用' if headless_mode else '禁用'}", "INFO")

            # 创建WebDriver
            if self.use_batch_proxy:
                # 使用优化的批量存稿代理设置
                self.log_message.emit("使用优化配置创建WebDriver...", "INFO")
                options = Options()

                # 基本配置
                if headless_mode:
                    options.add_argument('--headless=new')  # 使用新版无头模式

                # 性能优化选项
                options.add_argument('--disable-gpu')  # 禁用GPU加速
                options.add_argument('--no-sandbox')  # 禁用沙盒
                options.add_argument('--disable-dev-shm-usage')  # 禁用/dev/shm使用

                # 内存优化
                options.add_argument('--js-flags=--expose-gc')  # 启用JavaScript垃圾回收
                options.add_argument('--aggressive-cache-discard')  # 积极丢弃缓存
                options.add_argument('--disable-cache')  # 禁用缓存
                options.add_argument('--disable-application-cache')  # 禁用应用缓存
                options.add_argument('--disable-offline-load-stale-cache')  # 禁用离线加载过期缓存
                options.add_argument('--disk-cache-size=0')  # 将磁盘缓存大小设为0

                # 减少资源使用
                options.add_argument('--disable-extensions')  # 禁用扩展
                options.add_argument('--disable-component-extensions-with-background-pages')  # 禁用带有后台页面的组件扩展
                options.add_argument('--disable-default-apps')  # 禁用默认应用
                options.add_argument('--disable-background-networking')  # 禁用后台网络
                options.add_argument('--disable-background-timer-throttling')  # 禁用后台计时器节流
                options.add_argument('--disable-backgrounding-occluded-windows')  # 禁用遮挡窗口的后台处理
                options.add_argument('--disable-breakpad')  # 禁用崩溃报告
                options.add_argument('--disable-domain-reliability')  # 禁用域可靠性

                # 界面相关选项
                options.add_argument('--disable-infobars')  # 禁用信息栏
                options.add_argument('--disable-browser-side-navigation')  # 禁用浏览器侧导航
                options.add_argument('--disable-hang-monitor')  # 禁用挂起监视器
                options.add_argument('--disable-prompt-on-repost')  # 禁用重新发布提示
                options.add_argument('--disable-client-side-phishing-detection')  # 禁用客户端钓鱼检测

                # 自动化相关选项
                options.add_experimental_option('excludeSwitches', ['enable-automation', 'enable-logging'])  # 排除自动化开关
                options.add_experimental_option('detach', False)  # 确保浏览器不会在后台运行
                options.add_experimental_option('prefs', {
                    'profile.default_content_setting_values.notifications': 2,  # 禁用通知
                    'profile.default_content_settings.popups': 0,  # 禁用弹出窗口
                    'download.prompt_for_download': False,  # 禁用下载提示
                    'download.directory_upgrade': True,  # 启用下载目录升级
                    'safebrowsing.enabled': False  # 禁用安全浏览
                })

                # 进程管理选项
                options.add_argument('--disable-features=site-per-process')  # 禁用每个站点一个进程
                # 移除单进程模式，允许多线程并发
                # options.add_argument('--single-process')  # 使用单进程模式，减少资源使用

                # 应用批量存稿代理设置
                try:
                    # 强制轮换代理，确保每个浏览器实例使用不同的代理IP
                    force_rotate_proxy()
                    self.log_message.emit("已强制轮换代理", "INFO")

                    # 获取随机代理
                    random_proxy = get_random_proxy_from_list()
                    if random_proxy:
                        # 测试代理是否可用
                        if test_proxy(random_proxy, timeout=3):
                            self.log_message.emit(f"代理测试成功: {random_proxy}", "INFO")
                        else:
                            self.log_message.emit("代理测试失败，尝试使用默认代理", "WARNING")

                    # 应用代理设置
                    if apply_proxy_to_chrome_options(options):
                        current_proxy = get_current_proxy()
                        self.log_message.emit(f"已应用代理: {current_proxy}", "INFO")
                except Exception as proxy_error:
                    self.log_message.emit(f"应用代理设置时出错: {str(proxy_error)}", "WARNING")

                # 创建WebDriver
                try:
                    if driver_path:
                        service = Service(executable_path=driver_path)
                        driver = webdriver.Chrome(service=service, options=options)
                    else:
                        driver = webdriver.Chrome(options=options)
                    self.log_message.emit("WebDriver创建成功", "INFO")
                except Exception as driver_err:
                    self.log_message.emit(f"创建WebDriver失败: {str(driver_err)}", "ERROR")
                    raise
            elif self.proxy_manager:
                # 使用代理管理器创建driver
                self.log_message.emit("使用代理管理器创建WebDriver...", "INFO")
                driver = self.proxy_manager.create_driver(driver_path, headless_mode)

                # 记录当前使用的代理
                if hasattr(self.proxy_manager, 'current_proxy') and self.proxy_manager.current_proxy:
                    self.log_message.emit(f"使用代理: {self.proxy_manager.current_proxy}", "INFO")

                self.log_message.emit("使用自定义浏览器配置创建WebDriver成功", "INFO")
            else:
                # 使用优化的默认配置创建driver
                self.log_message.emit("使用优化配置创建WebDriver...", "INFO")
                options = Options()

                # 基本配置
                if headless_mode:
                    options.add_argument('--headless=new')  # 使用新版无头模式

                # 性能优化选项
                options.add_argument('--disable-gpu')  # 禁用GPU加速
                options.add_argument('--no-sandbox')  # 禁用沙盒
                options.add_argument('--disable-dev-shm-usage')  # 禁用/dev/shm使用

                # 内存优化
                options.add_argument('--js-flags=--expose-gc')  # 启用JavaScript垃圾回收
                options.add_argument('--aggressive-cache-discard')  # 积极丢弃缓存
                options.add_argument('--disable-cache')  # 禁用缓存
                options.add_argument('--disable-application-cache')  # 禁用应用缓存
                options.add_argument('--disable-offline-load-stale-cache')  # 禁用离线加载过期缓存
                options.add_argument('--disk-cache-size=0')  # 将磁盘缓存大小设为0

                # 减少资源使用
                options.add_argument('--disable-extensions')  # 禁用扩展
                options.add_argument('--disable-component-extensions-with-background-pages')  # 禁用带有后台页面的组件扩展
                options.add_argument('--disable-default-apps')  # 禁用默认应用
                options.add_argument('--disable-background-networking')  # 禁用后台网络
                options.add_argument('--disable-background-timer-throttling')  # 禁用后台计时器节流
                options.add_argument('--disable-backgrounding-occluded-windows')  # 禁用遮挡窗口的后台处理
                options.add_argument('--disable-breakpad')  # 禁用崩溃报告
                options.add_argument('--disable-domain-reliability')  # 禁用域可靠性

                # 界面相关选项
                options.add_argument('--disable-infobars')  # 禁用信息栏
                options.add_argument('--disable-browser-side-navigation')  # 禁用浏览器侧导航
                options.add_argument('--disable-hang-monitor')  # 禁用挂起监视器
                options.add_argument('--disable-prompt-on-repost')  # 禁用重新发布提示
                options.add_argument('--disable-client-side-phishing-detection')  # 禁用客户端钓鱼检测

                # 自动化相关选项
                options.add_experimental_option('excludeSwitches', ['enable-automation', 'enable-logging'])  # 排除自动化开关
                options.add_experimental_option('detach', False)  # 确保浏览器不会在后台运行
                options.add_experimental_option('prefs', {
                    'profile.default_content_setting_values.notifications': 2,  # 禁用通知
                    'profile.default_content_settings.popups': 0,  # 禁用弹出窗口
                    'download.prompt_for_download': False,  # 禁用下载提示
                    'download.directory_upgrade': True,  # 启用下载目录升级
                    'safebrowsing.enabled': False  # 禁用安全浏览
                })

                # 进程管理选项
                options.add_argument('--disable-features=site-per-process')  # 禁用每个站点一个进程
                # 移除单进程模式，允许多线程并发
                # options.add_argument('--single-process')  # 使用单进程模式，减少资源使用

                try:
                    if driver_path:
                        service = Service(executable_path=driver_path)
                        driver = webdriver.Chrome(service=service, options=options)
                    else:
                        # 使用ChromeDriver管理器创建浏览器
                        from app.utils.chromedriver_manager import create_chrome_driver
                        driver = create_chrome_driver(options)
                    self.log_message.emit("WebDriver创建成功", "INFO")
                except Exception as driver_err:
                    self.log_message.emit(f"创建WebDriver失败: {str(driver_err)}", "ERROR")
                    raise

            # 设置页面加载超时
            driver.set_page_load_timeout(30)
            self.log_message.emit("设置页面加载超时: 30秒", "INFO")

            # 登录账号
            self.log_message.emit(f"开始加载账号 {account_name} 的Cookie...", "INFO")
            success = self.load_cookies(driver, account)
            if not success:
                self.log_message.emit(f"账号 {account_name} 登录失败，跳过养号", "ERROR")
                return

            # 依次执行养号行为
            self.log_message.emit(f"账号 {account_name} 登录成功，开始执行养号行为", "INFO")
            self.execute_nurture_behaviors(driver)

            self.log_message.emit(f"账号 {account_name} 养号完成", "INFO")

        except Exception as e:
            self.log_message.emit(f"处理账号 {account_name} 过程出错: {str(e)}", "ERROR")
            traceback.print_exc()

        finally:
            # 关闭浏览器
            if driver:
                try:
                    self.log_message.emit(f"正在关闭账号 {account_name} 的浏览器...", "INFO")

                    # 先关闭所有窗口
                    try:
                        # 获取所有窗口句柄
                        handles = driver.window_handles
                        if len(handles) > 1:
                            self.log_message.emit(f"检测到 {len(handles)} 个窗口，逐个关闭", "INFO")
                            # 保留主窗口，关闭其他窗口
                            main_handle = handles[0]
                            for handle in handles[1:]:
                                try:
                                    driver.switch_to.window(handle)
                                    driver.close()
                                except:
                                    pass
                            # 切回主窗口
                            driver.switch_to.window(main_handle)
                    except Exception as window_err:
                        self.log_message.emit(f"关闭多余窗口时出错: {str(window_err)}", "WARNING")

                    # 退出浏览器
                    driver.quit()

                    # 确保浏览器进程完全关闭
                    time.sleep(2)
                    self.log_message.emit(f"账号 {account_name} 的浏览器已关闭", "INFO")

                except Exception as quit_err:
                    self.log_message.emit(f"关闭账号 {account_name} 的浏览器时出错: {str(quit_err)}", "WARNING")
                    # 尝试强制关闭Chrome进程
                    try:
                        import psutil
                        killed_count = 0
                        for proc in psutil.process_iter(['pid', 'name']):
                            if 'chrome' in proc.info['name'].lower():
                                try:
                                    proc.kill()
                                    killed_count += 1
                                except:
                                    pass
                        if killed_count > 0:
                            self.log_message.emit(f"强制关闭了 {killed_count} 个Chrome进程", "WARNING")
                    except Exception as kill_err:
                        self.log_message.emit(f"强制关闭Chrome进程时出错: {str(kill_err)}", "WARNING")

    def get_chrome_driver_path(self):
        """获取ChromeDriver路径

        Returns:
            str: ChromeDriver路径
        """
        try:
            # 尝试从设置中获取
            driver_path = self.settings.get('chrome_driver_path', '')
            if driver_path and os.path.exists(driver_path):
                return driver_path

            # 尝试从默认位置获取
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            driver_path = os.path.join(base_dir, 'drivers', 'chromedriver.exe')
            if os.path.exists(driver_path):
                return driver_path

            return None  # 返回None让Selenium自动查找

        except Exception as e:
            self.log_message.emit(f"获取ChromeDriver路径时出错: {str(e)}", "ERROR")
            return None

    def load_cookies(self, driver, account):
        """加载cookie

        Args:
            driver: Selenium WebDriver对象
            account: 账号信息
        """
        try:
            account_name = account.get('account_name', '')
            self.status_update.emit(f"正在加载账号 {account_name} 的Cookie...")

            # 先访问头条网站
            driver.get("https://www.toutiao.com/")
            time.sleep(1)

            # 获取cookie文件路径
            cookie_file = account.get('cookie_file', '')
            if not cookie_file or not os.path.exists(cookie_file):
                self.log_message.emit(f"账号 {account_name} 的Cookie文件不存在", "ERROR")
                return False

            # 读取cookie文件
            success = False
            try:
                with open(cookie_file, 'r', encoding='utf-8') as f:
                    file_content = f.read().strip()

                if not file_content:
                    self.log_message.emit("Cookie文件为空", "ERROR")
                    return False

                # 尝试解析JSON格式cookie
                try:
                    cookies = json.loads(file_content)

                    # 处理列表格式的cookie
                    if isinstance(cookies, list):
                        for cookie in cookies:
                            try:
                                if 'domain' not in cookie or not cookie['domain']:
                                    cookie['domain'] = '.toutiao.com'
                                # 过滤掉可能导致问题的cookie字段
                                if 'sameSite' in cookie:
                                    del cookie['sameSite']
                                if 'expiry' in cookie and isinstance(cookie['expiry'], float):
                                    cookie['expiry'] = int(cookie['expiry'])
                                driver.add_cookie(cookie)
                            except Exception as e:
                                self.log_message.emit(f"添加cookie出错: {str(e)[:100]}", "WARNING")
                        success = True
                        self.log_message.emit(f"已加载列表格式cookie，共 {len(cookies)} 项", "INFO")

                    # 处理字典格式的cookie
                    elif isinstance(cookies, dict):
                        # 检查是否包含标准格式的cookie
                        if 'cookies' in cookies and isinstance(cookies['cookies'], list):
                            for cookie in cookies['cookies']:
                                try:
                                    if 'domain' not in cookie or not cookie['domain']:
                                        cookie['domain'] = '.toutiao.com'
                                    # 过滤掉可能导致问题的cookie字段
                                    if 'sameSite' in cookie:
                                        del cookie['sameSite']
                                    if 'expiry' in cookie and isinstance(cookie['expiry'], float):
                                        cookie['expiry'] = int(cookie['expiry'])
                                    driver.add_cookie(cookie)
                                except Exception as e:
                                    self.log_message.emit(f"添加cookie出错: {str(e)[:100]}", "WARNING")
                            success = True
                            self.log_message.emit("已加载嵌套列表格式cookie", "INFO")
                        elif 'cookies' in cookies and isinstance(cookies['cookies'], dict):
                            # 使用键值对作为cookie
                            for name, value in cookies['cookies'].items():
                                try:
                                    driver.add_cookie({
                                        'name': name,
                                        'value': value,
                                        'domain': '.toutiao.com'
                                    })
                                except Exception as e:
                                    self.log_message.emit(f"添加cookie出错: {str(e)[:100]}", "WARNING")
                            success = True
                            self.log_message.emit("已加载嵌套键值对格式cookie", "INFO")
                        else:
                            # 直接使用键值对作为cookie
                            for name, value in cookies.items():
                                try:
                                    driver.add_cookie({
                                        'name': name,
                                        'value': value,
                                        'domain': '.toutiao.com'
                                    })
                                except Exception as e:
                                    self.log_message.emit(f"添加cookie出错: {str(e)[:100]}", "WARNING")
                            success = True
                            self.log_message.emit("已加载键值对格式cookie", "INFO")

                # 如果JSON解析失败，尝试其他格式
                except json.JSONDecodeError:
                    self.log_message.emit(f"非JSON格式，尝试解析文本格式cookie", "WARNING")

                    # 尝试键值对文本格式
                    cookie_count = 0
                    if "=" in file_content:
                        for line in file_content.split("\n"):
                            if "=" in line:
                                try:
                                    name, value = line.split("=", 1)
                                    driver.add_cookie({
                                        'name': name.strip(),
                                        'value': value.strip(),
                                        'domain': '.toutiao.com'
                                    })
                                    cookie_count += 1
                                except Exception as e:
                                    self.log_message.emit(f"添加文本cookie失败: {str(e)[:100]}", "WARNING")
                        if cookie_count > 0:
                            success = True
                            self.log_message.emit(f"已加载文本格式cookie，共 {cookie_count} 项", "INFO")
            except Exception as read_err:
                self.log_message.emit(f"处理Cookie文件出错: {str(read_err)}", "ERROR")
                return False

            # 确认是否成功加载了cookie
            if not success:
                self.log_message.emit("Cookie格式无法识别或加载失败", "ERROR")
                return False

            # 刷新页面以应用cookie
            driver.refresh()
            time.sleep(2)

            # 检查是否登录成功
            login_success = False
            max_wait_time = 20
            start_time = time.time()

            while time.time() - start_time < max_wait_time:
                # 判断是否成功登录
                if "退出登录" in driver.page_source or "个人中心" in driver.page_source:
                    login_success = True
                    break
                time.sleep(1)

            if login_success:
                self.log_message.emit(f"账号 {account_name} 登录成功", "INFO")
                return True
            else:
                self.log_message.emit(f"账号 {account_name} 登录失败，Cookie可能已过期", "WARNING")
                return False

        except Exception as e:
            self.log_message.emit(f"加载Cookie时出错: {str(e)}", "ERROR")
            return False

    def browse_content(self, driver):
        """浏览内容 - 优化版本

        Args:
            driver: Selenium WebDriver对象
        """
        try:
            # 获取配置的浏览次数
            browse_count = self.settings.get('browse_count', 8)
            average_duration = self.settings.get('browse_duration', 60)
            enable_scroll = self.settings.get('enable_scroll', True)

            # 检查是否启用了增强自然行为模拟
            natural_behavior = self.settings.get('natural_behavior', True)

            # 添加随机性，使行为更自然
            if natural_behavior:
                # 增强随机性，使行为更自然
                actual_browse_count = max(1, int(browse_count * random.uniform(0.7, 1.3)))
                actual_duration = max(10, int(average_duration * random.uniform(0.7, 1.3)))
                self.log_message.emit("已启用增强自然行为模拟，浏览行为将更加随机", "INFO")
            else:
                # 基本随机性
                actual_browse_count = max(1, int(browse_count * random.uniform(0.9, 1.1)))
                actual_duration = max(10, int(average_duration * random.uniform(0.9, 1.1)))

            self.status_update.emit(f"正在浏览内容，计划浏览 {actual_browse_count} 篇文章...")
            self.log_message.emit(f"开始浏览内容，计划浏览 {actual_browse_count} 篇文章，平均时长 {actual_duration} 秒", "INFO")

            # 进入头条首页 - 添加重试机制
            max_retries = 3
            for retry in range(max_retries):
                try:
                    driver.get("https://www.toutiao.com/")
                    # 等待页面加载完成
                    WebDriverWait(driver, 10).until(
                        lambda d: d.execute_script("return document.readyState") == "complete"
                    )
                    self.log_message.emit("头条首页加载完成", "INFO")
                    break
                except Exception as e:
                    if retry < max_retries - 1:
                        self.log_message.emit(f"加载头条首页失败，正在重试 ({retry+1}/{max_retries}): {str(e)[:50]}", "WARNING")
                        time.sleep(2)
                    else:
                        self.log_message.emit(f"加载头条首页失败，已达最大重试次数: {str(e)[:50]}", "ERROR")
                        raise

            # 智能滚动页面，查找可点击的文章
            if enable_scroll:
                # 根据浏览次数动态调整滚动次数
                scroll_count = min(10, max(3, int(actual_browse_count * 0.8)))
                self.log_message.emit(f"智能滚动页面 {scroll_count} 次以加载更多内容", "INFO")
                self.scroll_page(driver, scroll_count)

            # 查找文章元素 - 增加更多选择器尝试
            articles = []
            selectors = [
                (By.TAG_NAME, "article"),
                (By.CLASS_NAME, "feed-card-article"),
                (By.CLASS_NAME, "feedbox-item"),
                (By.CSS_SELECTOR, ".feed-card-wrapper .title-box"),
                (By.CSS_SELECTOR, "[data-log-click]"),
                (By.CSS_SELECTOR, ".feed-card"),
                (By.CSS_SELECTOR, ".feed-card-wrapper"),
                (By.CSS_SELECTOR, ".title-box"),
                (By.CSS_SELECTOR, ".feed-item"),
                (By.CSS_SELECTOR, "a.link")
            ]

            # 按优先级尝试不同的选择器
            for selector_type, selector_value in selectors:
                try:
                    found_elements = driver.find_elements(selector_type, selector_value)
                    if found_elements:
                        self.log_message.emit(f"使用选择器 {selector_type}:{selector_value} 找到 {len(found_elements)} 个元素", "INFO")
                        articles = found_elements
                        break
                except Exception as e:
                    continue

            if not articles:
                self.log_message.emit("未找到可浏览的文章元素，尝试通用链接选择器", "WARNING")
                # 最后尝试通用链接搜索
                try:
                    articles = driver.find_elements(By.TAG_NAME, "a")
                    # 只保留可能是文章的链接
                    articles = [a for a in articles if a.text and len(a.text) > 5 and not a.get_attribute("href").endswith("#")]
                except Exception as e:
                    self.log_message.emit(f"通用链接搜索失败: {str(e)}", "ERROR")

            if not articles:
                self.log_message.emit("所有方法都未找到可浏览的文章，尝试刷新页面", "WARNING")
                driver.refresh()
                time.sleep(3)
                try:
                    articles = driver.find_elements(By.TAG_NAME, "a")
                    articles = [a for a in articles if a.text and len(a.text) > 10]
                except:
                    pass

            if not articles:
                self.log_message.emit("未能找到任何可浏览的文章元素", "ERROR")
                return

            self.log_message.emit(f"最终找到 {len(articles)} 篇可浏览的文章", "INFO")

            # 随机选择文章浏览
            browse_count = min(browse_count, len(articles))
            indices = random.sample(range(len(articles)), browse_count)

            # 记录当前窗口句柄
            main_window = driver.current_window_handle
            articles_viewed = 0

            for i, index in enumerate(indices):
                if not self.running:
                    break

                try:
                    article = articles[index]

                    # 获取并记录文章标题
                    article_title = article.text[:30] if article.text else "未知标题"
                    self.log_message.emit(f"准备打开文章: {article_title}...", "INFO")

                    # 确保元素在视图中
                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", article)
                    time.sleep(random.uniform(0.8, 1.8))

                    # 尝试点击文章 - 多种方法尝试
                    clicked = False

                    # 方法1: 直接点击
                    try:
                        article.click()
                        clicked = True
                        self.log_message.emit(f"直接点击打开第 {i+1}/{browse_count} 篇文章", "INFO")
                    except Exception as e:
                        self.log_message.emit(f"直接点击失败: {str(e)[:50]}", "WARNING")

                    # 方法2: JavaScript点击
                    if not clicked:
                        try:
                            driver.execute_script("arguments[0].click();", article)
                            clicked = True
                            self.log_message.emit(f"通过JavaScript点击打开第 {i+1}/{browse_count} 篇文章", "INFO")
                        except Exception as e:
                            self.log_message.emit(f"JavaScript点击失败: {str(e)[:50]}", "WARNING")

                    # 方法3: 使用href属性
                    if not clicked:
                        try:
                            href = article.get_attribute("href")
                            if href and not href.startswith("javascript:") and not href == "#":
                                # 在新标签页打开链接
                                driver.execute_script("window.open(arguments[0]);", href)
                                clicked = True
                                self.log_message.emit(f"通过href打开第 {i+1}/{browse_count} 篇文章: {href[:30]}...", "INFO")
                        except Exception as e:
                            self.log_message.emit(f"通过href打开失败: {str(e)[:50]}", "WARNING")

                    # 检查点击是否成功
                    if not clicked:
                        self.log_message.emit(f"第 {i+1}/{browse_count} 篇文章无法点击，尝试下一篇", "WARNING")
                        continue

                    # 等待新窗口或标签页打开
                    wait_count = 0
                    new_window_found = False

                    while wait_count < 10 and not new_window_found:
                        all_handles = driver.window_handles
                        if len(all_handles) > 1:
                            new_window_found = True
                            break
                        time.sleep(0.5)
                        wait_count += 1

                    if not new_window_found:
                        self.log_message.emit(f"点击后未打开新窗口，尝试下一篇文章", "WARNING")
                        continue

                    # 切换到新窗口
                    for handle in driver.window_handles:
                        if handle != main_window:
                            driver.switch_to.window(handle)
                            break

                    # 检查是否成功加载文章页面
                    current_url = driver.current_url
                    if "toutiao.com" not in current_url:
                        self.log_message.emit(f"打开的页面不是头条文章: {current_url[:50]}...", "WARNING")

                    # 模拟阅读行为
                    self.simulate_reading(driver, average_duration)
                    articles_viewed += 1

                    # 在文章内执行点赞操作
                    if random.random() < self.settings.get('like_probability', 70) / 100:
                        self.like_article_content(driver)

                    # 关闭当前窗口并切换回主窗口
                    if driver.current_window_handle != main_window:
                        driver.close()
                        driver.switch_to.window(main_window)

                    # 重新获取文章列表，因为页面可能已更新
                    if i < browse_count - 1:  # 如果不是最后一篇文章
                        # 刷新页面以获取新内容
                        if random.random() < 0.3:  # 30%概率刷新页面
                            driver.refresh()
                            time.sleep(random.uniform(2, 4))

                            # 重新查找文章元素
                            for selector_type, selector_value in selectors:
                                try:
                                    found_elements = driver.find_elements(selector_type, selector_value)
                                    if found_elements:
                                        articles = found_elements
                                        break
                                except:
                                    continue

                            if articles:
                                # 更新剩余要浏览的文章索引
                                remaining_count = browse_count - (i + 1)
                                if remaining_count > 0 and len(articles) > 0:
                                    new_indices = random.sample(range(len(articles)), min(remaining_count, len(articles)))
                                    indices[i+1:] = new_indices[:remaining_count]

                        # 滚动页面以加载新内容
                        if enable_scroll:
                            self.scroll_page(driver, random.randint(1, 3))

                except Exception as e:
                    self.log_message.emit(f"浏览第 {i+1} 篇文章时出错: {str(e)}", "ERROR")
                    # 确保返回主窗口
                    try:
                        if driver.current_window_handle != main_window:
                            driver.close()
                            driver.switch_to.window(main_window)
                    except:
                        # 如果出错，尝试重新加载页面
                        try:
                            driver.get("https://www.toutiao.com/")
                            time.sleep(2)
                        except:
                            pass

            self.log_message.emit(f"浏览内容完成，实际浏览了 {articles_viewed} 篇文章", "INFO")

        except Exception as e:
            self.log_message.emit(f"浏览内容过程出错: {str(e)}", "ERROR")

    def like_content(self, driver):
        """点赞内容

        Args:
            driver: Selenium WebDriver对象
        """
        try:
            # 获取配置的点赞次数
            like_count = self.settings.get('like_count', 3)

            self.status_update.emit(f"正在执行点赞操作，计划点赞 {like_count} 次...")
            self.log_message.emit(f"开始点赞操作，计划点赞 {like_count} 次", "INFO")

            # 首先确保在头条首页
            if "toutiao.com" not in driver.current_url:
                driver.get("https://www.toutiao.com/")
                time.sleep(random.uniform(2, 4))

            # 滚动页面以加载更多内容
            self.scroll_page(driver, random.randint(3, 6))

            # 查找点赞按钮 - 使用多种选择器尝试
            like_buttons = []
            like_selectors = [
                (By.CSS_SELECTOR, ".like-button"),
                (By.CSS_SELECTOR, ".tt-fav-btn"),
                (By.CSS_SELECTOR, "[data-log-click*='like']"),
                (By.CSS_SELECTOR, ".tt-fav-wrap"),
                (By.CSS_SELECTOR, ".article-item .action-item"),
                (By.CSS_SELECTOR, ".action-list .action-item"),
                (By.CSS_SELECTOR, ".tt-video-like"),
                (By.CSS_SELECTOR, ".tt-laud-button"),
                (By.CSS_SELECTOR, "button[aria-label*='赞']"),
                (By.CSS_SELECTOR, "div[role='button'][aria-label*='赞']"),
                (By.XPATH, "//span[contains(text(), '赞') or contains(text(), '点赞')]"),
                (By.XPATH, "//div[contains(@class, 'like') or contains(@class, 'laud')]")
            ]

            # 按优先级尝试不同的选择器
            for selector_type, selector_value in like_selectors:
                try:
                    found_buttons = driver.find_elements(selector_type, selector_value)
                    if found_buttons:
                        self.log_message.emit(f"使用选择器 {selector_type}:{selector_value} 找到 {len(found_buttons)} 个点赞按钮", "INFO")
                        like_buttons = found_buttons
                        break
                except Exception as e:
                    continue

            # 如果当前页面没有找到点赞按钮，尝试进入一篇文章寻找
            if not like_buttons:
                self.log_message.emit("当前页面未找到点赞按钮，尝试进入文章页面寻找", "INFO")

                # 尝试找到文章链接并点击
                article_clicked = False
                try:
                    # 寻找可能的文章链接
                    articles = driver.find_elements(By.TAG_NAME, "a")
                    articles = [a for a in articles if a.text and len(a.text) > 10 and a.get_attribute("href") and not a.get_attribute("href").endswith("#")]

                    if articles:
                        # 随机选择一篇文章
                        article = random.choice(articles)
                        article_title = article.text[:30] if article.text else "未知标题"

                        # 滚动到文章位置并点击
                        driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", article)
                        time.sleep(random.uniform(0.8, 1.5))

                        # 记录当前窗口句柄
                        main_window = driver.current_window_handle

                        # 尝试点击文章
                        try:
                            article.click()
                            article_clicked = True
                        except:
                            try:
                                driver.execute_script("arguments[0].click();", article)
                                article_clicked = True
                            except Exception as e:
                                self.log_message.emit(f"点击文章失败: {str(e)[:50]}", "WARNING")

                        if article_clicked:
                            # 等待新窗口打开
                            time.sleep(random.uniform(2, 3))

                            # 切换到新窗口
                            for handle in driver.window_handles:
                                if handle != main_window:
                                    driver.switch_to.window(handle)
                                    break

                            # 在文章页面寻找点赞按钮
                            for selector_type, selector_value in like_selectors:
                                try:
                                    found_buttons = driver.find_elements(selector_type, selector_value)
                                    if found_buttons:
                                        self.log_message.emit(f"在文章页面使用选择器 {selector_type}:{selector_value} 找到 {len(found_buttons)} 个点赞按钮", "INFO")
                                        like_buttons = found_buttons
                                        break
                                except:
                                    continue

                            # 记录文章页寻找结果
                            if like_buttons:
                                self.log_message.emit(f"在文章 \"{article_title}\" 中找到 {len(like_buttons)} 个点赞按钮", "INFO")
                            else:
                                self.log_message.emit(f"在文章 \"{article_title}\" 中未找到点赞按钮", "WARNING")

                            # 如果没找到点赞按钮，关闭文章页面返回主页
                            if not like_buttons:
                                if driver.current_window_handle != main_window:
                                    driver.close()
                                    driver.switch_to.window(main_window)
                except Exception as e:
                    self.log_message.emit(f"尝试进入文章页面寻找点赞按钮时出错: {str(e)}", "ERROR")
                    # 确保返回主窗口
                    try:
                        if 'main_window' in locals() and driver.current_window_handle != main_window:
                            driver.close()
                            driver.switch_to.window(main_window)
                    except:
                        pass

            if not like_buttons:
                self.log_message.emit("未找到可点赞的元素，跳过点赞操作", "WARNING")
                return

            # 过滤已点赞的按钮
            try:
                unclicked_buttons = []
                for btn in like_buttons:
                    # 尝试检查是否已点赞(通过检查按钮颜色、文本或类名)
                    is_already_liked = False

                    try:
                        # 检查类名中是否包含 active, highlighted 等表示已点赞的标识
                        btn_class = btn.get_attribute("class") or ""
                        if "active" in btn_class or "highlighted" in btn_class or "liked" in btn_class:
                            is_already_liked = True
                    except:
                        pass

                    try:
                        # 检查元素颜色
                        color = btn.value_of_css_property("color")
                        if "rgb(254" in color or "rgb(255, 0" in color:  # 红色系通常表示已点赞
                            is_already_liked = True
                    except:
                        pass

                    # 如果未点赞，添加到候选列表
                    if not is_already_liked:
                        unclicked_buttons.append(btn)

                if unclicked_buttons:
                    like_buttons = unclicked_buttons
                    self.log_message.emit(f"过滤后剩余 {len(like_buttons)} 个未点赞的按钮", "INFO")
                else:
                    self.log_message.emit("所有按钮可能已被点赞，尝试继续使用原列表", "WARNING")
            except Exception as e:
                self.log_message.emit(f"过滤已点赞按钮时出错: {str(e)}", "WARNING")

            # 随机选择要点赞的元素
            like_count = min(like_count, len(like_buttons))
            indices = random.sample(range(len(like_buttons)), like_count)

            likes_completed = 0
            for i, index in enumerate(indices):
                if not self.running:
                    break

                try:
                    # 滚动到点赞按钮位置
                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", like_buttons[index])
                    time.sleep(random.uniform(0.5, 1.5))

                    # 点赞 - 尝试不同方法
                    clicked = False

                    # 方法1: 直接点击
                    try:
                        like_buttons[index].click()
                        clicked = True
                        self.log_message.emit(f"直接点击完成第 {i+1}/{like_count} 次点赞", "INFO")
                    except Exception as e:
                        self.log_message.emit(f"直接点赞失败: {str(e)[:50]}", "WARNING")

                    # 方法2: JavaScript点击
                    if not clicked:
                        try:
                            driver.execute_script("arguments[0].click();", like_buttons[index])
                            clicked = True
                            self.log_message.emit(f"通过JavaScript完成第 {i+1}/{like_count} 次点赞", "INFO")
                        except Exception as e:
                            self.log_message.emit(f"JavaScript点赞失败: {str(e)[:50]}", "WARNING")

                    # 检查点赞是否成功
                    if clicked:
                        likes_completed += 1

                    # 点赞后停顿
                    time.sleep(random.uniform(1, 3))

                except Exception as e:
                    self.log_message.emit(f"执行第 {i+1} 次点赞时出错: {str(e)}", "ERROR")

            self.log_message.emit(f"点赞操作完成，成功点赞 {likes_completed} 次", "INFO")

            # 如果在文章页面中，返回主页
            if article_clicked and 'main_window' in locals():
                try:
                    if driver.current_window_handle != main_window:
                        driver.close()
                        driver.switch_to.window(main_window)
                except:
                    # 如果切换失败，直接加载头条首页
                    driver.get("https://www.toutiao.com/")

        except Exception as e:
            self.log_message.emit(f"点赞操作过程出错: {str(e)}", "ERROR")

    def comment_content(self, driver):
        """评论内容

        Args:
            driver: Selenium WebDriver对象
        """
        try:
            # 获取评论池
            comment_pool = self.settings.get('comment_pool', [])
            if not comment_pool:
                self.log_message.emit("评论池为空，跳过评论操作", "WARNING")
                return

            self.status_update.emit("正在执行评论操作...")
            self.log_message.emit("开始评论操作", "INFO")

            # 检查是否已在文章页面
            is_article_page = False
            main_window = driver.current_window_handle  # 提前定义主窗口变量
            article_opened = False

            try:
                current_url = driver.current_url
                if "toutiao.com/article/" in current_url or "toutiao.com/a" in current_url:
                    is_article_page = True
                    self.log_message.emit("当前已在文章页面", "INFO")
            except:
                pass

            # 如果不在文章页面，先打开一篇文章
            if not is_article_page:
                self.log_message.emit("当前不在文章页面，尝试打开一篇文章", "INFO")
                try:
                    # 确保在头条首页
                    if "toutiao.com" not in driver.current_url:
                        driver.get("https://www.toutiao.com/")
                        time.sleep(random.uniform(2, 4))

                    # 滚动页面加载内容
                    self.scroll_page(driver, random.randint(2, 4))

                    # 寻找文章链接
                    articles = []
                    selectors = [
                        (By.TAG_NAME, "article"),
                        (By.CSS_SELECTOR, ".feed-card-article"),
                        (By.CSS_SELECTOR, ".title-box"),
                        (By.CSS_SELECTOR, "a.link"),
                        (By.CSS_SELECTOR, "[data-log-click]")
                    ]

                    for selector_type, selector_value in selectors:
                        try:
                            found_articles = driver.find_elements(selector_type, selector_value)
                            if found_articles:
                                articles = found_articles
                                self.log_message.emit(f"使用选择器 {selector_type}:{selector_value} 找到 {len(articles)} 篇文章", "INFO")
                                break
                        except:
                            continue

                    # 如果仍未找到文章，尝试通用链接搜索
                    if not articles:
                        try:
                            all_links = driver.find_elements(By.TAG_NAME, "a")
                            articles = [a for a in all_links if a.text and len(a.text) > 10 and a.get_attribute("href") and "article" in a.get_attribute("href")]
                            if not articles:
                                # 放宽条件
                                articles = [a for a in all_links if a.text and len(a.text) > 10 and a.get_attribute("href") and not a.get_attribute("href").endswith("#")]
                        except:
                            pass

                    if not articles:
                        self.log_message.emit("未找到可打开的文章，跳过评论操作", "WARNING")
                        return

                    # 随机选择一篇文章
                    article = random.choice(articles)
                    article_title = article.text[:30] if article.text else "未知标题"
                    self.log_message.emit(f"选择文章: {article_title}...", "INFO")

                    # 确保记录当前窗口句柄是最新的
                    main_window = driver.current_window_handle

                    # 滚动到文章位置
                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", article)
                    time.sleep(random.uniform(0.8, 1.5))

                    # 点击文章
                    clicked = False
                    try:
                        article.click()
                        clicked = True
                    except:
                        try:
                            driver.execute_script("arguments[0].click();", article)
                            clicked = True
                        except:
                            # 尝试通过href打开
                            try:
                                href = article.get_attribute("href")
                                if href and not href == "#":
                                    driver.execute_script("window.open(arguments[0]);", href)
                                    clicked = True
                            except Exception as e:
                                self.log_message.emit(f"所有点击方法都失败: {str(e)[:50]}", "ERROR")

                    if not clicked:
                        self.log_message.emit("无法打开文章，跳过评论操作", "WARNING")
                        return

                    # 等待新窗口打开
                    time.sleep(random.uniform(2, 3))

                    # 切换到新窗口
                    try:
                        for handle in driver.window_handles:
                            if handle != main_window:
                                driver.switch_to.window(handle)
                                article_opened = True
                                break
                    except Exception as e:
                        self.log_message.emit(f"切换窗口失败: {str(e)[:50]}", "ERROR")
                        return

                except Exception as e:
                    self.log_message.emit(f"打开文章过程出错: {str(e)}", "ERROR")
                    return

            # 等待页面加载完成
            time.sleep(random.uniform(2, 4))

            # 随机选择一条评论
            comment_text = random.choice(comment_pool)
            self.log_message.emit(f"选择评论内容: {comment_text}", "INFO")

            # 查找评论区和评论输入框 - 尝试多种选择器
            found_comment_section = False

            # 先找评论区容器并点击
            comment_section_selectors = [
                (By.CSS_SELECTOR, ".comment-entrance"),
                (By.CSS_SELECTOR, ".comment-button"),
                (By.CSS_SELECTOR, ".tt-comment-section"),
                (By.CSS_SELECTOR, ".comment-area"),
                (By.CSS_SELECTOR, ".article-footer .comment"),
                (By.CSS_SELECTOR, "[data-label='评论']"),
                (By.XPATH, "//div[contains(text(), '评论') and contains(@class, 'button')]"),
                (By.XPATH, "//span[contains(text(), '评论')]"),
                (By.XPATH, "//button[contains(text(), '写评论')]")
            ]

            # 滚动到页面底部，评论区通常在底部
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight * 0.7);")
            time.sleep(random.uniform(1, 2))

            # 尝试点击评论区入口
            for selector_type, selector_value in comment_section_selectors:
                if found_comment_section:
                    break

                try:
                    elements = driver.find_elements(selector_type, selector_value)
                    if elements:
                        for element in elements:
                            try:
                                # 滚动到元素位置
                                driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element)
                                time.sleep(random.uniform(0.5, 1))

                                # 检查元素是否可见且可点击
                                if element.is_displayed():
                                    element.click()
                                    found_comment_section = True
                                    self.log_message.emit(f"成功点击评论区入口: {selector_type}:{selector_value}", "INFO")
                                    time.sleep(random.uniform(1, 2))
                                    break
                            except:
                                try:
                                    # 尝试JavaScript点击
                                    driver.execute_script("arguments[0].click();", element)
                                    found_comment_section = True
                                    self.log_message.emit(f"通过JavaScript点击评论区入口: {selector_type}:{selector_value}", "INFO")
                                    time.sleep(random.uniform(1, 2))
                                    break
                                except:
                                    continue
                except:
                    continue

            # 查找评论输入框
            comment_input = None
            comment_input_selectors = [
                # 添加用户提供的XPath路径（优先级最高）
                (By.XPATH, "/html/body/div[1]/div[2]/div[2]/div[3]/div/div/div[2]/div[2]/div[1]"),
                (By.CSS_SELECTOR, ".comment-input"),
                (By.CSS_SELECTOR, "textarea.comment-textarea"),
                (By.CSS_SELECTOR, ".tt-comment-textarea"),
                (By.CSS_SELECTOR, ".comment-compose-box textarea"),
                (By.CSS_SELECTOR, ".comment-editor textarea"),
                (By.CSS_SELECTOR, "[placeholder*='评论']"),
                (By.XPATH, "//textarea[contains(@placeholder, '写评论')]"),
                (By.XPATH, "//div[contains(@class, 'comment')]//textarea")
            ]

            for selector_type, selector_value in comment_input_selectors:
                try:
                    elements = driver.find_elements(selector_type, selector_value)
                    if elements:
                        for element in elements:
                            if element.is_displayed():
                                comment_input = element
                                self.log_message.emit(f"找到评论输入框: {selector_type}:{selector_value}", "INFO")
                                break
                        if comment_input:
                            break
                except:
                    continue

            # 如果没有找到评论输入框，可能需要再次点击某个元素才能显示
            if not comment_input:
                self.log_message.emit("未找到评论输入框，尝试其他方法", "WARNING")

                # 尝试直接使用用户提供的XPath路径
                try:
                    self.log_message.emit("尝试直接使用精确XPath路径定位评论框", "INFO")
                    # 滚动到页面底部，评论区通常在底部
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight * 0.8);")
                    time.sleep(1)

                    # 直接尝试定位评论框
                    try:
                        comment_box = driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div[2]/div[3]/div/div/div[2]/div[2]/div[1]")
                        if comment_box:
                            self.log_message.emit("成功找到评论框元素", "INFO")
                            driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", comment_box)
                            time.sleep(0.5)
                            comment_input = comment_box
                    except Exception as e:
                        self.log_message.emit(f"直接定位评论框失败: {str(e)[:50]}", "WARNING")
                except Exception as e:
                    self.log_message.emit(f"尝试直接XPath定位评论框时出错: {str(e)[:50]}", "WARNING")

                # 如果直接XPath方法失败，尝试找到可能是评论入口的元素
                if not comment_input:
                    try:
                        placeholder_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '写评论')]")
                        if placeholder_elements:
                            for element in placeholder_elements:
                                try:
                                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element)
                                    time.sleep(0.5)
                                    element.click()
                                    time.sleep(1)

                                    # 再次尝试找评论框
                                    for selector_type, selector_value in comment_input_selectors:
                                        try:
                                            elements = driver.find_elements(selector_type, selector_value)
                                            if elements:
                                                for element in elements:
                                                    if element.is_displayed():
                                                        comment_input = element
                                                        break
                                                if comment_input:
                                                    break
                                        except:
                                            continue
                                except:
                                    continue
                                if comment_input:
                                    break
                    except:
                        pass

            if not comment_input:
                self.log_message.emit("未能找到评论输入框，无法评论", "WARNING")

                # 关闭文章窗口，如果是新打开的
                if article_opened and main_window:
                    try:
                        driver.close()
                        driver.switch_to.window(main_window)
                    except:
                        pass
                return

            # 滚动到评论输入框位置
            driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", comment_input)
            time.sleep(random.uniform(0.5, 1.5))

            # 点击输入框
            try:
                comment_input.click()
                time.sleep(random.uniform(0.5, 1))
            except:
                try:
                    driver.execute_script("arguments[0].click();", comment_input)
                    time.sleep(random.uniform(0.5, 1))
                except Exception as e:
                    self.log_message.emit(f"点击评论输入框失败: {str(e)[:50]}", "WARNING")

            # 输入评论
            try:
                comment_input.clear()
                # 使用JavaScript设置值，避免输入法问题
                driver.execute_script("arguments[0].value = arguments[1];", comment_input, comment_text)
                time.sleep(0.5)
                # 再发送一次键盘事件，确保内容被识别
                comment_input.send_keys(" ")
                time.sleep(0.5)
                self.log_message.emit(f"成功输入评论: {comment_text}", "INFO")
            except Exception as e:
                # 尝试普通的send_keys方法
                try:
                    comment_input.clear()
                    comment_input.send_keys(comment_text)
                    self.log_message.emit(f"通过send_keys输入评论: {comment_text}", "INFO")
                except Exception as e:
                    self.log_message.emit(f"输入评论内容失败: {str(e)[:50]}", "ERROR")

                    # 关闭文章窗口，如果是新打开的
                    if article_opened and main_window:
                        try:
                            driver.close()
                            driver.switch_to.window(main_window)
                        except:
                            pass
                    return

            # 随机停顿，模拟思考
            time.sleep(random.uniform(1, 3))

            # 查找提交按钮
            submit_button = None
            submit_button_selectors = [
                # 添加用户提供的XPath路径（优先级最高）
                (By.XPATH, "/html/body/div[1]/div[2]/div[2]/div[3]/div/div/div[2]/div[2]/div[2]/button"),
                (By.CSS_SELECTOR, ".submit-comment"),
                (By.CSS_SELECTOR, ".comment-submit"),
                (By.CSS_SELECTOR, ".comment-button"),
                (By.CSS_SELECTOR, ".tt-comment-publish"),
                (By.CSS_SELECTOR, ".publish-btn"),
                (By.CSS_SELECTOR, "button[type='submit']"),
                (By.XPATH, "//button[contains(text(), '发布')]"),
                (By.XPATH, "//button[contains(text(), '评论')]"),
                (By.XPATH, "//div[contains(@class, 'submit') or contains(@class, 'publish')]")
            ]

            for selector_type, selector_value in submit_button_selectors:
                try:
                    elements = driver.find_elements(selector_type, selector_value)
                    if elements:
                        for element in elements:
                            if element.is_displayed() and element.is_enabled():
                                submit_button = element
                                self.log_message.emit(f"找到评论提交按钮: {selector_type}:{selector_value}", "INFO")
                                break
                        if submit_button:
                            break
                except:
                    continue

            # 如果找到提交按钮，尝试点击
            if submit_button:
                submit_clicked = False
                try:
                    submit_button.click()
                    submit_clicked = True
                    self.log_message.emit("直接点击提交评论成功", "INFO")
                except:
                    try:
                        driver.execute_script("arguments[0].click();", submit_button)
                        submit_clicked = True
                        self.log_message.emit("通过JavaScript点击提交评论成功", "INFO")
                    except Exception as e:
                        self.log_message.emit(f"点击提交按钮失败: {str(e)[:50]}", "ERROR")

                if submit_clicked:
                    # 提交后等待响应
                    time.sleep(random.uniform(2, 4))

                    # 检查是否有评论成功的提示
                    try:
                        page_source = driver.page_source
                        if "评论成功" in page_source or "提交成功" in page_source or comment_text in page_source:
                            self.log_message.emit("确认评论已成功提交", "INFO")
                        else:
                            self.log_message.emit("评论可能已提交，但未找到成功提示", "WARNING")
                    except:
                        pass
                else:
                    self.log_message.emit("评论提交失败", "WARNING")
            else:
                # 尝试直接使用用户提供的XPath路径定位提交按钮
                self.log_message.emit("尝试使用精确XPath路径定位提交按钮", "INFO")
                try:
                    submit_btn = driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div[2]/div[3]/div/div/div[2]/div[2]/div[2]/button")
                    if submit_btn:
                        self.log_message.emit("成功找到提交按钮", "INFO")
                        # 滚动到按钮位置
                        driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", submit_btn)
                        time.sleep(0.5)

                        # 尝试点击
                        try:
                            submit_btn.click()
                            self.log_message.emit("直接点击精确XPath提交按钮成功", "INFO")
                            time.sleep(random.uniform(2, 4))
                        except:
                            try:
                                driver.execute_script("arguments[0].click();", submit_btn)
                                self.log_message.emit("通过JavaScript点击精确XPath提交按钮成功", "INFO")
                                time.sleep(random.uniform(2, 4))
                            except Exception as e:
                                self.log_message.emit(f"点击精确XPath提交按钮失败: {str(e)[:50]}", "ERROR")
                except Exception as e:
                    self.log_message.emit(f"精确XPath定位提交按钮失败: {str(e)[:50]}", "WARNING")

                    # 尝试按回车键提交
                    try:
                        comment_input.send_keys("\n")
                        time.sleep(1)
                        self.log_message.emit("通过回车键提交评论", "INFO")
                    except:
                        self.log_message.emit("未找到评论提交按钮且回车提交失败", "WARNING")

            # 评论后的停顿
            time.sleep(random.uniform(1, 3))

            # 关闭文章窗口，如果是新打开的
            if article_opened and main_window:
                try:
                    driver.close()
                    driver.switch_to.window(main_window)
                    self.log_message.emit("已关闭文章页面并返回主页", "INFO")
                except Exception as e:
                    self.log_message.emit(f"关闭文章页面失败: {str(e)[:50]}", "WARNING")

        except Exception as e:
            self.log_message.emit(f"评论操作过程出错: {str(e)}", "ERROR")

            # 确保返回主窗口
            if 'main_window' in locals() and main_window:
                try:
                    if driver.current_window_handle != main_window:
                        driver.close()
                        driver.switch_to.window(main_window)
                except:
                    pass

    def follow_authors(self, driver):
        """关注作者

        Args:
            driver: Selenium WebDriver对象
        """
        try:
            # 获取配置的关注次数
            follow_count = self.settings.get('follow_count', 1)

            self.status_update.emit(f"正在执行关注操作，计划关注 {follow_count} 个作者...")
            self.log_message.emit(f"开始关注操作，计划关注 {follow_count} 个作者", "INFO")

            # 查找关注按钮
            follow_buttons = []

            # 首先尝试使用用户提供的精确XPath路径
            try:
                follow_button = driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div[3]/div[1]/div/div[1]/button/span")
                if follow_button and follow_button.is_displayed():
                    follow_buttons.append(follow_button)
                    self.log_message.emit("使用精确XPath路径找到关注按钮", "INFO")
            except:
                self.log_message.emit("使用精确XPath路径未找到关注按钮，尝试其他选择器", "INFO")

            # 如果精确XPath未找到，尝试其他选择器
            if not follow_buttons:
                try:
                    elements = driver.find_elements(By.CLASS_NAME, "follow-button")
                    if elements:
                        follow_buttons.extend([e for e in elements if e.is_displayed()])
                        self.log_message.emit(f"使用class='follow-button'找到 {len(elements)} 个关注按钮", "INFO")
                except:
                    pass

            if not follow_buttons:
                try:
                    elements = driver.find_elements(By.CLASS_NAME, "author-follow")
                    if elements:
                        follow_buttons.extend([e for e in elements if e.is_displayed()])
                        self.log_message.emit(f"使用class='author-follow'找到 {len(elements)} 个关注按钮", "INFO")
                except:
                    pass

            # 尝试更多通用选择器
            if not follow_buttons:
                try:
                    # 尝试查找包含"关注"文本的按钮
                    elements = driver.find_elements(By.XPATH, "//button[contains(text(), '关注') or .//span[contains(text(), '关注')]]")
                    if elements:
                        follow_buttons.extend([e for e in elements if e.is_displayed()])
                        self.log_message.emit(f"使用文本内容查找找到 {len(elements)} 个关注按钮", "INFO")
                except:
                    pass

            if not follow_buttons:
                self.log_message.emit("未找到可关注的作者", "WARNING")
                return

            self.log_message.emit(f"找到 {len(follow_buttons)} 个可关注的作者", "INFO")

            # 随机选择要关注的作者
            follow_count = min(follow_count, len(follow_buttons))
            indices = random.sample(range(len(follow_buttons)), follow_count)

            for i, index in enumerate(indices):
                if not self.running:
                    break

                try:
                    # 滚动到关注按钮位置
                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", follow_buttons[index])
                    time.sleep(random.uniform(0.5, 1.5))

                    # 关注
                    try:
                        follow_buttons[index].click()
                    except:
                        # 尝试JavaScript点击
                        driver.execute_script("arguments[0].click();", follow_buttons[index])

                    self.log_message.emit(f"完成第 {i+1}/{follow_count} 次关注", "INFO")

                    # 关注后停顿
                    time.sleep(random.uniform(1, 3))

                except Exception as e:
                    self.log_message.emit(f"执行第 {i+1} 次关注时出错: {str(e)}", "ERROR")

            self.log_message.emit(f"关注操作完成，共关注 {follow_count} 个作者", "INFO")

        except Exception as e:
            self.log_message.emit(f"关注操作过程出错: {str(e)}", "ERROR")

    def scroll_page(self, driver, scroll_count=3):
        """滚动页面 - 优化版本，更自然的滚动行为

        Args:
            driver: Selenium WebDriver对象
            scroll_count: 滚动次数
        """
        try:
            # 获取页面高度信息
            try:
                page_height = driver.execute_script("return Math.max(document.body.scrollHeight, document.documentElement.scrollHeight);")
                viewport_height = driver.execute_script("return window.innerHeight;")
                scrollable_height = max(0, page_height - viewport_height)
                self.log_message.emit(f"页面总高度: {page_height}px, 可滚动高度: {scrollable_height}px", "INFO")
            except Exception as e:
                self.log_message.emit(f"获取页面高度失败: {str(e)[:50]}, 使用默认滚动行为", "WARNING")
                scrollable_height = 5000  # 默认假设页面有5000px高

            # 当前滚动位置
            current_position = driver.execute_script("return window.pageYOffset;")

            # 根据页面长度动态调整滚动行为
            if scrollable_height > 0:
                # 计算每次滚动的平均距离
                avg_scroll_distance = min(800, max(300, scrollable_height / (scroll_count + 1)))

                for i in range(scroll_count):
                    if not hasattr(self, 'running') or self.running:
                        # 变化滚动距离，模拟自然浏览
                        if i == 0:
                            # 第一次滚动距离较小
                            scroll_distance = random.randint(200, int(avg_scroll_distance * 0.7))
                        elif i == scroll_count - 1:
                            # 最后一次滚动距离也较小
                            scroll_distance = random.randint(200, int(avg_scroll_distance * 0.8))
                        else:
                            # 中间滚动距离较大
                            variation = random.uniform(0.8, 1.2)  # 增加随机性
                            scroll_distance = int(avg_scroll_distance * variation)

                        # 确保不会滚动超出页面
                        remaining_scroll = scrollable_height - current_position
                        if remaining_scroll <= 0:
                            # 如果已经到底，随机向上滚动一点
                            if random.random() < 0.3:  # 30%概率
                                up_scroll = random.randint(100, min(500, int(current_position * 0.3)))
                                driver.execute_script(f"window.scrollBy(0, {-up_scroll});")
                                current_position = max(0, current_position - up_scroll)
                                self.log_message.emit(f"已到页面底部，向上滚动 {up_scroll}px", "INFO")
                            break

                        # 限制滚动距离不超过剩余可滚动高度
                        scroll_distance = min(scroll_distance, int(remaining_scroll * 0.9))

                        # 执行滚动
                        if scroll_distance > 0:
                            # 使用平滑滚动
                            driver.execute_script(f"""
                                window.scrollBy({{
                                    top: {scroll_distance},
                                    left: 0,
                                    behavior: 'smooth'
                                }});
                            """)
                            current_position += scroll_distance

                        # 随机等待时间，模拟阅读行为
                        if i == 0 or i == scroll_count - 1:
                            # 第一次和最后一次停留时间较长
                            wait_time = random.uniform(1.5, 3.0)
                        else:
                            wait_time = random.uniform(0.8, 2.2)

                        time.sleep(wait_time)

                        # 在滚动过程中随机点赞 (15%概率)
                        if random.random() < 0.15 and i > 1:  # 不在开始就点赞，更自然
                            self.log_message.emit("在浏览过程中尝试随机点赞", "INFO")
                            try:
                                # 尝试查找当前视口中的点赞按钮
                                like_selectors = [
                                    # 用户提供的精确XPath路径
                                    (By.XPATH, "/html/body/div[1]/div[2]/div[1]/div/div[2]/div/div[1]/div"),
                                    # 其他常见点赞按钮选择器
                                    (By.CSS_SELECTOR, ".tt-article-like"),
                                    (By.CSS_SELECTOR, ".like-button"),
                                    (By.CSS_SELECTOR, ".tt-fav-btn"),
                                    (By.CSS_SELECTOR, "[data-log-click*='like']"),
                                    (By.CSS_SELECTOR, "button[aria-label*='赞']"),
                                    (By.XPATH, "//span[contains(text(), '赞') or contains(text(), '点赞')]"),
                                    (By.XPATH, "//div[contains(@class, 'like') or contains(@class, 'digg')]")
                                ]

                                # 查找当前视口中的点赞按钮
                                like_buttons = []
                                for selector_type, selector_value in like_selectors:
                                    try:
                                        elements = driver.find_elements(selector_type, selector_value)
                                        for element in elements:
                                            try:
                                                if element.is_displayed():
                                                    # 检查元素是否在当前视口内
                                                    is_in_viewport = driver.execute_script("""
                                                        var rect = arguments[0].getBoundingClientRect();
                                                        var windowHeight = window.innerHeight || document.documentElement.clientHeight;
                                                        var windowWidth = window.innerWidth || document.documentElement.clientWidth;
                                                        var vertInView = (rect.top >= 0) && (rect.bottom <= windowHeight);
                                                        var horInView = (rect.left >= 0) && (rect.right <= windowWidth);
                                                        return vertInView && horInView;
                                                    """, element)

                                                    if is_in_viewport:
                                                        like_buttons.append(element)
                                            except:
                                                continue
                                    except:
                                        continue

                                if like_buttons:
                                    # 随机选择一个点赞按钮
                                    like_button = random.choice(like_buttons)
                                    self.log_message.emit("在浏览过程中找到点赞按钮，尝试点赞", "INFO")

                                    # 尝试点击
                                    try:
                                        like_button.click()
                                        self.log_message.emit("浏览过程中点赞成功", "INFO")
                                    except:
                                        try:
                                            driver.execute_script("arguments[0].click();", like_button)
                                            self.log_message.emit("浏览过程中通过JavaScript点赞成功", "INFO")
                                        except Exception as e:
                                            self.log_message.emit(f"浏览过程中点赞失败: {str(e)[:50]}", "WARNING")

                                    # 点赞后短暂停顿
                                    time.sleep(random.uniform(0.5, 1.5))
                            except Exception as e:
                                self.log_message.emit(f"浏览过程中尝试点赞时出错: {str(e)[:50]}", "WARNING")

                        # 在滚动过程中随机评论 (使用设置中的评论概率，且在中后段滚动时)
                        comment_probability = self.settings.get('comment_probability', 30) / 100
                        if random.random() < comment_probability * 0.3 and i > scroll_count * 0.5:  # 乘以0.3是为了降低滚动过程中的评论频率
                            self.log_message.emit("在浏览过程中尝试随机评论", "INFO")
                            try:
                                # 尝试查找评论框
                                comment_input = None
                                comment_input_selectors = [
                                    # 用户提供的XPath路径
                                    (By.XPATH, "/html/body/div[1]/div[2]/div[2]/div[3]/div/div/div[2]/div[2]/div[1]"),
                                    # 其他常见评论框选择器
                                    (By.CSS_SELECTOR, ".comment-input"),
                                    (By.CSS_SELECTOR, "textarea.comment-textarea"),
                                    (By.CSS_SELECTOR, "[placeholder*='评论']"),
                                    (By.XPATH, "//textarea[contains(@placeholder, '写评论')]")
                                ]

                                # 查找评论框
                                for selector_type, selector_value in comment_input_selectors:
                                    try:
                                        elements = driver.find_elements(selector_type, selector_value)
                                        for element in elements:
                                            if element.is_displayed():
                                                comment_input = element
                                                break
                                        if comment_input:
                                            break
                                    except:
                                        continue

                                if comment_input:
                                    self.log_message.emit("在浏览过程中找到评论框，尝试评论", "INFO")

                                    # 滚动到评论框位置
                                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", comment_input)
                                    time.sleep(random.uniform(0.5, 1.0))

                                    # 点击评论框
                                    try:
                                        comment_input.click()
                                    except:
                                        driver.execute_script("arguments[0].click();", comment_input)

                                    # 随机生成评论内容
                                    comments = [
                                        "不错的文章，学习了",
                                        "感谢分享，很有用",
                                        "写得真好，点赞",
                                        "支持一下，继续加油",
                                        "这个观点很有意思",
                                        "学到了新知识，谢谢",
                                        "内容很充实，喜欢",
                                        "分析得很到位",
                                        "很实用的信息",
                                        "期待更多类似的内容"
                                    ]
                                    comment_text = random.choice(comments)

                                    # 输入评论
                                    try:
                                        comment_input.clear()
                                        driver.execute_script("arguments[0].value = arguments[1];", comment_input, comment_text)
                                        time.sleep(0.5)
                                        comment_input.send_keys(" ")
                                        self.log_message.emit(f"成功输入评论: {comment_text}", "INFO")

                                        # 查找提交按钮
                                        submit_button = None
                                        submit_button_selectors = [
                                            # 用户提供的XPath路径
                                            (By.XPATH, "/html/body/div[1]/div[2]/div[2]/div[3]/div/div/div[2]/div[2]/div[2]/button"),
                                            # 其他常见提交按钮选择器
                                            (By.CSS_SELECTOR, ".submit-comment"),
                                            (By.CSS_SELECTOR, ".comment-submit"),
                                            (By.XPATH, "//button[contains(text(), '发布')]"),
                                            (By.XPATH, "//button[contains(text(), '评论')]")
                                        ]

                                        for selector_type, selector_value in submit_button_selectors:
                                            try:
                                                elements = driver.find_elements(selector_type, selector_value)
                                                for element in elements:
                                                    if element.is_displayed() and element.is_enabled():
                                                        submit_button = element
                                                        break
                                                if submit_button:
                                                    break
                                            except:
                                                continue

                                        if submit_button:
                                            # 点击提交按钮
                                            try:
                                                submit_button.click()
                                                self.log_message.emit("浏览过程中评论提交成功", "INFO")
                                            except:
                                                try:
                                                    driver.execute_script("arguments[0].click();", submit_button)
                                                    self.log_message.emit("浏览过程中通过JavaScript评论提交成功", "INFO")
                                                except Exception as e:
                                                    self.log_message.emit(f"浏览过程中评论提交失败: {str(e)[:50]}", "WARNING")
                                        else:
                                            # 尝试回车提交
                                            try:
                                                comment_input.send_keys("\n")
                                                self.log_message.emit("浏览过程中通过回车键提交评论", "INFO")
                                            except:
                                                self.log_message.emit("浏览过程中未找到评论提交按钮且回车提交失败", "WARNING")

                                        # 评论后停顿
                                        time.sleep(random.uniform(1.0, 2.0))
                                    except Exception as e:
                                        self.log_message.emit(f"浏览过程中输入评论失败: {str(e)[:50]}", "WARNING")
                            except Exception as e:
                                self.log_message.emit(f"浏览过程中尝试评论时出错: {str(e)[:50]}", "WARNING")

                        # 在滚动过程中随机关注作者 (使用设置中的关注概率)
                        follow_probability = self.settings.get('follow_probability', 20) / 100
                        if random.random() < follow_probability * 0.2 and i > scroll_count * 0.4:  # 乘以0.2是为了降低滚动过程中的关注频率
                            self.log_message.emit("在浏览过程中尝试随机关注作者", "INFO")
                            try:
                                # 尝试查找关注按钮
                                follow_button = None
                                try:
                                    # 使用用户提供的精确XPath路径
                                    follow_button = driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div[3]/div[1]/div/div[1]/button/span")
                                    if follow_button and follow_button.is_displayed():
                                        self.log_message.emit("在浏览过程中找到关注按钮", "INFO")
                                except:
                                    # 尝试其他通用选择器
                                    try:
                                        elements = driver.find_elements(By.XPATH, "//button[contains(text(), '关注') or .//span[contains(text(), '关注')]]")
                                        for element in elements:
                                            if element.is_displayed():
                                                follow_button = element
                                                break
                                    except:
                                        pass

                                if follow_button:
                                    # 滚动到关注按钮位置
                                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", follow_button)
                                    time.sleep(random.uniform(0.5, 1.0))

                                    # 尝试点击关注按钮
                                    try:
                                        follow_button.click()
                                        self.log_message.emit("浏览过程中关注作者成功", "INFO")
                                    except:
                                        try:
                                            driver.execute_script("arguments[0].click();", follow_button)
                                            self.log_message.emit("浏览过程中通过JavaScript关注作者成功", "INFO")
                                        except Exception as e:
                                            self.log_message.emit(f"浏览过程中关注作者失败: {str(e)[:50]}", "WARNING")

                                    # 关注后停顿
                                    time.sleep(random.uniform(1.0, 2.0))
                            except Exception as e:
                                self.log_message.emit(f"浏览过程中尝试关注作者时出错: {str(e)[:50]}", "WARNING")

                        # 偶尔小幅度上下滚动，模拟用户查看内容
                        if random.random() < 0.2:  # 20%概率
                            small_scroll = random.randint(-100, 100)
                            driver.execute_script(f"window.scrollBy(0, {small_scroll});")
                            current_position = max(0, current_position + small_scroll)
                            time.sleep(random.uniform(0.5, 1.0))
            else:
                # 如果无法获取页面高度，使用简单滚动
                for i in range(scroll_count):
                    if not hasattr(self, 'running') or self.running:
                        # 随机滚动距离
                        scroll_distance = random.randint(300, 800)
                        driver.execute_script(f"window.scrollBy(0, {scroll_distance});")

                        # 随机等待时间
                        wait_time = random.uniform(0.8, 2.0)
                        time.sleep(wait_time)

                        # 在简单滚动模式下也添加随机点赞 (15%概率)
                        if random.random() < 0.15 and i > 1:
                            self.log_message.emit("在简单滚动模式下尝试随机点赞", "INFO")
                            try:
                                # 使用与上面相同的点赞逻辑
                                like_selectors = [
                                    (By.XPATH, "/html/body/div[1]/div[2]/div[1]/div/div[2]/div/div[1]/div"),
                                    (By.CSS_SELECTOR, ".like-button"),
                                    (By.CSS_SELECTOR, ".tt-article-like"),
                                    (By.XPATH, "//span[contains(text(), '赞')]")
                                ]

                                # 查找当前视口中的点赞按钮
                                like_buttons = []
                                for selector_type, selector_value in like_selectors:
                                    try:
                                        elements = driver.find_elements(selector_type, selector_value)
                                        for element in elements:
                                            if element.is_displayed():
                                                like_buttons.append(element)
                                    except:
                                        continue

                                if like_buttons:
                                    # 随机选择一个点赞按钮
                                    like_button = random.choice(like_buttons)

                                    # 尝试点击
                                    try:
                                        like_button.click()
                                        self.log_message.emit("简单滚动模式下点赞成功", "INFO")
                                    except:
                                        try:
                                            driver.execute_script("arguments[0].click();", like_button)
                                            self.log_message.emit("简单滚动模式下通过JavaScript点赞成功", "INFO")
                                        except:
                                            pass

                                    # 点赞后短暂停顿
                                    time.sleep(random.uniform(0.5, 1.0))
                            except:
                                pass

                        # 在简单滚动模式下也添加随机评论 (使用设置中的评论概率)
                        comment_probability = self.settings.get('comment_probability', 30) / 100
                        if random.random() < comment_probability * 0.3 and i > scroll_count * 0.5:  # 乘以0.3是为了降低滚动过程中的评论频率
                            self.log_message.emit("在简单滚动模式下尝试随机评论", "INFO")
                            try:
                                # 使用与上面相同的评论逻辑
                                comment_input = None
                                comment_input_selectors = [
                                    (By.XPATH, "/html/body/div[1]/div[2]/div[2]/div[3]/div/div/div[2]/div[2]/div[1]"),
                                    (By.CSS_SELECTOR, ".comment-input"),
                                    (By.XPATH, "//textarea[contains(@placeholder, '写评论')]")
                                ]

                                # 查找评论框
                                for selector_type, selector_value in comment_input_selectors:
                                    try:
                                        elements = driver.find_elements(selector_type, selector_value)
                                        for element in elements:
                                            if element.is_displayed():
                                                comment_input = element
                                                break
                                        if comment_input:
                                            break
                                    except:
                                        continue

                                if comment_input:
                                    # 简化版评论流程
                                    try:
                                        # 点击评论框
                                        driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", comment_input)
                                        time.sleep(0.5)
                                        driver.execute_script("arguments[0].click();", comment_input)

                                        # 随机评论内容
                                        comments = ["不错的文章", "学习了", "感谢分享", "支持一下", "写得真好"]
                                        comment_text = random.choice(comments)

                                        # 输入评论
                                        driver.execute_script("arguments[0].value = arguments[1];", comment_input, comment_text)
                                        comment_input.send_keys(" ")

                                        # 查找提交按钮
                                        submit_button = driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div[2]/div[3]/div/div/div[2]/div[2]/div[2]/button")
                                        if submit_button:
                                            driver.execute_script("arguments[0].click();", submit_button)
                                            self.log_message.emit("简单滚动模式下评论成功", "INFO")
                                        else:
                                            comment_input.send_keys("\n")

                                        # 评论后停顿
                                        time.sleep(random.uniform(1.0, 2.0))
                                    except:
                                        pass
                            except:
                                pass

                        # 在简单滚动模式下也添加随机关注 (使用设置中的关注概率)
                        follow_probability = self.settings.get('follow_probability', 20) / 100
                        if random.random() < follow_probability * 0.2 and i > scroll_count * 0.4:
                            self.log_message.emit("在简单滚动模式下尝试随机关注作者", "INFO")
                            try:
                                # 简化版关注逻辑
                                follow_button = None

                                # 尝试使用精确XPath路径
                                try:
                                    follow_button = driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div[3]/div[1]/div/div[1]/button/span")
                                except:
                                    # 尝试通用选择器
                                    try:
                                        elements = driver.find_elements(By.XPATH, "//button[contains(text(), '关注')]")
                                        if elements:
                                            for element in elements:
                                                if element.is_displayed():
                                                    follow_button = element
                                                    break
                                    except:
                                        pass

                                if follow_button:
                                    # 尝试点击
                                    try:
                                        driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", follow_button)
                                        time.sleep(0.5)
                                        driver.execute_script("arguments[0].click();", follow_button)
                                        self.log_message.emit("简单滚动模式下关注作者成功", "INFO")
                                        time.sleep(random.uniform(1.0, 2.0))
                                    except:
                                        pass
                            except:
                                pass
        except Exception as e:
            self.log_message.emit(f"滚动页面时出错: {str(e)}", "ERROR")

    def simulate_reading(self, driver, average_duration=60):
        """模拟更自然的阅读行为

        Args:
            driver: Selenium WebDriver对象
            average_duration: 平均阅读时间，单位秒
        """
        try:
            # 获取页面高度，用于计算滚动比例
            try:
                page_height = driver.execute_script("return Math.max(document.body.scrollHeight, document.documentElement.scrollHeight);")
                viewport_height = driver.execute_script("return window.innerHeight;")
                self.log_message.emit(f"页面高度: {page_height}px, 视口高度: {viewport_height}px", "INFO")
            except Exception as e:
                self.log_message.emit(f"获取页面高度失败: {str(e)[:50]}, 使用默认值", "WARNING")
                page_height = 5000
                viewport_height = 800

            # 计算可滚动距离
            scrollable_height = max(0, page_height - viewport_height)

            # 随机阅读时间，使用正态分布使其更自然
            read_time = random.normalvariate(average_duration, average_duration * 0.2)
            read_time = max(15, min(read_time, average_duration * 1.5))

            # 根据文章长度动态调整阅读时间
            if scrollable_height > 0:
                # 页面越长，阅读时间越长
                length_factor = min(2.0, max(0.8, scrollable_height / 5000))
                read_time = read_time * length_factor
                self.log_message.emit(f"根据页面长度调整阅读时间，系数: {length_factor:.2f}", "INFO")

            # 分成多次滚动，滚动次数与页面长度和阅读时间成正比
            scroll_count = max(3, min(20, int(read_time / 8) + int(scrollable_height / 1000)))
            self.log_message.emit(f"模拟阅读，计划时间: {int(read_time)}秒，分 {scroll_count} 次滚动", "INFO")

            # 计算每次滚动的平均距离
            avg_scroll_distance = scrollable_height / scroll_count if scroll_count > 0 else 300

            # 记录当前滚动位置
            current_position = 0

            # 模拟阅读行为 - 更自然的滚动模式
            for i in range(scroll_count):
                if not self.running:
                    break

                # 前30%缓慢滚动，中间40%快速滚动，最后30%再次缓慢滚动
                if i < scroll_count * 0.3 or i > scroll_count * 0.7:
                    # 开始和结束阶段缓慢滚动
                    scroll_distance = random.randint(int(avg_scroll_distance * 0.5), int(avg_scroll_distance * 0.8))
                    wait_time = random.uniform(8, 15)  # 停留时间更长
                else:
                    # 中间阶段快速滚动
                    scroll_distance = random.randint(int(avg_scroll_distance * 0.8), int(avg_scroll_distance * 1.2))
                    wait_time = random.uniform(5, 10)  # 停留时间较短

                # 确保不会滚动超出页面
                if current_position + scroll_distance > scrollable_height:
                    scroll_distance = max(0, scrollable_height - current_position)

                # 执行滚动
                if scroll_distance > 0:
                    driver.execute_script(f"window.scrollBy(0, {scroll_distance});")
                    current_position += scroll_distance

                # 随机等待时间
                time.sleep(wait_time)

                # 随机暂停并向上滚动一点，模拟回看行为 (20%概率)
                if random.random() < 0.2 and i > 0:
                    back_scroll = random.randint(50, min(200, int(current_position * 0.3)))
                    driver.execute_script(f"window.scrollBy(0, {-back_scroll});")
                    current_position = max(0, current_position - back_scroll)
                    self.log_message.emit(f"模拟回看，向上滚动 {back_scroll}px", "INFO")
                    time.sleep(random.uniform(3, 7))  # 回看后停留一会

            # 在阅读结束时，有50%的概率滚动到页面底部
            if random.random() < 0.5 and scrollable_height > current_position:
                remaining_scroll = scrollable_height - current_position
                self.log_message.emit(f"阅读结束，滚动到页面底部，剩余 {remaining_scroll}px", "INFO")

                # 分2-3次滚动到底部
                steps = random.randint(2, 3)
                for step in range(steps):
                    step_scroll = remaining_scroll // steps
                    driver.execute_script(f"window.scrollBy(0, {step_scroll});")
                    time.sleep(random.uniform(1, 3))

            # 阅读完成后，有30%的概率返回页面顶部
            elif random.random() < 0.3:
                self.log_message.emit("阅读结束，返回页面顶部", "INFO")
                driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(random.uniform(1, 3))

            self.log_message.emit("阅读模拟完成", "INFO")

        except Exception as e:
            self.log_message.emit(f"模拟阅读时出错: {str(e)}", "ERROR")

    def like_article_content(self, driver):
        """在文章内进行点赞操作 - 优化版本

        Args:
            driver: Selenium WebDriver对象
        """
        try:
            self.log_message.emit("尝试在文章内点赞", "INFO")

            # 首先尝试等待页面完全加载
            try:
                WebDriverWait(driver, 5).until(
                    lambda d: d.execute_script("return document.readyState") == "complete"
                )
            except:
                self.log_message.emit("等待页面加载完成超时，继续尝试点赞", "WARNING")

            # 先滚动到页面中部，因为点赞按钮通常在文章底部
            try:
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight * 0.7);")
                time.sleep(random.uniform(1, 2))
            except:
                self.log_message.emit("滚动到页面中部失败", "WARNING")

            # 查找文章内的点赞按钮 - 扩展选择器列表
            like_selectors = [
                # 用户提供的精确XPath路径（优先级最高）
                (By.XPATH, "/html/body/div[1]/div[2]/div[1]/div/div[2]/div/div[1]/div"),

                # 常见点赞按钮选择器
                (By.CSS_SELECTOR, ".tt-article-like"),
                (By.CSS_SELECTOR, ".like-button"),
                (By.CSS_SELECTOR, ".tt-fav-btn"),
                (By.CSS_SELECTOR, "[data-log-click*='like']"),
                (By.CSS_SELECTOR, "button[aria-label*='赞']"),
                (By.CSS_SELECTOR, ".tt-video-like"),
                (By.CSS_SELECTOR, ".tt-laud-button"),
                (By.CSS_SELECTOR, ".article-footer .action-item"),

                # 新增更精确的选择器
                (By.CSS_SELECTOR, ".article-footer .like-button"),
                (By.CSS_SELECTOR, ".article-action .like"),
                (By.CSS_SELECTOR, ".action-list .like"),
                (By.CSS_SELECTOR, ".tt-article-action .like"),
                (By.CSS_SELECTOR, ".tt-action-list .like"),
                (By.CSS_SELECTOR, ".action-bar .like"),
                (By.CSS_SELECTOR, ".digg-btn"),
                (By.CSS_SELECTOR, ".digg-wrapper"),

                # 基于文本内容的选择器
                (By.XPATH, "//span[contains(text(), '赞') or contains(text(), '点赞')]"),
                (By.XPATH, "//div[contains(@class, 'like') or contains(@class, 'digg') or contains(@class, 'laud')]"),
                (By.XPATH, "//button[contains(@class, 'like') or contains(@class, 'digg') or contains(@class, 'laud')]"),
                (By.XPATH, "//i[contains(@class, 'like') or contains(@class, 'digg') or contains(@class, 'laud')]"),

                # 通用交互元素选择器
                (By.XPATH, "//div[contains(@class, 'action') or contains(@class, 'interact')]//span[contains(text(), '赞')]"),
                (By.XPATH, "//div[contains(@class, 'footer') or contains(@class, 'bottom')]//span[contains(text(), '赞')]")
            ]

            like_buttons = []

            # 尝试不同的选择器，收集所有可能的点赞按钮
            for selector_type, selector_value in like_selectors:
                try:
                    elements = driver.find_elements(selector_type, selector_value)
                    if elements:
                        for element in elements:
                            try:
                                if element.is_displayed():
                                    # 检查元素是否在视口内或附近
                                    is_near_viewport = driver.execute_script("""
                                        var rect = arguments[0].getBoundingClientRect();
                                        var windowHeight = window.innerHeight || document.documentElement.clientHeight;
                                        var windowWidth = window.innerWidth || document.documentElement.clientWidth;
                                        var vertInView = (rect.top <= windowHeight * 1.5) && (rect.bottom >= -windowHeight * 0.5);
                                        var horInView = (rect.left <= windowWidth) && (rect.right >= 0);
                                        return vertInView && horInView;
                                    """, element)

                                    if is_near_viewport:
                                        like_buttons.append(element)
                            except:
                                continue
                except Exception as e:
                    continue

            if not like_buttons:
                self.log_message.emit("未找到文章内的点赞按钮，尝试滚动到页面底部", "WARNING")

                # 如果没找到，尝试滚动到页面底部再查找
                try:
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(random.uniform(1, 2))

                    # 再次尝试查找
                    for selector_type, selector_value in like_selectors:
                        try:
                            elements = driver.find_elements(selector_type, selector_value)
                            if elements:
                                for element in elements:
                                    if element.is_displayed():
                                        like_buttons.append(element)
                        except:
                            continue
                except:
                    pass

            if not like_buttons:
                self.log_message.emit("经过多次尝试仍未找到文章内的点赞按钮", "WARNING")
                return

            self.log_message.emit(f"找到 {len(like_buttons)} 个可能的点赞按钮", "INFO")

            # 过滤已点赞的按钮
            unliked_buttons = []
            for btn in like_buttons:
                try:
                    # 检查是否已点赞
                    is_already_liked = False

                    # 方法1: 检查类名
                    btn_class = btn.get_attribute("class") or ""
                    if "active" in btn_class or "highlighted" in btn_class or "liked" in btn_class:
                        is_already_liked = True

                    # 方法2: 检查颜色
                    if not is_already_liked:
                        try:
                            color = btn.value_of_css_property("color")
                            if "rgb(254" in color or "rgb(255, 0" in color or "rgb(255, 43" in color:  # 红色系通常表示已点赞
                                is_already_liked = True
                        except:
                            pass

                    # 方法3: 检查文本内容
                    if not is_already_liked:
                        try:
                            btn_text = btn.text.strip()
                            if "已" in btn_text or "取消" in btn_text:
                                is_already_liked = True
                        except:
                            pass

                    if not is_already_liked:
                        unliked_buttons.append(btn)
                except:
                    # 如果无法确定是否已点赞，假设未点赞
                    unliked_buttons.append(btn)

            if not unliked_buttons:
                self.log_message.emit("所有按钮可能已被点赞，跳过点赞操作", "INFO")
                return

            # 选择第一个未点赞的按钮
            like_button = unliked_buttons[0]

            # 滚动到点赞按钮位置，确保可见
            try:
                driver.execute_script("""
                    arguments[0].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                """, like_button)
                time.sleep(random.uniform(1, 2))
            except Exception as e:
                self.log_message.emit(f"滚动到点赞按钮位置失败: {str(e)[:50]}", "WARNING")

            # 点赞 - 使用多种方法尝试
            clicked = False

            # 方法1: 直接点击
            try:
                like_button.click()
                clicked = True
                self.log_message.emit("成功点赞文章", "INFO")
            except Exception as e:
                self.log_message.emit(f"直接点赞失败: {str(e)[:50]}", "WARNING")

            # 方法2: JavaScript点击
            if not clicked:
                try:
                    driver.execute_script("arguments[0].click();", like_button)
                    clicked = True
                    self.log_message.emit("通过JavaScript成功点赞文章", "INFO")
                except Exception as e:
                    self.log_message.emit(f"JavaScript点赞失败: {str(e)[:50]}", "WARNING")

            # 方法3: 使用ActionChains模拟点击
            if not clicked:
                try:
                    from selenium.webdriver.common.action_chains import ActionChains
                    actions = ActionChains(driver)
                    actions.move_to_element(like_button).click().perform()
                    clicked = True
                    self.log_message.emit("通过ActionChains成功点赞文章", "INFO")
                except Exception as e:
                    self.log_message.emit(f"ActionChains点赞失败: {str(e)[:50]}", "WARNING")

            # 方法4: 直接使用用户提供的XPath路径
            if not clicked:
                try:
                    self.log_message.emit("尝试使用精确XPath路径直接定位点赞按钮", "INFO")
                    # 滚动到页面中部
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight * 0.5);")
                    time.sleep(1)

                    # 直接使用XPath定位
                    like_btn = driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div[1]/div/div[2]/div/div[1]/div")
                    if like_btn:
                        self.log_message.emit("成功找到精确XPath点赞按钮", "INFO")
                        # 滚动到按钮位置
                        driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", like_btn)
                        time.sleep(0.5)

                        # 尝试点击
                        try:
                            like_btn.click()
                            clicked = True
                            self.log_message.emit("直接点击精确XPath点赞按钮成功", "INFO")
                        except:
                            try:
                                driver.execute_script("arguments[0].click();", like_btn)
                                clicked = True
                                self.log_message.emit("通过JavaScript点击精确XPath点赞按钮成功", "INFO")
                            except Exception as e:
                                self.log_message.emit(f"点击精确XPath点赞按钮失败: {str(e)[:50]}", "WARNING")
                except Exception as e:
                    self.log_message.emit(f"精确XPath定位点赞按钮失败: {str(e)[:50]}", "WARNING")

            # 点赞后检查是否成功
            if clicked:
                time.sleep(random.uniform(1, 2))
                try:
                    # 重新获取按钮状态
                    btn_class = like_button.get_attribute("class") or ""
                    if "active" in btn_class or "highlighted" in btn_class or "liked" in btn_class:
                        self.log_message.emit("点赞成功确认：按钮状态已更新", "INFO")
                    else:
                        # 检查颜色变化
                        try:
                            color = like_button.value_of_css_property("color")
                            if "rgb(254" in color or "rgb(255, 0" in color or "rgb(255, 43" in color:
                                self.log_message.emit("点赞成功确认：按钮颜色已变化", "INFO")
                        except:
                            pass
                except:
                    pass

                # 点赞后停顿
                time.sleep(random.uniform(1, 3))
            else:
                self.log_message.emit("所有点赞方法均失败", "WARNING")

        except Exception as e:
            self.log_message.emit(f"文章内点赞操作出错: {str(e)}", "ERROR")

    def execute_nurture_behaviors(self, driver):
        """执行养号行为

        Args:
            driver: Selenium WebDriver对象
        """
        try:
            # 记录开始时间
            start_time = datetime.now()
            self.log_message.emit(f"开始执行养号行为: {start_time.strftime('%Y-%m-%d %H:%M:%S')}", "INFO")

            # 浏览内容 (包含在文章内的点赞操作)
            self.browse_content(driver)

            # 随机执行评论操作
            if random.random() < self.settings.get('comment_probability', 30) / 100:
                self.comment_content(driver)

            # 随机执行关注操作
            if random.random() < self.settings.get('follow_probability', 20) / 100:
                self.follow_authors(driver)

            # 记录结束时间
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            self.log_message.emit(f"养号行为执行完成，耗时: {duration:.2f}秒", "INFO")

        except Exception as e:
            self.log_message.emit(f"执行养号行为时出错: {str(e)}", "ERROR")
            raise


class NurtureThread(QThread):
    """头条号养号线程"""

    # 定义信号
    status_update = pyqtSignal(str)  # 状态更新信号
    progress_update = pyqtSignal(int, int)  # 进度更新信号 (当前, 总数)
    log_message = pyqtSignal(str, str)  # 日志信号 (消息, 级别)
    error_message = pyqtSignal(str, str)  # 错误信号 (标题, 消息)
    account_finished = pyqtSignal(int)  # 单个账号完成信号

    def __init__(self, accounts, settings):
        """初始化养号线程

        Args:
            accounts: 需要养号的账号列表
            settings: 养号设置
        """
        super().__init__()
        self.accounts = accounts
        self.settings = settings
        self.running = True

        # 多线程相关
        self.max_threads = settings.get('max_threads', 3)  # 默认最多3个线程
        self.active_threads = 0
        self.thread_lock = threading.Lock()
        self.account_queue = list(enumerate(accounts))
        self.completed_accounts = 0
        self.total_accounts = len(accounts)

        # 添加集合来跟踪已处理和正在处理的账号
        self.processed_accounts = set()  # 已处理完成的账号索引
        self.processing_accounts = set()  # 正在处理中的账号索引

        # 导入代理管理器
        try:
            from app.utils.browser_proxy import BrowserProxyManager
            self.proxy_manager = BrowserProxyManager(settings)
        except ImportError:
            self.proxy_manager = None
            self.log_message.emit("无法导入代理管理器，将使用默认浏览器配置", "WARNING")

    def create_timer_callback(self, func, *args, **kwargs):
        """创建一个无返回值的回调函数，用于QTimer.singleShot

        Args:
            func: 要调用的函数
            *args: 传递给函数的位置参数
            **kwargs: 传递给函数的关键字参数

        Returns:
            function: 无返回值的回调函数
        """
        def callback():
            func(*args, **kwargs)
        return callback

    def get_chrome_driver_path(self):
        """获取ChromeDriver路径

        Returns:
            str: ChromeDriver路径
        """
        try:
            # 尝试从设置中获取
            driver_path = self.settings.get('chrome_driver_path', '')
            if driver_path and os.path.exists(driver_path):
                return driver_path

            # 尝试从默认位置获取
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            driver_path = os.path.join(base_dir, 'drivers', 'chromedriver.exe')
            if os.path.exists(driver_path):
                return driver_path

            return None  # 返回None让Selenium自动查找

        except Exception as e:
            self.log_message.emit(f"获取ChromeDriver路径时出错: {str(e)}", "ERROR")
            return None

    def load_cookies(self, driver, account):
        """加载cookie

        Args:
            driver: Selenium WebDriver对象
            account: 账号信息
        """
        try:
            account_name = account.get('account_name', '')
            self.status_update.emit(f"正在加载账号 {account_name} 的Cookie...")

            # 先访问头条网站
            driver.get("https://www.toutiao.com/")
            time.sleep(1)

            # 获取cookie文件路径
            cookie_file = account.get('cookie_file', '')
            if not cookie_file or not os.path.exists(cookie_file):
                self.log_message.emit(f"账号 {account_name} 的Cookie文件不存在", "ERROR")
                return False

            # 读取cookie文件 - 优化版本
            success = False
            try:
                # 检查文件扩展名，支持.txt和.json
                file_ext = os.path.splitext(cookie_file)[1].lower()

                # 读取文件内容
                with open(cookie_file, 'r', encoding='utf-8') as f:
                    file_content = f.read().strip()

                if not file_content:
                    self.log_message.emit("Cookie文件为空", "ERROR")
                    return False

                # 根据文件扩展名预处理
                if file_ext == '.txt' and not file_content.startswith('{') and not file_content.startswith('['):
                    self.log_message.emit("检测到文本格式Cookie文件", "INFO")
                    # 直接按文本格式处理
                    cookie_count = 0
                    if "=" in file_content:
                        for line in file_content.split("\n"):
                            line = line.strip()
                            if not line or line.startswith('#'):  # 跳过空行和注释
                                continue

                            if "=" in line:
                                try:
                                    name, value = line.split("=", 1)
                                    driver.add_cookie({
                                        'name': name.strip(),
                                        'value': value.strip(),
                                        'domain': '.toutiao.com'
                                    })
                                    cookie_count += 1
                                except Exception as e:
                                    self.log_message.emit(f"添加文本cookie失败: {str(e)[:100]}", "WARNING")

                        if cookie_count > 0:
                            success = True
                            self.log_message.emit(f"已加载文本格式cookie，共 {cookie_count} 项", "INFO")
                    else:
                        self.log_message.emit("文本格式Cookie文件中未找到有效的键值对", "WARNING")
                else:
                    # 尝试解析JSON格式cookie
                    try:
                        cookies = json.loads(file_content)

                        # 处理列表格式的cookie
                        if isinstance(cookies, list):
                            added_count = 0
                            for cookie in cookies:
                                try:
                                    # 确保必要字段存在
                                    if 'name' not in cookie or 'value' not in cookie:
                                        continue

                                    # 设置或修正domain
                                    if 'domain' not in cookie or not cookie['domain']:
                                        cookie['domain'] = '.toutiao.com'
                                    elif not cookie['domain'].startswith('.'):
                                        # 确保domain格式正确
                                        cookie['domain'] = '.' + cookie['domain'].lstrip('.')

                                    # 过滤掉可能导致问题的cookie字段
                                    for field in ['sameSite', 'sourceScheme', 'priority']:
                                        if field in cookie:
                                            del cookie[field]

                                    # 修正expiry类型
                                    if 'expiry' in cookie:
                                        if isinstance(cookie['expiry'], float):
                                            cookie['expiry'] = int(cookie['expiry'])
                                        elif isinstance(cookie['expiry'], str):
                                            try:
                                                cookie['expiry'] = int(float(cookie['expiry']))
                                            except:
                                                del cookie['expiry']

                                    # 添加cookie
                                    driver.add_cookie(cookie)
                                    added_count += 1
                                except Exception as e:
                                    self.log_message.emit(f"添加cookie出错: {str(e)[:100]}", "WARNING")

                            if added_count > 0:
                                success = True
                                self.log_message.emit(f"已加载列表格式cookie，成功添加 {added_count}/{len(cookies)} 项", "INFO")

                        # 处理字典格式的cookie
                        elif isinstance(cookies, dict):
                            # 检查是否包含标准格式的cookie
                            if 'cookies' in cookies and isinstance(cookies['cookies'], list):
                                added_count = 0
                                for cookie in cookies['cookies']:
                                    try:
                                        if 'name' not in cookie or 'value' not in cookie:
                                            continue

                                        if 'domain' not in cookie or not cookie['domain']:
                                            cookie['domain'] = '.toutiao.com'
                                        elif not cookie['domain'].startswith('.'):
                                            cookie['domain'] = '.' + cookie['domain'].lstrip('.')

                                        # 过滤掉可能导致问题的cookie字段
                                        for field in ['sameSite', 'sourceScheme', 'priority']:
                                            if field in cookie:
                                                del cookie[field]

                                        # 修正expiry类型
                                        if 'expiry' in cookie:
                                            if isinstance(cookie['expiry'], float):
                                                cookie['expiry'] = int(cookie['expiry'])
                                            elif isinstance(cookie['expiry'], str):
                                                try:
                                                    cookie['expiry'] = int(float(cookie['expiry']))
                                                except:
                                                    del cookie['expiry']

                                        driver.add_cookie(cookie)
                                        added_count += 1
                                    except Exception as e:
                                        self.log_message.emit(f"添加cookie出错: {str(e)[:100]}", "WARNING")

                                if added_count > 0:
                                    success = True
                                    self.log_message.emit(f"已加载嵌套列表格式cookie，成功添加 {added_count}/{len(cookies['cookies'])} 项", "INFO")

                            elif 'cookies' in cookies and isinstance(cookies['cookies'], dict):
                                # 使用键值对作为cookie
                                added_count = 0
                                for name, value in cookies['cookies'].items():
                                    try:
                                        driver.add_cookie({
                                            'name': name,
                                            'value': str(value),  # 确保值是字符串
                                            'domain': '.toutiao.com'
                                        })
                                        added_count += 1
                                    except Exception as e:
                                        self.log_message.emit(f"添加cookie出错: {str(e)[:100]}", "WARNING")

                                if added_count > 0:
                                    success = True
                                    self.log_message.emit(f"已加载嵌套键值对格式cookie，成功添加 {added_count}/{len(cookies['cookies'])} 项", "INFO")

                            else:
                                # 直接使用键值对作为cookie
                                added_count = 0
                                for name, value in cookies.items():
                                    try:
                                        # 跳过非cookie字段
                                        if name in ['version', 'timestamp', 'metadata', 'info']:
                                            continue

                                        driver.add_cookie({
                                            'name': name,
                                            'value': str(value),  # 确保值是字符串
                                            'domain': '.toutiao.com'
                                        })
                                        added_count += 1
                                    except Exception as e:
                                        self.log_message.emit(f"添加cookie出错: {str(e)[:100]}", "WARNING")

                                if added_count > 0:
                                    success = True
                                    self.log_message.emit(f"已加载键值对格式cookie，成功添加 {added_count}/{len(cookies)} 项", "INFO")

                    # 如果JSON解析失败，尝试其他格式
                    except json.JSONDecodeError:
                        self.log_message.emit(f"非标准JSON格式，尝试解析文本格式cookie", "WARNING")

                        # 尝试键值对文本格式
                        cookie_count = 0
                        if "=" in file_content:
                            for line in file_content.split("\n"):
                                line = line.strip()
                                if not line or line.startswith('#'):  # 跳过空行和注释
                                    continue

                                if "=" in line:
                                    try:
                                        name, value = line.split("=", 1)
                                        driver.add_cookie({
                                            'name': name.strip(),
                                            'value': value.strip(),
                                            'domain': '.toutiao.com'
                                        })
                                        cookie_count += 1
                                    except Exception as e:
                                        self.log_message.emit(f"添加文本cookie失败: {str(e)[:100]}", "WARNING")

                            if cookie_count > 0:
                                success = True
                                self.log_message.emit(f"已加载文本格式cookie，共 {cookie_count} 项", "INFO")
            except Exception as read_err:
                self.log_message.emit(f"处理Cookie文件出错: {str(read_err)}", "ERROR")
                return False

            # 确认是否成功加载了cookie
            if not success:
                self.log_message.emit("Cookie格式无法识别或加载失败", "ERROR")
                return False

            # 刷新页面以应用cookie
            driver.refresh()
            time.sleep(2)

            # 优化登录检测逻辑
            login_success = False
            max_wait_time = 25  # 增加等待时间
            start_time = time.time()

            # 定义多种登录成功的检测方法
            login_indicators = [
                # 文本指标
                ("退出登录", "页面包含'退出登录'文本"),
                ("个人中心", "页面包含'个人中心'文本"),
                ("我的主页", "页面包含'我的主页'文本"),
                ("账号设置", "页面包含'账号设置'文本"),

                # 元素指标
                (".logout-btn", "找到退出登录按钮"),
                (".user-info", "找到用户信息元素"),
                (".user-name", "找到用户名元素"),
                (".user-avatar", "找到用户头像元素"),
                (".tt-user-card", "找到用户卡片元素")
            ]

            # 检测循环
            check_count = 0
            while time.time() - start_time < max_wait_time:
                check_count += 1
                self.log_message.emit(f"第 {check_count} 次检查登录状态...", "INFO")

                # 方法1: 检查页面文本
                page_source = driver.page_source
                for text, desc in login_indicators[:4]:  # 前4个是文本指标
                    if text in page_source:
                        login_success = True
                        self.log_message.emit(f"登录成功确认: {desc}", "INFO")
                        break

                # 如果文本检查成功，跳出循环
                if login_success:
                    break

                # 方法2: 检查页面元素
                for selector, desc in login_indicators[4:]:  # 后面的是元素指标
                    try:
                        element = driver.find_element(By.CSS_SELECTOR, selector)
                        if element and element.is_displayed():
                            login_success = True
                            self.log_message.emit(f"登录成功确认: {desc}", "INFO")
                            break
                    except:
                        pass

                # 如果元素检查成功，跳出循环
                if login_success:
                    break

                # 方法3: 尝试访问需要登录的页面
                if check_count == 3:  # 第3次检查时尝试访问个人中心
                    try:
                        current_url = driver.current_url
                        driver.get("https://www.toutiao.com/c/user/center/")
                        time.sleep(2)

                        # 检查URL是否包含用户ID，通常登录成功后URL会包含用户ID
                        if "/c/user/" in driver.current_url and "login" not in driver.current_url:
                            login_success = True
                            self.log_message.emit("登录成功确认: 成功访问个人中心页面", "INFO")
                            break

                        # 返回原页面
                        driver.get(current_url)
                        time.sleep(1)
                    except:
                        pass

                # 等待一段时间再次检查
                time.sleep(1)

            # 最终登录结果处理
            if login_success:
                self.log_message.emit(f"账号 {account_name} 登录成功", "INFO")

                # 尝试获取用户名，进一步确认登录状态
                try:
                    username_elements = driver.find_elements(By.CSS_SELECTOR, ".user-name, .username, .name")
                    if username_elements:
                        for elem in username_elements:
                            if elem.is_displayed() and elem.text.strip():
                                self.log_message.emit(f"检测到用户名: {elem.text.strip()}", "INFO")
                                break
                except:
                    pass

                return True
            else:
                self.log_message.emit(f"账号 {account_name} 登录失败，Cookie可能已过期", "WARNING")

                # 记录当前URL和页面标题，帮助诊断问题
                try:
                    self.log_message.emit(f"当前URL: {driver.current_url}", "WARNING")
                    self.log_message.emit(f"页面标题: {driver.title}", "WARNING")
                except:
                    pass

                return False

        except Exception as e:
            self.log_message.emit(f"加载Cookie时出错: {str(e)}", "ERROR")
            return False

    def run(self):
        """运行线程"""
        self.running = True
        self.status_update.emit("开始执行养号任务...")
        self.log_message.emit("开始执行养号任务...", "INFO")

        try:
            # 初始化账号队列和状态
            self.account_queue = list(enumerate(self.accounts))
            self.completed_accounts = 0
            self.total_accounts = len(self.accounts)
            self.active_threads = 0
            self.processing_accounts = set()
            self.processed_accounts = set()
            self.retry_count = {}
            self.thread_starting = False

            # 输出账号队列信息
            self.log_message.emit(f"初始化账号队列完成，共 {self.total_accounts} 个账号", "INFO")
            if self.total_accounts > 0:
                first_account = self.accounts[0]
                first_account_name = first_account.get('account_name', '未知账号')
                self.log_message.emit(f"第一个账号: {first_account_name}", "INFO")

            # 启动初始线程
            initial_threads = min(self.max_threads, self.total_accounts)
            self.log_message.emit(f"启动 {initial_threads} 个并发线程处理账号", "INFO")

            # 获取线程启动间隔设置（毫秒）- 使用更短的间隔以快速启动所有线程
            thread_start_interval = self.settings.get('thread_start_interval', 2)  # 默认2秒
            interval_ms = max(500, thread_start_interval * 1000)  # 最小500毫秒间隔
            self.log_message.emit(f"线程启动间隔: {interval_ms/1000:.1f}秒", "INFO")

            # 使用QTimer启动所有初始线程，包括第一个
            for i in range(initial_threads):
                # 第一个线程立即启动，后续线程使用递增间隔
                delay = 0 if i == 0 else i * interval_ms
                self.log_message.emit(f"计划在 {delay/1000:.1f}秒 后启动第 {i+1} 个线程", "INFO")
                QTimer.singleShot(delay, self.create_timer_callback(self.start_initial_thread))

            # 设置监控定时器，定期检查线程状态
            self.monitor_timer = QTimer()
            self.monitor_timer.timeout.connect(self.monitor_threads)
            self.monitor_timer.start(3000)  # 每3秒检查一次
            self.log_message.emit("已启动线程监控定时器", "INFO")

            # 设置状态检查定时器，替代阻塞式等待
            self.status_timer = QTimer()
            self.status_timer.timeout.connect(self.check_completion_status)
            self.status_timer.start(1000)  # 每1秒检查一次完成状态
            self.log_message.emit("已启动状态检查定时器，使用事件驱动方式等待任务完成", "INFO")

            # 使用事件循环等待，而不是阻塞式sleep
            self.log_message.emit("开始事件驱动等待所有账号处理完成", "INFO")
            self.exec_()  # 启动事件循环，等待任务完成

        except Exception as e:
            self.error_message.emit("养号任务出错", str(e))
            self.log_message.emit(f"养号任务出错: {str(e)}", "ERROR")
            traceback.print_exc()
        finally:
            # 停止所有定时器
            if hasattr(self, 'monitor_timer') and self.monitor_timer:
                self.monitor_timer.stop()
                self.log_message.emit("已停止线程监控定时器", "INFO")

            if hasattr(self, 'status_timer') and self.status_timer:
                self.status_timer.stop()
                self.log_message.emit("已停止状态检查定时器", "INFO")

            if self.completed_accounts >= self.total_accounts:
                self.log_message.emit("所有账号处理完成", "INFO")
            elif not self.running:
                self.log_message.emit("养号任务被手动停止", "INFO")

    def check_completion_status(self):
        """检查任务完成状态"""
        try:
            # 检查是否所有账号都已处理完成
            if self.completed_accounts >= self.total_accounts:
                self.log_message.emit("所有账号处理完成，退出事件循环", "INFO")
                self.quit()  # 退出事件循环
                return

            # 检查是否被手动停止
            if not self.running:
                self.log_message.emit("养号任务被手动停止，退出事件循环", "INFO")
                self.quit()  # 退出事件循环
                return

            # 输出当前状态
            self.log_message.emit(f"养号任务进行中: 已完成 {self.completed_accounts}/{self.total_accounts}, 活跃线程: {self.active_threads}, 队列长度: {len(self.account_queue)}", "INFO")

            # 如果没有活跃线程但队列不为空，尝试启动新线程
            if self.active_threads == 0 and self.account_queue and self.running:
                self.log_message.emit("检测到没有活跃线程但队列中还有账号，尝试启动新线程", "INFO")
                self.thread_starting = False  # 重置线程启动标志
                self.start_initial_thread()  # 直接调用，不使用QTimer

        except Exception as e:
            self.log_message.emit(f"检查完成状态时出错: {str(e)}", "ERROR")

    def monitor_threads(self):
        """监控线程状态，确保任务正常进行"""
        try:
            # 检查是否有活跃线程，如果没有但还有账号，尝试启动
            if self.active_threads == 0 and self.account_queue and self.running:
                self.log_message.emit("监控检测到没有活跃线程但队列中还有账号，尝试启动新线程", "INFO")

                # 重置线程启动标志，以防之前的启动过程卡住
                self.thread_starting = False

                # 启动新线程
                QTimer.singleShot(500, self.create_timer_callback(self.start_new_thread_when_idle))

            # 检查是否有僵尸线程（计数器显示活跃但实际已经结束）
            if self.active_threads > 0 and len(self.processing_accounts) == 0:
                self.log_message.emit(f"检测到可能的僵尸线程: 活跃计数为 {self.active_threads} 但处理中账号集合为空", "WARNING")
                # 修正计数器
                self.active_threads = 0
                self.log_message.emit("已修正活跃线程计数", "INFO")

                # 尝试启动新线程
                if self.account_queue and self.running:
                    QTimer.singleShot(500, self.create_timer_callback(self.start_new_thread_when_idle))

        except Exception as e:
            self.log_message.emit(f"监控线程状态时出错: {str(e)}", "ERROR")

    def start_initial_thread(self):
        """启动初始线程"""
        try:
            self.log_message.emit("开始执行start_initial_thread方法", "INFO")

            # 检查基本条件
            if not self.running:
                self.log_message.emit("养号任务已停止，无法启动初始线程", "WARNING")
                return False

            if not self.account_queue:
                self.log_message.emit("账号队列为空，无法启动初始线程", "WARNING")
                return False

            # 输出当前状态
            self.log_message.emit(f"当前状态: 活跃线程数={self.active_threads}, 队列长度={len(self.account_queue)}, 已完成账号数={self.completed_accounts}", "INFO")

            # 使用更简单的方法启动线程，避免复杂的嵌套逻辑
            return self.start_next_account_thread()
        except Exception as e:
            self.log_message.emit(f"start_initial_thread方法执行出错: {str(e)}", "ERROR")
            import traceback
            self.log_message.emit(traceback.format_exc(), "ERROR")
            return False

    def start_new_thread_when_idle(self):
        """当没有活跃线程但队列中还有账号时，启动新线程"""
        # 使用线程锁，防止多个线程同时启动
        with self.thread_lock:
            # 检查是否已经有线程正在启动
            if self.thread_starting:
                self.log_message.emit("已有线程正在启动中，跳过", "INFO")
                return

            # 检查是否满足启动条件
            if self.active_threads > 0:
                self.log_message.emit(f"当前有 {self.active_threads} 个活跃线程，不需要启动新线程", "INFO")
                return

            if not self.account_queue:
                self.log_message.emit("账号队列为空，无法启动新线程", "INFO")
                return

            if not self.running:
                self.log_message.emit("养号任务已停止，无法启动新线程", "INFO")
                return

            # 设置线程启动标志
            self.thread_starting = True

            try:
                # 启动新线程
                success = self.start_next_account_thread()
                if success:
                    self.log_message.emit("成功启动新线程", "INFO")
                else:
                    self.log_message.emit("启动新线程失败", "WARNING")

                    # 如果启动失败但队列不为空，可能是临时问题，等待一段时间后再次尝试
                    if self.account_queue and self.running:
                        self.log_message.emit("5秒后将再次尝试启动新线程", "INFO")
                        QTimer.singleShot(5000, self.create_timer_callback(self.reset_thread_starting_flag))
            finally:
                # 如果成功启动，在线程完成时会重置标志
                # 如果启动失败，需要在这里重置标志
                if not success:
                    self.thread_starting = False

    def reset_thread_starting_flag(self):
        """重置线程启动标志"""
        self.thread_starting = False
        self.log_message.emit("线程启动标志已重置", "INFO")

        # 再次检查是否需要启动新线程
        if self.active_threads == 0 and self.account_queue and self.running:
            self.log_message.emit("重新尝试启动新线程", "INFO")
            QTimer.singleShot(500, self.create_timer_callback(self.start_new_thread_when_idle))

    def start_next_account_thread(self):
        """启动下一个账号处理线程"""
        with self.thread_lock:
            if not self.account_queue:
                self.log_message.emit("账号队列为空，无法启动新线程", "WARNING")
                return False
            if not self.running:
                self.log_message.emit("养号任务已停止，无法启动新线程", "WARNING")
                return False

            # 记录当前线程数
            self.log_message.emit(f"启动新线程前，当前活跃线程数: {self.active_threads}", "INFO")

            # 检查是否已达到最大线程数
            if self.active_threads >= self.max_threads:
                self.log_message.emit(f"已达到最大线程数 {self.max_threads}，暂不启动新线程", "INFO")
                return False

            # 使用迭代而不是递归来查找可处理的账号
            valid_account_found = False
            skipped_accounts = []

            while self.account_queue and not valid_account_found:
                # 从队列中获取下一个账号
                i, account = self.account_queue.pop(0)

                # 检查账号是否已经处理过或正在处理中
                if hasattr(self, 'processed_accounts') and i in self.processed_accounts:
                    self.log_message.emit(f"账号索引 {i} 已经处理过，跳过", "WARNING")
                    skipped_accounts.append((i, account))  # 保存跳过的账号
                    continue

                if hasattr(self, 'processing_accounts') and i in self.processing_accounts:
                    self.log_message.emit(f"账号索引 {i} 正在处理中，跳过", "WARNING")
                    skipped_accounts.append((i, account))  # 保存跳过的账号
                    continue

                # 找到有效账号
                valid_account_found = True

                # 标记账号为正在处理中
                if hasattr(self, 'processing_accounts'):
                    self.processing_accounts.add(i)
                self.active_threads += 1

                # 创建并启动线程
                account_name = account.get('account_name', f'账号{i+1}')
                self.log_message.emit(f"启动线程处理账号 {account_name} ({i+1}/{self.total_accounts})，当前活跃线程: {self.active_threads}", "INFO")
                self.progress_update.emit(i+1, self.total_accounts)

                try:
                    thread = threading.Thread(
                        target=self.process_account_thread,
                        args=(i, account),
                        daemon=True
                    )
                    thread.start()
                    self.log_message.emit(f"成功创建并启动线程处理账号 {account_name}", "INFO")
                except Exception as e:
                    self.log_message.emit(f"创建线程处理账号 {account_name} 时出错: {str(e)}", "ERROR")
                    self.active_threads -= 1  # 回滚计数器
                    # 从处理中集合中移除
                    if hasattr(self, 'processing_accounts') and i in self.processing_accounts:
                        self.processing_accounts.remove(i)
                    # 将账号放回队列
                    skipped_accounts.append((i, account))
                    valid_account_found = False

            # 将跳过的账号放回队列末尾
            if skipped_accounts:
                self.account_queue.extend(skipped_accounts)
                self.log_message.emit(f"将 {len(skipped_accounts)} 个跳过的账号放回队列末尾", "INFO")

            return valid_account_found

    def execute_nurture_behaviors(self, driver):
        """执行养号行为

        Args:
            driver: Selenium WebDriver对象
        """
        try:
            # 记录开始时间
            start_time = datetime.now()
            self.log_message.emit(f"开始执行养号行为: {start_time.strftime('%Y-%m-%d %H:%M:%S')}", "INFO")

            # 浏览内容 (包含在文章内的点赞、评论和关注操作)
            self.browse_content(driver)

            # 随机执行额外的点赞操作
            like_probability = self.settings.get('like_probability', 70) / 100
            if random.random() < like_probability:
                self.log_message.emit("浏览完成后，额外执行点赞操作", "INFO")
                try:
                    # 进入头条首页
                    driver.get("https://www.toutiao.com/")
                    time.sleep(random.uniform(2, 4))

                    # 滚动页面
                    self.scroll_page(driver, random.randint(2, 4))

                    # 执行点赞
                    self.like_article_content(driver)
                except Exception as e:
                    self.log_message.emit(f"额外执行点赞操作失败: {str(e)[:50]}", "WARNING")

            # 记录结束时间
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            self.log_message.emit(f"养号行为执行完成，耗时: {duration:.2f}秒", "INFO")

        except Exception as e:
            self.log_message.emit(f"执行养号行为时出错: {str(e)}", "ERROR")
            raise

    def browse_content(self, driver):
        """浏览内容

        Args:
            driver: Selenium WebDriver对象
        """
        try:
            # 获取配置的浏览次数
            browse_count = self.settings.get('browse_count', 8)
            average_duration = self.settings.get('browse_duration', 60)
            enable_scroll = self.settings.get('enable_scroll', True)

            self.status_update.emit(f"正在浏览内容，计划浏览 {browse_count} 篇文章...")
            self.log_message.emit(f"开始浏览内容，计划浏览 {browse_count} 篇文章", "INFO")

            # 进入头条首页
            driver.get("https://www.toutiao.com/")
            time.sleep(random.uniform(2, 4))

            # 随机滚动页面，查找可点击的文章
            if enable_scroll:
                self.scroll_page(driver, random.randint(3, 6))

            # 查找文章元素
            articles = []
            selectors = [
                (By.TAG_NAME, "article"),
                (By.CSS_SELECTOR, ".feed-card-article"),
                (By.CSS_SELECTOR, ".title-box"),
                (By.CSS_SELECTOR, "a.link"),
                (By.CSS_SELECTOR, "[data-log-click]")
            ]

            # 尝试不同的选择器查找文章
            for selector_type, selector_value in selectors:
                try:
                    found_elements = driver.find_elements(selector_type, selector_value)
                    if found_elements:
                        articles = found_elements
                        self.log_message.emit(f"使用选择器 {selector_type}:{selector_value} 找到 {len(found_elements)} 个文章元素", "INFO")
                        break
                except Exception as e:
                    self.log_message.emit(f"使用选择器 {selector_type}:{selector_value} 查找文章失败: {str(e)[:50]}", "WARNING")

            if not articles:
                self.log_message.emit("未找到可浏览的文章元素，尝试通用链接选择器", "WARNING")
                # 最后尝试通用链接搜索
                try:
                    articles = driver.find_elements(By.TAG_NAME, "a")
                    # 只保留可能是文章的链接
                    articles = [a for a in articles if a.text and len(a.text) > 5 and not a.get_attribute("href").endswith("#")]
                    self.log_message.emit(f"使用通用链接选择器找到 {len(articles)} 个可能的文章元素", "INFO")
                except Exception as e:
                    self.log_message.emit(f"通用链接搜索失败: {str(e)}", "ERROR")

            if not articles:
                self.log_message.emit("所有方法都未找到可浏览的文章，尝试刷新页面", "WARNING")
                driver.refresh()
                time.sleep(3)
                try:
                    articles = driver.find_elements(By.TAG_NAME, "a")
                    articles = [a for a in articles if a.text and len(a.text) > 10]
                    self.log_message.emit(f"刷新页面后找到 {len(articles)} 个可能的文章元素", "INFO")
                except Exception as e:
                    self.log_message.emit(f"刷新页面后查找文章失败: {str(e)}", "ERROR")

            if not articles:
                self.log_message.emit("未能找到任何可浏览的文章元素", "ERROR")
                return

            # 随机选择要浏览的文章
            if len(articles) > browse_count:
                indices = random.sample(range(len(articles)), browse_count)
            else:
                indices = list(range(len(articles)))
                # 如果文章数量不足，可能需要重复浏览
                while len(indices) < browse_count:
                    indices.append(random.randint(0, len(articles) - 1))

            # 记录主窗口句柄
            main_window = driver.current_window_handle
            articles_viewed = 0

            # 浏览选定的文章
            for i in range(browse_count):
                if not self.running:
                    self.log_message.emit("养号任务已停止，中断浏览", "WARNING")
                    break

                # 获取要浏览的文章索引
                if i < len(indices):
                    index = indices[i]
                else:
                    # 如果索引超出范围，随机选择一篇文章
                    index = random.randint(0, len(articles) - 1)

                # 确保索引在有效范围内
                index = min(index, len(articles) - 1)

                try:
                    article = articles[index]

                    # 获取并记录文章标题
                    article_title = article.text[:30] if article.text else "未知标题"
                    self.log_message.emit(f"准备打开文章: {article_title}...", "INFO")

                    # 确保元素在视图中
                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", article)
                    time.sleep(random.uniform(0.8, 1.8))

                    # 尝试获取文章链接
                    article_url = None
                    try:
                        # 尝试获取href属性
                        href = article.get_attribute("href")
                        if href and not href.startswith("javascript:") and not href == "#":
                            article_url = href
                            self.log_message.emit(f"获取到文章链接: {href[:50]}...", "INFO")
                    except Exception as e:
                        self.log_message.emit(f"获取文章链接失败: {str(e)[:50]}", "WARNING")

                    # 如果没有获取到链接，尝试从父元素或子元素获取
                    if not article_url:
                        try:
                            # 尝试从父元素获取
                            parent = driver.execute_script("return arguments[0].parentNode;", article)
                            href = parent.get_attribute("href")
                            if href and not href.startswith("javascript:") and not href == "#":
                                article_url = href
                                self.log_message.emit(f"从父元素获取到文章链接: {href[:50]}...", "INFO")
                        except Exception as e:
                            self.log_message.emit(f"从父元素获取文章链接失败: {str(e)[:50]}", "WARNING")

                    # 如果仍然没有获取到链接，尝试从子元素获取
                    if not article_url:
                        try:
                            # 尝试从子元素获取
                            links = article.find_elements(By.TAG_NAME, "a")
                            for link in links:
                                href = link.get_attribute("href")
                                if href and not href.startswith("javascript:") and not href == "#":
                                    article_url = href
                                    self.log_message.emit(f"从子元素获取到文章链接: {href[:50]}...", "INFO")
                                    break
                        except Exception as e:
                            self.log_message.emit(f"从子元素获取文章链接失败: {str(e)[:50]}", "WARNING")

                    # 如果没有获取到链接，尝试下一篇文章
                    if not article_url:
                        self.log_message.emit(f"无法获取第 {i+1}/{browse_count} 篇文章的链接，尝试下一篇", "WARNING")
                        continue

                    # 记录当前窗口数量
                    original_handles = driver.window_handles
                    original_handle_count = len(original_handles)

                    # 直接在新标签页打开链接
                    try:
                        # 使用JavaScript打开新标签页
                        driver.execute_script("window.open(arguments[0]);", article_url)
                        self.log_message.emit(f"通过JavaScript在新标签页打开第 {i+1}/{browse_count} 篇文章", "INFO")

                        # 等待新窗口打开
                        wait_count = 0
                        new_window_found = False

                        while wait_count < 20 and not new_window_found:  # 增加等待时间
                            current_handles = driver.window_handles
                            if len(current_handles) > original_handle_count:
                                new_window_found = True
                                break
                            time.sleep(0.5)
                            wait_count += 1

                        if not new_window_found:
                            # 如果没有检测到新窗口，尝试直接导航到文章页面
                            self.log_message.emit(f"未检测到新窗口，尝试直接导航到文章页面", "WARNING")

                            # 保存当前URL，以便稍后返回
                            current_url = driver.current_url

                            # 直接导航到文章页面
                            driver.get(article_url)
                            self.log_message.emit(f"直接导航到文章页面: {article_url[:50]}...", "INFO")

                            # 模拟阅读行为
                            self.simulate_reading(driver, average_duration)
                            articles_viewed += 1

                            # 在文章内执行点赞操作
                            if random.random() < self.settings.get('like_probability', 70) / 100:
                                self.like_article_content(driver)

                            # 返回原页面
                            driver.get(current_url)
                            time.sleep(random.uniform(2, 4))

                            # 继续下一篇文章
                            continue
                    except Exception as e:
                        self.log_message.emit(f"打开新标签页失败: {str(e)[:50]}", "WARNING")
                        continue

                    # 切换到新窗口
                    for handle in driver.window_handles:
                        if handle != main_window:
                            driver.switch_to.window(handle)
                            break

                    # 检查是否成功加载文章页面
                    current_url = driver.current_url
                    if "toutiao.com" not in current_url:
                        self.log_message.emit(f"打开的页面不是头条文章: {current_url[:50]}...", "WARNING")

                    # 模拟阅读行为
                    self.simulate_reading(driver, average_duration)
                    articles_viewed += 1

                    # 在文章内执行点赞操作
                    if random.random() < self.settings.get('like_probability', 70) / 100:
                        self.like_article_content(driver)

                    # 关闭当前窗口并切换回主窗口
                    if driver.current_window_handle != main_window:
                        driver.close()
                        driver.switch_to.window(main_window)

                    # 重新获取文章列表，因为页面可能已更新
                    if i < browse_count - 1:  # 如果不是最后一篇文章
                        # 刷新页面以获取新内容
                        if random.random() < 0.3:  # 30%概率刷新页面
                            driver.refresh()
                            time.sleep(random.uniform(2, 4))

                            # 重新查找文章元素
                            for selector_type, selector_value in selectors:
                                try:
                                    found_elements = driver.find_elements(selector_type, selector_value)
                                    if found_elements:
                                        articles = found_elements
                                        break
                                except:
                                    continue

                            if articles:
                                # 更新剩余要浏览的文章索引
                                remaining_count = browse_count - (i + 1)
                                if remaining_count > 0 and len(articles) > 0:
                                    new_indices = random.sample(range(len(articles)), min(remaining_count, len(articles)))
                                    indices[i+1:] = new_indices[:remaining_count]

                        # 滚动页面以加载新内容
                        if enable_scroll:
                            self.scroll_page(driver, random.randint(1, 3))

                except Exception as e:
                    self.log_message.emit(f"浏览第 {i+1} 篇文章时出错: {str(e)}", "ERROR")
                    # 确保返回主窗口
                    try:
                        if driver.current_window_handle != main_window:
                            driver.close()
                            driver.switch_to.window(main_window)
                    except Exception as window_err:
                        self.log_message.emit(f"切换回主窗口时出错: {str(window_err)}", "ERROR")
                        # 如果出错，尝试重新加载页面
                        try:
                            driver.get("https://www.toutiao.com/")
                            time.sleep(2)
                        except:
                            pass

            self.log_message.emit(f"浏览内容完成，实际浏览了 {articles_viewed} 篇文章", "INFO")

        except Exception as e:
            self.log_message.emit(f"浏览内容过程出错: {str(e)}", "ERROR")

    def scroll_page(self, driver, scroll_count=3):
        """滚动页面

        Args:
            driver: Selenium WebDriver对象
            scroll_count: 滚动次数
        """
        try:
            for _ in range(scroll_count):
                # 随机滚动距离
                scroll_distance = random.randint(300, 800)
                driver.execute_script(f"window.scrollBy(0, {scroll_distance});")

                # 随机等待时间
                time.sleep(random.uniform(0.5, 2))
        except Exception as e:
            self.log_message.emit(f"滚动页面时出错: {str(e)}", "ERROR")

    def simulate_reading(self, driver, average_duration=60):
        """模拟阅读行为

        Args:
            driver: Selenium WebDriver对象
            average_duration: 平均阅读时间，单位秒
        """
        try:
            # 重置当前文章的评论次数计数器
            setattr(self, '_current_article_comments', 0)
            self.log_message.emit("已重置当前文章评论计数器", "INFO")

            # 随机阅读时间，围绕平均值浮动
            read_time = random.normalvariate(average_duration, average_duration * 0.2)
            read_time = max(10, min(read_time, average_duration * 1.5))

            # 分成多次滚动
            scroll_count = int(read_time / 10) + 1
            self.log_message.emit(f"模拟阅读，计划时间: {int(read_time)}秒，分 {scroll_count} 次滚动", "INFO")

            # 模拟阅读行为
            for i in range(scroll_count):
                if not self.running:
                    break

                # 随机滚动距离
                scroll_distance = random.randint(100, 500)
                driver.execute_script(f"window.scrollBy(0, {scroll_distance});")

                # 随机等待时间
                wait_time = random.uniform(5, 15)
                time.sleep(wait_time)

                # 在滚动过程中随机执行点赞操作 (使用设置中的点赞概率)
                like_probability = self.settings.get('like_probability', 70) / 100
                if random.random() < like_probability:
                    self.log_message.emit("在阅读过程中尝试随机点赞", "INFO")
                    try:
                        # 尝试查找当前视口中的点赞按钮
                        like_selectors = [
                            # 用户提供的精确XPath路径
                            (By.XPATH, "/html/body/div[1]/div[2]/div[1]/div/div[2]/div/div[1]/div"),
                            # 其他常见点赞按钮选择器
                            (By.CSS_SELECTOR, ".tt-article-like"),
                            (By.CSS_SELECTOR, ".like-button"),
                            (By.CSS_SELECTOR, ".tt-fav-btn"),
                            (By.CSS_SELECTOR, "[data-log-click*='like']"),
                            (By.CSS_SELECTOR, "button[aria-label*='赞']"),
                            (By.XPATH, "//span[contains(text(), '赞') or contains(text(), '点赞')]"),
                            (By.XPATH, "//div[contains(@class, 'like') or contains(@class, 'digg')]")
                        ]

                        like_button = None
                        for selector_type, selector_value in like_selectors:
                            try:
                                elements = driver.find_elements(selector_type, selector_value)
                                for element in elements:
                                    if element.is_displayed():
                                        like_button = element
                                        break
                                if like_button:
                                    break
                            except:
                                continue

                        if like_button:
                            # 滚动到点赞按钮位置
                            driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", like_button)
                            time.sleep(random.uniform(0.5, 1.0))

                            # 尝试点击点赞按钮
                            try:
                                like_button.click()
                                self.log_message.emit("阅读过程中点赞成功", "INFO")
                            except:
                                try:
                                    driver.execute_script("arguments[0].click();", like_button)
                                    self.log_message.emit("阅读过程中通过JavaScript点赞成功", "INFO")
                                except Exception as e:
                                    self.log_message.emit(f"阅读过程中点赞失败: {str(e)[:50]}", "WARNING")

                            # 点赞后停顿
                            time.sleep(random.uniform(1.0, 2.0))
                    except Exception as e:
                        self.log_message.emit(f"阅读过程中尝试点赞时出错: {str(e)[:50]}", "WARNING")

                # 在滚动过程中随机执行关注操作 (使用设置中的关注概率，且不在开始就执行)
                follow_probability = self.settings.get('follow_probability', 20) / 100
                if random.random() < follow_probability and i > 1:
                    self.log_message.emit("在阅读过程中尝试随机关注作者", "INFO")
                    try:
                        # 尝试查找关注按钮
                        follow_button = None
                        try:
                            # 使用用户提供的精确XPath路径
                            follow_button = driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div[3]/div[1]/div/div[1]/button/span")
                            if follow_button and follow_button.is_displayed():
                                self.log_message.emit("阅读过程中找到关注按钮", "INFO")
                        except:
                            # 尝试其他通用选择器
                            try:
                                elements = driver.find_elements(By.XPATH, "//button[contains(text(), '关注') or .//span[contains(text(), '关注')]]")
                                for element in elements:
                                    if element.is_displayed():
                                        follow_button = element
                                        break
                            except:
                                pass

                        if follow_button:
                            # 滚动到关注按钮位置
                            driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", follow_button)
                            time.sleep(random.uniform(0.5, 1.0))

                            # 尝试点击关注按钮
                            try:
                                follow_button.click()
                                self.log_message.emit("阅读过程中关注作者成功", "INFO")
                            except:
                                try:
                                    driver.execute_script("arguments[0].click();", follow_button)
                                    self.log_message.emit("阅读过程中通过JavaScript关注作者成功", "INFO")
                                except Exception as e:
                                    self.log_message.emit(f"阅读过程中关注作者失败: {str(e)[:50]}", "WARNING")

                            # 关注后停顿
                            time.sleep(random.uniform(1.0, 2.0))
                    except Exception as e:
                        self.log_message.emit(f"阅读过程中尝试关注作者时出错: {str(e)[:50]}", "WARNING")

                # 在滚动过程中随机执行评论操作 (使用设置中的评论概率，且在阅读过程中段执行)
                comment_probability = self.settings.get('comment_probability', 30) / 100
                max_comments_per_article = self.settings.get('comment_count', 1)  # 获取每篇文章最大评论次数

                # 确保_current_article_comments属性存在
                if not hasattr(self, '_current_article_comments'):
                    setattr(self, '_current_article_comments', 0)

                # 获取当前文章已评论次数
                current_comments = getattr(self, '_current_article_comments', 0)

                # 记录详细的评论状态信息
                self.log_message.emit(f"评论状态检查: 设置概率={comment_probability*100}%, 最大评论数={max_comments_per_article}, 当前已评论={current_comments}, 滚动位置={i+1}/{scroll_count}", "INFO")

                # 只有当当前文章的评论次数小于设置的最大评论次数时，才尝试评论
                # 添加额外的检查，确保评论次数不会超过最大值
                if current_comments >= max_comments_per_article:
                    self.log_message.emit(f"已达到最大评论次数 {max_comments_per_article}，跳过评论", "INFO")
                    continue  # 跳过此次评论尝试

                # 根据概率决定是否评论
                if random.random() < comment_probability:
                    self.log_message.emit("在阅读过程中尝试随机评论", "INFO")
                    try:
                        # 获取评论池
                        comment_pool = self.settings.get('comment_pool', [])
                        self.log_message.emit(f"评论池状态: {'非空，包含'+str(len(comment_pool))+'条评论' if comment_pool else '为空'}", "INFO")

                        if not comment_pool:
                            self.log_message.emit("评论池为空，跳过评论操作", "WARNING")
                        else:
                            # 尝试查找评论框
                            comment_input = None
                            self.log_message.emit("开始查找评论框...", "INFO")

                            # 先尝试查找评论区入口按钮
                            try:
                                comment_buttons = driver.find_elements(By.XPATH, "//span[contains(text(), '评论')]")
                                if comment_buttons:
                                    self.log_message.emit(f"找到 {len(comment_buttons)} 个评论按钮", "INFO")
                                    # 尝试点击第一个评论按钮
                                    for btn in comment_buttons:
                                        if btn.is_displayed():
                                            self.log_message.emit("尝试点击评论按钮", "INFO")
                                            try:
                                                driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", btn)
                                                time.sleep(1)
                                                driver.execute_script("arguments[0].click();", btn)
                                                self.log_message.emit("成功点击评论按钮", "INFO")
                                                time.sleep(2)
                                                break
                                            except Exception as e:
                                                self.log_message.emit(f"点击评论按钮失败: {str(e)[:50]}", "WARNING")
                            except Exception as e:
                                self.log_message.emit(f"查找评论按钮失败: {str(e)[:50]}", "WARNING")

                            try:
                                # 使用用户提供的精确XPath路径
                                self.log_message.emit("尝试使用精确XPath路径查找评论框", "INFO")
                                try:
                                    comment_input = driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div[2]/div[3]/div/div/div[2]/div[2]/div[1]")
                                    if comment_input and comment_input.is_displayed():
                                        self.log_message.emit("使用精确XPath路径找到评论框", "INFO")
                                except Exception as e:
                                    self.log_message.emit(f"使用精确XPath路径查找评论框失败: {str(e)[:50]}", "WARNING")

                                if not comment_input:
                                    # 尝试其他通用选择器
                                    self.log_message.emit("尝试使用通用选择器查找评论框", "INFO")
                                    comment_selectors = [
                                        (By.CSS_SELECTOR, ".comment-input"),
                                        (By.CSS_SELECTOR, "textarea[placeholder*='评论']"),
                                        (By.XPATH, "//textarea[contains(@placeholder, '评论')]"),
                                        (By.XPATH, "//div[contains(@class, 'comment-input')]"),
                                        (By.XPATH, "//div[contains(@class, 'comment')]//textarea"),
                                        (By.XPATH, "//textarea"),
                                        (By.XPATH, "//div[@contenteditable='true']")
                                    ]

                                    for selector_type, selector_value in comment_selectors:
                                        try:
                                            self.log_message.emit(f"尝试使用选择器 {selector_type}:{selector_value} 查找评论框", "INFO")
                                            elements = driver.find_elements(selector_type, selector_value)
                                            self.log_message.emit(f"使用选择器 {selector_type}:{selector_value} 找到 {len(elements)} 个元素", "INFO")

                                            for element in elements:
                                                try:
                                                    if element.is_displayed():
                                                        comment_input = element
                                                        self.log_message.emit(f"使用选择器 {selector_type}:{selector_value} 找到可见的评论框", "INFO")
                                                        break
                                                except:
                                                    pass

                                            if comment_input:
                                                break
                                        except Exception as e:
                                            self.log_message.emit(f"使用选择器 {selector_type}:{selector_value} 查找评论框失败: {str(e)[:50]}", "WARNING")
                            except Exception as e:
                                self.log_message.emit(f"查找评论框过程中出错: {str(e)}", "ERROR")

                            if comment_input:
                                # 简化版评论流程
                                try:
                                    # 点击评论框
                                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", comment_input)
                                    time.sleep(0.5)
                                    driver.execute_script("arguments[0].click();", comment_input)

                                    # 随机评论内容
                                    comment_text = random.choice(comment_pool)

                                    # 输入评论 - 使用更可靠的方法
                                    # 注意：头条评论框可能是特殊的编辑器，不是标准的input或textarea

                                    # 方法1: 直接点击评论框并输入
                                    try:
                                        self.log_message.emit(f"尝试直接点击评论框并输入评论: {comment_text}", "INFO")
                                        # 先点击评论框使其获得焦点
                                        driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", comment_input)
                                        time.sleep(0.5)
                                        driver.execute_script("arguments[0].click();", comment_input)
                                        time.sleep(0.5)

                                        # 直接输入文本
                                        comment_input.send_keys(comment_text)
                                        self.log_message.emit("成功输入评论内容", "INFO")
                                    except Exception as e:
                                        self.log_message.emit(f"直接点击评论框并输入评论失败: {str(e)[:50]}", "WARNING")

                                        # 方法2: 使用JavaScript模拟输入
                                        try:
                                            self.log_message.emit("尝试使用JavaScript模拟输入评论", "INFO")
                                            # 先聚焦元素
                                            driver.execute_script("arguments[0].focus();", comment_input)
                                            time.sleep(0.5)

                                            # 使用JavaScript模拟键盘输入
                                            for char in comment_text:
                                                # 使用JavaScript模拟按键事件
                                                js_code = f"""
                                                var evt = document.createEvent('TextEvent');
                                                evt.initTextEvent('textInput', true, true, window, '{char}');
                                                arguments[0].dispatchEvent(evt);
                                                """
                                                driver.execute_script(js_code, comment_input)
                                                time.sleep(0.05)  # 短暂延迟模拟真实输入

                                            self.log_message.emit("成功使用JavaScript模拟输入评论", "INFO")
                                        except Exception as e:
                                            self.log_message.emit(f"使用JavaScript模拟输入评论失败: {str(e)[:50]}", "WARNING")

                                            # 方法3: 使用剪贴板粘贴
                                            try:
                                                self.log_message.emit("尝试使用剪贴板粘贴评论", "INFO")
                                                # 使用JavaScript设置剪贴板内容并粘贴
                                                js_code = f"""
                                                arguments[0].focus();
                                                document.execCommand('insertText', false, '{comment_text}');
                                                """
                                                driver.execute_script(js_code, comment_input)
                                                self.log_message.emit("成功使用剪贴板粘贴评论", "INFO")
                                            except Exception as e:
                                                self.log_message.emit(f"使用剪贴板粘贴评论失败: {str(e)[:50]}", "WARNING")

                                    # 查找提交按钮
                                    submit_button = None
                                    self.log_message.emit("开始查找评论提交按钮...", "INFO")

                                    # 先尝试使用精确XPath路径
                                    try:
                                        self.log_message.emit("尝试使用精确XPath路径查找提交按钮", "INFO")
                                        submit_button = driver.find_element(By.XPATH, "/html/body/div[1]/div[2]/div[2]/div[3]/div/div/div[2]/div[2]/div[2]/button")
                                        if submit_button and submit_button.is_displayed():
                                            self.log_message.emit("使用精确XPath路径找到提交按钮", "INFO")
                                    except Exception as e:
                                        self.log_message.emit(f"使用精确XPath路径查找提交按钮失败: {str(e)[:50]}", "WARNING")

                                    # 如果精确XPath路径失败，尝试其他通用选择器
                                    if not submit_button:
                                        self.log_message.emit("尝试使用通用选择器查找提交按钮", "INFO")
                                        submit_selectors = [
                                            (By.CSS_SELECTOR, ".comment-submit"),
                                            (By.CSS_SELECTOR, "button[class*='comment']"),
                                            (By.CSS_SELECTOR, "button.submit"),
                                            (By.CSS_SELECTOR, "button.send"),
                                            (By.XPATH, "//button[contains(text(), '发布') or contains(text(), '评论') or contains(text(), '提交')]"),
                                            (By.XPATH, "//div[contains(@class, 'comment')]//button"),
                                            (By.XPATH, "//button[contains(@class, 'submit') or contains(@class, 'send')]")
                                        ]

                                        for selector_type, selector_value in submit_selectors:
                                            try:
                                                self.log_message.emit(f"尝试使用选择器 {selector_type}:{selector_value} 查找提交按钮", "INFO")
                                                elements = driver.find_elements(selector_type, selector_value)
                                                self.log_message.emit(f"使用选择器 {selector_type}:{selector_value} 找到 {len(elements)} 个元素", "INFO")

                                                for element in elements:
                                                    try:
                                                        if element.is_displayed():
                                                            submit_button = element
                                                            self.log_message.emit(f"使用选择器 {selector_type}:{selector_value} 找到可见的提交按钮", "INFO")
                                                            break
                                                    except:
                                                        pass

                                                if submit_button:
                                                    break
                                            except Exception as e:
                                                self.log_message.emit(f"使用选择器 {selector_type}:{selector_value} 查找提交按钮失败: {str(e)[:50]}", "WARNING")

                                    # 尝试提交评论
                                    if submit_button:
                                        try:
                                            self.log_message.emit("尝试点击提交按钮", "INFO")
                                            # 先滚动到按钮位置
                                            driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", submit_button)
                                            time.sleep(0.5)

                                            # 尝试直接点击
                                            try:
                                                submit_button.click()
                                                self.log_message.emit("直接点击提交按钮成功", "INFO")
                                            except Exception as e:
                                                self.log_message.emit(f"直接点击提交按钮失败: {str(e)[:50]}", "WARNING")

                                                # 尝试使用JavaScript点击
                                                try:
                                                    driver.execute_script("arguments[0].click();", submit_button)
                                                    self.log_message.emit("使用JavaScript点击提交按钮成功", "INFO")
                                                except Exception as e:
                                                    self.log_message.emit(f"使用JavaScript点击提交按钮失败: {str(e)[:50]}", "WARNING")

                                            # 更新当前文章的评论次数
                                            # 确保不会超过最大评论次数
                                            max_comments = self.settings.get('comment_count', 1)
                                            current_comments = getattr(self, '_current_article_comments', 0)

                                            # 只有在当前评论次数小于最大值时才增加计数
                                            if current_comments < max_comments:
                                                current_comments += 1
                                                setattr(self, '_current_article_comments', current_comments)
                                                self.log_message.emit(f"✅ 评论成功！当前文章已评论 {current_comments}/{max_comments} 次", "INFO")
                                            else:
                                                self.log_message.emit(f"⚠️ 评论次数已达上限 {max_comments}，不再增加计数", "WARNING")
                                        except Exception as e:
                                            self.log_message.emit(f"点击提交按钮过程中出错: {str(e)}", "ERROR")
                                    else:
                                        self.log_message.emit("未找到提交按钮，尝试使用回车键提交", "WARNING")
                                        try:
                                            # 先聚焦到评论框
                                            driver.execute_script("arguments[0].focus();", comment_input)
                                            time.sleep(0.5)

                                            # 发送回车键
                                            comment_input.send_keys("\n")

                                            # 更新当前文章的评论次数
                                            # 确保不会超过最大评论次数
                                            max_comments = self.settings.get('comment_count', 1)
                                            current_comments = getattr(self, '_current_article_comments', 0)

                                            # 只有在当前评论次数小于最大值时才增加计数
                                            if current_comments < max_comments:
                                                current_comments += 1
                                                setattr(self, '_current_article_comments', current_comments)
                                                self.log_message.emit(f"✅ 使用回车键评论成功！当前文章已评论 {current_comments}/{max_comments} 次", "INFO")
                                            else:
                                                self.log_message.emit(f"⚠️ 评论次数已达上限 {max_comments}，不再增加计数", "WARNING")
                                        except Exception as e:
                                            self.log_message.emit(f"使用回车键提交评论失败: {str(e)[:50]}", "WARNING")

                                            # 最后尝试使用Tab键+回车键组合
                                            try:
                                                comment_input.send_keys("\t\n")

                                                # 更新当前文章的评论次数
                                                # 确保不会超过最大评论次数
                                                max_comments = self.settings.get('comment_count', 1)
                                                current_comments = getattr(self, '_current_article_comments', 0)

                                                # 只有在当前评论次数小于最大值时才增加计数
                                                if current_comments < max_comments:
                                                    current_comments += 1
                                                    setattr(self, '_current_article_comments', current_comments)
                                                    self.log_message.emit(f"✅ 使用Tab+回车键评论成功！当前文章已评论 {current_comments}/{max_comments} 次", "INFO")
                                                else:
                                                    self.log_message.emit(f"⚠️ 评论次数已达上限 {max_comments}，不再增加计数", "WARNING")
                                            except Exception as e:
                                                self.log_message.emit(f"使用Tab+回车键组合提交评论失败: {str(e)[:50]}", "WARNING")

                                    # 评论后停顿
                                    time.sleep(random.uniform(1.0, 2.0))
                                except Exception as e:
                                    self.log_message.emit(f"阅读过程中评论失败: {str(e)[:50]}", "WARNING")
                    except Exception as e:
                        self.log_message.emit(f"阅读过程中尝试评论时出错: {str(e)[:50]}", "WARNING")

            # 随机向上滚动，模拟回看
            if random.random() < 0.3:
                driver.execute_script(f"window.scrollBy(0, {-random.randint(200, 500)});")
                time.sleep(random.uniform(2, 5))

            self.log_message.emit("阅读模拟完成", "INFO")

        except Exception as e:
            self.log_message.emit(f"模拟阅读时出错: {str(e)}", "ERROR")

    def like_article_content(self, driver):
        """在文章内点赞

        Args:
            driver: Selenium WebDriver对象
        """
        try:
            # 查找点赞按钮
            like_buttons = []
            selectors = [
                (By.CSS_SELECTOR, ".like-button"),
                (By.CSS_SELECTOR, ".tt-article-like"),
                (By.CSS_SELECTOR, "[data-log-click*='like']"),
                (By.XPATH, "//div[contains(@class, 'like') or contains(@class, 'digg')]"),
                (By.XPATH, "//span[contains(text(), '点赞') or contains(text(), '赞')]")
            ]

            # 尝试不同的选择器查找点赞按钮
            for selector_type, selector_value in selectors:
                try:
                    found_elements = driver.find_elements(selector_type, selector_value)
                    if found_elements:
                        like_buttons.extend(found_elements)
                        self.log_message.emit(f"使用选择器 {selector_type}:{selector_value} 找到 {len(found_elements)} 个点赞按钮", "INFO")
                except Exception as e:
                    self.log_message.emit(f"使用选择器 {selector_type}:{selector_value} 查找点赞按钮失败: {str(e)[:50]}", "WARNING")

            if not like_buttons:
                self.log_message.emit("未找到点赞按钮", "WARNING")
                return

            # 选择第一个点赞按钮
            like_button = like_buttons[0]

            # 滚动到点赞按钮位置
            driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", like_button)
            time.sleep(random.uniform(0.5, 1.5))

            # 点赞
            clicked = False

            # 方法1: 直接点击
            try:
                like_button.click()
                clicked = True
                self.log_message.emit("成功点赞文章", "INFO")
            except Exception as e:
                self.log_message.emit(f"直接点赞失败: {str(e)[:50]}", "WARNING")

            # 方法2: JavaScript点击
            if not clicked:
                try:
                    driver.execute_script("arguments[0].click();", like_button)
                    clicked = True
                    self.log_message.emit("通过JavaScript成功点赞文章", "INFO")
                except Exception as e:
                    self.log_message.emit(f"JavaScript点赞失败: {str(e)[:50]}", "WARNING")

            # 点赞后停顿
            if clicked:
                time.sleep(random.uniform(1, 3))

        except Exception as e:
            self.log_message.emit(f"文章内点赞操作出错: {str(e)}", "ERROR")

    def process_account_thread(self, index, account):
        """在线程中处理单个账号

        Args:
            index: 账号索引
            account: 账号信息
        """
        account_name = account.get('account_name', f'账号{index+1}')
        driver = None
        try:
            self.log_message.emit(f"开始处理账号 {account_name} ({index+1}/{self.total_accounts})", "INFO")

            # 检查账号信息是否完整
            if not account:
                self.log_message.emit(f"账号 {account_name} 信息不完整，跳过处理", "ERROR")
                return

            # 检查cookie文件是否存在
            cookie_file = account.get('cookie_file', '')
            if not cookie_file:
                self.log_message.emit(f"账号 {account_name} 未指定cookie文件，跳过处理", "ERROR")
                return

            if not os.path.exists(cookie_file):
                self.log_message.emit(f"账号 {account_name} 的cookie文件不存在: {cookie_file}", "ERROR")
                return

            self.log_message.emit(f"账号 {account_name} 的cookie文件: {cookie_file}", "INFO")

            # 在启动新浏览器前，智能检查并清理可能残留的Chrome进程
            try:
                # 检查是否启用了强制关闭浏览器进程的选项
                force_close_browser = self.settings.get('force_close_browser', False)  # 默认改为False，减少不必要的进程终止

                if force_close_browser:
                    import psutil
                    chrome_processes = []
                    chrome_count = 0

                    # 首先收集所有Chrome进程信息
                    for proc in psutil.process_iter(['pid', 'name', 'create_time']):
                        if 'chrome' in proc.info['name'].lower():
                            chrome_count += 1
                            try:
                                # 收集进程创建时间，用于识别僵尸进程
                                proc_info = {
                                    'pid': proc.info['pid'],
                                    'name': proc.info['name'],
                                    'create_time': proc.info['create_time'],
                                    'running_time': time.time() - proc.info['create_time']
                                }
                                chrome_processes.append(proc_info)
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                pass

                    # 只有当Chrome进程数量超过阈值时才进行清理
                    if chrome_count > 8:  # 增加阈值，避免过早清理
                        self.log_message.emit(f"检测到 {chrome_count} 个Chrome进程，进行智能清理...", "WARNING")

                        # 按运行时间排序，优先关闭运行时间长的进程
                        chrome_processes.sort(key=lambda x: x['running_time'], reverse=True)

                        # 只关闭一部分最老的进程，而不是全部
                        processes_to_kill = chrome_processes[:chrome_count-5]  # 保留5个最新的进程

                        for proc_info in processes_to_kill:
                            try:
                                proc = psutil.Process(proc_info['pid'])
                                proc.kill()
                                self.log_message.emit(f"智能关闭Chrome进程: {proc_info['pid']}，运行时间: {int(proc_info['running_time'])}秒", "WARNING")
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                pass

                        # 等待进程关闭，减少等待时间
                        time.sleep(1)
                else:
                    self.log_message.emit("已禁用强制关闭浏览器进程功能，跳过Chrome进程清理", "INFO")
            except Exception as e:
                self.log_message.emit(f"清理Chrome进程时出错: {str(e)}", "WARNING")

            # 获取ChromeDriver路径
            driver_path = self.get_chrome_driver_path()
            self.log_message.emit(f"ChromeDriver路径: {driver_path if driver_path else '使用系统默认'}", "INFO")

            # 配置Chrome选项
            headless_mode = self.settings.get('headless_mode', False)
            self.log_message.emit(f"无头模式: {'启用' if headless_mode else '禁用'}", "INFO")

            # 创建WebDriver
            if self.proxy_manager:
                # 使用代理管理器创建driver
                self.log_message.emit("使用代理管理器创建WebDriver...", "INFO")
                driver = self.proxy_manager.create_driver(driver_path, headless_mode)
                self.log_message.emit("使用自定义浏览器配置创建WebDriver成功", "INFO")
            else:
                # 使用默认配置创建driver
                self.log_message.emit("使用默认配置创建WebDriver...", "INFO")
                options = Options()
                if headless_mode:
                    options.add_argument('--headless')
                options.add_argument('--disable-gpu')
                options.add_argument('--no-sandbox')
                # 添加额外选项以防止浏览器实例残留
                options.add_argument('--disable-dev-shm-usage')
                options.add_argument('--disable-extensions')
                options.add_argument('--disable-browser-side-navigation')
                options.add_argument('--disable-infobars')
                options.add_experimental_option('excludeSwitches', ['enable-automation'])
                options.add_experimental_option('detach', False)  # 确保浏览器不会在后台运行

                # 添加进程终止选项，确保浏览器进程在driver.quit()时被完全终止
                options.add_argument("--disable-features=site-per-process")
                options.add_argument("--disable-hang-monitor")

                try:
                    if driver_path:
                        service = Service(executable_path=driver_path)
                        driver = webdriver.Chrome(service=service, options=options)
                    else:
                        driver = webdriver.Chrome(options=options)
                    self.log_message.emit("WebDriver创建成功", "INFO")
                except Exception as driver_err:
                    self.log_message.emit(f"创建WebDriver失败: {str(driver_err)}", "ERROR")
                    raise

            # 设置页面加载超时
            driver.set_page_load_timeout(30)
            self.log_message.emit("设置页面加载超时: 30秒", "INFO")

            # 登录账号
            self.log_message.emit(f"开始加载账号 {account_name} 的Cookie...", "INFO")
            success = self.load_cookies(driver, account)
            if not success:
                self.log_message.emit(f"账号 {account_name} 登录失败，跳过养号", "ERROR")
                return

            # 依次执行养号行为
            self.log_message.emit(f"账号 {account_name} 登录成功，开始执行养号行为", "INFO")
            self.execute_nurture_behaviors(driver)

            self.log_message.emit(f"账号 {account_name} 养号完成", "INFO")

        except Exception as e:
            self.log_message.emit(f"处理账号 {account_name} 时出错: {str(e)}", "ERROR")
            traceback.print_exc()
        finally:
            # 关闭浏览器
            if driver:
                try:
                    self.log_message.emit(f"正在关闭账号 {account_name} 的浏览器...", "INFO")

                    # 先关闭所有窗口
                    try:
                        # 获取所有窗口句柄
                        handles = driver.window_handles
                        if len(handles) > 1:
                            self.log_message.emit(f"检测到 {len(handles)} 个窗口，逐个关闭", "INFO")
                            # 保留主窗口，关闭其他窗口
                            main_handle = handles[0]
                            for handle in handles[1:]:
                                try:
                                    driver.switch_to.window(handle)
                                    driver.close()
                                except:
                                    pass
                            # 切回主窗口
                            driver.switch_to.window(main_handle)
                    except Exception as window_err:
                        self.log_message.emit(f"关闭多余窗口时出错: {str(window_err)}", "WARNING")

                    # 退出浏览器
                    driver.quit()

                    # 确保浏览器进程完全关闭
                    time.sleep(2)
                    self.log_message.emit(f"账号 {account_name} 的浏览器已关闭", "INFO")

                except Exception as quit_err:
                    self.log_message.emit(f"关闭账号 {account_name} 的浏览器时出错: {str(quit_err)}", "WARNING")
                    # 尝试强制关闭Chrome进程
                    try:
                        # 检查是否启用了强制关闭浏览器进程的选项
                        force_close_browser = self.settings.get('force_close_browser', True)

                        if force_close_browser:
                            import psutil
                            killed_count = 0
                            for proc in psutil.process_iter(['pid', 'name']):
                                if 'chrome' in proc.info['name'].lower():
                                    try:
                                        proc.kill()
                                        killed_count += 1
                                    except:
                                        pass
                            if killed_count > 0:
                                self.log_message.emit(f"强制关闭了 {killed_count} 个Chrome进程", "WARNING")
                        else:
                            self.log_message.emit("已禁用强制关闭浏览器进程功能，跳过Chrome进程清理", "INFO")
                    except Exception as kill_err:
                        self.log_message.emit(f"强制关闭Chrome进程时出错: {str(kill_err)}", "WARNING")

            # 通知账号处理完成
            self.on_account_finished(index)

    def on_account_finished(self, index):
        """账号处理完成回调

        Args:
            index: 完成处理的账号索引
        """
        with self.thread_lock:
            self.completed_accounts += 1
            self.active_threads -= 1

            # 重置线程启动标志
            self.thread_starting = False

            # 更新账号状态跟踪
            if hasattr(self, 'processing_accounts') and index in self.processing_accounts:
                self.processing_accounts.remove(index)

            if hasattr(self, 'processed_accounts'):
                self.processed_accounts.add(index)

            self.log_message.emit(f"账号 {index+1} 处理完成，已完成 {self.completed_accounts}/{self.total_accounts}，剩余队列长度: {len(self.account_queue)}", "INFO")
            self.log_message.emit(f"已处理账号数: {len(self.processed_accounts)}, 正在处理账号数: {len(self.processing_accounts)}", "INFO")

            # 启动下一个账号处理线程
            if self.account_queue and self.running:
                self.log_message.emit(f"准备启动下一个账号处理线程，当前活跃线程: {self.active_threads}", "INFO")

                # 添加短暂延迟，确保资源释放
                time.sleep(1)  # 根据您的偏好，将等待时间从6秒减少到1秒

                # 记录下一个账号的信息，便于调试
                if self.account_queue:
                    next_account_index, next_account = self.account_queue[0]
                    next_account_name = next_account.get('account_name', f'账号{next_account_index+1}')
                    self.log_message.emit(f"下一个账号: {next_account_name} ({next_account_index+1}/{self.total_accounts})", "INFO")

                # 使用QTimer延迟启动下一个账号，避免在回调中直接启动可能导致的问题
                QTimer.singleShot(100, self.create_timer_callback(self.delayed_start_next_account))
            else:
                if not self.account_queue:
                    self.log_message.emit("账号队列为空，不再启动新线程", "INFO")
                if not self.running:
                    self.log_message.emit("养号任务已停止，不再启动新线程", "INFO")

            # 更新进度
            self.progress_update.emit(self.completed_accounts, self.total_accounts)

            # 检查是否所有账号都已处理完成
            if self.completed_accounts >= self.total_accounts:
                self.log_message.emit("所有账号处理完成", "INFO")
                # 在主线程中安全地发出完成信号
                QMetaObject.invokeMethod(self, "finish_task", Qt.ConnectionType.QueuedConnection)

    def delayed_start_next_account(self):
        """延迟启动下一个账号处理线程，避免在回调中直接启动可能导致的问题"""
        try:
            self.log_message.emit("开始执行延迟启动下一个账号", "INFO")

            # 检查基本条件
            if not self.account_queue:
                self.log_message.emit("账号队列为空，无法启动新线程", "WARNING")
                return

            if not self.running:
                self.log_message.emit("养号任务已停止，无法启动新线程", "WARNING")
                return

            # 重置线程启动标志，确保能够启动新线程
            self.thread_starting = False

            # 启动下一个账号
            with self.thread_lock:
                # 再次检查条件（可能在获取锁的过程中条件已经改变）
                if not self.account_queue or not self.running:
                    self.log_message.emit("获取锁后条件已改变，取消启动", "WARNING")
                    return

                # 获取下一个账号信息用于日志
                next_account_index, next_account = self.account_queue[0]
                next_account_name = next_account.get('account_name', f'账号{next_account_index+1}')
                self.log_message.emit(f"准备启动账号: {next_account_name} ({next_account_index+1}/{self.total_accounts})", "INFO")

                # 直接启动线程，不使用start_next_account_thread方法
                # 从队列中获取下一个账号
                i, account = self.account_queue.pop(0)
                self.active_threads += 1

                # 标记账号为正在处理中
                if hasattr(self, 'processing_accounts'):
                    self.processing_accounts.add(i)

                # 创建并启动线程
                account_name = account.get('account_name', f'账号{i+1}')
                self.log_message.emit(f"启动线程处理账号 {account_name} ({i+1}/{self.total_accounts})，当前活跃线程: {self.active_threads}", "INFO")
                self.progress_update.emit(i+1, self.total_accounts)

                try:
                    thread = threading.Thread(
                        target=self.process_account_thread,
                        args=(i, account),
                        daemon=True
                    )
                    thread.start()
                    self.log_message.emit(f"成功创建并启动线程处理账号 {account_name}", "INFO")
                    return True
                except Exception as e:
                    self.log_message.emit(f"创建线程处理账号 {account_name} 时出错: {str(e)}", "ERROR")
                    self.active_threads -= 1  # 回滚计数器
                    # 从处理中集合中移除
                    if hasattr(self, 'processing_accounts') and i in self.processing_accounts:
                        self.processing_accounts.remove(i)
                    # 将账号放回队列
                    self.account_queue.insert(0, (i, account))
                    self.log_message.emit(f"将账号 {account_name} 放回队列首位", "INFO")
                    return False
        except Exception as e:
            self.log_message.emit(f"延迟启动下一个账号时出错: {str(e)}", "ERROR")
            import traceback
            self.log_message.emit(traceback.format_exc(), "ERROR")
            return False

    @pyqtSlot()
    def finish_task(self):
        """完成任务，在主线程中调用"""
        self.status_update.emit("养号任务执行完成")
        self.log_message.emit("养号任务执行完成", "INFO")
        self.running = False

    def stop(self):
        """停止线程"""
        self.running = False
        self.log_message.emit("正在停止养号任务...", "WARNING")
        self.status_update.emit("正在停止养号任务...")