# pyright: reportOptionalMemberAccess=false
# pyright: reportAttributeAccessIssue=false
# pyright: reportGeneralTypeIssues=false

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout,
                          QLabel, QCheckBox, QLineEdit, QPushButton,
                          QGroupBox, QFormLayout, QSpinBox, QComboBox,
                          QSizePolicy, QStyle, QTabWidget, QFileDialog,
                          QMessageBox, QTimeEdit, QDateEdit, QScrollArea,
                          QSplitter, QTextEdit)
from PyQt5.QtCore import Qt, pyqtSignal, QUrl, QSize, QTime, QDate
from PyQt5.QtGui import QIcon, QFont
from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEnginePage
import json
import os
import sys
from datetime import datetime, timedelta

# 导入增强的日期时间选择组件
try:
    from app.widgets.datetime_selector import (WeekdaySelector, DateRangeSelector,
                                              CustomTimeSelector, TimezoneSelector,
                                              TimePreviewWidget, TimeValidator, ConflictDetector)
    DATETIME_SELECTOR_AVAILABLE = True
except ImportError:
    DATETIME_SELECTOR_AVAILABLE = False
    print("警告: 增强的日期时间选择组件不可用，将使用基础版本")

class SettingTab(QWidget):
    # 定义信号
    cookie_path_changed = pyqtSignal(str)
    settings_saved = pyqtSignal(dict)  # 添加设置保存信号

    def __init__(self):
        super().__init__()
        self.settings_file = "settings.json"  # 设置文件路径
        self.default_settings = {
            "cookie_path": "D:/头条全自动/账号",
            "data_path": "D:/头条全自动/数据",
            "log_path": "D:/头条全自动/日志",
            "video_path": "D:/头条全自动/视频",
            "cover_path": "D:/头条全自动/封面",
            "auto_login": True,
            "auto_load_accounts": False,
            "enable_notify": True,
            "enable_sound": False,

            # 指纹浏览器设置
            "use_fingerprint": False,
            "enable_random_ua": True,
            "device_type": "自动随机",
            "browser_type": "自动随机",
            "resolution_type": "随机分辨率",
            "pixel_ratio": "随机",
            "browser_language": "中文",
            "randomize_canvas": True,
            "disable_webrtc": True,
            "enable_anti_detection": True
        }
        self.init_ui()
        self.load_settings()  # 加载设置

    def init_ui(self):
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # 样式定义
        self.groupbox_style = """
            QGroupBox {
                font-weight: bold;
                border: 1px solid #DDD;
                border-radius: 5px;
                margin-top: 1.5ex;
                padding: 10px;
                background-color: #FFFFFF;
                color: #000000;
                font-size: 13px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 5px;
                color: #000080;
                font-weight: bold;
            }
        """

        self.input_style = """
            QLineEdit, QSpinBox, QComboBox {
                border: 1px solid #DDD;
                border-radius: 4px;
                padding: 5px;
                background-color: #FFFFFF;
                color: #000000;
                font-size: 12px;
            }
            QLineEdit:focus, QSpinBox:focus, QComboBox:focus {
                border: 1px solid #1E88E5;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 15px;
                border-left: 1px solid #DDD;
            }
        """

        self.button_style = """
            QPushButton {
                border-radius: 4px;
                padding: 5px 10px;
                font-weight: bold;
                border: none;
                color: white;
                font-size: 13px;
            }
            QPushButton:hover {
                opacity: 0.8;
            }
            QPushButton:pressed {
                opacity: 0.6;
            }
        """

        # 设置整个标签页自适应
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 创建设置标签页
        self.setting_tabs = QTabWidget()
        self.setting_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 5px;
                background-color: #FFFFFF;
            }
            QTabBar::tab {
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                border: 1px solid #DDD;
                border-bottom: none;
                background-color: #EEEEEE;
                color: #000000;
                font-size: 12px;
            }
            QTabBar::tab:selected {
                background-color: #FFFFFF;
                color: #000000;
                font-weight: bold;
                border-bottom: 2px solid #2980B9;
            }
            QTabBar::tab:hover {
                background-color: #E3F2FD;
            }
        """)

        # 基本设置标签页
        basic_tab = QWidget()
        basic_layout = QVBoxLayout(basic_tab)

        # 基本设置组
        basic_group = QGroupBox("基本设置")
        basic_group.setStyleSheet(self.groupbox_style)
        basic_group_layout = QFormLayout()
        basic_group_layout.setContentsMargins(10, 15, 10, 10)
        basic_group_layout.setSpacing(10)

        # 登录设置
        self.auto_login = QCheckBox("自动登录")
        self.auto_login.setChecked(True)
        self.auto_login.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #000000;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        basic_group_layout.addRow("自动登录:", self.auto_login)

        # 自动加载账号设置
        self.auto_load_accounts = QCheckBox("启动时自动加载")
        self.auto_load_accounts.setChecked(False)
        self.auto_load_accounts.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #000000;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        basic_group_layout.addRow("自动加载账号:", self.auto_load_accounts)

        # 快速启动模式设置
        self.fast_startup_enabled = QCheckBox("启用快速启动")
        self.fast_startup_enabled.setChecked(True)  # 默认启用
        self.fast_startup_enabled.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #000000;
                font-size: 14px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        self.fast_startup_enabled.setToolTip("启用后，程序会先显示界面，然后在后台多线程加载账号数据，大幅提升启动速度")
        basic_group_layout.addRow("快速启动模式:", self.fast_startup_enabled)



        basic_group.setLayout(basic_group_layout)
        basic_layout.addWidget(basic_group)

        basic_layout.addStretch()

        # 路径设置标签页
        path_tab = QWidget()
        path_layout = QVBoxLayout(path_tab)

        # 路径设置组
        path_group = QGroupBox("路径设置")
        path_group.setStyleSheet(self.groupbox_style)
        path_group_layout = QFormLayout()
        path_group_layout.setContentsMargins(10, 15, 10, 10)
        path_group_layout.setSpacing(15)  # 增加间距

        # 账号路径（Cookie路径）
        self.cookie_path = QLineEdit("E:/toutiaoyuanma1/cookies")
        self.cookie_path.setStyleSheet(self.input_style)

        cookie_path_btn = QPushButton(" 浏览...")
        cookie_path_btn.setStyleSheet("""
            QPushButton {
                background-color: #2980B9;
                color: white;
                border-radius: 4px;
                padding: 5px 10px;
                font-weight: bold;
                border: 1px solid #000000;
            }
            QPushButton:hover {
                background-color: #3498DB;
            }
        """)
        cookie_path_btn.setIcon(self.style().standardIcon(QStyle.SP_DirIcon))
        cookie_path_btn.clicked.connect(lambda: self.browse_folder("cookie_path"))

        cookie_path_layout = QHBoxLayout()
        cookie_path_layout.addWidget(self.cookie_path)
        cookie_path_layout.addWidget(cookie_path_btn)
        path_group_layout.addRow("账号路径:", cookie_path_layout)

        # 数据目录
        self.data_path = QLineEdit("E:/toutiaoyuanma1/data")
        self.data_path.setStyleSheet(self.input_style)

        data_path_btn = QPushButton(" 浏览...")
        data_path_btn.setStyleSheet("""
            QPushButton {
                background-color: #2980B9;
                color: white;
                border-radius: 4px;
                padding: 5px 10px;
                font-weight: bold;
                border: 1px solid #000000;
            }
            QPushButton:hover {
                background-color: #3498DB;
            }
        """)
        data_path_btn.setIcon(self.style().standardIcon(QStyle.SP_DirIcon))
        data_path_btn.clicked.connect(lambda: self.browse_folder("data_path"))

        data_path_layout = QHBoxLayout()
        data_path_layout.addWidget(self.data_path)
        data_path_layout.addWidget(data_path_btn)
        path_group_layout.addRow("数据目录:", data_path_layout)

        # 日志目录
        self.log_path = QLineEdit("E:/toutiaoyuanma1/logs")
        self.log_path.setStyleSheet(self.input_style)

        log_path_btn = QPushButton(" 浏览...")
        log_path_btn.setStyleSheet("""
            QPushButton {
                background-color: #2980B9;
                color: white;
                border-radius: 4px;
                padding: 5px 10px;
                font-weight: bold;
                border: 1px solid #000000;
            }
            QPushButton:hover {
                background-color: #3498DB;
            }
        """)
        log_path_btn.setIcon(self.style().standardIcon(QStyle.SP_DirIcon))
        log_path_btn.clicked.connect(lambda: self.browse_folder("log_path"))

        log_path_layout = QHBoxLayout()
        log_path_layout.addWidget(self.log_path)
        log_path_layout.addWidget(log_path_btn)
        path_group_layout.addRow("日志目录:", log_path_layout)

        # 视频路径
        self.video_path = QLineEdit("E:/toutiaoyuanma1/videos")
        self.video_path.setStyleSheet(self.input_style)

        video_path_btn = QPushButton(" 浏览...")
        video_path_btn.setStyleSheet("""
            QPushButton {
                background-color: #2980B9;
                color: white;
                border-radius: 4px;
                padding: 5px 10px;
                font-weight: bold;
                border: 1px solid #000000;
            }
            QPushButton:hover {
                background-color: #3498DB;
            }
        """)
        video_path_btn.setIcon(self.style().standardIcon(QStyle.SP_DirIcon))
        video_path_btn.clicked.connect(lambda: self.browse_folder("video_path"))

        video_path_layout = QHBoxLayout()
        video_path_layout.addWidget(self.video_path)
        video_path_layout.addWidget(video_path_btn)
        path_group_layout.addRow("视频目录:", video_path_layout)

        # 封面路径
        self.cover_path = QLineEdit("E:/toutiaoyuanma1/covers")
        self.cover_path.setStyleSheet(self.input_style)

        cover_path_btn = QPushButton(" 浏览...")
        cover_path_btn.setStyleSheet("""
            QPushButton {
                background-color: #2980B9;
                color: white;
                border-radius: 4px;
                padding: 5px 10px;
                font-weight: bold;
                border: 1px solid #000000;
            }
            QPushButton:hover {
                background-color: #3498DB;
            }
        """)
        cover_path_btn.setIcon(self.style().standardIcon(QStyle.SP_DirIcon))
        cover_path_btn.clicked.connect(lambda: self.browse_folder("cover_path"))

        cover_path_layout = QHBoxLayout()
        cover_path_layout.addWidget(self.cover_path)
        cover_path_layout.addWidget(cover_path_btn)
        path_group_layout.addRow("封面目录:", cover_path_layout)

        # 添加一键创建默认文件夹按钮
        create_default_dirs_btn = QPushButton(" 一键创建默认文件夹")
        create_default_dirs_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF8C00;
                color: white;
                border-radius: 4px;
                padding: 12px 15px;
                font-weight: bold;
                border: 2px solid #000000;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #FF4500;
                border: 2px solid #FFFF00;
            }
        """)
        create_default_dirs_btn.setIcon(self.style().standardIcon(QStyle.SP_FileDialogNewFolder))
        create_default_dirs_btn.setIconSize(QSize(24, 24))  # 增大图标尺寸
        create_default_dirs_btn.setMinimumHeight(50)  # 增加按钮高度
        create_default_dirs_btn.clicked.connect(self.create_default_directories)
        path_group_layout.addRow("", create_default_dirs_btn)

        path_group.setLayout(path_group_layout)
        path_layout.addWidget(path_group)
        path_layout.addStretch()

        # 通知设置标签页
        notify_tab = QWidget()
        notify_layout = QVBoxLayout(notify_tab)

        # 通知设置组
        notify_group = QGroupBox("通知设置")
        notify_group.setStyleSheet(self.groupbox_style)
        notify_group_layout = QFormLayout()
        notify_group_layout.setContentsMargins(10, 15, 10, 10)
        notify_group_layout.setSpacing(10)

        # 启用通知
        self.enable_notify = QCheckBox("启用")
        self.enable_notify.setChecked(True)
        self.enable_notify.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #000000;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        notify_group_layout.addRow("启用通知:", self.enable_notify)

        # 声音提示
        self.enable_sound = QCheckBox("启用")
        self.enable_sound.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #000000;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        notify_group_layout.addRow("声音提示:", self.enable_sound)



        notify_group.setLayout(notify_group_layout)
        notify_layout.addWidget(notify_group)
        notify_layout.addStretch()

        # 创建浏览器设置标签页
        browser_tab = self.create_browser_settings_tab()

        # 创建软件教程标签页
        tutorial_tab = self.create_tutorial_tab()

        # 创建自动运行设置标签页
        automation_tab = self.create_automation_settings_tab()

        # 添加标签页到设置标签页
        self.setting_tabs.addTab(basic_tab, "基本设置")
        self.setting_tabs.addTab(path_tab, "路径设置")
        self.setting_tabs.addTab(browser_tab, "浏览器设置")
        self.setting_tabs.addTab(automation_tab, "自动运行设置")
        self.setting_tabs.addTab(notify_tab, "通知设置")
        self.setting_tabs.addTab(tutorial_tab, "软件教程")

        main_layout.addWidget(self.setting_tabs)

        # 保存按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        self.save_btn = QPushButton(" 保存设置")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border-radius: 4px;
                padding: 8px 15px;
                font-weight: bold;
                border: 2px solid #000000;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2ECC71;
                border: 2px solid #FFFF00;
            }
        """)
        self.save_btn.setIcon(self.style().standardIcon(QStyle.SP_DialogSaveButton))
        self.save_btn.clicked.connect(self.save_settings)  # 连接保存设置方法
        button_layout.addWidget(self.save_btn)

        self.reset_btn = QPushButton(" 恢复默认")
        self.reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #95A5A6;
                color: white;
                border-radius: 4px;
                padding: 8px 15px;
                font-weight: bold;
                border: 2px solid #000000;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #7F8C8D;
                border: 2px solid #FFFF00;
            }
        """)
        self.reset_btn.setIcon(self.style().standardIcon(QStyle.SP_DialogResetButton))
        self.reset_btn.clicked.connect(self.reset_settings)  # 连接重置设置方法
        button_layout.addWidget(self.reset_btn)

        main_layout.addLayout(button_layout)

    def browse_folder(self, target):
        """打开文件夹选择对话框

        Args:
            target: 目标路径变量名，如"cookie_path"或"data_path"
        """
        dialog = QFileDialog()
        dialog.setFileMode(QFileDialog.DirectoryOnly)

        # 获取当前路径作为起始目录
        if target == "cookie_path":
            current_path = self.cookie_path.text()
        elif target == "data_path":
            current_path = self.data_path.text()
        elif target == "log_path":
            current_path = self.log_path.text()
        elif target == "video_path":
            current_path = self.video_path.text()
        elif target == "cover_path":
            current_path = self.cover_path.text()
        else:
            current_path = "E:/"

        # 打开对话框
        folder = dialog.getExistingDirectory(
            self, "选择文件夹", current_path, QFileDialog.ShowDirsOnly
        )

        # 更新路径
        if folder:
            if target == "cookie_path":
                self.cookie_path.setText(folder)
                # 发送cookie路径变更信号
                self.cookie_path_changed.emit(folder)
            elif target == "data_path":
                self.data_path.setText(folder)
            elif target == "log_path":
                self.log_path.setText(folder)
            elif target == "video_path":
                self.video_path.setText(folder)
            elif target == "cover_path":
                self.cover_path.setText(folder)

    def save_settings(self):
        """保存所有设置到配置文件 - 优化版本，添加异步保存和更好的错误处理"""
        import threading

        # 收集当前设置
        settings = {
            "cookie_path": self.cookie_path.text(),
            "data_path": self.data_path.text(),
            "log_path": self.log_path.text(),
            "video_path": self.video_path.text(),
            "cover_path": self.cover_path.text(),
            "auto_login": self.auto_login.isChecked(),
            "auto_load_accounts": self.auto_load_accounts.isChecked(),
            "fast_startup_enabled": self.fast_startup_enabled.isChecked(),
            "enable_notify": self.enable_notify.isChecked(),
            "enable_sound": self.enable_sound.isChecked(),

            # 指纹浏览器设置
            "use_fingerprint": self.use_fingerprint.isChecked(),
            "enable_random_ua": self.enable_random_ua.isChecked(),
            "device_type": self.device_type.currentText(),
            "browser_type": self.browser_type.currentText(),
            "resolution_type": self.resolution_type.currentText(),
            "pixel_ratio": self.pixel_ratio.currentText(),
            "browser_language": self.browser_language.currentText(),
            "randomize_canvas": self.randomize_canvas.isChecked(),
            "disable_webrtc": self.disable_webrtc.isChecked(),
            "enable_anti_detection": self.enable_anti_detection.isChecked(),
            # 自动运行设置
            "enable_automation": self.enable_automation.isChecked(),
            "schedule_mode": self.schedule_mode.currentText(),
            "interval_hours": self.interval_hours.value(),
            "task_interval": self.task_interval.value(),
            "auto_retry": self.auto_retry.isChecked(),
            "max_retries": self.max_retries.value(),
            "loop_mode": self.loop_mode.currentText(),
            "loop_count": self.loop_count.value()
        }

        # 添加增强的自动化设置（如果可用）
        if DATETIME_SELECTOR_AVAILABLE:
            # 每日定时设置
            settings["daily_time"] = self.daily_time.time().toString('HH:mm')

            # 星期选择
            if hasattr(self, 'weekday_selector'):
                settings["weekdays"] = self.weekday_selector.get_selected_days()

            # 日期范围
            if hasattr(self, 'date_range_selector'):
                start_date, end_date = self.date_range_selector.get_date_range()
                if start_date and end_date:
                    settings["start_date"] = start_date.toString("yyyy-MM-dd")
                    settings["end_date"] = end_date.toString("yyyy-MM-dd")
                else:
                    settings["start_date"] = None
                    settings["end_date"] = None

            # 自定义时间设置
            if hasattr(self, 'custom_time_selector'):
                custom_times = self.custom_time_selector.get_times()
                settings["custom_times"] = [dt.isoformat() for dt in custom_times]

            # 时区设置
            if hasattr(self, 'timezone_selector'):
                settings["timezone"] = self.timezone_selector.get_timezone()
        else:
            # 基础版本的设置
            settings.update({
                "daily_time": self.daily_time.time().toString('HH:mm'),
                "weekdays": [1, 2, 3, 4, 5],  # 默认工作日
                "start_date": None,
                "end_date": None,
                "custom_times": [],
                "timezone": "+08:00"
            })

        # 继续原有的设置保存逻辑
        # 移除这个空的update调用

        # 立即发送设置保存信号，不等待文件写入
        self.settings_saved.emit(settings)

        # 定义异步保存函数
        def async_save(settings_file, settings_data):
            try:
                # 先写入临时文件，然后重命名，避免写入过程中的文件损坏
                temp_file = settings_file + ".tmp"
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(settings_data, f, ensure_ascii=False, indent=4)

                # 如果原文件存在，尝试备份
                if os.path.exists(settings_file):
                    backup_file = settings_file + ".bak"
                    try:
                        if os.path.exists(backup_file):
                            os.remove(backup_file)
                        os.rename(settings_file, backup_file)
                    except:
                        # 备份失败，直接覆盖
                        pass

                # 重命名临时文件为正式文件
                os.rename(temp_file, settings_file)

                # 在主线程中显示成功消息
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(0, lambda: self.show_save_success())

            except Exception as e:
                # 在主线程中显示错误消息
                from PyQt5.QtCore import QTimer
                error_msg = str(e)
                QTimer.singleShot(0, lambda: self.show_save_error(error_msg))

        # 启动异步保存线程
        save_thread = threading.Thread(
            target=async_save,
            args=(self.settings_file, settings)
        )
        save_thread.daemon = True
        save_thread.start()

    def show_save_success(self):
        """显示保存成功消息"""
        QMessageBox.information(self, "保存成功", "设置已保存")

    def show_save_error(self, error_msg):
        """显示保存失败消息"""
        QMessageBox.critical(self, "保存失败", f"保存设置时出错：{error_msg}")

    def load_settings(self):
        """从配置文件加载设置 - 优化版本，添加超时机制和更好的错误处理"""
        import threading
        import time
        from concurrent.futures import ThreadPoolExecutor, TimeoutError

        # 定义读取设置文件的函数
        def read_settings_file(file_path, timeout=2.0):
            """带超时的设置文件读取函数

            Args:
                file_path: 设置文件路径
                timeout: 超时时间（秒）

            Returns:
                tuple: (是否成功, 设置数据/错误信息)
            """
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return False, f"设置文件不存在: {file_path}"

            # 检查文件大小
            try:
                file_size = os.path.getsize(file_path)
                if file_size > 1024 * 1024:  # 1MB
                    return False, f"设置文件过大 ({file_size/1024:.2f}KB)，可能已损坏"
                elif file_size == 0:
                    return False, "设置文件为空"
            except Exception as e:
                return False, f"检查设置文件大小时出错: {str(e)}"

            # 使用Future实现超时
            with ThreadPoolExecutor(max_workers=1) as executor:
                try:
                    future = executor.submit(lambda: _read_json_file(file_path))
                    return future.result(timeout=timeout)
                except TimeoutError:
                    return False, f"读取设置文件超时（{timeout}秒）"
                except Exception as e:
                    return False, f"读取设置文件时出错: {str(e)}"

        def _read_json_file(file_path):
            """读取JSON文件

            Args:
                file_path: 文件路径

            Returns:
                tuple: (是否成功, 数据/错误信息)
            """
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return True, data
            except json.JSONDecodeError as e:
                return False, f"设置文件格式错误: {str(e)}"
            except UnicodeDecodeError as e:
                return False, f"设置文件编码错误: {str(e)}"
            except Exception as e:
                return False, f"读取设置文件时出错: {str(e)}"

        # 主加载逻辑
        try:
            # 使用默认设置作为备份
            settings = self.default_settings.copy()

            # 尝试加载设置文件
            if os.path.exists(self.settings_file):
                success, result = read_settings_file(self.settings_file)

                if success:
                    # 更新设置，但保留默认值作为备份
                    loaded_settings = result
                    for key in self.default_settings:
                        if key in loaded_settings:
                            settings[key] = loaded_settings[key]
                else:
                    # 加载失败，使用默认设置并显示警告
                    print(f"加载设置文件失败: {result}")
                    # 不显示弹窗，避免阻塞UI

            # 应用设置到UI
            self.cookie_path.setText(settings.get("cookie_path", self.default_settings["cookie_path"]))
            self.data_path.setText(settings.get("data_path", self.default_settings["data_path"]))
            self.log_path.setText(settings.get("log_path", self.default_settings["log_path"]))
            self.video_path.setText(settings.get("video_path", self.default_settings["video_path"]))
            self.cover_path.setText(settings.get("cover_path", self.default_settings["cover_path"]))
            self.auto_login.setChecked(settings.get("auto_login", self.default_settings["auto_login"]))
            self.auto_load_accounts.setChecked(settings.get("auto_load_accounts", self.default_settings["auto_load_accounts"]))
            self.fast_startup_enabled.setChecked(settings.get("fast_startup_enabled", True))  # 默认启用快速启动
            self.enable_notify.setChecked(settings.get("enable_notify", self.default_settings["enable_notify"]))
            self.enable_sound.setChecked(settings.get("enable_sound", self.default_settings["enable_sound"]))


            # 应用指纹浏览器设置到UI
            self.use_fingerprint.setChecked(settings.get("use_fingerprint", self.default_settings["use_fingerprint"]))
            self.enable_random_ua.setChecked(settings.get("enable_random_ua", self.default_settings["enable_random_ua"]))
            self.device_type.setCurrentText(settings.get("device_type", self.default_settings["device_type"]))
            self.browser_type.setCurrentText(settings.get("browser_type", self.default_settings["browser_type"]))
            self.resolution_type.setCurrentText(settings.get("resolution_type", self.default_settings["resolution_type"]))
            self.pixel_ratio.setCurrentText(settings.get("pixel_ratio", self.default_settings["pixel_ratio"]))
            self.browser_language.setCurrentText(settings.get("browser_language", self.default_settings["browser_language"]))
            self.randomize_canvas.setChecked(settings.get("randomize_canvas", self.default_settings["randomize_canvas"]))
            self.disable_webrtc.setChecked(settings.get("disable_webrtc", self.default_settings["disable_webrtc"]))
            self.enable_anti_detection.setChecked(settings.get("enable_anti_detection", self.default_settings["enable_anti_detection"]))

            # 应用自动运行设置到UI
            self.enable_automation.setChecked(settings.get("enable_automation", False))
            self.schedule_mode.setCurrentText(settings.get("schedule_mode", "间隔执行"))
            self.interval_hours.setValue(settings.get("interval_hours", 2))
            self.task_interval.setValue(settings.get("task_interval", 5))
            self.auto_retry.setChecked(settings.get("auto_retry", True))
            self.max_retries.setValue(settings.get("max_retries", 3))
            self.loop_mode.setCurrentText(settings.get("loop_mode", "连续执行"))
            self.loop_count.setValue(settings.get("loop_count", 1))

            # 应用增强的自动化设置（如果可用）
            if DATETIME_SELECTOR_AVAILABLE:
                # 每日定时设置
                daily_time = settings.get('daily_time', '09:00')
                hour, minute = map(int, daily_time.split(':'))
                self.daily_time.setTime(QTime(hour, minute))

                # 星期选择
                if hasattr(self, 'weekday_selector'):
                    weekdays = settings.get('weekdays', [1, 2, 3, 4, 5])  # 默认工作日
                    self.weekday_selector.set_selected_days(weekdays)

                # 日期范围
                if hasattr(self, 'date_range_selector'):
                    start_date_str = settings.get('start_date')
                    end_date_str = settings.get('end_date')
                    if start_date_str and end_date_str:
                        start_date = QDate.fromString(start_date_str, "yyyy-MM-dd")
                        end_date = QDate.fromString(end_date_str, "yyyy-MM-dd")
                        self.date_range_selector.set_date_range(start_date, end_date)
                    else:
                        self.date_range_selector.set_date_range(None, None)

                # 自定义时间设置
                if hasattr(self, 'custom_time_selector'):
                    custom_times_str = settings.get('custom_times', [])
                    if custom_times_str:
                        # 转换字符串时间为datetime对象
                        custom_times = []
                        for time_str in custom_times_str:
                            try:
                                if len(time_str) == 5:  # HH:MM格式
                                    # 转换为今天的datetime
                                    hour, minute = map(int, time_str.split(':'))
                                    dt = datetime.now().replace(hour=hour, minute=minute, second=0, microsecond=0)
                                    if dt <= datetime.now():
                                        dt += timedelta(days=1)  # 如果时间已过，设为明天
                                    custom_times.append(dt)
                                else:
                                    # 完整的datetime字符串
                                    dt = datetime.fromisoformat(time_str)
                                    custom_times.append(dt)
                            except:
                                continue

                        if custom_times:
                            self.custom_time_selector.set_times(custom_times)

                # 时区设置
                if hasattr(self, 'timezone_selector'):
                    timezone = settings.get('timezone', '+08:00')
                    self.timezone_selector.set_timezone(timezone)

                # 更新显示
                self.on_schedule_mode_changed(self.schedule_mode.currentText())
            else:
                # 基础版本只设置每日时间
                daily_time = settings.get('daily_time', '09:00')
                hour, minute = map(int, daily_time.split(':'))
                self.daily_time.setTime(QTime(hour, minute))

        except Exception as e:
            # 出现异常时，使用默认设置并记录错误
            print(f"加载设置时出错：{str(e)}，将使用默认设置")

            # 应用默认设置
            self.cookie_path.setText(self.default_settings["cookie_path"])
            self.data_path.setText(self.default_settings["data_path"])
            self.log_path.setText(self.default_settings["log_path"])
            self.video_path.setText(self.default_settings["video_path"])
            self.cover_path.setText(self.default_settings["cover_path"])
            self.auto_login.setChecked(self.default_settings["auto_login"])
            self.auto_load_accounts.setChecked(self.default_settings["auto_load_accounts"])
            self.fast_startup_enabled.setChecked(True)  # 默认启用快速启动
            self.enable_notify.setChecked(self.default_settings["enable_notify"])
            self.enable_sound.setChecked(self.default_settings["enable_sound"])


            # 应用默认指纹浏览器设置
            self.use_fingerprint.setChecked(self.default_settings["use_fingerprint"])
            self.enable_random_ua.setChecked(self.default_settings["enable_random_ua"])
            self.device_type.setCurrentText(self.default_settings["device_type"])
            self.browser_type.setCurrentText(self.default_settings["browser_type"])
            self.resolution_type.setCurrentText(self.default_settings["resolution_type"])
            self.pixel_ratio.setCurrentText(self.default_settings["pixel_ratio"])
            self.browser_language.setCurrentText(self.default_settings["browser_language"])
            self.randomize_canvas.setChecked(self.default_settings["randomize_canvas"])
            self.disable_webrtc.setChecked(self.default_settings["disable_webrtc"])
            self.enable_anti_detection.setChecked(self.default_settings["enable_anti_detection"])

    def create_browser_settings_tab(self):
        """创建浏览器设置标签页"""
        browser_tab = QWidget()
        browser_layout = QVBoxLayout(browser_tab)

        # 指纹浏览器设置组
        fingerprint_group = QGroupBox("指纹浏览器设置")
        fingerprint_group.setStyleSheet(self.groupbox_style)
        fingerprint_group_layout = QFormLayout()
        fingerprint_group_layout.setContentsMargins(10, 15, 10, 10)
        fingerprint_group_layout.setSpacing(10)

        # 启用指纹浏览器
        self.use_fingerprint = QCheckBox("启用指纹浏览器")
        self.use_fingerprint.setChecked(False)
        self.use_fingerprint.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #000000;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        self.use_fingerprint.setToolTip("启用浏览器指纹伪装，降低被检测为自动化工具的风险")
        fingerprint_group_layout.addRow("启用指纹浏览器:", self.use_fingerprint)

        # 随机User-Agent
        self.enable_random_ua = QCheckBox("启用随机User-Agent")
        self.enable_random_ua.setChecked(True)
        self.enable_random_ua.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #000000;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        self.enable_random_ua.setToolTip("随机生成浏览器User-Agent字符串")
        fingerprint_group_layout.addRow("随机User-Agent:", self.enable_random_ua)

        # 设备类型
        self.device_type = QComboBox()
        self.device_type.addItems(["自动随机", "仅移动设备", "仅桌面设备"])
        self.device_type.setCurrentText("自动随机")
        self.device_type.setStyleSheet(self.input_style)
        self.device_type.setToolTip("选择模拟的设备类型")
        fingerprint_group_layout.addRow("设备类型:", self.device_type)

        # 浏览器类型
        self.browser_type = QComboBox()
        self.browser_type.addItems(["自动随机", "Chrome", "Firefox", "Safari", "Edge"])
        self.browser_type.setCurrentText("自动随机")
        self.browser_type.setStyleSheet(self.input_style)
        self.browser_type.setToolTip("选择模拟的浏览器类型")
        fingerprint_group_layout.addRow("浏览器类型:", self.browser_type)

        # 分辨率类型
        self.resolution_type = QComboBox()
        self.resolution_type.addItems(["随机分辨率", "1920x1080", "1366x768", "1440x900", "1280x720"])
        self.resolution_type.setCurrentText("随机分辨率")
        self.resolution_type.setStyleSheet(self.input_style)
        self.resolution_type.setToolTip("选择屏幕分辨率")
        fingerprint_group_layout.addRow("分辨率:", self.resolution_type)

        # 像素比
        self.pixel_ratio = QComboBox()
        self.pixel_ratio.addItems(["随机", "1", "1.25", "1.5", "2", "3"])
        self.pixel_ratio.setCurrentText("随机")
        self.pixel_ratio.setStyleSheet(self.input_style)
        self.pixel_ratio.setToolTip("设置设备像素比")
        fingerprint_group_layout.addRow("像素比:", self.pixel_ratio)

        # 浏览器语言
        self.browser_language = QComboBox()
        self.browser_language.addItems(["中文", "英文", "随机"])
        self.browser_language.setCurrentText("中文")
        self.browser_language.setStyleSheet(self.input_style)
        self.browser_language.setToolTip("设置浏览器语言")
        fingerprint_group_layout.addRow("浏览器语言:", self.browser_language)

        fingerprint_group.setLayout(fingerprint_group_layout)
        browser_layout.addWidget(fingerprint_group)

        # 高级设置组
        advanced_group = QGroupBox("高级设置")
        advanced_group.setStyleSheet(self.groupbox_style)
        advanced_group_layout = QFormLayout()
        advanced_group_layout.setContentsMargins(10, 15, 10, 10)
        advanced_group_layout.setSpacing(10)

        # Canvas指纹随机化
        self.randomize_canvas = QCheckBox("启用Canvas指纹随机化")
        self.randomize_canvas.setChecked(True)
        self.randomize_canvas.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #000000;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        self.randomize_canvas.setToolTip("随机化Canvas指纹，防止被追踪")
        advanced_group_layout.addRow("Canvas随机化:", self.randomize_canvas)

        # 禁用WebRTC
        self.disable_webrtc = QCheckBox("禁用WebRTC")
        self.disable_webrtc.setChecked(True)
        self.disable_webrtc.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #000000;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        self.disable_webrtc.setToolTip("禁用WebRTC，防止IP泄露")
        advanced_group_layout.addRow("禁用WebRTC:", self.disable_webrtc)

        # 反检测脚本
        self.enable_anti_detection = QCheckBox("启用反检测脚本")
        self.enable_anti_detection.setChecked(True)
        self.enable_anti_detection.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #000000;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        self.enable_anti_detection.setToolTip("注入反自动化检测脚本")
        advanced_group_layout.addRow("反检测脚本:", self.enable_anti_detection)

        advanced_group.setLayout(advanced_group_layout)
        browser_layout.addWidget(advanced_group)

        # 添加说明文本
        info_label = QLabel("说明：\n"
                           "• 指纹浏览器可以降低被检测为自动化工具的风险\n"
                           "• 随机化设置可以让每次访问都使用不同的浏览器特征\n"
                           "• 高级设置提供更细粒度的反检测控制\n"
                           "• 这些设置主要影响单账号登录和批量操作的浏览器行为")
        info_label.setStyleSheet("color: #666; font-size: 10pt; padding: 10px;")
        info_label.setWordWrap(True)
        browser_layout.addWidget(info_label)

        browser_layout.addStretch()
        return browser_tab

    def create_automation_settings_tab(self):
        """创建自动运行设置标签页"""
        automation_tab = QWidget()

        if DATETIME_SELECTOR_AVAILABLE:
            # 使用增强版本的自动运行设置
            return self.create_enhanced_automation_settings_tab(automation_tab)
        else:
            # 使用基础版本的自动运行设置
            return self.create_basic_automation_settings_tab(automation_tab)

    def create_enhanced_automation_settings_tab(self, automation_tab):
        """创建增强版自动运行设置标签页"""
        # 创建分割器布局
        splitter = QSplitter(Qt.Horizontal)

        # 左侧配置区域
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)

        # 基本设置组
        basic_group = QGroupBox("🤖 基本设置")
        basic_group.setStyleSheet(self.groupbox_style)
        basic_layout = QFormLayout()
        basic_layout.setContentsMargins(10, 15, 10, 10)
        basic_layout.setSpacing(15)

        # 启用自动运行
        self.enable_automation = QCheckBox("启用自动运行")
        self.enable_automation.setChecked(False)
        self.enable_automation.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #000000;
                font-size: 12px;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        basic_layout.addRow("启用自动运行:", self.enable_automation)

        # 循环模式
        self.loop_mode = QComboBox()
        self.loop_mode.addItems(["连续执行", "指定次数"])
        self.loop_mode.setStyleSheet(self.input_style)
        basic_layout.addRow("循环模式:", self.loop_mode)

        # 执行次数
        self.loop_count = QSpinBox()
        self.loop_count.setRange(1, 999)
        self.loop_count.setValue(1)
        self.loop_count.setSuffix(" 次")
        self.loop_count.setStyleSheet(self.input_style)
        basic_layout.addRow("执行次数:", self.loop_count)

        # 任务间隔
        self.task_interval = QSpinBox()
        self.task_interval.setRange(1, 1440)  # 1分钟到24小时
        self.task_interval.setValue(5)
        self.task_interval.setSuffix(" 分钟")
        self.task_interval.setStyleSheet(self.input_style)
        basic_layout.addRow("任务间隔:", self.task_interval)

        basic_group.setLayout(basic_layout)
        left_layout.addWidget(basic_group)

        # 调度设置组
        schedule_group = QGroupBox("⏰ 调度设置")
        schedule_group.setStyleSheet(self.groupbox_style)
        schedule_layout = QVBoxLayout()

        # 调度模式选择
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("调度模式:"))
        self.schedule_mode = QComboBox()
        self.schedule_mode.addItems(["间隔执行", "每日定时", "自定义时间"])
        self.schedule_mode.currentTextChanged.connect(self.on_schedule_mode_changed)
        self.schedule_mode.setStyleSheet(self.input_style)
        mode_layout.addWidget(self.schedule_mode)
        mode_layout.addStretch()
        schedule_layout.addLayout(mode_layout)

        # 时区选择器
        self.timezone_selector = TimezoneSelector()
        schedule_layout.addWidget(self.timezone_selector)

        # 间隔执行设置
        self.interval_widget = QWidget()
        interval_layout = QHBoxLayout(self.interval_widget)
        interval_layout.addWidget(QLabel("间隔时间:"))
        self.interval_hours = QSpinBox()
        self.interval_hours.setRange(1, 168)  # 1小时到7天
        self.interval_hours.setValue(2)
        self.interval_hours.setSuffix(" 小时")
        self.interval_hours.setStyleSheet(self.input_style)
        interval_layout.addWidget(self.interval_hours)
        interval_layout.addStretch()
        schedule_layout.addWidget(self.interval_widget)

        # 每日定时设置
        self.daily_widget = QWidget()
        daily_layout = QVBoxLayout(self.daily_widget)

        # 时间选择
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("执行时间:"))
        self.daily_time = QTimeEdit()
        self.daily_time.setTime(QTime(9, 0))
        self.daily_time.setDisplayFormat("HH:mm")
        self.daily_time.setStyleSheet(self.input_style)
        time_layout.addWidget(self.daily_time)
        time_layout.addStretch()
        daily_layout.addLayout(time_layout)

        # 星期选择器
        self.weekday_selector = WeekdaySelector()
        daily_layout.addWidget(self.weekday_selector)

        # 日期范围选择器
        self.date_range_selector = DateRangeSelector()
        daily_layout.addWidget(self.date_range_selector)

        schedule_layout.addWidget(self.daily_widget)

        # 自定义时间设置
        self.custom_widget = QWidget()
        custom_layout = QVBoxLayout(self.custom_widget)

        # 自定义时间选择器
        self.custom_time_selector = CustomTimeSelector()
        custom_layout.addWidget(self.custom_time_selector)

        schedule_layout.addWidget(self.custom_widget)

        schedule_group.setLayout(schedule_layout)
        left_layout.addWidget(schedule_group)

        # 高级设置组
        advanced_group = QGroupBox("⚙️ 高级设置")
        advanced_group.setStyleSheet(self.groupbox_style)
        advanced_layout = QFormLayout()
        advanced_layout.setContentsMargins(10, 15, 10, 10)
        advanced_layout.setSpacing(15)

        # 自动重试
        self.auto_retry = QCheckBox("启用自动重试")
        self.auto_retry.setChecked(True)
        self.auto_retry.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #000000;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        advanced_layout.addRow("自动重试:", self.auto_retry)

        # 最大重试次数
        self.max_retries = QSpinBox()
        self.max_retries.setRange(1, 10)
        self.max_retries.setValue(3)
        self.max_retries.setSuffix(" 次")
        self.max_retries.setStyleSheet(self.input_style)
        advanced_layout.addRow("最大重试次数:", self.max_retries)

        advanced_group.setLayout(advanced_layout)
        left_layout.addWidget(advanced_group)

        left_layout.addStretch()

        # 右侧预览和验证区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # 时间预览组
        preview_group = QGroupBox("🕐 时间预览")
        preview_group.setStyleSheet(self.groupbox_style)
        preview_layout = QVBoxLayout()

        # 时间预览组件
        self.time_preview = TimePreviewWidget()
        preview_layout.addWidget(self.time_preview)

        preview_group.setLayout(preview_layout)
        right_layout.addWidget(preview_group)

        # 验证结果组
        validation_group = QGroupBox("🔍 配置验证")
        validation_group.setStyleSheet(self.groupbox_style)
        validation_layout = QVBoxLayout()

        # 验证结果显示
        self.validation_label = QLabel("配置验证: 等待配置...")
        self.validation_label.setStyleSheet("""
            QLabel {
                padding: 5px;
                border: 1px solid #f39c12;
                border-radius: 3px;
                background-color: #fef9e7;
                color: #f39c12;
                font-weight: normal;
            }
        """)
        validation_layout.addWidget(self.validation_label)

        # 冲突检测结果
        self.conflict_label = QLabel("冲突检测: 无冲突")
        self.conflict_label.setStyleSheet("""
            QLabel {
                padding: 5px;
                border: 1px solid #27ae60;
                border-radius: 3px;
                background-color: #d5f4e6;
                color: #27ae60;
                font-weight: normal;
            }
        """)
        validation_layout.addWidget(self.conflict_label)

        # 验证按钮
        self.validate_btn = QPushButton("🔍 验证配置")
        self.validate_btn.clicked.connect(self.validate_automation_configuration)
        self.validate_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        validation_layout.addWidget(self.validate_btn)

        validation_group.setLayout(validation_layout)
        right_layout.addWidget(validation_group)

        # 说明信息
        info_label = QLabel("""📋 自动运行功能说明：
• 启用后，系统将按照设定的时间自动执行视频处理和批量存稿
• 工作流程：视频处理 → 批量存稿
• 建议在系统空闲时间启用自动运行功能
• 可以通过主界面右上角的心跳指示器查看运行状态""")
        info_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 11px;
                padding: 10px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                margin: 10px 0;
            }
        """)
        info_label.setWordWrap(True)
        right_layout.addWidget(info_label)

        right_layout.addStretch()

        # 添加到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([400, 300])  # 设置初始比例

        # 设置主布局
        main_layout = QVBoxLayout(automation_tab)
        main_layout.addWidget(splitter)

        # 初始化显示状态
        self.on_schedule_mode_changed("间隔执行")

        return automation_tab

    def create_basic_automation_settings_tab(self, automation_tab):
        """创建基础版自动运行设置标签页（当增强组件不可用时）"""
        automation_layout = QVBoxLayout(automation_tab)

        # 自动运行设置组
        automation_group = QGroupBox("🤖 自动运行设置")
        automation_group.setStyleSheet(self.groupbox_style)
        automation_group_layout = QFormLayout()
        automation_group_layout.setContentsMargins(10, 15, 10, 10)
        automation_group_layout.setSpacing(15)

        # 启用自动运行
        self.enable_automation = QCheckBox("启用自动运行")
        self.enable_automation.setChecked(False)
        self.enable_automation.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #000000;
                font-size: 12px;
                font-weight: bold;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        automation_group_layout.addRow("启用自动运行:", self.enable_automation)

        # 调度模式
        self.schedule_mode = QComboBox()
        self.schedule_mode.addItems(["间隔执行", "每日定时", "自定义时间"])
        self.schedule_mode.setStyleSheet(self.input_style)
        automation_group_layout.addRow("调度模式:", self.schedule_mode)

        # 间隔时间
        self.interval_hours = QSpinBox()
        self.interval_hours.setRange(1, 168)  # 1小时到7天
        self.interval_hours.setValue(2)
        self.interval_hours.setSuffix(" 小时")
        self.interval_hours.setStyleSheet(self.input_style)
        automation_group_layout.addRow("间隔时间:", self.interval_hours)

        # 每日执行时间
        self.daily_time = QTimeEdit()
        self.daily_time.setTime(QTime(9, 0))
        self.daily_time.setDisplayFormat("HH:mm")
        self.daily_time.setStyleSheet(self.input_style)
        automation_group_layout.addRow("每日执行时间:", self.daily_time)

        # 任务间隔
        self.task_interval = QSpinBox()
        self.task_interval.setRange(1, 1440)  # 1分钟到24小时
        self.task_interval.setValue(5)
        self.task_interval.setSuffix(" 分钟")
        self.task_interval.setStyleSheet(self.input_style)
        automation_group_layout.addRow("任务间隔:", self.task_interval)

        # 自动重试
        self.auto_retry = QCheckBox("启用自动重试")
        self.auto_retry.setChecked(True)
        self.auto_retry.setStyleSheet("""
            QCheckBox {
                spacing: 5px;
                color: #000000;
                font-size: 12px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
        """)
        automation_group_layout.addRow("自动重试:", self.auto_retry)

        # 最大重试次数
        self.max_retries = QSpinBox()
        self.max_retries.setRange(1, 10)
        self.max_retries.setValue(3)
        self.max_retries.setSuffix(" 次")
        self.max_retries.setStyleSheet(self.input_style)
        automation_group_layout.addRow("最大重试次数:", self.max_retries)

        automation_group.setLayout(automation_group_layout)
        automation_layout.addWidget(automation_group)

        # 工作流程设置组
        workflow_group = QGroupBox("🔄 工作流程设置")
        workflow_group.setStyleSheet(self.groupbox_style)
        workflow_group_layout = QFormLayout()
        workflow_group_layout.setContentsMargins(10, 15, 10, 10)
        workflow_group_layout.setSpacing(15)

        # 循环模式
        self.loop_mode = QComboBox()
        self.loop_mode.addItems(["连续执行", "指定次数"])
        self.loop_mode.setStyleSheet(self.input_style)
        workflow_group_layout.addRow("循环模式:", self.loop_mode)

        # 执行次数
        self.loop_count = QSpinBox()
        self.loop_count.setRange(1, 999)
        self.loop_count.setValue(1)
        self.loop_count.setSuffix(" 次")
        self.loop_count.setStyleSheet(self.input_style)
        workflow_group_layout.addRow("执行次数:", self.loop_count)

        workflow_group.setLayout(workflow_group_layout)
        automation_layout.addWidget(workflow_group)

        # 说明信息
        info_label = QLabel("""📋 自动运行功能说明：
• 启用后，系统将按照设定的时间自动执行视频处理和批量存稿
• 工作流程：视频处理 → 批量存稿
• 建议在系统空闲时间启用自动运行功能
• 可以通过主界面右上角的心跳指示器查看运行状态

⚠️ 注意：当前使用基础版本，如需更多功能请确保增强组件正确安装""")
        info_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 11px;
                padding: 10px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                margin: 10px 0;
            }
        """)
        info_label.setWordWrap(True)
        automation_layout.addWidget(info_label)

        automation_layout.addStretch()

        return automation_tab

    def on_schedule_mode_changed(self, mode):
        """调度模式变更处理"""
        try:
            if not DATETIME_SELECTOR_AVAILABLE:
                return

            # 隐藏所有模式相关的控件
            if hasattr(self, 'interval_widget'):
                self.interval_widget.setVisible(False)
            if hasattr(self, 'daily_widget'):
                self.daily_widget.setVisible(False)
            if hasattr(self, 'custom_widget'):
                self.custom_widget.setVisible(False)

            # 根据模式显示相应控件
            if mode == "间隔执行" and hasattr(self, 'interval_widget'):
                self.interval_widget.setVisible(True)
            elif mode == "每日定时" and hasattr(self, 'daily_widget'):
                self.daily_widget.setVisible(True)
            elif mode == "自定义时间" and hasattr(self, 'custom_widget'):
                self.custom_widget.setVisible(True)

            # 更新时间预览
            if hasattr(self, 'time_preview'):
                self.update_automation_time_preview()

            # 验证配置
            if hasattr(self, 'validate_btn'):
                self.validate_automation_configuration()

        except Exception as e:
            print(f"处理调度模式变更失败: {str(e)}")

    def update_automation_time_preview(self):
        """更新自动化时间预览"""
        try:
            if not DATETIME_SELECTOR_AVAILABLE or not hasattr(self, 'time_preview'):
                return

            next_time = self.calculate_automation_next_execution_time()
            if next_time:
                self.time_preview.set_next_execution_time(next_time)
            else:
                self.time_preview.set_next_execution_time(None)
        except Exception as e:
            print(f"更新自动化时间预览失败: {str(e)}")

    def calculate_automation_next_execution_time(self):
        """计算自动化下次执行时间"""
        try:
            if not hasattr(self, 'schedule_mode'):
                return None

            mode = self.schedule_mode.currentText()
            now = datetime.now()

            if mode == "间隔执行":
                hours = self.interval_hours.value()
                return now + timedelta(hours=hours)

            elif mode == "每日定时" and hasattr(self, 'daily_time'):
                time_obj = self.daily_time.time()

                # 如果有星期选择器，获取选中的星期
                if hasattr(self, 'weekday_selector'):
                    selected_weekdays = self.weekday_selector.get_selected_days()
                    if not selected_weekdays:
                        return None

                    # 找到下一个符合条件的日期
                    for days_ahead in range(8):  # 检查未来7天
                        check_date = now.date() + timedelta(days=days_ahead)
                        weekday = check_date.weekday()
                        # 转换weekday格式 (Monday=0 -> Monday=1, Sunday=6 -> Sunday=0)
                        weekday_mapped = weekday + 1 if weekday < 6 else 0

                        if weekday_mapped in selected_weekdays:
                            check_datetime = datetime.combine(check_date, time_obj.toPyTime())
                            if check_datetime > now:
                                # 检查日期范围
                                if hasattr(self, 'date_range_selector'):
                                    start_date, end_date = self.date_range_selector.get_date_range()
                                    if start_date and end_date:
                                        if not (start_date <= check_date <= end_date):
                                            continue
                                return check_datetime
                else:
                    # 没有星期选择器，使用简单的每日模式
                    today = now.date()
                    check_datetime = datetime.combine(today, time_obj.toPyTime())
                    if check_datetime > now:
                        return check_datetime
                    else:
                        return check_datetime + timedelta(days=1)

            elif mode == "自定义时间" and hasattr(self, 'custom_time_selector'):
                times = self.custom_time_selector.get_times()
                future_times = [t for t in times if t > now]
                if future_times:
                    return min(future_times)

            return None

        except Exception as e:
            print(f"计算自动化下次执行时间失败: {str(e)}")
            return None

    def validate_automation_configuration(self):
        """验证自动化配置"""
        try:
            if not DATETIME_SELECTOR_AVAILABLE:
                return

            mode = self.schedule_mode.currentText()
            validation_messages = []
            conflict_messages = []

            if mode == "间隔执行":
                # 验证间隔时间
                hours = self.interval_hours.value()
                if hours < 1:
                    validation_messages.append("间隔时间不能少于1小时")
                elif hours > 24:
                    validation_messages.append("建议间隔时间不超过24小时")

            elif mode == "每日定时":
                # 验证星期选择
                if hasattr(self, 'weekday_selector'):
                    weekdays = self.weekday_selector.get_selected_days()
                    is_valid, message = TimeValidator.validate_weekday_selection(weekdays)
                    if not is_valid:
                        validation_messages.append(message)

                # 验证日期范围
                if hasattr(self, 'date_range_selector'):
                    start_date, end_date = self.date_range_selector.get_date_range()
                    if start_date and end_date:
                        is_valid, message = TimeValidator.validate_date_range(start_date, end_date)
                        if not is_valid:
                            validation_messages.append(message)

            elif mode == "自定义时间":
                # 验证自定义时间
                if hasattr(self, 'custom_time_selector'):
                    times = self.custom_time_selector.get_times()

                    # 检查时间是否在未来
                    is_valid, message = TimeValidator.validate_future_time(times)
                    if not is_valid:
                        validation_messages.append(message)

                    # 检查时间间隔
                    is_valid, message = TimeValidator.validate_time_interval(times)
                    if not is_valid:
                        validation_messages.append(message)

                    # 检查执行频率
                    is_valid, message = TimeValidator.validate_reasonable_frequency(times)
                    if not is_valid:
                        validation_messages.append(message)

                    # 检测时间冲突
                    conflicts = ConflictDetector.detect_time_conflicts(times)
                    if conflicts:
                        conflict_messages.append(f"发现{len(conflicts)}个时间冲突")

            # 更新验证结果显示
            if hasattr(self, 'validation_label'):
                if validation_messages:
                    self.validation_label.setText(f"配置验证: {'; '.join(validation_messages)}")
                    self.validation_label.setStyleSheet("""
                        QLabel {
                            padding: 5px;
                            border: 1px solid #e74c3c;
                            border-radius: 3px;
                            background-color: #fdf2f2;
                            color: #e74c3c;
                            font-weight: normal;
                        }
                    """)
                else:
                    self.validation_label.setText("配置验证: 配置正常")
                    self.validation_label.setStyleSheet("""
                        QLabel {
                            padding: 5px;
                            border: 1px solid #27ae60;
                            border-radius: 3px;
                            background-color: #d5f4e6;
                            color: #27ae60;
                            font-weight: normal;
                        }
                    """)

            # 更新冲突检测结果
            if hasattr(self, 'conflict_label'):
                if conflict_messages:
                    self.conflict_label.setText(f"冲突检测: {'; '.join(conflict_messages)}")
                    self.conflict_label.setStyleSheet("""
                        QLabel {
                            padding: 5px;
                            border: 1px solid #f39c12;
                            border-radius: 3px;
                            background-color: #fef9e7;
                            color: #f39c12;
                            font-weight: normal;
                        }
                    """)
                else:
                    self.conflict_label.setText("冲突检测: 无冲突")
                    self.conflict_label.setStyleSheet("""
                        QLabel {
                            padding: 5px;
                            border: 1px solid #27ae60;
                            border-radius: 3px;
                            background-color: #d5f4e6;
                            color: #27ae60;
                            font-weight: normal;
                        }
                    """)

        except Exception as e:
            print(f"验证自动化配置失败: {str(e)}")

    def reset_settings(self):
        """恢复默认设置"""
        reply = QMessageBox.question(
            self,
            "确认重置",
            "确定要恢复默认设置吗？当前设置将丢失。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 恢复默认设置
            self.cookie_path.setText(self.default_settings["cookie_path"])
            self.data_path.setText(self.default_settings["data_path"])
            self.log_path.setText(self.default_settings["log_path"])
            self.video_path.setText(self.default_settings["video_path"])
            self.cover_path.setText(self.default_settings["cover_path"])
            self.auto_login.setChecked(self.default_settings["auto_login"])
            self.auto_load_accounts.setChecked(self.default_settings["auto_load_accounts"])
            self.fast_startup_enabled.setChecked(True)  # 默认启用快速启动
            self.enable_notify.setChecked(self.default_settings["enable_notify"])
            self.enable_sound.setChecked(self.default_settings["enable_sound"])


            # 恢复指纹浏览器设置
            self.use_fingerprint.setChecked(self.default_settings["use_fingerprint"])
            self.enable_random_ua.setChecked(self.default_settings["enable_random_ua"])
            self.device_type.setCurrentText(self.default_settings["device_type"])
            self.browser_type.setCurrentText(self.default_settings["browser_type"])
            self.resolution_type.setCurrentText(self.default_settings["resolution_type"])
            self.pixel_ratio.setCurrentText(self.default_settings["pixel_ratio"])
            self.browser_language.setCurrentText(self.default_settings["browser_language"])
            self.randomize_canvas.setChecked(self.default_settings["randomize_canvas"])
            self.disable_webrtc.setChecked(self.default_settings["disable_webrtc"])
            self.enable_anti_detection.setChecked(self.default_settings["enable_anti_detection"])

            QMessageBox.information(self, "重置成功", "设置已恢复为默认值")

    def create_tutorial_tab(self):
        """创建软件教程标签页

        Returns:
            QWidget: 教程标签页部件
        """
        # 创建标签页容器
        tutorial_tab = QWidget()
        tutorial_layout = QVBoxLayout(tutorial_tab)
        tutorial_layout.setContentsMargins(0, 0, 0, 0)

        try:
            # 创建Web视图
            self.web_view = QWebEngineView()

            # 获取教程HTML文件的路径
            # 首先尝试从开发环境路径获取
            tutorial_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                        "app", "resources", "tutorial", "tutorial.html")

            # 如果开发环境路径不存在，尝试从打包后的路径获取
            if not os.path.exists(tutorial_path):
                # 尝试使用resource_path函数获取资源路径
                try:
                    from app.utils.resource_path import resource_path
                    tutorial_path = resource_path(os.path.join("app", "resources", "tutorial", "tutorial.html"))
                except ImportError:
                    # 如果resource_path不可用，尝试其他方法
                    base_dir = os.path.dirname(os.path.abspath(sys.argv[0]))
                    tutorial_path = os.path.join(base_dir, "app", "resources", "tutorial", "tutorial.html")

                    # 如果仍然找不到，尝试相对于当前目录的路径
                    if not os.path.exists(tutorial_path):
                        tutorial_path = os.path.join("app", "resources", "tutorial", "tutorial.html")

                        # 如果仍然找不到，尝试相对于可执行文件的路径
                        if not os.path.exists(tutorial_path):
                            tutorial_path = os.path.join(os.path.dirname(sys.executable),
                                                        "app", "resources", "tutorial", "tutorial.html")

            # 检查文件是否存在
            if os.path.exists(tutorial_path):
                # 加载HTML文件
                self.web_view.load(QUrl.fromLocalFile(tutorial_path))
                print(f"成功加载教程文件: {tutorial_path}")
            else:
                # 如果文件不存在，显示内置的简易教程
                self.web_view.setHtml(f"""
                    <html>
                    <head>
                        <style>
                            body {{
                                font-family: 'Microsoft YaHei', sans-serif;
                                margin: 0;
                                padding: 20px;
                                background-color: #f5f5f5;
                                color: #333;
                            }}
                            .container {{
                                max-width: 800px;
                                margin: 0 auto;
                                background: white;
                                padding: 20px;
                                border-radius: 8px;
                                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                            }}
                            h1, h2 {{
                                color: #2980b9;
                                border-bottom: 1px solid #eee;
                                padding-bottom: 10px;
                            }}
                            .feature {{
                                margin: 20px 0;
                                padding: 15px;
                                background: #f9f9f9;
                                border-left: 4px solid #2980b9;
                                border-radius: 4px;
                            }}
                            .feature h3 {{
                                margin-top: 0;
                                color: #2980b9;
                            }}
                            .note {{
                                background-color: #fffde7;
                                padding: 10px;
                                border-left: 4px solid #ffd600;
                                margin: 10px 0;
                            }}
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <h1>头条内容社交工具使用教程</h1>

                            <div class="note">
                                注意：完整教程文件未找到，这是简易版教程。请确保软件安装完整。
                            </div>

                            <h2>主要功能</h2>

                            <div class="feature">
                                <h3>1. 账号管理</h3>
                                <p>支持批量导入和管理头条账号，自动加载cookie文件。</p>
                                <p>在"路径设置"中设置账号路径，然后点击"加载账号"按钮。</p>
                            </div>

                            <div class="feature">
                                <h3>2. 批量存稿</h3>
                                <p>支持批量上传视频并保存为草稿，提高内容发布效率。</p>
                                <p>选择账号后点击"批量存稿"按钮，按照提示操作。</p>
                            </div>

                            <div class="feature">
                                <h3>3. 账号养号</h3>
                                <p>自动执行浏览、点赞、评论、关注等操作，提高账号活跃度。</p>
                                <p>在养号设置中配置行为参数，然后开始养号任务。</p>
                            </div>

                            <div class="feature">
                                <h3>4. 数据采集</h3>
                                <p>支持采集热门视频数据，分析内容趋势。</p>
                                <p>使用"视频爬虫"功能，输入关键词开始采集。</p>
                            </div>

                            <h2>使用技巧</h2>
                            <ul>
                                <li>确保所有路径设置正确，特别是账号路径和视频路径</li>
                                <li>批量操作前先测试单个账号，确保功能正常</li>
                                <li>定期检查日志，及时发现并解决问题</li>
                                <li>使用代理IP可以提高操作成功率和安全性</li>
                            </ul>
                        </div>
                    </body>
                    </html>
                """)
                print(f"未找到教程文件，使用内置简易教程。尝试路径: {tutorial_path}")

            # 创建导航按钮
            nav_widget = QWidget()
            nav_layout = QHBoxLayout(nav_widget)

            # 当前页码显示
            self.page_label = QLabel("第1页 / 共8页")
            self.page_label.setStyleSheet("font-size: 14px; font-weight: bold;")

            # 上一页按钮
            self.prev_btn = QPushButton(" 上一页")
            self.prev_btn.setIcon(self.style().standardIcon(QStyle.SP_ArrowLeft))
            self.prev_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498DB;
                    color: white;
                    border-radius: 4px;
                    padding: 8px 15px;
                    font-weight: bold;
                    border: none;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #2980B9;
                }
                QPushButton:disabled {
                    background-color: #95A5A6;
                }
            """)
            self.prev_btn.clicked.connect(self.go_prev_slide)

            # 下一页按钮
            self.next_btn = QPushButton("下一页 ")
            self.next_btn.setIcon(self.style().standardIcon(QStyle.SP_ArrowRight))
            self.next_btn.setLayoutDirection(Qt.RightToLeft)  # 图标在右侧
            self.next_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498DB;
                    color: white;
                    border-radius: 4px;
                    padding: 8px 15px;
                    font-weight: bold;
                    border: none;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #2980B9;
                }
                QPushButton:disabled {
                    background-color: #95A5A6;
                }
            """)
            self.next_btn.clicked.connect(self.go_next_slide)

            # 添加按钮到导航布局
            nav_layout.addWidget(self.prev_btn)
            nav_layout.addStretch()
            nav_layout.addWidget(self.page_label)
            nav_layout.addStretch()
            nav_layout.addWidget(self.next_btn)

            # 添加Web视图和导航按钮到布局
            tutorial_layout.addWidget(self.web_view, 1)  # 1是拉伸因子，让Web视图占据大部分空间
            tutorial_layout.addWidget(nav_widget)

            # 设置JavaScript通信
            self.web_view.page().runJavaScript("""
                window.updateSlideNumber = function(current, total) {
                    window.currentSlide = current;
                    window.totalSlides = total;
                    // 这里不需要做任何事情，因为我们会通过按钮点击来获取当前页码
                };
            """)

            # 当前幻灯片编号
            self.current_slide = 1
            self.total_slides = 8

        except Exception as e:
            # 如果出现异常，显示错误信息
            error_widget = QLabel(f"加载教程时出错: {str(e)}")
            error_widget.setStyleSheet("color: red; font-size: 14px; padding: 20px;")
            tutorial_layout.addWidget(error_widget)

        return tutorial_tab

    def go_next_slide(self):
        """转到下一张幻灯片"""
        if self.current_slide < self.total_slides:
            self.current_slide += 1
            self.update_slide()

    def go_prev_slide(self):
        """转到上一张幻灯片"""
        if self.current_slide > 1:
            self.current_slide -= 1
            self.update_slide()

    def update_slide(self):
        """更新当前幻灯片"""
        # 更新页码显示
        self.page_label.setText(f"第{self.current_slide}页 / 共{self.total_slides}页")

        # 更新按钮状态
        self.prev_btn.setEnabled(self.current_slide > 1)
        self.next_btn.setEnabled(self.current_slide < self.total_slides)

        # 执行JavaScript切换幻灯片
        self.web_view.page().runJavaScript(f"showSlide({self.current_slide});")

    def create_default_directories(self):
        """一键创建默认文件夹"""
        # 创建主目录
        main_dir = "D:/头条全自动"
        try:
            if not os.path.exists(main_dir):
                os.makedirs(main_dir)
                print(f"创建主目录: {main_dir}")
        except Exception as e:
            QMessageBox.critical(self, "创建失败", f"创建主目录失败：{str(e)}")
            return

        # 设置默认路径
        default_cookie_path = "D:/头条全自动/账号"
        default_data_path = "D:/头条全自动/数据"
        default_log_path = "D:/头条全自动/日志"
        default_video_path = "D:/头条全自动/视频"
        default_cover_path = "D:/头条全自动/封面"

        # 更新UI中的路径
        self.cookie_path.setText(default_cookie_path)
        self.data_path.setText(default_data_path)
        self.log_path.setText(default_log_path)
        self.video_path.setText(default_video_path)
        self.cover_path.setText(default_cover_path)

        # 定义需要创建的目录列表
        directories = [
            default_cookie_path,
            default_data_path,
            default_log_path,
            default_video_path,
            default_cover_path
        ]

        # 创建目录
        created_count = 0
        failed_dirs = []

        for directory in directories:
            try:
                # 强制创建目录，不管是否存在
                if os.path.exists(directory):
                    print(f"目录已存在: {directory}")
                else:
                    os.makedirs(directory)
                    created_count += 1
                    print(f"创建目录: {directory}")
            except Exception as e:
                failed_dirs.append(f"{directory} (错误: {str(e)})")
                print(f"创建目录失败: {directory}, 错误: {str(e)}")

        # 立即保存设置
        try:
            settings = {
                "cookie_path": default_cookie_path,
                "data_path": default_data_path,
                "log_path": default_log_path,
                "video_path": default_video_path,
                "cover_path": default_cover_path,
                "auto_login": self.auto_login.isChecked(),
                "auto_load_accounts": self.auto_load_accounts.isChecked(),
                "fast_startup_enabled": self.fast_startup_enabled.isChecked(),
                "enable_notify": self.enable_notify.isChecked(),
                "enable_sound": self.enable_sound.isChecked(),

                # 指纹浏览器设置
                "use_fingerprint": self.use_fingerprint.isChecked(),
                "enable_random_ua": self.enable_random_ua.isChecked(),
                "device_type": self.device_type.currentText(),
                "browser_type": self.browser_type.currentText(),
                "resolution_type": self.resolution_type.currentText(),
                "pixel_ratio": self.pixel_ratio.currentText(),
                "browser_language": self.browser_language.currentText(),
                "randomize_canvas": self.randomize_canvas.isChecked(),
                "disable_webrtc": self.disable_webrtc.isChecked(),
                "enable_anti_detection": self.enable_anti_detection.isChecked()
            }

            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=4)

            # 发送设置保存信号
            self.settings_saved.emit(settings)
            print(f"设置已保存到: {self.settings_file}")
        except Exception as e:
            print(f"保存设置失败: {str(e)}")

        # 显示结果
        if failed_dirs:
            error_msg = "以下目录创建失败:\n" + "\n".join(failed_dirs)
            QMessageBox.warning(self, "部分创建失败", error_msg)
        elif created_count > 0:
            QMessageBox.information(self, "创建成功", f"成功创建了 {created_count} 个目录。\n\n所有目录已设置在D盘的'头条全自动'文件夹中。\n\n设置已保存。")
        else:
            QMessageBox.information(self, "创建结果", "所有目录已存在。\n\n路径设置已更新并保存。")

    def get_fingerprint_settings(self):
        """获取指纹浏览器设置

        Returns:
            dict: 指纹浏览器设置字典
        """
        return {
            'use_fingerprint': self.use_fingerprint.isChecked(),
            'enable_random_ua': self.enable_random_ua.isChecked(),
            'device_type': self.device_type.currentText(),
            'browser_type': self.browser_type.currentText(),
            'resolution_type': self.resolution_type.currentText(),
            'pixel_ratio': self.pixel_ratio.currentText(),
            'browser_language': self.browser_language.currentText(),
            'randomize_canvas': self.randomize_canvas.isChecked(),
            'disable_webrtc': self.disable_webrtc.isChecked(),
            'enable_anti_detection': self.enable_anti_detection.isChecked()
        }

    def get_all_settings(self):
        """获取所有设置

        Returns:
            dict: 所有设置的字典
        """
        settings = {
            "cookie_path": self.cookie_path.text(),
            "data_path": self.data_path.text(),
            "log_path": self.log_path.text(),
            "video_path": self.video_path.text(),
            "cover_path": self.cover_path.text(),
            "auto_login": self.auto_login.isChecked(),
            "auto_load_accounts": self.auto_load_accounts.isChecked(),
            "fast_startup_enabled": self.fast_startup_enabled.isChecked(),
            "enable_notify": self.enable_notify.isChecked(),
            "enable_sound": self.enable_sound.isChecked(),

        }

        # 添加指纹浏览器设置
        settings.update(self.get_fingerprint_settings())

        return settings