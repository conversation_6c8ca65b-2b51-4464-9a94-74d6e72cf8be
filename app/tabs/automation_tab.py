#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动运行设置选项卡
提供自动化任务调度的配置界面
"""

import os
import sys
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                            QLabel, QSpinBox, QTimeEdit, QComboBox, QCheckBox,
                            QPushButton, QTextEdit, QTableWidget, QTableWidgetItem,
                            QHeaderView, QMessageBox, QFrame, QGridLayout,
                            QScrollArea, QSplitter)
from PyQt5.QtCore import Qt, QTime, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QColor, QPalette

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from app.utils.logger import info, warning, error, debug
from app.widgets.datetime_selector import (WeekdaySelector, DateRangeSelector,
                                          CustomTimeSelector, TimezoneSelector,
                                          TimePreviewWidget, TimeValidator, ConflictDetector)


class AutomationTab(QWidget):
    """自动运行设置选项卡"""
    
    # 信号定义
    config_changed = pyqtSignal(dict)  # 配置变更信号
    
    def __init__(self, automation_scheduler=None):
        super().__init__()
        self.automation_scheduler = automation_scheduler
        self.init_ui()
        
        # 连接调度器信号
        if self.automation_scheduler:
            self.automation_scheduler.status_updated.connect(self.on_status_updated)
            self.automation_scheduler.task_started.connect(self.on_task_started)
            self.automation_scheduler.task_completed.connect(self.on_task_completed)
            self.automation_scheduler.workflow_completed.connect(self.on_workflow_completed)
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status_display)
        self.status_timer.start(5000)  # 每5秒更新一次状态

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        layout.setSpacing(15)
        
        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 主内容区域
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        
        # 标题
        title_label = QLabel("🤖 自动运行设置")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        content_layout.addWidget(title_label)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧配置区域
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 基本设置组
        left_layout.addWidget(self.create_basic_settings_group())
        
        # 调度设置组
        left_layout.addWidget(self.create_schedule_settings_group())
        
        # 高级设置组
        left_layout.addWidget(self.create_advanced_settings_group())
        
        # 控制按钮组
        left_layout.addWidget(self.create_control_buttons_group())
        
        # 右侧状态和日志区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)

        # 时间预览组
        right_layout.addWidget(self.create_time_preview_group())

        # 状态显示组
        right_layout.addWidget(self.create_status_display_group())

        # 执行日志组
        right_layout.addWidget(self.create_execution_log_group())

        # 统计信息组
        right_layout.addWidget(self.create_statistics_group())
        
        # 添加到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([400, 600])  # 设置初始比例
        
        content_layout.addWidget(splitter)
        
        # 设置滚动区域
        scroll.setWidget(content_widget)
        layout.addWidget(scroll)
        
        self.setLayout(layout)
        
        # 加载当前配置
        self.load_current_config()

    def create_basic_settings_group(self):
        """创建基本设置组"""
        group = QGroupBox("📋 基本设置")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QGridLayout()
        
        # 启用自动化
        self.enable_automation = QCheckBox("启用自动化任务")
        self.enable_automation.setStyleSheet("font-weight: normal;")
        layout.addWidget(self.enable_automation, 0, 0, 1, 2)
        
        # 循环模式
        layout.addWidget(QLabel("循环模式:"), 1, 0)
        self.loop_mode = QComboBox()
        self.loop_mode.addItems(["连续执行", "指定次数"])
        self.loop_mode.setStyleSheet("font-weight: normal;")
        layout.addWidget(self.loop_mode, 1, 1)
        
        # 执行次数
        layout.addWidget(QLabel("执行次数:"), 2, 0)
        self.loop_count = QSpinBox()
        self.loop_count.setRange(1, 999)
        self.loop_count.setValue(1)
        self.loop_count.setStyleSheet("font-weight: normal;")
        layout.addWidget(self.loop_count, 2, 1)
        
        # 任务间隔
        layout.addWidget(QLabel("任务间隔(分钟):"), 3, 0)
        self.task_interval = QSpinBox()
        self.task_interval.setRange(1, 1440)  # 1分钟到24小时
        self.task_interval.setValue(5)
        self.task_interval.setStyleSheet("font-weight: normal;")
        layout.addWidget(self.task_interval, 3, 1)
        
        group.setLayout(layout)
        return group

    def create_schedule_settings_group(self):
        """创建调度设置组"""
        group = QGroupBox("⏰ 调度设置")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QVBoxLayout()

        # 调度模式选择
        mode_layout = QHBoxLayout()
        mode_layout.addWidget(QLabel("调度模式:"))
        self.schedule_mode = QComboBox()
        self.schedule_mode.addItems(["间隔执行", "每日定时", "自定义时间"])
        self.schedule_mode.currentTextChanged.connect(self.on_schedule_mode_changed)
        self.schedule_mode.setStyleSheet("font-weight: normal;")
        mode_layout.addWidget(self.schedule_mode)
        mode_layout.addStretch()
        layout.addLayout(mode_layout)

        # 时区选择器
        self.timezone_selector = TimezoneSelector()
        self.timezone_selector.timezone_changed.connect(self.on_timezone_changed)
        layout.addWidget(self.timezone_selector)

        # 间隔执行设置
        self.interval_widget = QWidget()
        interval_layout = QHBoxLayout(self.interval_widget)
        interval_layout.addWidget(QLabel("间隔时间:"))
        self.interval_hours = QSpinBox()
        self.interval_hours.setRange(1, 168)  # 1小时到7天
        self.interval_hours.setValue(2)
        self.interval_hours.setSuffix(" 小时")
        self.interval_hours.setStyleSheet("font-weight: normal;")
        interval_layout.addWidget(self.interval_hours)
        interval_layout.addStretch()
        layout.addWidget(self.interval_widget)

        # 每日定时设置
        self.daily_widget = QWidget()
        daily_layout = QVBoxLayout(self.daily_widget)

        # 时间选择
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("执行时间:"))
        self.daily_time = QTimeEdit()
        self.daily_time.setTime(QTime(9, 0))
        self.daily_time.setDisplayFormat("HH:mm")
        self.daily_time.setStyleSheet("font-weight: normal;")
        time_layout.addWidget(self.daily_time)
        time_layout.addStretch()
        daily_layout.addLayout(time_layout)

        # 星期选择器
        self.weekday_selector = WeekdaySelector()
        self.weekday_selector.selection_changed.connect(self.on_weekdays_changed)
        daily_layout.addWidget(self.weekday_selector)

        # 日期范围选择器
        self.date_range_selector = DateRangeSelector()
        self.date_range_selector.range_changed.connect(self.on_date_range_changed)
        daily_layout.addWidget(self.date_range_selector)

        layout.addWidget(self.daily_widget)

        # 自定义时间设置
        self.custom_widget = QWidget()
        custom_layout = QVBoxLayout(self.custom_widget)

        # 自定义时间选择器
        self.custom_time_selector = CustomTimeSelector()
        self.custom_time_selector.times_changed.connect(self.on_custom_times_changed)
        custom_layout.addWidget(self.custom_time_selector)

        layout.addWidget(self.custom_widget)

        group.setLayout(layout)

        # 初始化显示状态
        self.on_schedule_mode_changed("间隔执行")

        return group

    def create_advanced_settings_group(self):
        """创建高级设置组"""
        group = QGroupBox("⚙️ 高级设置")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QGridLayout()
        
        # 自动重试
        self.auto_retry = QCheckBox("启用自动重试")
        self.auto_retry.setStyleSheet("font-weight: normal;")
        layout.addWidget(self.auto_retry, 0, 0, 1, 2)
        
        # 最大重试次数
        layout.addWidget(QLabel("最大重试次数:"), 1, 0)
        self.max_retries = QSpinBox()
        self.max_retries.setRange(1, 10)
        self.max_retries.setValue(3)
        self.max_retries.setStyleSheet("font-weight: normal;")
        layout.addWidget(self.max_retries, 1, 1)
        
        # 重试延迟
        layout.addWidget(QLabel("重试延迟(分钟):"), 2, 0)
        self.retry_delay = QSpinBox()
        self.retry_delay.setRange(1, 60)
        self.retry_delay.setValue(10)
        self.retry_delay.setStyleSheet("font-weight: normal;")
        layout.addWidget(self.retry_delay, 2, 1)
        
        group.setLayout(layout)
        return group

    def create_control_buttons_group(self):
        """创建控制按钮组"""
        group = QGroupBox("🎮 控制操作")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        layout = QGridLayout()
        
        # 保存配置按钮
        self.save_config_btn = QPushButton("💾 保存配置")
        self.save_config_btn.clicked.connect(self.save_config)
        self.save_config_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(self.save_config_btn, 0, 0)
        
        # 启动调度器按钮
        self.start_btn = QPushButton("▶️ 启动调度器")
        self.start_btn.clicked.connect(self.start_scheduler)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        layout.addWidget(self.start_btn, 0, 1)
        
        # 停止调度器按钮
        self.stop_btn = QPushButton("⏹️ 停止调度器")
        self.stop_btn.clicked.connect(self.stop_scheduler)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        layout.addWidget(self.stop_btn, 1, 0)
        
        # 暂停/恢复按钮
        self.pause_btn = QPushButton("⏸️ 暂停调度器")
        self.pause_btn.clicked.connect(self.toggle_pause)
        self.pause_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        layout.addWidget(self.pause_btn, 1, 1)
        
        # 立即执行按钮
        self.execute_now_btn = QPushButton("🚀 立即执行")
        self.execute_now_btn.clicked.connect(self.execute_now)
        self.execute_now_btn.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8e44ad;
            }
        """)
        layout.addWidget(self.execute_now_btn, 2, 0, 1, 2)
        
        group.setLayout(layout)
        return group

    def create_status_display_group(self):
        """创建状态显示组"""
        group = QGroupBox("📊 运行状态")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QGridLayout()

        # 调度器状态
        layout.addWidget(QLabel("调度器状态:"), 0, 0)
        self.scheduler_status = QLabel("已停止")
        self.scheduler_status.setStyleSheet("""
            QLabel {
                font-weight: normal;
                padding: 4px 8px;
                border-radius: 4px;
                background-color: #e74c3c;
                color: white;
            }
        """)
        layout.addWidget(self.scheduler_status, 0, 1)

        # 当前任务
        layout.addWidget(QLabel("当前任务:"), 1, 0)
        self.current_task = QLabel("无")
        self.current_task.setStyleSheet("font-weight: normal;")
        layout.addWidget(self.current_task, 1, 1)

        # 下次执行时间
        layout.addWidget(QLabel("下次执行:"), 2, 0)
        self.next_run_time = QLabel("未设置")
        self.next_run_time.setStyleSheet("font-weight: normal;")
        layout.addWidget(self.next_run_time, 2, 1)

        # 上次执行时间
        layout.addWidget(QLabel("上次执行:"), 3, 0)
        self.last_run_time = QLabel("从未执行")
        self.last_run_time.setStyleSheet("font-weight: normal;")
        layout.addWidget(self.last_run_time, 3, 1)

        group.setLayout(layout)
        return group

    def create_execution_log_group(self):
        """创建执行日志组"""
        group = QGroupBox("📝 执行日志")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QVBoxLayout()

        # 日志显示区域
        self.log_display = QTextEdit()
        self.log_display.setMaximumHeight(200)
        self.log_display.setReadOnly(True)
        self.log_display.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 4px;
            }
        """)
        layout.addWidget(self.log_display)

        # 日志控制按钮
        log_controls = QHBoxLayout()

        self.clear_log_btn = QPushButton("🗑️ 清空日志")
        self.clear_log_btn.clicked.connect(self.clear_log)
        self.clear_log_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        log_controls.addWidget(self.clear_log_btn)

        log_controls.addStretch()
        layout.addLayout(log_controls)

        group.setLayout(layout)
        return group

    def create_statistics_group(self):
        """创建统计信息组"""
        group = QGroupBox("📈 统计信息")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QGridLayout()

        # 总执行次数
        layout.addWidget(QLabel("总执行次数:"), 0, 0)
        self.total_workflows = QLabel("0")
        self.total_workflows.setStyleSheet("font-weight: normal;")
        layout.addWidget(self.total_workflows, 0, 1)

        # 成功次数
        layout.addWidget(QLabel("成功次数:"), 1, 0)
        self.successful_workflows = QLabel("0")
        self.successful_workflows.setStyleSheet("font-weight: normal; color: #27ae60;")
        layout.addWidget(self.successful_workflows, 1, 1)

        # 失败次数
        layout.addWidget(QLabel("失败次数:"), 2, 0)
        self.failed_workflows = QLabel("0")
        self.failed_workflows.setStyleSheet("font-weight: normal; color: #e74c3c;")
        layout.addWidget(self.failed_workflows, 2, 1)

        # 成功率
        layout.addWidget(QLabel("成功率:"), 3, 0)
        self.success_rate = QLabel("0%")
        self.success_rate.setStyleSheet("font-weight: normal;")
        layout.addWidget(self.success_rate, 3, 1)

        group.setLayout(layout)
        return group

    def create_time_preview_group(self):
        """创建时间预览组"""
        group = QGroupBox("🕐 时间预览与验证")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        layout = QVBoxLayout()

        # 时间预览组件
        self.time_preview = TimePreviewWidget()
        layout.addWidget(self.time_preview)

        # 验证结果显示
        self.validation_label = QLabel("配置验证: 等待配置...")
        self.validation_label.setStyleSheet("""
            QLabel {
                padding: 5px;
                border: 1px solid #f39c12;
                border-radius: 3px;
                background-color: #fef9e7;
                color: #f39c12;
                font-weight: normal;
            }
        """)
        layout.addWidget(self.validation_label)

        # 冲突检测结果
        self.conflict_label = QLabel("冲突检测: 无冲突")
        self.conflict_label.setStyleSheet("""
            QLabel {
                padding: 5px;
                border: 1px solid #27ae60;
                border-radius: 3px;
                background-color: #d5f4e6;
                color: #27ae60;
                font-weight: normal;
            }
        """)
        layout.addWidget(self.conflict_label)

        # 验证按钮
        validate_layout = QHBoxLayout()

        self.validate_btn = QPushButton("🔍 验证配置")
        self.validate_btn.clicked.connect(self.validate_configuration)
        self.validate_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        validate_layout.addWidget(self.validate_btn)

        validate_layout.addStretch()
        layout.addLayout(validate_layout)

        group.setLayout(layout)
        return group

    def on_schedule_mode_changed(self, mode):
        """调度模式变更处理"""
        try:
            # 隐藏所有模式相关的控件
            self.interval_widget.setVisible(False)
            self.daily_widget.setVisible(False)
            self.custom_widget.setVisible(False)

            # 根据模式显示相应控件
            if mode == "间隔执行":
                self.interval_widget.setVisible(True)
            elif mode == "每日定时":
                self.daily_widget.setVisible(True)
            elif mode == "自定义时间":
                self.custom_widget.setVisible(True)

            # 更新时间预览
            self.update_time_preview()

            # 验证配置
            self.validate_configuration()

        except Exception as e:
            error(f"处理调度模式变更失败: {str(e)}")

    def on_timezone_changed(self, timezone):
        """时区变更处理"""
        try:
            info(f"时区已更改为: {timezone}")
            self.update_time_preview()
        except Exception as e:
            error(f"处理时区变更失败: {str(e)}")

    def on_weekdays_changed(self, weekdays):
        """星期选择变更处理"""
        try:
            info(f"选择的星期: {weekdays}")
            self.update_time_preview()
            self.validate_configuration()
        except Exception as e:
            error(f"处理星期选择变更失败: {str(e)}")

    def on_date_range_changed(self, start_date, end_date):
        """日期范围变更处理"""
        try:
            info(f"日期范围: {start_date.toString()} 到 {end_date.toString()}")
            self.update_time_preview()
            self.validate_configuration()
        except Exception as e:
            error(f"处理日期范围变更失败: {str(e)}")

    def on_custom_times_changed(self, times):
        """自定义时间变更处理"""
        try:
            info(f"自定义时间点数量: {len(times)}")
            self.update_time_preview()
            self.validate_configuration()
        except Exception as e:
            error(f"处理自定义时间变更失败: {str(e)}")

    def update_time_preview(self):
        """更新时间预览"""
        try:
            next_time = self.calculate_next_execution_time()
            if next_time:
                self.time_preview.set_next_execution_time(next_time)
            else:
                self.time_preview.set_next_execution_time(None)
        except Exception as e:
            error(f"更新时间预览失败: {str(e)}")

    def calculate_next_execution_time(self):
        """计算下次执行时间"""
        try:
            mode = self.schedule_mode.currentText()
            now = datetime.now()

            if mode == "间隔执行":
                hours = self.interval_hours.value()
                return now + timedelta(hours=hours)

            elif mode == "每日定时":
                time_obj = self.daily_time.time()
                selected_weekdays = self.weekday_selector.get_selected_days()

                if not selected_weekdays:
                    return None

                # 找到下一个符合条件的日期
                for days_ahead in range(8):  # 检查未来7天
                    check_date = now.date() + timedelta(days=days_ahead)
                    weekday = check_date.weekday()
                    # 转换weekday格式 (Monday=0 -> Monday=1, Sunday=6 -> Sunday=0)
                    weekday_mapped = weekday + 1 if weekday < 6 else 0

                    if weekday_mapped in selected_weekdays:
                        check_datetime = datetime.combine(check_date, time_obj.toPyTime())
                        if check_datetime > now:
                            # 检查日期范围
                            start_date, end_date = self.date_range_selector.get_date_range()
                            if start_date and end_date:
                                if not (start_date <= check_date <= end_date):
                                    continue
                            return check_datetime

            elif mode == "自定义时间":
                times = self.custom_time_selector.get_times()
                future_times = [t for t in times if t > now]
                if future_times:
                    return min(future_times)

            return None

        except Exception as e:
            error(f"计算下次执行时间失败: {str(e)}")
            return None

    def validate_configuration(self):
        """验证配置"""
        try:
            mode = self.schedule_mode.currentText()
            validation_messages = []
            conflict_messages = []

            if mode == "间隔执行":
                # 验证间隔时间
                hours = self.interval_hours.value()
                if hours < 1:
                    validation_messages.append("间隔时间不能少于1小时")
                elif hours > 24:
                    validation_messages.append("建议间隔时间不超过24小时")

            elif mode == "每日定时":
                # 验证星期选择
                weekdays = self.weekday_selector.get_selected_days()
                is_valid, message = TimeValidator.validate_weekday_selection(weekdays)
                if not is_valid:
                    validation_messages.append(message)

                # 验证日期范围
                start_date, end_date = self.date_range_selector.get_date_range()
                if start_date and end_date:
                    is_valid, message = TimeValidator.validate_date_range(start_date, end_date)
                    if not is_valid:
                        validation_messages.append(message)

            elif mode == "自定义时间":
                # 验证自定义时间
                times = self.custom_time_selector.get_times()

                # 检查时间是否在未来
                is_valid, message = TimeValidator.validate_future_time(times)
                if not is_valid:
                    validation_messages.append(message)

                # 检查时间间隔
                is_valid, message = TimeValidator.validate_time_interval(times)
                if not is_valid:
                    validation_messages.append(message)

                # 检查执行频率
                is_valid, message = TimeValidator.validate_reasonable_frequency(times)
                if not is_valid:
                    validation_messages.append(message)

                # 检测时间冲突
                conflicts = ConflictDetector.detect_time_conflicts(times)
                if conflicts:
                    conflict_messages.append(f"发现{len(conflicts)}个时间冲突")

            # 更新验证结果显示
            if validation_messages:
                self.validation_label.setText(f"配置验证: {'; '.join(validation_messages)}")
                self.validation_label.setStyleSheet("""
                    QLabel {
                        padding: 5px;
                        border: 1px solid #e74c3c;
                        border-radius: 3px;
                        background-color: #fdf2f2;
                        color: #e74c3c;
                        font-weight: normal;
                    }
                """)
            else:
                self.validation_label.setText("配置验证: 配置正常")
                self.validation_label.setStyleSheet("""
                    QLabel {
                        padding: 5px;
                        border: 1px solid #27ae60;
                        border-radius: 3px;
                        background-color: #d5f4e6;
                        color: #27ae60;
                        font-weight: normal;
                    }
                """)

            # 更新冲突检测结果
            if conflict_messages:
                self.conflict_label.setText(f"冲突检测: {'; '.join(conflict_messages)}")
                self.conflict_label.setStyleSheet("""
                    QLabel {
                        padding: 5px;
                        border: 1px solid #f39c12;
                        border-radius: 3px;
                        background-color: #fef9e7;
                        color: #f39c12;
                        font-weight: normal;
                    }
                """)
            else:
                self.conflict_label.setText("冲突检测: 无冲突")
                self.conflict_label.setStyleSheet("""
                    QLabel {
                        padding: 5px;
                        border: 1px solid #27ae60;
                        border-radius: 3px;
                        background-color: #d5f4e6;
                        color: #27ae60;
                        font-weight: normal;
                    }
                """)

        except Exception as e:
            error(f"验证配置失败: {str(e)}")

    def load_current_config(self):
        """加载当前配置"""
        try:
            if not self.automation_scheduler:
                return

            config = self.automation_scheduler.config

            # 基本设置
            self.enable_automation.setChecked(config.get('enabled', False))

            loop_mode = config.get('loop_mode', 'continuous')
            self.loop_mode.setCurrentText("连续执行" if loop_mode == 'continuous' else "指定次数")

            self.loop_count.setValue(config.get('loop_count', 1))
            self.task_interval.setValue(config.get('task_interval_minutes', 5))

            # 调度设置
            schedule_mode = config.get('schedule_mode', 'interval')
            mode_map = {'interval': '间隔执行', 'daily': '每日定时', 'custom': '自定义时间'}
            self.schedule_mode.setCurrentText(mode_map.get(schedule_mode, '间隔执行'))

            # 间隔执行设置
            self.interval_hours.setValue(config.get('interval_hours', 2))

            # 每日定时设置
            daily_time = config.get('daily_time', '09:00')
            hour, minute = map(int, daily_time.split(':'))
            self.daily_time.setTime(QTime(hour, minute))

            # 星期选择
            weekdays = config.get('weekdays', [1, 2, 3, 4, 5])  # 默认工作日
            self.weekday_selector.set_selected_days(weekdays)

            # 日期范围
            start_date_str = config.get('start_date')
            end_date_str = config.get('end_date')
            if start_date_str and end_date_str:
                from PyQt5.QtCore import QDate
                start_date = QDate.fromString(start_date_str, "yyyy-MM-dd")
                end_date = QDate.fromString(end_date_str, "yyyy-MM-dd")
                self.date_range_selector.set_date_range(start_date, end_date)
            else:
                self.date_range_selector.set_date_range(None, None)

            # 自定义时间设置
            custom_times_str = config.get('custom_times', [])
            if custom_times_str:
                # 转换字符串时间为datetime对象
                custom_times = []
                for time_str in custom_times_str:
                    try:
                        if len(time_str) == 5:  # HH:MM格式
                            # 转换为今天的datetime
                            hour, minute = map(int, time_str.split(':'))
                            dt = datetime.now().replace(hour=hour, minute=minute, second=0, microsecond=0)
                            if dt <= datetime.now():
                                dt += timedelta(days=1)  # 如果时间已过，设为明天
                            custom_times.append(dt)
                        else:
                            # 完整的datetime字符串
                            dt = datetime.fromisoformat(time_str)
                            custom_times.append(dt)
                    except:
                        continue

                if custom_times:
                    self.custom_time_selector.set_times(custom_times)

            # 时区设置
            timezone = config.get('timezone', '+08:00')
            self.timezone_selector.set_timezone(timezone)

            # 高级设置
            self.auto_retry.setChecked(config.get('auto_retry', True))
            self.max_retries.setValue(config.get('max_retries', 3))
            self.retry_delay.setValue(config.get('retry_delay_minutes', 10))

            # 更新显示
            self.on_schedule_mode_changed(self.schedule_mode.currentText())

            info("自动化配置加载完成")

        except Exception as e:
            error(f"加载配置失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        try:
            if not self.automation_scheduler:
                QMessageBox.warning(self, "警告", "自动化调度器不可用")
                return

            # 先验证配置
            self.validate_configuration()

            # 收集基本配置
            config = {
                'enabled': self.enable_automation.isChecked(),
                'loop_mode': 'continuous' if self.loop_mode.currentText() == '连续执行' else 'count',
                'loop_count': self.loop_count.value(),
                'task_interval_minutes': self.task_interval.value(),
                'auto_retry': self.auto_retry.isChecked(),
                'max_retries': self.max_retries.value(),
                'retry_delay_minutes': self.retry_delay.value()
            }

            # 调度模式设置
            mode_map = {'间隔执行': 'interval', '每日定时': 'daily', '自定义时间': 'custom'}
            config['schedule_mode'] = mode_map.get(self.schedule_mode.currentText(), 'interval')

            # 时区设置
            config['timezone'] = self.timezone_selector.get_timezone()

            # 间隔执行设置
            config['interval_hours'] = self.interval_hours.value()

            # 每日定时设置
            config['daily_time'] = self.daily_time.time().toString('HH:mm')
            config['weekdays'] = self.weekday_selector.get_selected_days()

            # 日期范围设置
            start_date, end_date = self.date_range_selector.get_date_range()
            if start_date and end_date:
                config['start_date'] = start_date.toString("yyyy-MM-dd")
                config['end_date'] = end_date.toString("yyyy-MM-dd")
            else:
                config['start_date'] = None
                config['end_date'] = None

            # 自定义时间设置
            custom_times = self.custom_time_selector.get_times()
            config['custom_times'] = [dt.isoformat() for dt in custom_times]

            # 更新调度器配置
            self.automation_scheduler.update_config(config)

            QMessageBox.information(self, "成功", "配置保存成功")
            info("自动化配置保存成功")

            # 更新时间预览
            self.update_time_preview()

        except Exception as e:
            error(f"保存配置失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"保存配置失败: {str(e)}")

    def start_scheduler(self):
        """启动调度器"""
        try:
            if not self.automation_scheduler:
                QMessageBox.warning(self, "警告", "自动化调度器不可用")
                return

            # 先保存配置
            self.save_config()

            # 启动调度器
            if self.automation_scheduler.start_scheduler():
                QMessageBox.information(self, "成功", "自动化调度器启动成功")
                self.add_log("✅ 自动化调度器启动成功")
            else:
                QMessageBox.warning(self, "失败", "自动化调度器启动失败")
                self.add_log("❌ 自动化调度器启动失败")

        except Exception as e:
            error(f"启动调度器失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"启动调度器失败: {str(e)}")

    def stop_scheduler(self):
        """停止调度器"""
        try:
            if not self.automation_scheduler:
                QMessageBox.warning(self, "警告", "自动化调度器不可用")
                return

            self.automation_scheduler.stop_scheduler()
            QMessageBox.information(self, "成功", "自动化调度器已停止")
            self.add_log("⏹️ 自动化调度器已停止")

        except Exception as e:
            error(f"停止调度器失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"停止调度器失败: {str(e)}")

    def toggle_pause(self):
        """切换暂停/恢复状态"""
        try:
            if not self.automation_scheduler:
                QMessageBox.warning(self, "警告", "自动化调度器不可用")
                return

            if self.automation_scheduler.is_paused:
                if self.automation_scheduler.resume_scheduler():
                    self.pause_btn.setText("⏸️ 暂停调度器")
                    self.add_log("▶️ 自动化调度器已恢复")
            else:
                if self.automation_scheduler.pause_scheduler():
                    self.pause_btn.setText("▶️ 恢复调度器")
                    self.add_log("⏸️ 自动化调度器已暂停")

        except Exception as e:
            error(f"切换暂停状态失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"操作失败: {str(e)}")

    def execute_now(self):
        """立即执行工作流程"""
        try:
            if not self.automation_scheduler:
                QMessageBox.warning(self, "警告", "自动化调度器不可用")
                return

            if self.automation_scheduler.start_workflow():
                QMessageBox.information(self, "成功", "工作流程已启动")
                self.add_log("🚀 手动启动工作流程")
            else:
                QMessageBox.warning(self, "失败", "工作流程启动失败")
                self.add_log("❌ 工作流程启动失败")

        except Exception as e:
            error(f"立即执行失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"执行失败: {str(e)}")

    def clear_log(self):
        """清空日志"""
        self.log_display.clear()
        self.add_log("🗑️ 日志已清空")

    def add_log(self, message):
        """添加日志"""
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            log_entry = f"[{timestamp}] {message}"
            self.log_display.append(log_entry)

            # 自动滚动到底部
            cursor = self.log_display.textCursor()
            cursor.movePosition(cursor.End)
            self.log_display.setTextCursor(cursor)

        except Exception as e:
            error(f"添加日志失败: {str(e)}")

    def update_status_display(self):
        """更新状态显示"""
        try:
            if not self.automation_scheduler:
                return

            status_info = self.automation_scheduler.get_status_info()

            # 更新调度器状态
            if status_info.get('is_running', False):
                if status_info.get('is_paused', False):
                    self.scheduler_status.setText("已暂停")
                    self.scheduler_status.setStyleSheet("""
                        QLabel {
                            font-weight: normal;
                            padding: 4px 8px;
                            border-radius: 4px;
                            background-color: #f39c12;
                            color: white;
                        }
                    """)
                else:
                    self.scheduler_status.setText("运行中")
                    self.scheduler_status.setStyleSheet("""
                        QLabel {
                            font-weight: normal;
                            padding: 4px 8px;
                            border-radius: 4px;
                            background-color: #27ae60;
                            color: white;
                        }
                    """)
            else:
                self.scheduler_status.setText("已停止")
                self.scheduler_status.setStyleSheet("""
                    QLabel {
                        font-weight: normal;
                        padding: 4px 8px;
                        border-radius: 4px;
                        background-color: #e74c3c;
                        color: white;
                    }
                """)

            # 更新当前任务
            current_task = status_info.get('current_task', '无')
            self.current_task.setText(current_task if current_task else '无')

            # 更新统计信息
            stats = status_info.get('stats', {})
            self.total_workflows.setText(str(stats.get('total_workflows', 0)))
            self.successful_workflows.setText(str(stats.get('successful_workflows', 0)))
            self.failed_workflows.setText(str(stats.get('failed_workflows', 0)))

            # 计算成功率
            total = stats.get('total_workflows', 0)
            successful = stats.get('successful_workflows', 0)
            if total > 0:
                success_rate = (successful / total) * 100
                self.success_rate.setText(f"{success_rate:.1f}%")
            else:
                self.success_rate.setText("0%")

            # 更新时间信息
            next_run = stats.get('next_run_time', '未设置')
            self.next_run_time.setText(next_run if next_run else '未设置')

            last_run = stats.get('last_run_time', '从未执行')
            self.last_run_time.setText(last_run if last_run else '从未执行')

        except Exception as e:
            debug(f"更新状态显示失败: {str(e)}")

    def on_status_updated(self, message):
        """处理状态更新信号"""
        self.add_log(f"📊 {message}")

    def on_task_started(self, task_name):
        """处理任务开始信号"""
        self.add_log(f"▶️ 开始执行: {task_name}")

    def on_task_completed(self, task_name, success):
        """处理任务完成信号"""
        if success:
            self.add_log(f"✅ 任务完成: {task_name}")
        else:
            self.add_log(f"❌ 任务失败: {task_name}")

    def on_workflow_completed(self, success):
        """处理工作流程完成信号"""
        if success:
            self.add_log("🎉 工作流程执行成功")
        else:
            self.add_log("💥 工作流程执行失败")
