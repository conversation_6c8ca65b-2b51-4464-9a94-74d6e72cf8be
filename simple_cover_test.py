#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单测试封面水印是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PIL import Image

def simple_cover_test():
    """简单测试封面水印"""
    print("=" * 60)
    print("🧪 简单封面水印测试")
    print("=" * 60)
    
    try:
        # 1. 导入模块
        print("\n1. 导入模块...")
        from app.dialogs.video_processor_dialog import WatermarkConfig, VideoProcessorWorker
        print("   ✅ 导入成功")
        
        # 2. 创建配置
        print("\n2. 创建配置...")
        config = WatermarkConfig()
        config.enabled = True
        config.multi_line_enabled = False
        config.font_size = 60
        config.font_family = "Microsoft YaHei"
        config.text_color = "#FFFFFF"
        config.position_x = 85
        config.position_y = 85
        config.opacity = 90
        print(f"   ✅ 配置完成: {config.font_size}px")
        
        # 3. 创建worker
        print("\n3. 创建worker...")
        worker = VideoProcessorWorker(
            input_dir="test", output_dir="test", cover_dir="test",
            thread_count=1, worker_id=0, shared_data={},
            watermark_config=config
        )
        print("   ✅ Worker创建成功")
        
        # 4. 测试封面
        print("\n4. 测试封面...")
        test_img = Image.new('RGB', (1920, 1080), color='#2c3e50')
        test_text = "测试封面水印"
        
        result = worker.add_watermark_to_pil_image(test_img.copy(), test_text)
        if result:
            result.save("simple_cover_test.jpg", 'JPEG', quality=95)
            print("   ✅ 封面生成成功: simple_cover_test.jpg")
        else:
            print("   ❌ 封面生成失败")
        
        # 5. 测试多行
        print("\n5. 测试多行...")
        config.multi_line_enabled = True
        
        if len(config.multi_line_configs) >= 2:
            config.multi_line_configs[0].enabled = True
            config.multi_line_configs[0].char_count = 3
            config.multi_line_configs[0].font_size = 80
            
            config.multi_line_configs[1].enabled = True
            config.multi_line_configs[1].char_count = 2
            config.multi_line_configs[1].font_size = 70
        
        multiline_result = worker.add_watermark_to_pil_image(test_img.copy(), test_text)
        if multiline_result:
            multiline_result.save("simple_multiline_test.jpg", 'JPEG', quality=95)
            print("   ✅ 多行生成成功: simple_multiline_test.jpg")
        else:
            print("   ❌ 多行生成失败")
        
        print("\n✅ 测试完成!")
        print("生成文件:")
        print("- simple_cover_test.jpg: 单行封面")
        print("- simple_multiline_test.jpg: 多行封面")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    simple_cover_test()
