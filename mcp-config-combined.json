{"mcpServers": {"mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"], "timeout": 600, "env": {"MCP_DESKTOP_MODE": "true", "MCP_WEB_HOST": "127.0.0.1", "MCP_WEB_PORT": "8765", "MCP_DEBUG": "false", "MCP_LANGUAGE": "zh-CN"}, "autoApprove": ["interactive_feedback"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "timeout": 600, "env": {"NODE_ENV": "production"}}}}