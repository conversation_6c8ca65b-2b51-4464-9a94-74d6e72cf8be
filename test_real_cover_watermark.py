#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
直接在真实封面图片上测试多行水印
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PIL import Image

def test_real_cover_watermark():
    """在真实封面上测试多行水印"""
    print("=" * 80)
    print("🖼️ 真实封面多行水印测试")
    print("=" * 80)
    
    # 封面图片路径
    cover_path = r"D:\头条全自动\视频搬运\已处理封面\王冲美国超大规模军演祖国为什么很淡定 2.jpg"
    
    print(f"测试封面: {os.path.basename(cover_path)}")
    
    try:
        # 1. 检查封面文件是否存在
        print(f"\n1. 检查封面文件...")
        if not os.path.exists(cover_path):
            print(f"❌ 封面文件不存在: {cover_path}")
            return
        
        # 加载封面图片
        cover_img = Image.open(cover_path)
        print(f"✅ 封面加载成功: {cover_img.size}")
        
        # 2. 导入水印模块
        print(f"\n2. 导入水印模块...")
        from app.dialogs.video_processor_dialog import WatermarkConfig, VideoProcessorWorker
        print("   ✅ 模块导入成功")
        
        # 3. 创建多行水印配置
        print(f"\n3. 创建多行水印配置...")
        config = WatermarkConfig()
        config.enabled = True
        config.multi_line_enabled = True
        config.font_family = "Microsoft YaHei"
        config.text_color = "#FFFFFF"
        config.position_x = 85
        config.opacity = 90
        
        # 配置3行水印
        line_configs = [
            {"chars": 4, "size": 80, "pos_y": 75, "desc": "第1行"},
            {"chars": 3, "size": 70, "pos_y": 85, "desc": "第2行"},
            {"chars": 2, "size": 60, "pos_y": 95, "desc": "第3行"}
        ]
        
        for i, line_config in enumerate(line_configs):
            if i < len(config.multi_line_configs):
                config.multi_line_configs[i].enabled = True
                config.multi_line_configs[i].char_count = line_config["chars"]
                config.multi_line_configs[i].font_size = line_config["size"]
                config.multi_line_configs[i].position_y = line_config["pos_y"]
                config.multi_line_configs[i].font_family = "Microsoft YaHei"
                config.multi_line_configs[i].text_color = "#FFFFFF"
                config.multi_line_configs[i].opacity = 90
                
                print(f"   {line_config['desc']}: {line_config['chars']}字符, {line_config['size']}px, Y={line_config['pos_y']}%")
        
        # 4. 创建worker
        print(f"\n4. 创建VideoProcessorWorker...")
        worker = VideoProcessorWorker(
            input_dir="test", output_dir="test", cover_dir="test",
            thread_count=1, worker_id=0, shared_data={},
            watermark_config=config
        )
        print("   ✅ Worker创建成功")
        
        # 5. 测试多种水印文字
        test_texts = [
            "王冲美国超大规模军演",
            "祖国为什么很淡定",
            "多行水印测试效果",
            "短文字",
            "这是一个很长的测试文字内容"
        ]
        
        print(f"\n5. 在真实封面上测试多行水印...")
        
        for i, test_text in enumerate(test_texts):
            print(f"\n   测试 {i+1}: '{test_text}'")
            
            # 预测文字分割
            print(f"   预期分割:")
            remaining_text = test_text
            for j, line_config in enumerate(line_configs):
                if remaining_text:
                    line_text = remaining_text[:line_config["chars"]]
                    remaining_text = remaining_text[line_config["chars"]:]
                    print(f"     第{j+1}行: '{line_text}' ({line_config['size']}px)")
            
            if remaining_text:
                print(f"     剩余: '{remaining_text}' (未显示)")
            
            # 在封面上添加多行水印
            print(f"   添加多行水印...")
            watermarked = worker.add_watermark_to_pil_image(cover_img.copy(), test_text)
            
            if watermarked:
                # 保存结果
                output_path = f"real_cover_multiline_{i+1}.jpg"
                watermarked.save(output_path, 'JPEG', quality=95)
                print(f"   ✅ 多行水印封面: {output_path}")
                
                # 生成预览版本
                preview_img = watermarked.resize((640, 360), Image.LANCZOS)
                preview_path = f"real_cover_preview_{i+1}.jpg"
                preview_img.save(preview_path, 'JPEG', quality=95)
                print(f"   ✅ 预览版本: {preview_path}")
                
            else:
                print(f"   ❌ 多行水印添加失败")
        
        # 6. 对比测试：单行 vs 多行
        print(f"\n6. 对比测试：单行 vs 多行...")
        
        test_text = "对比测试文字效果"
        
        # 多行版本
        config.multi_line_enabled = True
        multiline_result = worker.add_watermark_to_pil_image(cover_img.copy(), test_text)
        if multiline_result:
            multiline_result.save("real_cover_comparison_multiline.jpg", 'JPEG', quality=95)
            print(f"   ✅ 多行版本: real_cover_comparison_multiline.jpg")
        
        # 单行版本
        config.multi_line_enabled = False
        single_result = worker.add_watermark_to_pil_image(cover_img.copy(), test_text)
        if single_result:
            single_result.save("real_cover_comparison_single.jpg", 'JPEG', quality=95)
            print(f"   ✅ 单行版本: real_cover_comparison_single.jpg")
        
        # 7. 测试不同字体大小配置
        print(f"\n7. 测试不同字体大小配置...")
        
        config.multi_line_enabled = True
        test_text = "字体大小测试"
        
        # 配置1：大字体
        size_configs = [
            {"name": "大字体", "sizes": [100, 90, 80]},
            {"name": "中字体", "sizes": [80, 70, 60]},
            {"name": "小字体", "sizes": [60, 50, 40]}
        ]
        
        for j, size_config in enumerate(size_configs):
            print(f"   配置 {j+1}: {size_config['name']}")
            
            # 更新字体大小
            for k, size in enumerate(size_config["sizes"]):
                if k < len(config.multi_line_configs):
                    config.multi_line_configs[k].font_size = size
                    print(f"     第{k+1}行: {size}px")
            
            # 生成水印
            watermarked = worker.add_watermark_to_pil_image(cover_img.copy(), test_text)
            if watermarked:
                output_path = f"real_cover_size_test_{j+1}_{size_config['name']}.jpg"
                watermarked.save(output_path, 'JPEG', quality=95)
                print(f"   ✅ 生成成功: {output_path}")
        
        # 8. 测试不同位置配置
        print(f"\n8. 测试不同位置配置...")
        
        test_text = "位置测试"
        
        position_configs = [
            {"name": "右下角", "positions": [75, 85, 95]},
            {"name": "右中", "positions": [45, 55, 65]},
            {"name": "右上", "positions": [15, 25, 35]}
        ]
        
        for j, pos_config in enumerate(position_configs):
            print(f"   位置 {j+1}: {pos_config['name']}")
            
            # 更新位置
            for k, pos_y in enumerate(pos_config["positions"]):
                if k < len(config.multi_line_configs):
                    config.multi_line_configs[k].position_y = pos_y
                    print(f"     第{k+1}行: Y={pos_y}%")
            
            # 生成水印
            watermarked = worker.add_watermark_to_pil_image(cover_img.copy(), test_text)
            if watermarked:
                output_path = f"real_cover_position_test_{j+1}_{pos_config['name']}.jpg"
                watermarked.save(output_path, 'JPEG', quality=95)
                print(f"   ✅ 生成成功: {output_path}")
        
        # 9. 总结测试结果
        print(f"\n9. 测试总结...")
        print(f"   ✅ 真实封面多行水印测试完成")
        print(f"   ✅ 原始封面尺寸: {cover_img.size}")
        print(f"   ✅ 生成了多种配置的测试图像")
        
        print(f"\n📊 生成的文件:")
        print(f"   - real_cover_multiline_*.jpg: 不同文字的多行水印测试")
        print(f"   - real_cover_preview_*.jpg: 对应的预览版本")
        print(f"   - real_cover_comparison_*.jpg: 单行vs多行对比")
        print(f"   - real_cover_size_test_*.jpg: 不同字体大小测试")
        print(f"   - real_cover_position_test_*.jpg: 不同位置测试")
        
        print(f"\n🔍 验证要点:")
        print(f"   1. 检查多行水印是否正确显示在封面上")
        print(f"   2. 检查文字分割是否符合预期")
        print(f"   3. 检查字体大小和位置是否正确")
        print(f"   4. 检查预览版本与原版的比例关系")
        print(f"   5. 对比单行和多行的视觉效果")
        
        print(f"\n✅ 真实封面多行水印测试全部完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    test_real_cover_watermark()
