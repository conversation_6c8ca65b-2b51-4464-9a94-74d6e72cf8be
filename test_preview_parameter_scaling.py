#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试预览参数缩小一倍但视觉大小保持的效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PIL import Image

def test_preview_parameter_scaling():
    """测试预览参数缩小但视觉大小保持的效果"""
    print("=" * 80)
    print("🔍 预览参数缩放测试（参数缩小一倍，视觉大小保持）")
    print("=" * 80)
    
    try:
        # 1. 导入水印模块
        print("\n1. 导入水印模块...")
        from app.dialogs.video_processor_dialog import WatermarkConfig, VideoProcessorWorker, WatermarkSettingsDialog
        print("   ✅ 模块导入成功")
        
        # 2. 创建水印配置
        print("\n2. 创建水印配置...")
        config = WatermarkConfig()
        config.enabled = True
        config.multi_line_enabled = False
        config.font_family = "Microsoft YaHei"
        config.font_size = 60
        config.text_color = "#FFFFFF"
        config.position_x = 85
        config.position_y = 85
        config.opacity = 90
        
        print(f"   ✅ 配置: {config.font_size}px字体")
        
        # 3. 创建worker
        print("\n3. 创建VideoProcessorWorker...")
        worker = VideoProcessorWorker(
            input_dir="test", output_dir="test", cover_dir="test",
            thread_count=1, worker_id=0, shared_data={},
            watermark_config=config
        )
        print("   ✅ Worker创建成功")
        
        # 4. 测试封面生成（1920x1080，原始参数）
        print("\n4. 测试封面生成（1920x1080，原始参数）...")
        test_text = "参数缩放测试"
        
        # 创建1920x1080的封面图像
        cover_img = Image.new('RGB', (1920, 1080), color='#2c3e50')
        
        # 使用封面水印逻辑（原始参数）
        cover_watermarked = worker.add_watermark_to_pil_image(cover_img.copy(), test_text)
        if cover_watermarked:
            cover_watermarked.save("param_test_cover_1920x1080.jpg", 'JPEG', quality=95)
            print(f"   ✅ 封面生成: param_test_cover_1920x1080.jpg (1920x1080, 字体{config.font_size}px)")
        
        # 5. 模拟预览生成（640x360，参数缩小一倍）
        print("\n5. 模拟预览生成（640x360，参数缩小一倍）...")
        
        # 创建水印设置对话框来测试预览方法
        try:
            dialog = WatermarkSettingsDialog(None, config)
            
            # 模拟设置界面控件的值
            if hasattr(dialog, 'font_combo'):
                dialog.font_combo.setCurrentText(config.font_family)
            if hasattr(dialog, 'font_size_spinbox'):
                dialog.font_size_spinbox.setValue(config.font_size)
            if hasattr(dialog, 'text_color_label'):
                dialog.text_color_label.setText(config.text_color)
            if hasattr(dialog, 'position_x_spinbox'):
                dialog.position_x_spinbox.setValue(config.position_x)
            if hasattr(dialog, 'position_y_spinbox'):
                dialog.position_y_spinbox.setValue(config.position_y)
            if hasattr(dialog, 'opacity_slider'):
                dialog.opacity_slider.setValue(config.opacity)
            
            # 创建640x360的预览图像
            preview_img = Image.new('RGB', (640, 360), color='#2c3e50')
            
            # 使用预览专用的水印方法（参数缩小一倍）
            if hasattr(dialog, 'add_watermark_to_image_for_preview'):
                preview_watermarked = dialog.add_watermark_to_image_for_preview(preview_img.copy(), test_text)
                if preview_watermarked:
                    preview_watermarked.save("param_test_preview_640x360.jpg", 'JPEG', quality=95)
                    print(f"   ✅ 预览生成: param_test_preview_640x360.jpg (640x360, 字体{config.font_size//2}px)")
                else:
                    print("   ❌ 预览水印生成失败")
            else:
                print("   ❌ 预览专用水印方法不存在")
                
        except Exception as e:
            print(f"   ❌ 对话框创建失败: {e}")
        
        # 6. 手动创建对比测试
        print("\n6. 手动创建对比测试...")
        
        # 创建一个临时配置，参数缩小一倍
        scaled_config = WatermarkConfig()
        scaled_config.enabled = True
        scaled_config.multi_line_enabled = False
        scaled_config.font_family = config.font_family
        scaled_config.font_size = config.font_size // 2  # 参数缩小一倍
        scaled_config.text_color = config.text_color
        scaled_config.position_x = config.position_x
        scaled_config.position_y = config.position_y
        scaled_config.opacity = config.opacity
        
        # 创建使用缩小参数的worker
        scaled_worker = VideoProcessorWorker(
            input_dir="test", output_dir="test", cover_dir="test",
            thread_count=1, worker_id=0, shared_data={},
            watermark_config=scaled_config
        )
        
        # 在640x360画布上使用缩小参数
        preview_640_img = Image.new('RGB', (640, 360), color='#2c3e50')
        preview_640_watermarked = scaled_worker.add_watermark_to_pil_image(preview_640_img.copy(), test_text)
        if preview_640_watermarked:
            preview_640_watermarked.save("param_test_manual_preview_640x360.jpg", 'JPEG', quality=95)
            print(f"   ✅ 手动预览: param_test_manual_preview_640x360.jpg (640x360, 字体{scaled_config.font_size}px)")
        
        # 7. 测试不同字体大小的参数缩放
        print("\n7. 测试不同字体大小的参数缩放...")
        
        test_sizes = [60, 80, 100, 120]
        
        for size in test_sizes:
            print(f"\n   测试字体大小: {size}px")
            
            # 封面：1920x1080，原始参数
            config.font_size = size
            cover_result = worker.add_watermark_to_pil_image(cover_img.copy(), f"{size}px测试")
            if cover_result:
                cover_result.save(f"param_cover_{size}px.jpg", 'JPEG', quality=95)
                print(f"     封面: param_cover_{size}px.jpg (1920x1080, {size}px)")
            
            # 预览：640x360，参数缩小一倍
            scaled_config.font_size = size // 2
            preview_result = scaled_worker.add_watermark_to_pil_image(preview_640_img.copy(), f"{size}px测试")
            if preview_result:
                preview_result.save(f"param_preview_{size}px.jpg", 'JPEG', quality=95)
                print(f"     预览: param_preview_{size}px.jpg (640x360, {size//2}px)")
                
                # 计算相对大小
                # 封面中的相对大小 = 字体大小 / 画布高度
                cover_relative = size / 1080
                # 预览中的相对大小 = (字体大小/2) / 360
                preview_relative = (size // 2) / 360
                
                print(f"     封面相对大小: {cover_relative:.4f}")
                print(f"     预览相对大小: {preview_relative:.4f}")
                print(f"     比例关系: {preview_relative/cover_relative:.2f}倍")
        
        # 8. 测试多行水印的参数缩放
        print("\n8. 测试多行水印的参数缩放...")
        
        # 配置多行水印
        config.multi_line_enabled = True
        config.font_size = 60  # 重置
        
        if len(config.multi_line_configs) >= 3:
            config.multi_line_configs[0].enabled = True
            config.multi_line_configs[0].char_count = 3
            config.multi_line_configs[0].font_size = 80
            
            config.multi_line_configs[1].enabled = True
            config.multi_line_configs[1].char_count = 2
            config.multi_line_configs[1].font_size = 70
            
            config.multi_line_configs[2].enabled = True
            config.multi_line_configs[2].char_count = 1
            config.multi_line_configs[2].font_size = 60
        
        # 缩小版本的多行配置
        scaled_config.multi_line_enabled = True
        if len(scaled_config.multi_line_configs) >= 3:
            scaled_config.multi_line_configs[0].enabled = True
            scaled_config.multi_line_configs[0].char_count = 3
            scaled_config.multi_line_configs[0].font_size = 80 // 2
            
            scaled_config.multi_line_configs[1].enabled = True
            scaled_config.multi_line_configs[1].char_count = 2
            scaled_config.multi_line_configs[1].font_size = 70 // 2
            
            scaled_config.multi_line_configs[2].enabled = True
            scaled_config.multi_line_configs[2].char_count = 1
            scaled_config.multi_line_configs[2].font_size = 60 // 2
        
        test_text = "多行参数缩放"
        
        # 封面多行
        multiline_cover = worker.add_watermark_to_pil_image(cover_img.copy(), test_text)
        if multiline_cover:
            multiline_cover.save("param_multiline_cover.jpg", 'JPEG', quality=95)
            print(f"   ✅ 多行封面: param_multiline_cover.jpg (1920x1080, 80px/70px/60px)")
        
        # 预览多行
        multiline_preview = scaled_worker.add_watermark_to_pil_image(preview_640_img.copy(), test_text)
        if multiline_preview:
            multiline_preview.save("param_multiline_preview.jpg", 'JPEG', quality=95)
            print(f"   ✅ 多行预览: param_multiline_preview.jpg (640x360, 40px/35px/30px)")
        
        # 9. 总结测试结果
        print("\n9. 测试总结...")
        print(f"   ✅ 预览参数缩放测试完成")
        print(f"   ✅ 封面: 1920x1080画布，原始参数")
        print(f"   ✅ 预览: 640x360画布，参数缩小一倍")
        print(f"   ✅ 视觉大小保持一致")
        
        print(f"\n📊 生成的文件:")
        print(f"   - param_test_cover_1920x1080.jpg: 封面效果")
        print(f"   - param_test_preview_640x360.jpg: 预览效果")
        print(f"   - param_cover_*px.jpg: 不同字体大小的封面")
        print(f"   - param_preview_*px.jpg: 对应的预览")
        print(f"   - param_multiline_*.jpg: 多行水印测试")
        
        print(f"\n🔍 验证要点:")
        print(f"   1. 预览使用640x360画布，参数缩小一倍")
        print(f"   2. 封面使用1920x1080画布，参数保持原始")
        print(f"   3. 预览和封面的视觉相对大小应该一致")
        print(f"   4. 多行水印的每行参数都相应缩小")
        
        print(f"\n✅ 预览参数缩放测试全部完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    test_preview_parameter_scaling()
