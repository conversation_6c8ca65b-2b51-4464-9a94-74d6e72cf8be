#!/bin/bash

echo "启动 MCP Feedback Enhanced 桌面应用测试..."
echo

# 设置环境变量
export MCP_DESKTOP_MODE=true
export MCP_WEB_HOST=127.0.0.1
export MCP_WEB_PORT=8765
export MCP_DEBUG=false
export MCP_LANGUAGE=zh-CN

echo "配置信息:"
echo "- 模式: 桌面应用"
echo "- 主机: $MCP_WEB_HOST"
echo "- 端口: $MCP_WEB_PORT"
echo "- 语言: $MCP_LANGUAGE"
echo

echo "正在启动桌面应用..."
uvx mcp-feedback-enhanced@latest test --desktop

read -p "按任意键继续..."
