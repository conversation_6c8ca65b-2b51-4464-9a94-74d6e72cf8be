#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
服务器验证 + 动态密钥Cookie保护方案
"""

import os
import json
import base64
import hashlib
import hmac
import time
import secrets
import requests
from typing import Optional, Dict, Tuple

try:
    from cryptography.fernet import Fernet
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography.hazmat.primitives.asymmetric import rsa, padding
    from cryptography.hazmat.primitives import serialization
    CRYPTOGRAPHY_AVAILABLE = True
except ImportError:
    CRYPTOGRAPHY_AVAILABLE = False

class ServerValidatedEncryption:
    """服务器验证的Cookie加密"""
    
    def __init__(self, server_url: str = "https://your-server.com/api", 
                 client_id: str = None):
        self.server_url = server_url
        self.client_id = client_id or self._generate_client_id()
        self.available = CRYPTOGRAPHY_AVAILABLE
        self._server_public_key = None
        self._client_private_key = None
        self._session_token = None
        
    def _generate_client_id(self) -> str:
        """生成客户端ID"""
        import platform
        import uuid
        
        machine_info = f"{platform.node()}_{uuid.getnode()}"
        return hashlib.sha256(machine_info.encode()).hexdigest()[:16]
    
    def _get_server_public_key(self) -> bytes:
        """从服务器获取公钥"""
        try:
            response = requests.get(f"{self.server_url}/public-key", 
                                  params={"client_id": self.client_id},
                                  timeout=10)
            if response.status_code == 200:
                key_data = response.json()
                return base64.b64decode(key_data["public_key"])
            else:
                raise Exception(f"获取公钥失败: {response.status_code}")
        except Exception as e:
            # 离线模式：使用内置公钥
            return self._get_fallback_public_key()
    
    def _get_fallback_public_key(self) -> bytes:
        """获取备用公钥（离线模式）"""
        # 这里应该是预先嵌入的公钥
        fallback_key = """
        -----BEGIN PUBLIC KEY-----
        MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
        -----END PUBLIC KEY-----
        """
        return fallback_key.encode('utf-8')
    
    def _request_encryption_token(self, cookie_hash: str) -> str:
        """向服务器请求加密令牌"""
        try:
            payload = {
                "client_id": self.client_id,
                "cookie_hash": cookie_hash,
                "timestamp": int(time.time()),
                "action": "encrypt"
            }
            
            response = requests.post(f"{self.server_url}/encryption-token",
                                   json=payload, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                return result["encryption_token"]
            else:
                raise Exception(f"获取加密令牌失败: {response.status_code}")
                
        except Exception as e:
            # 离线模式：生成本地令牌
            return self._generate_offline_token(cookie_hash)
    
    def _generate_offline_token(self, cookie_hash: str) -> str:
        """生成离线令牌"""
        offline_secret = "offline_fallback_secret_2024"
        token_data = f"{self.client_id}_{cookie_hash}_{int(time.time())}"
        return hmac.new(offline_secret.encode(), token_data.encode(), hashlib.sha256).hexdigest()
    
    def _validate_decryption_token(self, token: str, cookie_hash: str) -> bool:
        """验证解密令牌"""
        try:
            payload = {
                "client_id": self.client_id,
                "token": token,
                "cookie_hash": cookie_hash,
                "action": "decrypt"
            }
            
            response = requests.post(f"{self.server_url}/validate-token",
                                   json=payload, timeout=10)
            
            return response.status_code == 200 and response.json().get("valid", False)
            
        except Exception as e:
            # 离线模式：本地验证
            return self._validate_offline_token(token, cookie_hash)
    
    def _validate_offline_token(self, token: str, cookie_hash: str) -> bool:
        """离线验证令牌"""
        try:
            offline_secret = "offline_fallback_secret_2024"
            # 简化验证：检查令牌格式
            return len(token) == 64 and all(c in '0123456789abcdef' for c in token)
        except:
            return False
    
    def encrypt_cookie_data(self, cookie_data: dict) -> dict:
        """服务器验证的Cookie加密"""
        if not self.available:
            raise RuntimeError("cryptography库未安装")
        
        try:
            # 计算Cookie哈希
            cookie_json = json.dumps(cookie_data, sort_keys=True, separators=(',', ':'))
            cookie_hash = hashlib.sha256(cookie_json.encode()).hexdigest()
            
            # 请求加密令牌
            encryption_token = self._request_encryption_token(cookie_hash)
            
            # 生成动态密钥
            dynamic_key = self._generate_dynamic_key(encryption_token, cookie_hash)
            
            # 准备加密数据
            timestamp = int(time.time())
            nonce = secrets.token_hex(16)
            
            enhanced_data = {
                "original_data": cookie_data,
                "timestamp": timestamp,
                "nonce": nonce,
                "client_id": self.client_id,
                "cookie_hash": cookie_hash,
                "version": "4.0"
            }
            
            # 加密数据
            json_data = json.dumps(enhanced_data, ensure_ascii=False, separators=(',', ':')).encode('utf-8')
            
            fernet = Fernet(dynamic_key)
            encrypted_data = fernet.encrypt(json_data)
            
            # 生成服务器签名
            server_signature = self._generate_server_signature(encrypted_data, encryption_token)
            
            return {
                "encrypted": True,
                "version": "4.0",
                "algorithm": "AES-256-ServerValidated",
                "data": base64.b64encode(encrypted_data).decode('utf-8'),
                "encryption_token": encryption_token,
                "server_signature": server_signature,
                "client_id": self.client_id,
                "created_time": timestamp,
                "server_validated": True,
                "account_id": cookie_data.get("accountId", ""),
                "remark": cookie_data.get("remark", "")
            }
            
        except Exception as e:
            raise Exception(f"服务器验证加密失败: {str(e)}")
    
    def decrypt_cookie_data(self, encrypted_file_data: dict) -> dict:
        """服务器验证的Cookie解密"""
        if not self.available:
            raise RuntimeError("cryptography库未安装")
        
        try:
            # 验证是否为服务器验证文件
            if not encrypted_file_data.get("server_validated", False):
                raise Exception("此文件不是服务器验证加密文件")
            
            # 提取数据
            encrypted_data = base64.b64decode(encrypted_file_data["data"])
            encryption_token = encrypted_file_data["encryption_token"]
            server_signature = encrypted_file_data["server_signature"]
            
            # 验证服务器签名
            if not self._verify_server_signature(encrypted_data, encryption_token, server_signature):
                raise Exception("服务器签名验证失败")
            
            # 计算预期的Cookie哈希（用于验证）
            # 注意：这里我们还不知道原始数据，所以先解密再验证
            
            # 生成动态密钥
            # 我们需要从加密数据中提取cookie_hash，这里简化处理
            temp_key = self._generate_dynamic_key(encryption_token, "temp")
            
            try:
                fernet = Fernet(temp_key)
                decrypted_json = fernet.decrypt(encrypted_data)
                enhanced_data = json.loads(decrypted_json.decode('utf-8'))
                
                # 获取真实的cookie_hash
                cookie_hash = enhanced_data.get("cookie_hash", "")
                
                # 验证解密令牌
                if not self._validate_decryption_token(encryption_token, cookie_hash):
                    raise Exception("解密令牌验证失败")
                
                # 重新生成正确的密钥
                correct_key = self._generate_dynamic_key(encryption_token, cookie_hash)
                
                if temp_key != correct_key:
                    # 使用正确密钥重新解密
                    fernet = Fernet(correct_key)
                    decrypted_json = fernet.decrypt(encrypted_data)
                    enhanced_data = json.loads(decrypted_json.decode('utf-8'))
                
                # 验证时间戳
                timestamp = enhanced_data.get("timestamp", 0)
                current_time = int(time.time())
                max_age = 30 * 24 * 3600  # 30天
                
                if current_time - timestamp > max_age:
                    raise Exception("Cookie文件已过期")
                
                # 验证客户端ID
                if enhanced_data.get("client_id") != self.client_id:
                    raise Exception("客户端ID不匹配")
                
                return enhanced_data.get("original_data", {})
                
            except Exception as decrypt_error:
                raise Exception(f"解密过程失败: {str(decrypt_error)}")
            
        except Exception as e:
            raise Exception(f"服务器验证解密失败: {str(e)}")
    
    def _generate_dynamic_key(self, token: str, cookie_hash: str) -> bytes:
        """生成动态密钥"""
        key_material = f"{token}_{cookie_hash}_{self.client_id}"
        key_hash = hashlib.sha256(key_material.encode()).digest()
        return base64.urlsafe_b64encode(key_hash)
    
    def _generate_server_signature(self, data: bytes, token: str) -> str:
        """生成服务器签名"""
        signature_data = data + token.encode()
        signature = hmac.new(b"server_secret_key", signature_data, hashlib.sha256).digest()
        return base64.b64encode(signature).decode('utf-8')
    
    def _verify_server_signature(self, data: bytes, token: str, signature: str) -> bool:
        """验证服务器签名"""
        try:
            expected_signature = self._generate_server_signature(data, token)
            return hmac.compare_digest(expected_signature, signature)
        except:
            return False

# 使用示例
def test_server_validated_encryption():
    """测试服务器验证加密"""
    # 注意：这个测试需要实际的服务器支持
    encryptor = ServerValidatedEncryption(
        server_url="https://your-server.com/api",  # 替换为实际服务器
        client_id="test_client_123"
    )
    
    test_data = {
        "accountId": "test123",
        "remark": "测试账号",
        "cookies": {
            "sessionid": "test_session",
            "csrf_token": "test_csrf"
        }
    }
    
    try:
        # 加密
        encrypted = encryptor.encrypt_cookie_data(test_data)
        print("✅ 服务器验证加密成功")
        
        # 解密
        decrypted = encryptor.decrypt_cookie_data(encrypted)
        print("✅ 服务器验证解密成功")
        print(f"数据匹配: {decrypted == test_data}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    test_server_validated_encryption()
