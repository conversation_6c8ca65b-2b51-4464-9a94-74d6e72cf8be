#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Cookie验证服务器
提供动态密钥生成和验证服务
"""

from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import hashlib
import hmac
import time
import secrets
import jwt
import redis
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-super-secret-key-change-this'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///cookie_validation.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 初始化扩展
db = SQLAlchemy(app)
redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)

# 限流器
limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

# 数据库模型
class Client(db.Model):
    """客户端信息表"""
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.String(32), unique=True, nullable=False)
    client_secret = db.Column(db.String(64), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_seen = db.Column(db.DateTime, default=datetime.utcnow)
    machine_fingerprint = db.Column(db.String(64))
    license_key = db.Column(db.String(64))  # 授权码
    expires_at = db.Column(db.DateTime)  # 授权过期时间

class EncryptionToken(db.Model):
    """加密令牌表"""
    id = db.Column(db.Integer, primary_key=True)
    token = db.Column(db.String(128), unique=True, nullable=False)
    client_id = db.Column(db.String(32), nullable=False)
    cookie_hash = db.Column(db.String(64), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime, nullable=False)
    used_count = db.Column(db.Integer, default=0)
    max_uses = db.Column(db.Integer, default=100)  # 最大使用次数

class AuditLog(db.Model):
    """审计日志表"""
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.String(32), nullable=False)
    action = db.Column(db.String(32), nullable=False)  # encrypt/decrypt/validate
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(256))
    success = db.Column(db.Boolean, nullable=False)
    error_message = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# 服务器配置
class ServerConfig:
    """服务器配置"""
    TOKEN_EXPIRE_HOURS = 24  # 令牌过期时间（小时）
    MAX_TOKEN_USES = 100     # 令牌最大使用次数
    RATE_LIMIT_PER_HOUR = 50 # 每小时请求限制
    JWT_EXPIRE_MINUTES = 30  # JWT过期时间（分钟）
    
    # 安全密钥（生产环境中应该从环境变量读取）
    MASTER_SECRET = "your-master-secret-key-change-this"
    JWT_SECRET = "your-jwt-secret-key-change-this"

def verify_client_auth(client_id: str, client_secret: str) -> Optional[Client]:
    """验证客户端身份"""
    client = Client.query.filter_by(
        client_id=client_id,
        client_secret=client_secret,
        is_active=True
    ).first()
    
    if client:
        # 检查授权是否过期
        if client.expires_at and client.expires_at < datetime.utcnow():
            return None
        
        # 更新最后访问时间
        client.last_seen = datetime.utcnow()
        db.session.commit()
    
    return client

def generate_encryption_token(client_id: str, cookie_hash: str) -> str:
    """生成加密令牌"""
    # 生成随机令牌
    token_data = f"{client_id}_{cookie_hash}_{int(time.time())}_{secrets.token_hex(16)}"
    token = hashlib.sha256(token_data.encode()).hexdigest()
    
    # 存储到数据库
    expires_at = datetime.utcnow() + timedelta(hours=ServerConfig.TOKEN_EXPIRE_HOURS)
    
    encryption_token = EncryptionToken(
        token=token,
        client_id=client_id,
        cookie_hash=cookie_hash,
        expires_at=expires_at,
        max_uses=ServerConfig.MAX_TOKEN_USES
    )
    
    db.session.add(encryption_token)
    db.session.commit()
    
    # 缓存到Redis（可选，用于快速查询）
    redis_key = f"token:{token}"
    redis_client.setex(
        redis_key,
        ServerConfig.TOKEN_EXPIRE_HOURS * 3600,
        f"{client_id}:{cookie_hash}"
    )
    
    return token

def validate_encryption_token(token: str, client_id: str, cookie_hash: str) -> bool:
    """验证加密令牌"""
    # 先从Redis查询（快速路径）
    redis_key = f"token:{token}"
    cached_data = redis_client.get(redis_key)
    
    if cached_data:
        cached_client_id, cached_cookie_hash = cached_data.split(':', 1)
        if cached_client_id == client_id and cached_cookie_hash == cookie_hash:
            return True
    
    # 从数据库查询
    encryption_token = EncryptionToken.query.filter_by(
        token=token,
        client_id=client_id,
        cookie_hash=cookie_hash
    ).first()
    
    if not encryption_token:
        return False
    
    # 检查是否过期
    if encryption_token.expires_at < datetime.utcnow():
        return False
    
    # 检查使用次数
    if encryption_token.used_count >= encryption_token.max_uses:
        return False
    
    # 更新使用次数
    encryption_token.used_count += 1
    db.session.commit()
    
    return True

def log_audit(client_id: str, action: str, success: bool, error_message: str = None):
    """记录审计日志"""
    audit_log = AuditLog(
        client_id=client_id,
        action=action,
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent', ''),
        success=success,
        error_message=error_message
    )
    
    db.session.add(audit_log)
    db.session.commit()

# API 路由
@app.route('/api/register-client', methods=['POST'])
@limiter.limit("5 per hour")
def register_client():
    """注册新客户端"""
    try:
        data = request.get_json()
        license_key = data.get('license_key')
        machine_fingerprint = data.get('machine_fingerprint')
        
        # 验证授权码（这里简化处理）
        if not license_key or len(license_key) < 10:
            return jsonify({'error': '无效的授权码'}), 400
        
        # 生成客户端ID和密钥
        client_id = hashlib.sha256(f"{license_key}_{machine_fingerprint}".encode()).hexdigest()[:16]
        client_secret = secrets.token_hex(32)
        
        # 检查是否已存在
        existing_client = Client.query.filter_by(client_id=client_id).first()
        if existing_client:
            return jsonify({'error': '客户端已存在'}), 409
        
        # 创建新客户端
        expires_at = datetime.utcnow() + timedelta(days=365)  # 1年有效期
        
        new_client = Client(
            client_id=client_id,
            client_secret=client_secret,
            machine_fingerprint=machine_fingerprint,
            license_key=license_key,
            expires_at=expires_at
        )
        
        db.session.add(new_client)
        db.session.commit()
        
        return jsonify({
            'client_id': client_id,
            'client_secret': client_secret,
            'expires_at': expires_at.isoformat()
        })
        
    except Exception as e:
        logger.error(f"注册客户端失败: {str(e)}")
        return jsonify({'error': '注册失败'}), 500

@app.route('/api/encryption-token', methods=['POST'])
@limiter.limit("50 per hour")
def request_encryption_token():
    """请求加密令牌"""
    try:
        data = request.get_json()
        client_id = data.get('client_id')
        client_secret = data.get('client_secret')
        cookie_hash = data.get('cookie_hash')
        
        # 验证客户端
        client = verify_client_auth(client_id, client_secret)
        if not client:
            log_audit(client_id, 'encrypt', False, '客户端验证失败')
            return jsonify({'error': '客户端验证失败'}), 401
        
        # 生成加密令牌
        token = generate_encryption_token(client_id, cookie_hash)
        
        log_audit(client_id, 'encrypt', True)
        
        return jsonify({
            'encryption_token': token,
            'expires_in': ServerConfig.TOKEN_EXPIRE_HOURS * 3600
        })
        
    except Exception as e:
        logger.error(f"生成加密令牌失败: {str(e)}")
        log_audit(client_id, 'encrypt', False, str(e))
        return jsonify({'error': '生成令牌失败'}), 500

@app.route('/api/validate-token', methods=['POST'])
@limiter.limit("100 per hour")
def validate_token():
    """验证解密令牌"""
    try:
        data = request.get_json()
        client_id = data.get('client_id')
        client_secret = data.get('client_secret')
        token = data.get('token')
        cookie_hash = data.get('cookie_hash')
        
        # 验证客户端
        client = verify_client_auth(client_id, client_secret)
        if not client:
            log_audit(client_id, 'decrypt', False, '客户端验证失败')
            return jsonify({'valid': False, 'error': '客户端验证失败'}), 401
        
        # 验证令牌
        is_valid = validate_encryption_token(token, client_id, cookie_hash)
        
        log_audit(client_id, 'decrypt', is_valid, None if is_valid else '令牌验证失败')
        
        return jsonify({'valid': is_valid})
        
    except Exception as e:
        logger.error(f"验证令牌失败: {str(e)}")
        log_audit(client_id, 'decrypt', False, str(e))
        return jsonify({'valid': False, 'error': '验证失败'}), 500

@app.route('/api/revoke-token', methods=['POST'])
@limiter.limit("20 per hour")
def revoke_token():
    """撤销令牌"""
    try:
        data = request.get_json()
        client_id = data.get('client_id')
        client_secret = data.get('client_secret')
        token = data.get('token')
        
        # 验证客户端
        client = verify_client_auth(client_id, client_secret)
        if not client:
            return jsonify({'error': '客户端验证失败'}), 401
        
        # 撤销令牌
        encryption_token = EncryptionToken.query.filter_by(
            token=token,
            client_id=client_id
        ).first()
        
        if encryption_token:
            encryption_token.max_uses = 0  # 设置为0表示撤销
            db.session.commit()
            
            # 从Redis删除
            redis_key = f"token:{token}"
            redis_client.delete(redis_key)
        
        log_audit(client_id, 'revoke', True)
        
        return jsonify({'success': True})
        
    except Exception as e:
        logger.error(f"撤销令牌失败: {str(e)}")
        return jsonify({'error': '撤销失败'}), 500

@app.route('/api/client-status', methods=['GET'])
@limiter.limit("10 per hour")
def client_status():
    """获取客户端状态"""
    try:
        client_id = request.args.get('client_id')
        client_secret = request.args.get('client_secret')
        
        # 验证客户端
        client = verify_client_auth(client_id, client_secret)
        if not client:
            return jsonify({'error': '客户端验证失败'}), 401
        
        # 统计令牌使用情况
        active_tokens = EncryptionToken.query.filter_by(
            client_id=client_id
        ).filter(
            EncryptionToken.expires_at > datetime.utcnow(),
            EncryptionToken.used_count < EncryptionToken.max_uses
        ).count()
        
        return jsonify({
            'client_id': client.client_id,
            'is_active': client.is_active,
            'expires_at': client.expires_at.isoformat() if client.expires_at else None,
            'last_seen': client.last_seen.isoformat(),
            'active_tokens': active_tokens
        })
        
    except Exception as e:
        logger.error(f"获取客户端状态失败: {str(e)}")
        return jsonify({'error': '获取状态失败'}), 500

# 健康检查
@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0'
    })

# 初始化数据库
@app.before_first_request
def create_tables():
    """创建数据库表"""
    db.create_all()

if __name__ == '__main__':
    # 开发环境运行
    app.run(host='0.0.0.0', port=5000, debug=True)
