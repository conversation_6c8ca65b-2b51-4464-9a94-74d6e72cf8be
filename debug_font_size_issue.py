#!/usr/bin/env python3
"""
诊断字体大小问题的脚本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QDialog

# 添加项目路径
sys.path.append('.')

def debug_font_size_issue():
    """诊断字体大小问题"""
    try:
        from app.dialogs.video_processor_dialog import WatermarkConfigDialog, WatermarkConfig
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("字体大小问题诊断")
        main_window.setGeometry(100, 100, 1000, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("""
🔍 字体大小问题诊断

问题描述：
生成的封面水印越来越小

可能原因：
1. 预览与封面的字体大小计算不一致
2. 字体大小保存/加载有问题
3. 缩放比例计算错误
4. 重复缩放导致累积误差

诊断步骤：
1. 检查当前字体大小设置
2. 检查预览字体缩放计算
3. 检查封面字体大小使用
4. 对比预览与封面的字体效果
5. 测试字体大小调整的影响

请点击按钮开始诊断...
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #ffe4e1;
                padding: 20px;
                border-radius: 8px;
                font-size: 14px;
                line-height: 1.6;
                border: 2px solid #ff6347;
                color: #8b0000;
            }
        """)
        layout.addWidget(info_label)
        
        # 添加诊断按钮
        debug_button = QPushButton("🔍 开始字体大小问题诊断")
        debug_button.setStyleSheet("""
            QPushButton {
                background-color: #ff6347;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #ff4500;
            }
        """)
        
        def start_diagnosis():
            print("=" * 80)
            print("开始字体大小问题诊断")
            print("=" * 80)
            
            try:
                # 1. 检查默认配置
                print("1. 检查默认配置...")
                config = WatermarkConfig()
                print(f"   默认字体大小: {config.font_size}px")
                print(f"   默认字体: {config.font_family}")
                
                # 2. 创建对话框
                print("\n2. 创建水印配置对话框...")
                dialog = WatermarkConfigDialog(config, main_window)
                print("   ✅ 对话框创建成功")
                
                # 3. 检查界面初始值
                print("\n3. 检查界面初始值...")
                if hasattr(dialog, 'font_size_spinbox'):
                    ui_font_size = dialog.font_size_spinbox.value()
                    print(f"   界面字体大小: {ui_font_size}px")
                    if ui_font_size == config.font_size:
                        print("   ✅ 界面与配置一致")
                    else:
                        print(f"   ⚠️ 界面与配置不一致: {ui_font_size} vs {config.font_size}")
                
                # 4. 计算预览缩放
                print("\n4. 计算预览缩放...")
                preview_scale = 640 / 1920
                preview_font_size = int(config.font_size * preview_scale)
                print(f"   封面尺寸: 1920x1080")
                print(f"   预览尺寸: 640x360")
                print(f"   缩放比例: {preview_scale:.3f}")
                print(f"   封面字体: {config.font_size}px")
                print(f"   预览字体: {preview_font_size}px")
                
                # 5. 测试字体大小范围
                print("\n5. 测试字体大小范围...")
                test_sizes = [24, 36, 48, 60, 72]
                print("   字体大小对照表:")
                for size in test_sizes:
                    preview_size = int(size * preview_scale)
                    print(f"   设置{size}px -> 预览{preview_size}px -> 封面{size}px")
                
                # 6. 打开对话框进行交互测试
                print("\n6. 打开对话框进行交互测试...")
                print("   请在对话框中:")
                print("   - 记录当前字体大小设置")
                print("   - 观察预览中的字体效果")
                print("   - 调整字体大小，观察变化")
                print("   - 注意控制台的字体缩放信息")
                print("   - 确认预览效果是否符合期望")
                
                result = dialog.exec_()
                
                if result == QDialog.Accepted:
                    print("\n7. 最终状态检查...")
                    final_font_size = config.font_size
                    final_ui_size = dialog.font_size_spinbox.value()
                    final_preview_size = int(final_font_size * preview_scale)
                    
                    print(f"   最终配置字体大小: {final_font_size}px")
                    print(f"   最终界面字体大小: {final_ui_size}px")
                    print(f"   最终预览字体大小: {final_preview_size}px")
                    
                    if final_font_size == final_ui_size:
                        print("   ✅ 配置与界面一致")
                    else:
                        print(f"   ❌ 配置与界面不一致: {final_font_size} vs {final_ui_size}")
                    
                    # 8. 分析问题
                    print("\n8. 问题分析...")
                    if final_font_size < 24:
                        print("   ⚠️ 字体大小过小，可能导致封面水印不清晰")
                        print("   建议: 增加字体大小到36px以上")
                    elif final_font_size > 72:
                        print("   ⚠️ 字体大小过大，可能导致封面水印过于突出")
                        print("   建议: 减少字体大小到72px以下")
                    else:
                        print("   ✅ 字体大小在合理范围内")
                    
                    print(f"\n   预期效果:")
                    print(f"   - 预览中显示: {final_preview_size}px 效果")
                    print(f"   - 封面中显示: {final_font_size}px 实际大小")
                    print(f"   - 相对比例: 预览与封面应该看起来比例一致")
                    
                    # 9. 给出建议
                    print(f"\n9. 建议:")
                    if final_preview_size < 8:
                        print("   ❌ 预览字体过小，建议增加封面字体大小")
                        suggested_size = int(24 / preview_scale)
                        print(f"   建议封面字体大小: {suggested_size}px")
                    elif final_preview_size > 24:
                        print("   ❌ 预览字体过大，建议减少封面字体大小")
                        suggested_size = int(72 / preview_scale)
                        print(f"   建议封面字体大小: {suggested_size}px")
                    else:
                        print("   ✅ 字体大小合适")
                        
                else:
                    print("\n❌ 对话框已取消")
                
            except Exception as e:
                print(f"❌ 诊断过程中出现错误: {e}")
                import traceback
                traceback.print_exc()
            
            print("=" * 80)
        
        debug_button.clicked.connect(start_diagnosis)
        layout.addWidget(debug_button)
        
        # 添加解决方案
        solutions_label = QLabel("""
🔧 可能的解决方案：

如果封面水印太小：
✅ 增加字体大小设置（建议36px以上）
✅ 检查预览中的字体是否看起来合适
✅ 确认预览与封面的比例一致

如果预览与封面不一致：
✅ 检查缩放比例计算（应该是1/3）
✅ 确认预览使用缩放字体大小
✅ 确认封面使用原始字体大小

如果字体越来越小：
✅ 检查是否有重复缩放
✅ 确认配置保存和加载正确
✅ 重置配置到默认值

推荐字体大小：
- 小字体: 36-48px（适合长文件名）
- 中字体: 48-60px（适合中等文件名）
- 大字体: 60-72px（适合短文件名）

注意事项：
- 预览字体 = 封面字体 × (640/1920)
- 预览中12px ≈ 封面中36px
- 预览中16px ≈ 封面中48px
- 预览中24px ≈ 封面中72px
        """)
        solutions_label.setStyleSheet("""
            QLabel {
                background-color: #f0f8ff;
                padding: 15px;
                border-radius: 8px;
                font-size: 12px;
                color: #191970;
                border-left: 4px solid #4169e1;
            }
        """)
        layout.addWidget(solutions_label)
        
        main_window.show()
        
        print("字体大小问题诊断工具启动成功")
        print("请点击按钮开始诊断")
        
        return app.exec_()
        
    except Exception as e:
        print(f"诊断工具启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("启动字体大小问题诊断工具...")
    return debug_font_size_issue()

if __name__ == "__main__":
    sys.exit(main())
