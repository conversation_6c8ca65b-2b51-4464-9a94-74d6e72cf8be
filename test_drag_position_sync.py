#!/usr/bin/env python3
"""
测试拖动位置同步修复的脚本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QDialog

# 添加项目路径
sys.path.append('.')

def test_drag_position_sync():
    """测试拖动位置同步修复"""
    try:
        from app.dialogs.video_processor_dialog import WatermarkConfigDialog, WatermarkConfig
        
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("拖动位置同步修复测试")
        main_window.setGeometry(100, 100, 1000, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        main_window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 添加说明
        info_label = QLabel("""
🖱️ 拖动位置同步修复测试

问题描述：
在预览区域拖动调整位置后，参数控件不会更新，
导致设置其他参数时位置会复位到控件显示的旧值

问题原因：
on_multi_line_position_changed 方法只更新了配置中的位置，
但没有同步更新界面的位置控件（数值框和滑块）

修复内容：
✅ 拖动位置时同步更新位置数值框
✅ 拖动位置时同步更新位置滑块
✅ 添加防循环更新机制
✅ 只对当前选中行更新控件

测试步骤：
1. 打开水印配置对话框
2. 启用"多行水印模式"
3. 选择第1行，观察当前位置控件的值
4. 在预览区域拖动第1行水印到新位置
5. 观察位置控件是否更新为新的位置值
6. 修改其他参数（如颜色），观察位置是否保持
7. 切换到其他行，重复测试

预期效果：
- 拖动位置后，位置控件立即更新
- 修改其他参数时，位置不会复位
- 只有当前选中行的拖动会更新控件
- 不会出现循环更新问题
        """)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f0f8ff;
                padding: 20px;
                border-radius: 8px;
                font-size: 14px;
                line-height: 1.6;
                border: 2px solid #4169e1;
                color: #191970;
            }
        """)
        layout.addWidget(info_label)
        
        # 添加测试按钮
        test_button = QPushButton("🖱️ 测试拖动位置同步修复")
        test_button.setStyleSheet("""
            QPushButton {
                background-color: #4169e1;
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #1e90ff;
            }
        """)
        
        def open_dialog():
            print("=" * 80)
            print("开始拖动位置同步修复测试")
            print("=" * 80)
            
            try:
                # 创建水印配置
                config = WatermarkConfig()
                config.enabled = True
                config.multi_line_enabled = True
                
                print("✅ 水印配置创建成功")
                print(f"多行模式启用: {config.multi_line_enabled}")
                
                # 显示初始位置配置
                print("\n📋 初始位置配置:")
                for i, line_config in enumerate(config.multi_line_configs):
                    print(f"第{i+1}行: ({line_config.position_x}%, {line_config.position_y}%)")
                
                # 打开配置对话框
                dialog = WatermarkConfigDialog(config, main_window)
                print("✅ 对话框创建成功")
                
                print("\n🎯 请按以下步骤测试拖动位置同步:")
                print("1. 确认多行水印模式已启用")
                print("2. 选择第1行，记录当前位置控件的值")
                print("3. 在预览区域拖动第1行水印到新位置")
                print("4. 观察位置数值框和滑块是否更新")
                print("5. 修改文字颜色，观察位置是否保持")
                print("6. 选择第2行，重复上述测试")
                
                print("\n🔍 观察要点:")
                print("- 拖动时控制台会输出位置更新信息")
                print("- 位置控件应该立即反映拖动后的位置")
                print("- 修改其他参数时位置不应该复位")
                print("- 只有当前选中行的拖动会更新控件")
                
                print("\n📝 测试记录:")
                print("请记录以下信息:")
                print("- 拖动前的位置控件值")
                print("- 拖动后的位置控件值")
                print("- 修改其他参数后的位置是否保持")
                
                result = dialog.exec_()
                
                if result == QDialog.Accepted:
                    print("\n✅ 拖动位置同步测试完成")
                    print("最终位置配置:")
                    
                    for i, line_config in enumerate(config.multi_line_configs):
                        print(f"第{i+1}行最终位置: ({line_config.position_x}%, {line_config.position_y}%)")
                    
                    # 验证位置是否有变化
                    initial_positions = [(50, 90), (50, 90), (50, 90)]  # 默认位置
                    final_positions = [(line_config.position_x, line_config.position_y) 
                                     for line_config in config.multi_line_configs]
                    
                    print(f"\n📊 位置变化统计:")
                    changed_lines = 0
                    for i, (initial, final) in enumerate(zip(initial_positions, final_positions)):
                        if initial != final:
                            changed_lines += 1
                            print(f"第{i+1}行位置已改变: {initial} → {final}")
                        else:
                            print(f"第{i+1}行位置未改变: {final}")
                    
                    print(f"总计 {changed_lines} 行位置发生了改变")
                    
                    if changed_lines > 0:
                        print("✅ 成功: 拖动位置功能正常工作")
                    else:
                        print("ℹ️ 信息: 没有检测到位置变化（可能未进行拖动测试）")
                        
                else:
                    print("\n❌ 拖动位置同步测试已取消")
                
            except Exception as e:
                print(f"❌ 测试过程中出现错误: {e}")
                import traceback
                traceback.print_exc()
            
            print("=" * 80)
        
        test_button.clicked.connect(open_dialog)
        layout.addWidget(test_button)
        
        # 添加修复详情
        fix_details_label = QLabel("""
🔧 修复详情：

修复前的问题：
❌ 拖动调整位置 → 配置更新，但控件不更新
❌ 修改其他参数 → 从控件读取旧位置值
❌ 位置复位到拖动前的值

修复后的流程：
✅ 拖动调整位置 → 配置更新 + 控件同步更新
✅ 修改其他参数 → 从控件读取新位置值
✅ 位置保持拖动后的值

技术实现：
1. 在 on_multi_line_position_changed 中添加控件更新逻辑
2. 检查拖动的是否为当前选中行
3. 如果是，则同步更新位置数值框和滑块
4. 使用 _updating_position_controls 标志防止循环更新
5. 在 save_ui_config_to_line 中跳过位置保存（如果正在更新控件）

防循环机制：
- 设置 _updating_position_controls = True
- 更新位置控件
- 重置 _updating_position_controls = False
- save_ui_config_to_line 检查此标志，跳过位置保存

同步更新的控件：
- position_x_spinbox (X位置数值框)
- position_y_spinbox (Y位置数值框)  
- position_x_slider (X位置滑块)
- position_y_slider (Y位置滑块)
        """)
        fix_details_label.setStyleSheet("""
            QLabel {
                background-color: #f5f5f5;
                padding: 15px;
                border-radius: 8px;
                font-size: 12px;
                color: #333333;
                border-left: 4px solid #4169e1;
                font-family: 'Consolas', 'Monaco', monospace;
            }
        """)
        layout.addWidget(fix_details_label)
        
        main_window.show()
        
        print("拖动位置同步修复测试应用启动成功")
        print("请点击按钮开始测试")
        
        return app.exec_()
        
    except Exception as e:
        print(f"测试启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """主函数"""
    print("开始拖动位置同步修复测试...")
    return test_drag_position_sync()

if __name__ == "__main__":
    sys.exit(main())
