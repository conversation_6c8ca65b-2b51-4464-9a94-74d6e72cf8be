#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速打包脚本 - 使用PyInstaller直接打包
"""

import os
import sys
import subprocess
import datetime
from pathlib import Path

def main():
    """快速打包主函数"""
    print("🎯 头条内容社交工具 - 快速打包")
    print("=" * 40)
    
    # 获取当前时间
    now = datetime.datetime.now()
    build_date = now.strftime("%Y%m%d_%H%M")
    exe_name = f"头条内容社交工具_v6.0.2_{build_date}"
    
    print(f"📦 构建信息:")
    print(f"   版本: 6.0.2")
    print(f"   时间: {build_date}")
    print(f"   文件名: {exe_name}.exe")
    print(f"   优化: 排除用户数据目录，减小文件大小")
    print()
    
    # 检查必要文件
    if not os.path.exists('main.py'):
        print("❌ 找不到main.py文件")
        return 1
    
    # 清理旧的构建文件
    print("🧹 清理旧构建文件...")
    if os.path.exists('dist'):
        for file in os.listdir('dist'):
            if file.endswith('.exe'):
                try:
                    os.remove(os.path.join('dist', file))
                    print(f"   删除旧文件: {file}")
                except:
                    pass
    
    # 构建PyInstaller命令
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onefile',                    # 单文件模式
        '--windowed',                   # 无控制台窗口
        '--clean',                      # 清理临时文件
        '--noconfirm',                  # 不询问覆盖
        f'--name={exe_name}',           # 设置输出文件名
        '--add-data=app;app',           # 包含app目录（核心程序代码）
        # 注意：不包含用户数据目录（accounts, cookies, data, logs, videos）
        # 这些目录将在程序运行时自动创建，允许用户管理自己的数据
        
        # 隐藏导入
        '--hidden-import=PyQt5.QtCore',
        '--hidden-import=PyQt5.QtGui',
        '--hidden-import=PyQt5.QtWidgets',
        '--hidden-import=PyQt5.QtWebEngineWidgets',
        '--hidden-import=selenium',
        '--hidden-import=selenium.webdriver.chrome',
        '--hidden-import=requests',
        '--hidden-import=PIL',
        '--hidden-import=openpyxl',
        '--hidden-import=psutil',
        '--hidden-import=pandas',
        '--hidden-import=cv2',
        '--hidden-import=bs4',
        '--hidden-import=lxml',
        
        # 排除模块
        '--exclude-module=unittest',
        '--exclude-module=test',
        '--exclude-module=tests',
        '--exclude-module=pytest',
        '--exclude-module=matplotlib',
        
        'main.py'  # 主程序文件
    ]
    
    # 添加图标（如果存在）
    icon_paths = [
        'app/resources/icons/gray_wolf.ico',
        'app/resources/icons/app_icon.ico',
        'app/resources/icon.ico'
    ]
    
    for icon_path in icon_paths:
        if os.path.exists(icon_path):
            cmd.insert(-1, f'--icon={icon_path}')
            print(f"✅ 使用图标: {icon_path}")
            break
    else:
        print("⚠️ 未找到图标文件")
    
    print("🚀 开始打包...")
    print(f"命令: {' '.join(cmd[:5])} ... (共{len(cmd)}个参数)")
    print()
    
    try:
        # 执行打包命令
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ 打包成功！")
            
            # 检查输出文件
            exe_path = f"dist/{exe_name}.exe"
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"📁 输出文件: {exe_path}")
                print(f"📏 文件大小: {file_size:.1f} MB")
                
                # 询问是否打开目录
                try:
                    response = input("\n是否打开输出目录？(y/N): ").strip().lower()
                    if response in ['y', 'yes', '是']:
                        if os.name == 'nt':  # Windows
                            os.startfile('dist')
                        else:
                            subprocess.run(['xdg-open', 'dist'])
                except:
                    pass
                
                return 0
            else:
                print(f"❌ 输出文件不存在: {exe_path}")
                return 1
        else:
            print("❌ 打包失败！")
            print("错误信息:")
            print(result.stderr)
            return 1
            
    except subprocess.TimeoutExpired:
        print("❌ 打包超时（10分钟）")
        return 1
    except Exception as e:
        print(f"❌ 打包异常: {e}")
        return 1
    finally:
        # 清理spec文件
        spec_files = list(Path('.').glob('*.spec'))
        for spec_file in spec_files:
            if spec_file.name.startswith('头条内容社交工具_v6.0.2_'):
                try:
                    spec_file.unlink()
                    print(f"🧹 清理配置文件: {spec_file}")
                except:
                    pass

if __name__ == "__main__":
    sys.exit(main())
