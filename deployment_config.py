#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
部署配置和脚本
"""

import os
import yaml

# Docker Compose 配置
DOCKER_COMPOSE_CONFIG = """
version: '3.8'

services:
  cookie-validation-server:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - SECRET_KEY=${SECRET_KEY}
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - redis
      - postgres
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=cookie_validation
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - cookie-validation-server
    restart: unless-stopped

volumes:
  redis_data:
  postgres_data:
"""

# Nginx 配置
NGINX_CONFIG = """
events {
    worker_connections 1024;
}

http {
    upstream app {
        server cookie-validation-server:5000;
    }

    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=register:10m rate=1r/m;

    server {
        listen 80;
        server_name your-domain.com;
        
        # 重定向到HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        # SSL配置
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";

        # API路由
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 超时设置
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # 注册接口特殊限流
        location /api/register-client {
            limit_req zone=register burst=5 nodelay;
            
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # 健康检查
        location /health {
            proxy_pass http://app;
            access_log off;
        }
    }
}
"""

# Dockerfile
DOCKERFILE = """
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建日志目录
RUN mkdir -p logs

# 设置环境变量
ENV FLASK_APP=cookie_validation_server.py
ENV FLASK_ENV=production

# 暴露端口
EXPOSE 5000

# 启动命令
CMD ["gunicorn", "--bind", "0.0.0.0:5000", "--workers", "4", "--timeout", "30", "cookie_validation_server:app"]
"""

# 服务器依赖文件
REQUIREMENTS_TXT = """
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Flask-Limiter==3.5.0
PyJWT==2.8.0
redis==5.0.1
requests==2.31.0
cryptography==41.0.7
gunicorn==21.2.0
psycopg2-binary==2.9.7
"""

# 环境变量模板
ENV_TEMPLATE = """
# 数据库配置
DATABASE_URL=********************************************/cookie_validation
POSTGRES_USER=cookie_user
POSTGRES_PASSWORD=your_secure_password

# Redis配置
REDIS_URL=redis://redis:6379/0

# 安全密钥（请更改为随机值）
SECRET_KEY=your-super-secret-key-change-this-in-production
MASTER_SECRET=your-master-secret-key-change-this
JWT_SECRET=your-jwt-secret-key-change-this

# 服务器配置
FLASK_ENV=production
FLASK_DEBUG=False

# SSL证书路径（如果使用HTTPS）
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem
"""

# 部署脚本
DEPLOY_SCRIPT = """#!/bin/bash

# Cookie验证服务器部署脚本

set -e

echo "🚀 开始部署Cookie验证服务器..."

# 检查Docker和Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 创建必要的目录
mkdir -p logs ssl

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "⚠️ 未找到.env文件，创建模板文件..."
    cat > .env << 'EOF'
# 请修改以下配置
DATABASE_URL=***********************************************************/cookie_validation
POSTGRES_USER=cookie_user
POSTGRES_PASSWORD=your_secure_password
REDIS_URL=redis://redis:6379/0
SECRET_KEY=$(openssl rand -hex 32)
MASTER_SECRET=$(openssl rand -hex 32)
JWT_SECRET=$(openssl rand -hex 32)
EOF
    echo "📝 请编辑.env文件并设置正确的配置"
    exit 1
fi

# 生成SSL证书（自签名，生产环境请使用正式证书）
if [ ! -f ssl/cert.pem ]; then
    echo "🔐 生成SSL证书..."
    openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes \\
        -subj "/C=CN/ST=State/L=City/O=Organization/CN=your-domain.com"
fi

# 构建和启动服务
echo "🔨 构建Docker镜像..."
docker-compose build

echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
if curl -f http://localhost:5000/health > /dev/null 2>&1; then
    echo "✅ 服务启动成功！"
    echo "📊 服务状态："
    docker-compose ps
    echo ""
    echo "🌐 API地址: http://localhost:5000/api"
    echo "🔍 健康检查: http://localhost:5000/health"
else
    echo "❌ 服务启动失败，请检查日志："
    docker-compose logs
    exit 1
fi

echo "🎉 部署完成！"
"""

# 监控脚本
MONITOR_SCRIPT = """#!/bin/bash

# 服务监控脚本

echo "📊 Cookie验证服务器监控"
echo "========================"

# 检查服务状态
echo "🔍 服务状态："
docker-compose ps

echo ""
echo "💾 资源使用："
docker stats --no-stream --format "table {{.Container}}\\t{{.CPUPerc}}\\t{{.MemUsage}}"

echo ""
echo "🌐 网络连接："
netstat -tlnp | grep :5000 || echo "端口5000未监听"

echo ""
echo "📝 最近日志："
docker-compose logs --tail=20 cookie-validation-server

echo ""
echo "🔍 健康检查："
if curl -f http://localhost:5000/health > /dev/null 2>&1; then
    echo "✅ 服务正常"
else
    echo "❌ 服务异常"
fi
"""

def create_deployment_files():
    """创建部署文件"""
    files = {
        'docker-compose.yml': DOCKER_COMPOSE_CONFIG,
        'nginx.conf': NGINX_CONFIG,
        'Dockerfile': DOCKERFILE,
        'requirements.txt': REQUIREMENTS_TXT,
        '.env.template': ENV_TEMPLATE,
        'deploy.sh': DEPLOY_SCRIPT,
        'monitor.sh': MONITOR_SCRIPT
    }
    
    for filename, content in files.items():
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(content.strip())
        
        # 给脚本文件添加执行权限
        if filename.endswith('.sh'):
            os.chmod(filename, 0o755)
    
    print("✅ 部署文件创建完成！")
    print("\n📋 部署步骤：")
    print("1. 复制.env.template为.env并修改配置")
    print("2. 运行: ./deploy.sh")
    print("3. 使用: ./monitor.sh 监控服务")

if __name__ == "__main__":
    create_deployment_files()
