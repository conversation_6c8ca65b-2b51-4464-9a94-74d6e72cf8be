[tool.pylance]
# Pylance配置
reportMissingImports = "warning"
reportMissingTypeStubs = "none"
reportOptionalMemberAccess = "warning"
reportAttributeAccessIssue = "information"  # 降低PyQt5兼容性警告级别
reportArgumentType = "warning"
reportUnusedVariable = "information"

# PyQt5兼容性设置
stubPath = "typings"

[tool.pyright]
# Pyright配置
include = ["app"]
exclude = ["**/__pycache__", "build", "dist"]

# 类型检查设置
typeCheckingMode = "basic"
reportMissingImports = "warning"
reportMissingTypeStubs = false
reportOptionalMemberAccess = "warning"
reportAttributeAccessIssue = "information"  # 降低PyQt5兼容性警告级别
reportArgumentType = "warning"
reportUnusedVariable = "information"

# PyQt5特定设置
reportUnknownMemberType = "none"
reportUnknownVariableType = "none"
reportUnknownArgumentType = "none"

# 忽略PyQt5版本兼容性问题
reportGeneralTypeIssues = "warning"

# 忽略特定的PyQt5属性访问问题
reportAttributeAccessIssue = "information"
