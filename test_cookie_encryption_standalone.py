#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Cookie加密功能独立测试脚本
"""

import os
import sys
import json

# 添加项目路径到sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_cookie_encryption():
    """测试Cookie加密功能"""
    try:
        print("=" * 60)
        print("Cookie加密功能测试")
        print("=" * 60)
        
        # 导入加密模块
        from app.utils.cookie_encryption import CookieEncryption, test_cookie_encryption
        
        print("✅ 成功导入Cookie加密模块")
        
        # 运行内置测试
        print("\n🔧 运行内置加密测试...")
        test_result = test_cookie_encryption()
        
        if test_result:
            print("✅ 内置加密测试通过！")
        else:
            print("❌ 内置加密测试失败！")
            return False
        
        # 创建测试Cookie文件
        print("\n📁 创建测试Cookie文件...")
        test_cookie_data = {
            "accountId": "test_account_123",
            "remark": "测试账号",
            "cookies": {
                "sessionid": "test_session_value_123456",
                "csrf_token": "test_csrf_token_789012",
                "uid_tt": "test_uid_345678",
                "passport_csrf_token": "test_passport_csrf_901234",
                "sid_guard": "test_sid_guard_567890",
                "sid_tt": "test_sid_tt_123456"
            }
        }
        
        # 保存测试文件
        test_file = "test_cookie.json"
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_cookie_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 测试Cookie文件已创建: {test_file}")
        
        # 测试文件加密
        print("\n🔐 测试文件加密...")
        encryptor = CookieEncryption()
        
        # 加密文件
        encrypted_file = "test_cookie_encrypted.json"
        encrypt_success = encryptor.encrypt_cookie_file(test_file, encrypted_file)
        
        if encrypt_success:
            print(f"✅ 文件加密成功: {encrypted_file}")
        else:
            print("❌ 文件加密失败！")
            return False
        
        # 验证加密文件
        print("\n🔍 验证加密文件...")
        is_encrypted = encryptor.is_encrypted_file(encrypted_file)
        if is_encrypted:
            print("✅ 文件已正确加密")
        else:
            print("❌ 文件加密验证失败！")
            return False
        
        # 测试文件解密
        print("\n🔓 测试文件解密...")
        decrypted_file = "test_cookie_decrypted.json"
        decrypt_success = encryptor.decrypt_cookie_file(encrypted_file, decrypted_file)
        
        if decrypt_success:
            print(f"✅ 文件解密成功: {decrypted_file}")
        else:
            print("❌ 文件解密失败！")
            return False
        
        # 验证解密后的数据
        print("\n✅ 验证解密后的数据...")
        with open(decrypted_file, 'r', encoding='utf-8') as f:
            decrypted_data = json.load(f)
        
        if decrypted_data == test_cookie_data:
            print("✅ 解密数据与原始数据完全一致！")
        else:
            print("❌ 解密数据与原始数据不一致！")
            print("原始数据:", test_cookie_data)
            print("解密数据:", decrypted_data)
            return False
        
        # 清理测试文件
        print("\n🧹 清理测试文件...")
        for file_path in [test_file, encrypted_file, decrypted_file]:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"✅ 已删除: {file_path}")
        
        print("\n" + "=" * 60)
        print("🎉 Cookie加密功能测试全部通过！")
        print("🔐 加密算法: AES-256")
        print("🔑 密钥: zhengyang.0924")
        print("✅ 功能正常，可以安全使用")
        print("=" * 60)
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {str(e)}")
        print("请确保已安装 cryptography 库:")
        print("pip install cryptography==42.0.5")
        return False
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_unified_login_manager():
    """测试统一登录管理器对加密文件的支持"""
    try:
        print("\n" + "=" * 60)
        print("统一登录管理器加密支持测试")
        print("=" * 60)
        
        from app.utils.unified_login_manager import FastCookieParser
        from app.utils.cookie_encryption import CookieEncryption
        
        # 创建测试数据
        test_cookie_data = {
            "accountId": "test_login_account",
            "remark": "登录测试账号",
            "cookies": {
                "sessionid": "login_test_session_123",
                "csrf_token": "login_test_csrf_456",
                "passport_csrf_token": "login_test_passport_789",
                "sid_guard": "login_test_sid_guard_012",
                "sid_tt": "login_test_sid_tt_345",
                "uid_tt": "login_test_uid_678"
            }
        }
        
        # 创建加密文件
        encryptor = CookieEncryption()
        encrypted_data = encryptor.encrypt_cookie_data(test_cookie_data)
        
        encrypted_file_data = {
            "encrypted": True,
            "version": "1.0",
            "algorithm": "AES-256",
            "data": encrypted_data,
            "account_id": "test_login_account",
            "remark": "登录测试账号"
        }
        
        # 保存加密文件
        test_encrypted_file = "test_login_encrypted.json"
        with open(test_encrypted_file, 'w', encoding='utf-8') as f:
            json.dump(encrypted_file_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建加密登录测试文件: {test_encrypted_file}")
        
        # 测试FastCookieParser解析加密文件
        print("\n🔍 测试FastCookieParser解析加密文件...")
        parsed_cookies = FastCookieParser.parse_cookie_file(test_encrypted_file)
        
        if parsed_cookies:
            print(f"✅ 成功解析加密文件，提取到 {len(parsed_cookies)} 个关键Cookie")
            print("解析结果:", parsed_cookies)
            
            # 验证关键Cookie是否正确提取
            expected_cookies = ['sessionid', 'csrf_token', 'passport_csrf_token', 'sid_guard', 'sid_tt', 'uid_tt']
            found_cookies = list(parsed_cookies.keys())
            
            if all(cookie in found_cookies for cookie in expected_cookies if cookie in test_cookie_data['cookies']):
                print("✅ 所有关键Cookie都已正确提取")
            else:
                print("⚠️ 部分关键Cookie未提取到")
                
        else:
            print("❌ 解析加密文件失败！")
            return False
        
        # 清理测试文件
        if os.path.exists(test_encrypted_file):
            os.remove(test_encrypted_file)
            print(f"✅ 已删除测试文件: {test_encrypted_file}")
        
        print("\n✅ 统一登录管理器加密支持测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 统一登录管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("🚀 开始Cookie加密功能完整测试...")
    
    # 测试基础加密功能
    basic_test_result = test_cookie_encryption()
    
    if basic_test_result:
        # 测试登录管理器集成
        login_test_result = test_unified_login_manager()
        
        if login_test_result:
            print("\n🎉 所有测试都通过了！Cookie加密功能已准备就绪。")
            print("\n📋 使用说明:")
            print("1. 新添加的账号将自动使用AES-256加密保存")
            print("2. 点击'Cookie加密测试'按钮可以加密现有账号")
            print("3. 加密后的Cookie文件只能在本软件中使用")
            print("4. 原文件会自动备份到'加密前备份'文件夹")
        else:
            print("\n❌ 登录管理器集成测试失败")
            sys.exit(1)
    else:
        print("\n❌ 基础加密功能测试失败")
        sys.exit(1)
