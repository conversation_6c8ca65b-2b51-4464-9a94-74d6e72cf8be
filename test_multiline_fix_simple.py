#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的多行水印封面生成修复测试（无GUI）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_multiline_cover_fix():
    """测试多行水印封面生成修复"""
    print("=" * 80)
    print("🧪 多行水印封面生成修复测试")
    print("=" * 80)
    
    try:
        # 导入必要的模块
        print("\n1. 导入模块...")
        from app.dialogs.video_processor_dialog import WatermarkConfig, MultiLineWatermarkConfig
        from PIL import Image
        print("   ✅ 模块导入成功")
        
        # 2. 创建水印配置
        print("\n2. 创建水印配置...")
        config = WatermarkConfig()
        print(f"   默认多行模式: {config.multi_line_enabled}")
        
        # 启用多行模式
        config.multi_line_enabled = True
        print(f"   设置多行模式: {config.multi_line_enabled}")
        
        # 配置多行参数
        if len(config.multi_line_configs) >= 3:
            # 第一行：3个字符
            config.multi_line_configs[0].enabled = True
            config.multi_line_configs[0].char_count = 3
            config.multi_line_configs[0].font_size = 80
            config.multi_line_configs[0].position_y = 75
            
            # 第二行：2个字符
            config.multi_line_configs[1].enabled = True
            config.multi_line_configs[1].char_count = 2
            config.multi_line_configs[1].font_size = 80
            config.multi_line_configs[1].position_y = 85
            
            # 第三行：1个字符
            config.multi_line_configs[2].enabled = True
            config.multi_line_configs[2].char_count = 1
            config.multi_line_configs[2].font_size = 80
            config.multi_line_configs[2].position_y = 95
            
            print(f"   第1行: {config.multi_line_configs[0].char_count}字符, 字体{config.multi_line_configs[0].font_size}px")
            print(f"   第2行: {config.multi_line_configs[1].char_count}字符, 字体{config.multi_line_configs[1].font_size}px")
            print(f"   第3行: {config.multi_line_configs[2].char_count}字符, 字体{config.multi_line_configs[2].font_size}px")
        
        # 3. 创建模拟的VideoProcessorWorker
        print("\n3. 创建模拟的VideoProcessorWorker...")
        from app.dialogs.video_processor_dialog import VideoProcessorWorker
        
        # 创建worker实例
        worker = VideoProcessorWorker(
            input_dir="test", output_dir="test", cover_dir="test",
            thread_count=1, worker_id=0, shared_data={},
            watermark_config=config
        )
        print("   ✅ Worker创建成功")
        print(f"   Worker多行模式: {worker.watermark_config.multi_line_enabled}")
        
        # 4. 测试水印添加方法
        print("\n4. 测试水印添加方法...")
        test_img = Image.new('RGB', (1920, 1080), color='#2c3e50')
        test_text = "测试多行文字"
        
        print(f"   测试图像: {test_img.size}")
        print(f"   测试文字: '{test_text}'")
        
        # 检查方法是否存在
        if hasattr(worker, 'add_watermark_to_pil_image'):
            print("   ✅ add_watermark_to_pil_image 方法存在")
            
            # 测试水印添加
            print("   开始添加水印...")
            watermarked = worker.add_watermark_to_pil_image(test_img, test_text)
            
            if watermarked:
                print("   ✅ 水印添加成功")
                print(f"   结果图像: {watermarked.size}")
                
                # 保存测试结果
                test_output_path = "test_multiline_cover_simple.jpg"
                watermarked.save(test_output_path, 'JPEG', quality=95)
                print(f"   ✅ 测试结果已保存: {test_output_path}")
            else:
                print("   ❌ 水印添加失败")
        else:
            print("   ❌ add_watermark_to_pil_image 方法不存在")
        
        # 5. 验证修复效果
        print("\n5. 验证修复效果...")
        if config.multi_line_enabled:
            print("   ✅ 多行模式已启用")
            print("   ✅ 封面生成会检查多行模式")
            print("   ✅ 多行模式会调用专门的多行处理方法")
            print("   ✅ 每行会独立绘制水印")
        else:
            print("   ⚠️ 多行模式未启用")
        
        print("\n✅ 修复验证完成!")
        print("\n修复详情:")
        print("- add_watermark_to_pil_image 现在会检查 multi_line_enabled")
        print("- 多行模式调用 add_multiline_watermark_to_pil_image")
        print("- 单行模式调用 add_single_watermark_to_pil_image")
        print("- 确保封面生成与预览完全一致")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    test_multiline_cover_fix()
