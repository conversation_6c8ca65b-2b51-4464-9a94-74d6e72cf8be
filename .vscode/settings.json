{"python.defaultInterpreterPath": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\python.exe", "python.pythonPath": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\python.exe", "pylance.insidersChannel": "off", "python.analysis.extraPaths": ["C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\site-packages"], "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "python.analysis.diagnosticSeverityOverrides": {"reportAttributeAccessIssue": "none", "reportUnknownMemberType": "none", "reportUnknownArgumentType": "none", "reportUnknownVariableType": "none", "reportUnknownParameterType": "none", "reportMissingTypeStubs": "none", "reportPrivateUsage": "none", "reportUntypedFunctionDecorator": "none", "reportUntypedClassDecorator": "none", "reportCallInDefaultInitializer": "none", "reportUnnecessaryIsInstance": "none", "reportUnknownLambdaType": "none", "reportFunctionMemberAccess": "none", "reportUninitializedInstanceVariable": "none", "reportCallIssue": "none", "reportArgumentType": "none", "reportIncompatibleMethodOverride": "none", "reportOptionalMemberAccess": "none", "reportOptionalSubscript": "none", "reportOptionalCall": "none", "reportOptionalIterable": "none", "reportOptionalContextManager": "none", "reportOptionalOperand": "none"}, "python.analysis.stubPath": "./typings"}