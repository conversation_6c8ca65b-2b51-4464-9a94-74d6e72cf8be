#!/usr/bin/env python3
"""
测试定时器修复效果的脚本
"""

import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.append('.')

def test_single_shot_timer():
    """测试单次定时器功能"""
    print("=" * 50)
    print("测试单次定时器功能")
    print("=" * 50)

    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer

        app = QApplication([])

        # 计数器
        call_count = 0

        def test_callback():
            nonlocal call_count
            call_count += 1
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 回调被调用，次数: {call_count}")

        # 测试我们修复的 create_single_shot_timer 方法
        class TestMainWindow:
            def create_single_shot_timer(self, delay_ms, callback):
                """创建单次执行定时器的辅助方法"""
                def single_shot_wrapper():
                    """包装器函数，确保只执行一次后停止定时器"""
                    try:
                        callback()
                    finally:
                        # 执行完成后停止定时器
                        if hasattr(single_shot_wrapper, 'timer'):
                            single_shot_wrapper.timer.stop()

                timer = QTimer()
                timer.timeout.connect(single_shot_wrapper)
                single_shot_wrapper.timer = timer  # 保存定时器引用
                timer.start(delay_ms)
                return timer

        test_window = TestMainWindow()
        timer = test_window.create_single_shot_timer(1000, test_callback)

        print("单次定时器已启动，等待3秒...")

        # 等待3秒
        start_time = time.time()
        while time.time() - start_time < 3:
            app.processEvents()
            time.sleep(0.1)

        print(f"测试完成，回调总共被调用了 {call_count} 次")
        print("预期结果: 1次")
        print("测试结果:", "✅ 通过" if call_count == 1 else "❌ 失败")

        return call_count == 1

    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_user_status_checker():
    """测试用户状态检测器的重复启动防护"""
    print("\n" + "=" * 50)
    print("测试用户状态检测器重复启动防护")
    print("=" * 50)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from app.utils.yonghu_zhuangtai import UserStatusChecker
        
        app = QApplication([])
        
        # 创建状态检测器
        checker = UserStatusChecker(check_interval=300)
        
        print("第一次启动检测...")
        result1 = checker.start_checking()
        print(f"第一次启动结果: {result1}")
        
        print("第二次启动检测...")
        result2 = checker.start_checking()
        print(f"第二次启动结果: {result2}")
        
        print("第三次启动检测...")
        result3 = checker.start_checking()
        print(f"第三次启动结果: {result3}")
        
        # 检查是否正确防止了重复启动
        success = result1 and result2 and result3
        print(f"测试结果: {'✅ 通过' if success else '❌ 失败'}")
        
        # 清理
        checker.stop_checking()
        
        return success
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试定时器修复效果...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试结果
    results = []
    
    # 测试1: 单次定时器
    results.append(test_single_shot_timer())
    
    # 测试2: 用户状态检测器
    results.append(test_user_status_checker())
    
    # 总结
    print("\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试都通过了！定时器修复成功！")
        return 0
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
