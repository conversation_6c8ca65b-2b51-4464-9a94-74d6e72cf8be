#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自动登录工具 - 通过RoboCorp自动化登录工具
可在应用启动时自动尝试登录，支持重试机制和配置验证
"""

import os
import sys
import time
import logging
import json
import traceback
from pathlib import Path
from functools import wraps

# 为了导入应用程序模块，需要添加项目根目录到Python路径
script_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(script_dir)
if root_dir not in sys.path:
    sys.path.append(root_dir)

# 导入应用程序的卡密登录模块
from app.utils.kamidenglu import (
    force_login_dialog, KamiManager, CONFIG_FILE, 
    load_config, save_config, debug_environment
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(script_dir, "auto_login.log"), encoding="utf-8"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("auto_login")

def retry(max_attempts=3, delay=2):
    """重试装饰器，指定最大尝试次数和延迟时间
    
    Args:
        max_attempts: 最大尝试次数
        delay: 每次重试间隔时间(秒)
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            attempts = 0
            last_error = None
            
            while attempts < max_attempts:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    attempts += 1
                    last_error = e
                    logger.warning(f"尝试 {attempts}/{max_attempts} 失败: {str(e)}")
                    if attempts < max_attempts:
                        logger.info(f"等待 {delay} 秒后重试...")
                        time.sleep(delay)
            
            # 所有尝试失败后，记录详细错误并抛出最后一个异常
            logger.error(f"所有 {max_attempts} 次尝试都失败了: {str(last_error)}")
            logger.error(traceback.format_exc())
            raise last_error
        
        return wrapper
    return decorator

def is_config_valid():
    """检查卡密配置是否有效
    
    Returns:
        bool: 配置是否有效
    """
    try:
        if not os.path.exists(CONFIG_FILE):
            logger.warning(f"配置文件不存在: {CONFIG_FILE}")
            return False
            
        # 尝试加载配置
        config = load_config()
        if not config:
            logger.warning("配置为空或无效")
            return False
            
        # 检查必要的字段
        if not config.get('card'):
            logger.warning("配置中缺少卡密信息")
            return False
            
        # 检查是否设置了记住登录状态
        if config.get('remember_login') is None:
            logger.info("配置中未设置记住登录状态，添加默认值True")
            config['remember_login'] = True
            save_config(config)
        
        logger.info(f"配置有效: {config}")
        return True
    except Exception as e:
        logger.error(f"验证配置时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return False

@retry(max_attempts=3, delay=2)
def perform_auto_login(reset_config=False):
    """执行自动登录
    
    Args:
        reset_config: 是否重置配置
        
    Returns:
        bool: 登录是否成功
    """
    logger.info("开始自动登录流程")
    
    # 如果指定重置配置，先删除配置文件
    if reset_config:
        try:
            if os.path.exists(CONFIG_FILE):
                logger.info(f"正在删除配置文件: {CONFIG_FILE}")
                os.remove(CONFIG_FILE)
                logger.info("配置文件已删除")
        except Exception as e:
            logger.error(f"删除配置文件失败: {str(e)}")
    
    # 调用force_login_dialog函数执行登录
    logger.info("调用登录对话框")
    login_success = force_login_dialog(reset_config=False, auto_login=True)
    
    # 登录后检查结果
    if login_success:
        logger.info("登录成功")
        # 验证配置是否正确保存
        if is_config_valid():
            logger.info("配置文件验证成功")
            # 尝试使用KamiManager验证卡密
            try:
                kami_manager = KamiManager()
                valid, expiry_time = kami_manager.check_license()
                if valid:
                    logger.info(f"卡密验证成功，到期时间: {expiry_time}")
                else:
                    logger.warning(f"卡密验证失败: {expiry_time}")
                    return False
            except Exception as e:
                logger.error(f"验证卡密时出错: {str(e)}")
                logger.error(traceback.format_exc())
                return False
        else:
            logger.warning("登录成功但配置文件验证失败")
            return False
    else:
        logger.warning("登录失败")
        return False
    
    return True

def main():
    """主函数，处理自动登录流程"""
    logger.info("=== 自动登录工具启动 ===")
    
    # 打印调试信息
    debug_environment()
    
    # 检查现有配置
    if is_config_valid():
        logger.info("发现有效配置，尝试使用现有配置登录")
        try:
            # 尝试使用现有配置直接登录
            kami_manager = KamiManager()
            valid, expiry_time = kami_manager.check_license()
            
            if valid:
                logger.info(f"使用现有配置登录成功，到期时间: {expiry_time}")
                return True
            else:
                logger.warning(f"使用现有配置登录失败: {expiry_time}")
                # 配置无效，尝试重新登录
                return perform_auto_login(reset_config=False)
        except Exception as e:
            logger.error(f"使用现有配置登录时出错: {str(e)}")
            logger.error(traceback.format_exc())
            # 出现错误，尝试重新登录
            return perform_auto_login(reset_config=True)
    else:
        logger.info("未发现有效配置，执行新的登录流程")
        return perform_auto_login(reset_config=False)

if __name__ == "__main__":
    success = main()
    if success:
        logger.info("自动登录成功，程序退出")
        sys.exit(0)
    else:
        logger.error("自动登录失败，程序退出")
        sys.exit(1) 